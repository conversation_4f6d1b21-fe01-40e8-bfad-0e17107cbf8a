"""
配置文件 - 包含所有常量和配置参数
"""

# 文件路径配置
class FilePaths:
    # 动态获取项目根目录
    import os
    _script_dir = os.path.dirname(os.path.abspath(__file__))
    _project_root = os.path.dirname(_script_dir)  # 上一级目录
    BASE_DIR = os.path.join(_project_root, "IOT")
    DEFAULT_FILE1_NAME = "SETTLEMENT_REPORT_03062025_IOT.xlsx"
    DEFAULT_FILE2_NAME = "030625 CHINA IOT.xlsx"
    DEFAULT_SHEET_NAME = "TRANSACTION_LIST"

# 输出配置
class OutputConfig:
    DATA_SHEET_NAME = "DATA"
    LOG_SHEET_NAME = "LOG"

# 数据处理配置
class ProcessingConfig:
    # 时间阈值（秒）- 递进式匹配
    TIME_THRESHOLDS = [10, 30, 180, 300, 600, 1800, 3600, 10800]
    
    # 默认时间
    DEFAULT_TIME = "00:00:00"
    
    # 金额比较精度
    AMOUNT_PRECISION = 1e-2
    
    # 最大修正尝试次数
    MAX_CORRECTION_ATTEMPTS = 3

# 列名配置
class ColumnNames:
    # 第一文件必需列
    FILE1_REQUIRED_COLUMNS = [
        "Date", "Time", "Transaction ID", "Order ID", "Bill Amt", "Status"
    ]
    
    # 第一文件完整列名（28列）
    FILE1_FULL_HEADERS = [
        "Date", "Time", "Merchant / Sub Merchant ID", "Channel", "Transaction ID",
        "Order ID", "Billing Name", "Transaction Currency", "Bill Amt", "Actual Amt",
        "Status", "Settlement Ref No.", "Region", "Store ID", "Store Name", "Store Address",
        "Settlement Name", "Reference ID", "Type", "Discount Amount", "Discount Amount Remark",
        "Gross Amount", "Service Fees", "Service Fee Remark", "Convert Rate", "Convert Bill Amt",
        "Convert Actual Amt", "Settlement Currency"
    ]
    
    # 第一文件无Time列的列名（27列）
    FILE1_NO_TIME_HEADERS = [
        "Date", "Merchant / Sub Merchant ID", "Channel", "Transaction ID",
        "Order ID", "Billing Name", "Transaction Currency", "Bill Amt", "Actual Amt",
        "Status", "Settlement Ref No.", "Region", "Store ID", "Store Name", "Store Address",
        "Settlement Name", "Reference ID", "Type", "Discount Amount", "Discount Amount Remark",
        "Gross Amount", "Service Fees", "Service Fee Remark", "Convert Rate", "Convert Bill Amt",
        "Convert Actual Amt", "Settlement Currency"
    ]
    
    # 第二文件必需列
    FILE2_REQUIRED_COLUMNS = ["Order price", "Order status", "Order time"]
    
    # 第二文件列名映射
    FILE2_COLUMN_MAPPING = {
        "order price": "Order price",
        "orderprice": "Order price",
        "order status": "Order status", 
        "orderstatus": "Order status",
        "order time": "Order time",
        "ordertime": "Order time",
        "equipment id": "Equipment ID",
        "equipmentid": "Equipment ID",
        "order no.": "Order No.",
        "orderno": "Order No."
    }

# 订单类型配置
class OrderTypes:
    OFFLINE_ORDER = "Offline order"    # 9位ID对应
    NORMAL_ORDER = "Normal order"      # 超过9位ID对应
    ANOMALY_ORDER = "Anomaly order"    # 异常ID对应

# 订单ID类型
class OrderIDTypes:
    NINE_DIGIT = "9_digit"
    OVER_NINE = "over_9"
    ANOMALY = "anomaly"
    OTHER = "other"

# 状态配置
class StatusConfig:
    SETTLED = "settled"
    FINISH = "finish"

# 搜索字段配置
class SearchFields:
    EQUIPMENT_ID = "Equipment ID"
    ORDER_NO = "Order No."

# 日志消息模板
class LogMessages:
    ANOMALY_INSERTED = "{datetime} {order_id} ANOMALY inserted RM{amount:.2f}"
    PRICE_UPDATED = "{datetime} {order_id} updated price from RM{old_price:.2f} to RM{amount:.2f}"
    STATUS_UPDATED = "{datetime} {order_id} updated status from {old_status} to Finish RM{amount:.2f}"
    TRANSACTION_ID_MATCHED = "{datetime} {order_id} (Transaction ID: {trans_id}) matched by Transaction ID RM{amount:.2f}"
    TRANSACTION_ID_PRICE_UPDATED = "{datetime} {order_id} (Transaction ID: {trans_id}) updated price from RM{old_price:.2f} to RM{amount:.2f}"
    TRANSACTION_ID_STATUS_UPDATED = "{datetime} {order_id} (Transaction ID: {trans_id}) updated status from {old_status} to Finish RM{amount:.2f}"
    CONFLICT_DETECTED = "Conflict detected! Order No. {order_id} was matched by 9-digit ID"

# 验证配置
class ValidationConfig:
    # 预期的第一文件列数
    EXPECTED_FILE1_COLUMNS = 27
    
    # 无效的Transaction ID值
    INVALID_TRANSACTION_IDS = ["", "nan", "null", "none"]

# 自动修正配置
class AutoCorrectionConfig:
    # 金额差异阈值
    AMOUNT_DIFFERENCE_THRESHOLD = 0.01
    
    # 修正操作类型
    ADD_MISSING_ORDER = "添加缺失订单"
    DELETE_EXTRA_ORDER = "删除多余订单"
    
    # 修正日志模板
    ADD_ORDER_LOG = "自动修正: 添加缺失订单 {order_id}, 金额 RM{amount:.2f}, 时间 {datetime}"
    DELETE_ORDER_LOG = "自动修正: 删除多余订单 {order_id}, 金额 RM{amount:.2f}, 时间 {order_time}"

# 字段补全配置
class FieldCompletionConfig:
    # 默认设备名称映射
    DEFAULT_EQUIPMENT_NAMES = {
        # 可以根据需要添加设备ID到设备名称的映射
    }
    
    # 默认分支名称映射
    DEFAULT_BRANCH_NAMES = {
        # 可以根据需要添加分支映射
    }
    
    # 需要补全的字段
    FIELDS_TO_COMPLETE = ["Equipment name", "Branch name"]
