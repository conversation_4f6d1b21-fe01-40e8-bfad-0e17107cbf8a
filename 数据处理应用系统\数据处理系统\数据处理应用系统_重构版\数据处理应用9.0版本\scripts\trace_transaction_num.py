# -*- coding: utf-8 -*-
"""
追踪Transaction Num数据流
从Excel读取到数据库插入的完整流程追踪
"""

import pandas as pd
import sqlite3
import os
import sys
from datetime import datetime

def trace_excel_to_database():
    """追踪从Excel到数据库的完整数据流"""
    print("🔍 Transaction Num数据流完整追踪")
    print("=" * 80)
    
    # 步骤1: 检查最近的Excel文件
    print("📋 步骤1: 检查Excel文件中的Transaction Num")
    
    # 查找最近的Excel文件
    data_dir = "C:/Users/<USER>/Desktop/Day Report"
    excel_files = []
    
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith(('.xlsx', '.xls')) and 'Transaction' not in file:
                excel_files.append(os.path.join(root, file))
    
    # 按修改时间排序，取最新的
    if excel_files:
        excel_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        latest_excel = excel_files[0]
        
        print(f"📁 最新Excel文件: {latest_excel}")
        print(f"📅 修改时间: {datetime.fromtimestamp(os.path.getmtime(latest_excel))}")
        
        try:
            # 读取Excel文件
            df_excel = pd.read_excel(latest_excel)
            
            print(f"📊 Excel文件统计:")
            print(f"  总行数: {len(df_excel)}")
            print(f"  总列数: {len(df_excel.columns)}")
            
            # 检查Transaction Num列
            transaction_num_columns = [col for col in df_excel.columns if 'Transaction' in str(col) and 'Num' in str(col)]
            
            if transaction_num_columns:
                for col in transaction_num_columns:
                    print(f"\n📋 发现Transaction Num相关列: '{col}'")
                    
                    # 统计数据
                    non_null_count = df_excel[col].notna().sum()
                    non_empty_count = (df_excel[col].notna() & (df_excel[col] != '')).sum()
                    unique_count = df_excel[col].nunique()
                    
                    print(f"  非空值数量: {non_null_count}")
                    print(f"  非空字符串数量: {non_empty_count}")
                    print(f"  唯一值数量: {unique_count}")
                    
                    # 显示样本数据
                    sample_data = df_excel[col].dropna().head(10).tolist()
                    print(f"  样本数据: {sample_data}")
                    
                    if non_empty_count == 0:
                        print(f"  ⚠️ 警告: Excel文件中Transaction Num列为空！")
                        return False
                    else:
                        print(f"  ✅ Excel文件中Transaction Num有数据")
            else:
                print(f"  ❌ Excel文件中未找到Transaction Num列")
                print(f"  📋 可用列: {list(df_excel.columns)}")
                return False
                
        except Exception as e:
            print(f"  ❌ 读取Excel文件失败: {e}")
            return False
    else:
        print(f"  ❌ 未找到Excel文件")
        return False
    
    # 步骤2: 检查数据库中的Transaction Num
    print(f"\n📋 步骤2: 检查数据库中的Transaction Num")
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    if os.path.exists(db_path):
        print(f"📁 数据库路径: {db_path}")
        
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 检查ZERO_Sales表
                cursor.execute("SELECT COUNT(*) FROM ZERO_Sales")
                total_records = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM ZERO_Sales WHERE Transaction_Num IS NOT NULL AND Transaction_Num != ''")
                non_empty_transaction_num = cursor.fetchone()[0]
                
                print(f"📊 ZERO_Sales表统计:")
                print(f"  总记录数: {total_records}")
                print(f"  Transaction_Num非空记录数: {non_empty_transaction_num}")
                print(f"  Transaction_Num填充率: {(non_empty_transaction_num/total_records*100):.1f}%" if total_records > 0 else "0%")
                
                # 获取样本数据
                cursor.execute("SELECT Transaction_Num FROM ZERO_Sales WHERE Transaction_Num IS NOT NULL AND Transaction_Num != '' LIMIT 10")
                sample_transaction_nums = [row[0] for row in cursor.fetchall()]
                
                print(f"  样本Transaction_Num: {sample_transaction_nums}")
                
                if non_empty_transaction_num == 0:
                    print(f"  ❌ 数据库中Transaction_Num全部为空！")
                    
                    # 检查最近插入的记录
                    cursor.execute("SELECT Order_No, Transaction_Num, Import_Date FROM ZERO_Sales ORDER BY rowid DESC LIMIT 10")
                    recent_records = cursor.fetchall()
                    
                    print(f"  📋 最近10条记录:")
                    for i, (order_no, trans_num, import_date) in enumerate(recent_records, 1):
                        print(f"    {i}. Order_No: {order_no}, Transaction_Num: {trans_num}, Import_Date: {import_date}")
                    
                    return False
                else:
                    print(f"  ✅ 数据库中Transaction_Num有数据")
                    return True
                    
        except Exception as e:
            print(f"  ❌ 查询数据库失败: {e}")
            return False
    else:
        print(f"  ❌ 数据库文件不存在")
        return False

def check_data_import_process():
    """检查数据导入过程"""
    print(f"\n📋 步骤3: 检查数据导入过程")
    
    try:
        # 导入DataImportProcessor
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from data_import_optimized import DataImportProcessor
        
        print(f"✅ 成功导入DataImportProcessor")
        
        # 创建测试数据模拟导入过程
        test_data = {
            'Copartner name': ['测试商户'],
            'Order No.': ['TEST001'],
            'Transaction Num': ['TEST_TXN001'],  # 关键测试数据
            'Order types': ['Normal'],
            'Order status': ['Finished'],
            'Order price': [100.00],
            'Payment': [100.00],
            'Order time': ['2025-06-30 10:00:00'],
            'Equipment ID': ['TEST_EQ001'],
            'Equipment name': ['测试设备'],
            'Branch name': ['测试分店'],
            'Payment date': ['2025-06-30'],
            'User name': ['测试用户'],
            'Time': ['10:00'],
            'Matched Order ID': ['TEST_MATCH001'],
            'OrderTime_dt': ['2025-06-30 10:00:00'],
        }
        
        df_test = pd.DataFrame(test_data)
        
        print(f"📊 测试数据:")
        print(f"  Transaction Num原始值: {df_test['Transaction Num'].iloc[0]}")
        
        # 创建处理器并测试
        processor = DataImportProcessor()
        
        # 测试列名标准化
        df_standardized = processor._standardize_column_names(df_test)
        
        if 'Transaction_Num' in df_standardized.columns:
            print(f"  ✅ 列名标准化后Transaction_Num存在")
            print(f"  Transaction_Num标准化后值: {df_standardized['Transaction_Num'].iloc[0]}")
        else:
            print(f"  ❌ 列名标准化后Transaction_Num丢失")
            return False
        
        # 测试数据类型标准化
        df_typed = processor._standardize_data_types(df_standardized)
        
        if 'Transaction_Num' in df_typed.columns:
            print(f"  ✅ 数据类型标准化后Transaction_Num存在")
            print(f"  Transaction_Num类型标准化后值: {df_typed['Transaction_Num'].iloc[0]}")
            print(f"  Transaction_Num数据类型: {type(df_typed['Transaction_Num'].iloc[0])}")
        else:
            print(f"  ❌ 数据类型标准化后Transaction_Num丢失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据导入过程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 Transaction Num数据流完整追踪")
    print("=" * 80)
    
    step1_success = trace_excel_to_database()
    step2_success = check_data_import_process()
    
    print(f"\n📊 追踪结果总结:")
    print(f"  Excel文件检查: {'✅ 正常' if step1_success else '❌ 异常'}")
    print(f"  导入过程检查: {'✅ 正常' if step2_success else '❌ 异常'}")
    
    if not step1_success:
        print(f"\n🎯 问题定位: Excel文件中Transaction Num数据问题")
        print(f"  建议: 检查原始Excel文件是否包含Transaction Num数据")
    elif not step2_success:
        print(f"\n🎯 问题定位: 数据导入过程中Transaction Num丢失")
        print(f"  建议: 检查数据处理和数据库插入逻辑")
    else:
        print(f"\n🎯 问题定位: 可能是数据库插入或其他环节")
        print(f"  建议: 检查数据库插入逻辑和事务处理")

if __name__ == "__main__":
    main()
