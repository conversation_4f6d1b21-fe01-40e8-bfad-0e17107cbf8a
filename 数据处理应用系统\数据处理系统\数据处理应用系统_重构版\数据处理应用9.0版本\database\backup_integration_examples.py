# -*- coding: utf-8 -*-
"""
备份脚本应用集成使用示例
展示如何在不同场景下使用优化后的备份管理器
"""

import os
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.backup_manager import DatabaseBackupManager, get_backup_manager
from database.unified_backup_interface import get_unified_backup_interface


def example_main_app_integration():
    """示例：主应用集成模式"""
    print("🔧 示例：主应用集成模式")
    print("-" * 50)
    
    # 模拟主应用的配置管理器和GUI更新器
    class MockConfigManager:
        def get(self, section, key, default=None):
            config = {
                'Backup': {
                    'auto_backup': 'true',
                    'max_backup_files': '20'
                }
            }
            return config.get(section, {}).get(key, default)
    
    class MockGUIUpdater:
        def safe_log(self, message, level="info"):
            print(f"[GUI] {message}")
    
    # 创建备份管理器（主应用模式）
    config_manager = MockConfigManager()
    gui_updater = MockGUIUpdater()
    
    backup_manager = DatabaseBackupManager(
        db_path=r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db",
        backup_dir=None,
        config_manager=config_manager,
        gui_updater=gui_updater
    )
    
    # 主应用调用方式
    try:
        # 方式1：使用兼容方法
        backup_path = backup_manager.backup_database("主应用数据导入")
        print(f"✅ 主应用备份成功: {backup_path}")
        
        # 方式2：使用新方法
        backup_path2 = backup_manager.create_backup("主应用退款处理")
        print(f"✅ 主应用备份成功: {backup_path2}")
        
        # 获取备份列表
        backups = backup_manager.list_backups()
        print(f"📋 当前备份数量: {len(backups)}")
        
    except Exception as e:
        print(f"❌ 主应用备份失败: {e}")


def example_script_integration():
    """示例：独立脚本集成模式"""
    print("\n🔧 示例：独立脚本集成模式")
    print("-" * 50)
    
    # 创建备份管理器（独立脚本模式）
    backup_manager = DatabaseBackupManager(
        db_path=r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db",
        backup_dir=None  # 使用默认备份目录
    )
    
    # 独立脚本调用方式
    try:
        # 操作前自动备份
        backup_path = backup_manager.auto_backup_before_operation("退款处理脚本")
        print(f"✅ 脚本自动备份成功: {backup_path}")
        
        # 模拟操作失败，演示错误处理
        try:
            # 这里是实际的业务操作
            raise Exception("模拟的业务操作失败")
        except Exception as business_error:
            # 处理操作失败
            restored = backup_manager.handle_operation_failure(
                "退款处理脚本", business_error, backup_path
            )
            print(f"🔄 错误处理结果: {'已恢复' if restored else '未恢复'}")
        
    except Exception as e:
        print(f"❌ 脚本备份失败: {e}")


def example_unified_interface():
    """示例：统一接口模式"""
    print("\n🔧 示例：统一接口模式")
    print("-" * 50)
    
    # 使用统一接口（自动适配环境）
    unified_backup = get_unified_backup_interface(
        db_path=r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
    )
    
    try:
        # 统一的调用方式
        backup_path = unified_backup.create_backup("统一接口测试")
        print(f"✅ 统一接口备份成功: {backup_path}")
        
        # 获取备份列表
        backups = unified_backup.list_backups()
        print(f"📋 统一接口备份数量: {len(backups)}")
        
        # 操作前自动备份
        auto_backup = unified_backup.auto_backup_before_operation("统一接口操作")
        print(f"✅ 统一接口自动备份: {auto_backup}")
        
    except Exception as e:
        print(f"❌ 统一接口失败: {e}")


def example_global_manager():
    """示例：全局管理器模式"""
    print("\n🔧 示例：全局管理器模式")
    print("-" * 50)
    
    # 使用全局管理器
    backup_manager = get_backup_manager(
        db_path=r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
    )
    
    try:
        # 全局管理器调用
        backup_path = backup_manager.create_backup("全局管理器测试")
        print(f"✅ 全局管理器备份成功: {backup_path}")
        
        # 再次获取（应该返回同一个实例）
        backup_manager2 = get_backup_manager()
        print(f"🔄 实例一致性: {backup_manager is backup_manager2}")
        
    except Exception as e:
        print(f"❌ 全局管理器失败: {e}")


def example_migration_guide():
    """示例：迁移指南"""
    print("\n🔧 示例：现有代码迁移指南")
    print("-" * 50)
    
    print("📋 迁移步骤：")
    print("1. 主应用代码迁移：")
    print("   旧代码: self.backup_manager = DatabaseBackupManager(config, gui)")
    print("   新代码: 保持不变，已向后兼容")
    print()
    
    print("2. 独立脚本代码迁移：")
    print("   旧代码: backup_manager = SmartBackupManager(db_path)")
    print("   新代码: backup_manager = DatabaseBackupManager(db_path)")
    print()
    
    print("3. 统一接口使用：")
    print("   新代码: backup = get_unified_backup_interface(db_path, config=config)")
    print()
    
    print("4. 全局管理器使用：")
    print("   新代码: backup_manager = get_backup_manager(db_path)")
    print()
    
    print("✅ 所有现有代码都保持兼容，无需修改！")


def run_all_examples():
    """运行所有示例"""
    print("🎯 备份脚本应用集成优化示例")
    print("=" * 60)
    
    try:
        example_main_app_integration()
        example_script_integration()
        example_unified_interface()
        example_global_manager()
        example_migration_guide()
        
        print("\n🎉 所有示例运行完成！")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_examples()
