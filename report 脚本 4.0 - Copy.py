import pandas as pd
import numpy as np
import re
from datetime import datetime, timedelta
import os
import warnings

# 抑制 openpyxl 警告（可选）
warnings.simplefilter("ignore", category=UserWarning)

# -----------------------【配置区域】-----------------------
# 动态获取项目根目录
import os
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = script_dir  # 脚本在项目根目录
base_dir = os.path.join(project_root, "IOT")

# 第一文件（文件名：SETTLEMENT_REPORT_19032025_IOT.xlsx，sheet名：TRANSACTION_LIST）
file1_name = "SETTLEMENT_REPORT_22052025_IOT.xlsx"
file1_path = os.path.join(base_dir, file1_name)
sheet_name = "TRANSACTION_LIST"  # 请确认

# 第二文件（文件名：190325 CHINA IOT.xlsx）
file2_name = "220525 CHINA IOT - Copy.xlsx"
file2_path = os.path.join(base_dir, file2_name)

# 输出文件配置
# 不再创建新文件，而是在原始第二文件中添加新的sheet
output_file_path = file2_path  # 直接使用第二文件路径
output_data_sheet = "DATA"     # 数据sheet名称
output_log_sheet = "LOG"      # 日志sheet名称
# ---------------------------------------------------------

# -----------------------【第一文件数据处理】-----------------------
df1 = pd.read_excel(file1_path, sheet_name=sheet_name, header=0, engine="openpyxl")
print("第一文件读取的列名：", df1.columns.tolist())
if df1.shape[1] != 27:
    raise ValueError(f"第一文件实际列数 {df1.shape[1]} 不等于27，请检查文件内容。")

# 将 Date 列转换为 datetime（假设 Date 列中包含完整的日期时间字符串，如 "3/21/2025 12:08:10 AM"）
df1["DateTime"] = pd.to_datetime(df1["Date"], errors="coerce")
if df1["DateTime"].isnull().any():
    failed = df1[df1["DateTime"].isnull()]
    print("警告：以下记录 Date 转换失败，将被删除，请检查这些数据：")
    print(failed["Date"])
    df1 = df1[df1["DateTime"].notnull()].reset_index(drop=True)
# 生成24小时制时间字符串供后续匹配使用
df1["Time24"] = df1["DateTime"].dt.strftime("%H:%M:%S")

# -----------------------【筛选和统计】-----------------------
df1["Status"] = df1["Status"].astype(str)
df1_filtered = df1[df1["Status"].str.strip().str.lower().str.contains("settled")].copy()
if df1_filtered.empty:
    print("警告：第一文件筛选结果为空，请检查 Status 列数据！")
df1_filtered["Bill Amt"] = pd.to_numeric(df1_filtered["Bill Amt"], errors="coerce")
total_bill_amt = df1_filtered["Bill Amt"].sum()
freq_bill_amt = df1_filtered["Bill Amt"].round(2).value_counts().to_dict()

# 标准化 Order ID（去除空格）并判断类型
df1_filtered["Order ID"] = df1_filtered["Order ID"].astype(str).apply(lambda x: x.replace(" ", ""))


def check_order_id(oid):
    oid_str = str(oid).replace(" ", "")
    if re.search(r"[A-Za-z]", oid_str):
        return "anomaly"
    digits = re.sub(r"\D", "", oid_str)
    if len(digits) == 9:
        return "9_digit"
    elif len(digits) > 9:
        return "over_9"
    else:
        return "other"


df1_filtered["OrderID_Type"] = df1_filtered["Order ID"].apply(check_order_id)
anomaly_records = df1_filtered[df1_filtered["OrderID_Type"] == "anomaly"]

# 统计9位ID的数量
nine_digit_count = df1_filtered[df1_filtered["OrderID_Type"] == "9_digit"].shape[0]
print(f"第一文件9位ID数量：{nine_digit_count}")

# 统计第一文件中每个9位ID出现的次数
nine_digit_ids_count = {}
for _, row in df1_filtered[df1_filtered["OrderID_Type"] == "9_digit"].iterrows():
    oid = row["Order ID"]
    nine_digit_ids_count[oid] = nine_digit_ids_count.get(oid, 0) + 1

# -----------------------【第二文件数据处理】-----------------------
df2 = pd.read_excel(file2_path, engine="openpyxl")
df2.columns = [col.strip() for col in df2.columns]
mapping = {}
for col in df2.columns:
    cl = col.strip().lower()
    if cl in ["order price", "orderprice"]:
        mapping[col] = "Order price"
    elif cl in ["order status", "orderstatus"]:
        mapping[col] = "Order status"
    elif cl in ["order time", "ordertime"]:
        mapping[col] = "Order time"
    elif cl in ["equipment id", "equipmentid"]:
        mapping[col] = "Equipment ID"
    elif cl in ["order no.", "orderno"]:
        mapping[col] = "Order No."
df2.rename(columns=mapping, inplace=True)
print("第二文件标准化后列名：", df2.columns.tolist())
required = ["Order price", "Order status", "Order time"]
for r in required:
    if r not in df2.columns:
        raise KeyError(f"第二文件缺少关键列 {r}，请检查文件！")
df2["Order price"] = pd.to_numeric(df2["Order price"], errors="coerce")
df2["Payment"] = pd.to_numeric(df2["Payment"], errors="coerce")
df2["Order time"] = pd.to_datetime(df2["Order time"], errors="coerce")
df2["Time"] = df2["Order time"].dt.strftime("%H:%M:%S")
df2["Order status"] = df2["Order status"].astype(str)
# 排除Api order类型的记录
df2_finish = df2[(df2["Order status"].str.strip().str.lower() == "finish") & 
               ((df2["Order types"].str.strip().str.lower() != "api order") | 
                (df2["Order types"].isna()))].copy()
freq_order_price = df2_finish["Order price"].round(2).value_counts().to_dict()
for col in ["Equipment ID", "Order No."]:
    if col in df2.columns:
        df2[col] = df2[col].astype(str).apply(lambda x: x.replace(" ", ""))
for col in ["Equipment name", "Branch name"]:
    if col not in df2.columns:
        df2[col] = ""

# 添加Order types字段
if "Order types" not in df2.columns:
    df2["Order types"] = ""

# 保存原始数据用于日志对比
df2_original = df2.copy()
# 排除Api order类型的记录
df2_original_finish = df2_original[(df2_original["Order status"].str.strip().str.lower() == "finish") & 
                                 ((df2_original["Order types"].str.strip().str.lower() != "api order") | 
                                  (df2_original["Order types"].isna()))]
original_total = df2_original_finish["Order price"].sum()
original_freq = df2_original_finish["Order price"].round(2).value_counts().to_dict()

# 统计第二文件中每个9位ID出现的次数
second_nine_digit_ids_count = {}
for _, row in df2[df2["Equipment ID"].apply(lambda x: len(re.sub(r"\D", "", str(x))) == 9 and not re.search(r"[A-Za-z]", str(x)))].iterrows():
    # 排除Api order类型的记录
    if row["Order status"].strip().lower() == "finish" and \
       (str(row["Order types"]).strip().lower() != "api order" or pd.isna(row["Order types"])):
        oid = row["Equipment ID"]
        second_nine_digit_ids_count[oid] = second_nine_digit_ids_count.get(oid, 0) + 1

# -----------------------【备份第二文件】-----------------------
df2_backup = df2.copy()

# -----------------------【新增内存标记变量】-----------------------
matched_indices_second = set()
processed_9digit_ids = {}
processed_9digit_ids_time = {}

# 统计9位ID出现次数并记录时间信息
for _, row in df1_filtered[df1_filtered["OrderID_Type"] == "9_digit"].iterrows():
    oid = row["Order ID"]
    dt = row["DateTime"]
    processed_9digit_ids[oid] = processed_9digit_ids.get(oid, 0) + 1
    
    # 记录每个ID的时间信息
    if oid not in processed_9digit_ids_time:
        processed_9digit_ids_time[oid] = []
    processed_9digit_ids_time[oid].append(dt)

# 打印重复的9位ID信息
for oid, count in processed_9digit_ids.items():
    if count > 1:
        times = processed_9digit_ids_time[oid]
        time_diffs = []
        for i in range(1, len(times)):
            diff_seconds = abs((times[i] - times[i-1]).total_seconds())
            time_diffs.append(diff_seconds)
        

# 添加标记列，用于标记成功匹配的数据
if "Matched_Flag" not in df2.columns:
    df2["Matched_Flag"] = False
    
# 添加对超过9位ID的计数器
processed_over9_ids = {}
processed_over9_ids_time = {}

# 统计超过9位ID出现次数并记录时间信息
for _, row in df1_filtered[df1_filtered["OrderID_Type"] == "over_9"].iterrows():
    oid = row["Order ID"]
    dt = row["DateTime"]
    processed_over9_ids[oid] = processed_over9_ids.get(oid, 0) + 1
    
    # 记录每个ID的时间信息
    if oid not in processed_over9_ids_time:
        processed_over9_ids_time[oid] = []
    processed_over9_ids_time[oid].append(dt)

# 打印重复的超过9位ID信息
for oid, count in processed_over9_ids.items():
    if count > 1:
        times = processed_over9_ids_time[oid]
        time_diffs = []
        for i in range(1, len(times)):
            diff_seconds = abs((times[i] - times[i-1]).total_seconds())
            time_diffs.append(diff_seconds)
        print(f"重复的超过9位ID: {oid}, 出现次数: {count}, 时间差(秒): {time_diffs}")

# -----------------------【冲突检测函数】-----------------------
def check_conflict(oid, phase):
    if phase == "over_9":
        matches = df2[df2["Order No."] == oid]
        if any(i in matched_indices_second for i in matches.index):
            add_log((0, oid, f"Conflict detected! Order No. {oid} was matched by 9-digit ID"))
            return True
    return False

# -----------------------【匹配更新规则】-----------------------
if "Matched Order ID" not in df2.columns:
    df2["Matched Order ID"] = ""

# 初始化调试日志列表，日志存储为元组：(金额, Order ID, 信息)
note_logs = []


def add_log(log_tuple):
    if log_tuple not in note_logs:
        note_logs.append(log_tuple)


df2["OrderTime_dt"] = pd.to_datetime(df2["Order time"], errors="coerce")

# 添加对超过9位ID的计数器，类似于9位ID的处理方式
processed_over9_ids = {}

# 统计超过9位ID出现次数
for _, row in df1_filtered[df1_filtered["OrderID_Type"] == "over_9"].iterrows():
    oid = row["Order ID"]
    processed_over9_ids[oid] = processed_over9_ids.get(oid, 0) + 1

# 分阶段处理：先处理9位ID，再处理超长ID，最后处理异常值
for phase in ["9_digit", "over_9", "anomaly"]:
    for idx, row in df1_filtered[df1_filtered["OrderID_Type"] == phase].iterrows():
        try:
            oid = row["Order ID"]
            amt = row["Bill Amt"]
            dt1 = row["DateTime"]
            t_val = row["Time24"]
            
            # 根据ID类型设置Order types
            if phase == "9_digit":
                search_field = "Equipment ID"
                default_eq = oid
                order_type = "Offline order"  # 9位ID对应Offline order
            elif phase == "over_9":
                search_field = "Order No."
                default_eq = ""
                order_type = "Normal order"   # 超过9位ID对应Normal order
            else:
                order_type = "Anomaly order"  # 异常ID使用特殊标记
            
            # 跳过已处理的9位ID，但如果是重复ID且时间差较大，则不跳过
            if phase == "9_digit" and processed_9digit_ids.get(oid, 0) <= 0:
                # 检查是否是因为重复ID被处理过
                if oid in processed_9digit_ids_time and len(processed_9digit_ids_time[oid]) > 1:
                    # 查找当前时间最接近的记录
                    closest_time = None
                    min_diff = float('inf')
                    for recorded_time in processed_9digit_ids_time[oid]:
                        diff = abs((recorded_time - dt1).total_seconds())
                        if diff < min_diff:
                            min_diff = diff
                            closest_time = recorded_time
                    
                    # 如果时间差超过1秒，认为是不同的订单，需要单独处理
                    if min_diff > 1:
                        print(f"重复9位ID {oid} 时间差较大({min_diff}秒)，作为新订单处理")
                        # 不跳过，继续处理
                        pass
                    else:
                        continue
                else:
                    continue
                
            # 跳过已处理的超过9位ID，但如果是重复ID且时间差较大，则不跳过
            if phase == "over_9" and processed_over9_ids.get(oid, 0) <= 0:
                # 检查是否是因为重复ID被处理过
                if oid in processed_over9_ids_time and len(processed_over9_ids_time[oid]) > 1:
                    # 查找当前时间最接近的记录
                    closest_time = None
                    min_diff = float('inf')
                    for recorded_time in processed_over9_ids_time[oid]:
                        diff = abs((recorded_time - dt1).total_seconds())
                        if diff < min_diff:
                            min_diff = diff
                            closest_time = recorded_time
                    
                    # 如果时间差超过1秒，认为是不同的订单，需要单独处理
                    if min_diff > 1:
                        print(f"重复超过9位ID {oid} 时间差较大({min_diff}秒)，作为新订单处理")
                        # 不跳过，继续处理
                        pass
                    else:
                        continue
                else:
                    continue
                
            if phase == "anomaly":
                add_log((amt, oid, f"{row['DateTime']} {oid} ANOMALY inserted RM{amt:.2f}"))
                new_row = {
                    "Equipment ID": oid,
                    "Order price": amt,
                    "Order status": "Finish",
                    "Order time": dt1,
                    "Time": t_val,
                    "Matched Order ID": "",
                    "Matched_Flag": True,  # 添加标记
                    "Order types": order_type  # 设置Order types
                }
                df2 = pd.concat([df2, pd.DataFrame([new_row])], ignore_index=True)
                continue
                
            if check_conflict(oid, phase):  # 新增冲突检测
                continue
                
            updated_flag = False
            thresholds = [10, 30, 180, 300, 600, 1800, 3600, 10800]  # 递进式时间阈值
            
            for threshold in thresholds:
                matches = df2[df2[search_field] == oid]
                matches = matches[matches.index.map(lambda x: x not in matched_indices_second)]
                matches = matches[matches["OrderTime_dt"].apply(
                    lambda x: dt1 and x and abs((x - dt1).total_seconds()) <= threshold)]
                    
                if not matches.empty:
                    existing = matches[
                        (matches["Order price"].round(2) == round(amt, 2)) &
                        (matches["Order status"].str.strip().str.lower() == "finish")
                    ]
                    
                    if not existing.empty:
                        for m_idx in existing.index:
                            if threshold == 10800:
                                df2.at[m_idx, "Order time"] = dt1
                                df2.at[m_idx, "Time"] = t_val
                            matched_indices_second.add(m_idx)
                            df2.at[m_idx, "Matched_Flag"] = True  # 添加标记
                            df2.at[m_idx, "Order types"] = order_type  # 设置Order types
                            if phase == "9_digit":
                                processed_9digit_ids[oid] -= 1
                            elif phase == "over_9":
                                processed_over9_ids[oid] -= 1
                        updated_flag = True
                        break
                        
                    else:
                        for m_idx, m_row in matches.iterrows():
                            if pd.notnull(m_row["Order price"]) and abs(m_row["Order price"] - amt) > 1e-2:
                                old_price = m_row["Order price"]
                                df2.at[m_idx, "Order price"] = amt
                                df2.at[m_idx, "Order status"] = "Finish"
                                df2.at[m_idx, "Order types"] = order_type  # 设置Order types
                                
                                if phase == "9_digit":
                                    df2.at[m_idx, "Equipment ID"] = oid
                                    df2.at[m_idx, "Time"] = t_val
                                    df2.at[m_idx, "Order time"] = dt1
                                else:
                                    if str(m_row["Equipment ID"]).strip() == "":
                                        df2.at[m_idx, "Matched Order ID"] = oid
                                        
                                matched_indices_second.add(m_idx)
                                df2.at[m_idx, "Matched_Flag"] = True  # 添加标记
                                add_log((amt, oid, f"{row['DateTime']} {oid} updated price from RM{old_price:.2f} to RM{amt:.2f}"))
                                
                                if phase == "9_digit":
                                    processed_9digit_ids[oid] -= 1
                                elif phase == "over_9":
                                    processed_over9_ids[oid] -= 1
                                updated_flag = True
                                break
                                
                            elif pd.notnull(m_row["Order price"]) and abs(m_row["Order price"] - amt) <= 1e-2:
                                if m_row["Order status"].strip().lower() != "finish":
                                    old_status = m_row["Order status"]
                                    df2.at[m_idx, "Order status"] = "Finish"
                                    df2.at[m_idx, "Order types"] = order_type  # 设置Order types
                                    
                                    if phase == "9_digit":
                                        df2.at[m_idx, "Equipment ID"] = oid
                                        df2.at[m_idx, "Time"] = t_val
                                        df2.at[m_idx, "Order time"] = dt1
                                    else:
                                        if str(m_row["Equipment ID"]).strip() == "":
                                            df2.at[m_idx, "Matched Order ID"] = oid
                                            
                                    matched_indices_second.add(m_idx)
                                    df2.at[m_idx, "Matched_Flag"] = True  # 添加标记
                                    add_log((amt, oid, f"{row['DateTime']} {oid} updated status from {old_status} to Finish RM{amt:.2f}"))
                                    
                                    if phase == "9_digit":
                                        processed_9digit_ids[oid] -= 1
                                    elif phase == "over_9":
                                        processed_over9_ids[oid] -= 1
                                    updated_flag = True
                                    break
                        if updated_flag:
                            break
                            
            if not updated_flag:
                new_row = {
                    search_field: oid,
                    "Order price": amt,
                    "Order status": "Finish",
                    "Order time": dt1,
                    "Time": t_val,
                    "Equipment ID": default_eq,
                    "Matched Order ID": oid if phase == "over_9" else "",
                    "Matched_Flag": True,  # 添加标记
                    "Order types": order_type  # 设置Order types
                }
                df2 = pd.concat([df2, pd.DataFrame([new_row])], ignore_index=True)
                add_log((amt, oid, f"{row['DateTime']} {oid} CHINA NO RECORD inserted RM{amt:.2f}"))
                
                # 减少计数器
                if phase == "9_digit":
                    processed_9digit_ids[oid] -= 1
                elif phase == "over_9":
                    processed_over9_ids[oid] -= 1
                
        except Exception as e:
            add_log((0, oid, f"Error processing {oid}: {str(e)}"))
            continue
# ... existing code ...

# -----------------------【删除未匹配的数据】-----------------------
# 删除未匹配的数据并记录到最终日志中
df2_before_delete = df2.copy()
# 排除Api order类型的记录
df2_unmatched = df2[(df2["Order status"].str.strip().str.lower() == "finish") & 
                   (~df2["Matched_Flag"]) & 
                   ((df2["Order types"].str.strip().str.lower() != "api order") | 
                    (df2["Order types"].isna()))]

if not df2_unmatched.empty:
    unmatched_total = df2_unmatched["Order price"].sum()
    unmatched_count = len(df2_unmatched)
    
    # 记录被删除的数据详情到日志中
    note_logs.append((0, "", f"删除未匹配数据: {unmatched_count}条，总金额: RM{unmatched_total:.2f}"))
    for _, row in df2_unmatched.iterrows():
        note_logs.append((row["Order price"], str(row["Equipment ID"]), f"删除未匹配记录: RM{row['Order price']:.2f}"))
    
    # 删除未匹配的数据，同时排除Api order类型的记录
    df2 = df2[~((df2["Order status"].str.strip().str.lower() == "finish") & 
              (~df2["Matched_Flag"]) & 
              ((df2["Order types"].str.strip().str.lower() != "api order") | 
               (df2["Order types"].isna())))]

# -----------------------【频率修正】-----------------------
# 计算修正后的频率和总金额
# 排除Api order类型的记录
df2_after = df2[(df2["Order status"].str.strip().str.lower() == "finish") & 
               ((df2["Order types"].str.strip().str.lower() != "api order") | 
                (df2["Order types"].isna()))].copy()
after_total = df2_after["Order price"].sum()
after_freq = df2_after["Order price"].round(2).value_counts().to_dict()

# -----------------------【字段补全策略】-----------------------
# 对于新插入的记录，补全其他字段
for idx, row in df2.iterrows():
    # 排除异常值记录（Order types为"Anomaly order"的记录）
    if row["Matched_Flag"] and (pd.isnull(row["Equipment name"]) or row["Equipment name"] == "") and ("Order types" not in df2.columns or row["Order types"] != "Anomaly order"):
        # 策略1：从同一设备ID的其他记录中获取
        if "Equipment ID" in df2.columns and str(row["Equipment ID"]).strip() != "":
            same_eq = df2_backup[df2_backup["Equipment ID"] == row["Equipment ID"]]
            if not same_eq.empty:
                for col in ["Equipment name", "Branch name"]:
                    if col in same_eq.columns and col in df2.columns:
                        valid_values = same_eq[col].dropna().unique()
                        if len(valid_values) > 0 and str(valid_values[0]).strip() != "":
                            df2.at[idx, col] = valid_values[0]
        
        # 策略2：从同一订单号的其他记录中获取
        if "Order No." in df2.columns and str(row["Order No."]).strip() != "":
            same_order = df2_backup[df2_backup["Order No."] == row["Order No."]]
            if not same_order.empty:
                for col in ["Equipment name", "Branch name", "Equipment ID"]:
                    if col in same_order.columns and col in df2.columns:
                        if pd.isnull(row[col]) or str(row[col]).strip() == "":
                            valid_values = same_order[col].dropna().unique()
                            if len(valid_values) > 0 and str(valid_values[0]).strip() != "":
                                df2.at[idx, col] = valid_values[0]
        
        

# 方法2：利用映射字典（以 Equipment ID 为键）
df2_method2 = df2.copy()
df2_backup["Equipment ID"] = df2_backup["Equipment ID"].fillna("").astype(str).str.strip()
mapping_ename = df2_backup.drop_duplicates("Equipment ID").set_index("Equipment ID")["Equipment name"]
mapping_bname = df2_backup.drop_duplicates("Equipment ID").set_index("Equipment ID")["Branch name"]

# 排除异常值记录（Order types为"Anomaly order"的记录）
if "Order types" in df2_method2.columns:
    mask = df2_method2["Order types"] != "Anomaly order"
else:
    mask = pd.Series(True, index=df2_method2.index)
    
df2_method2.loc[mask, "Equipment name"] = df2_method2.loc[mask, "Equipment ID"].fillna("").astype(str).str.strip().map(
    mapping_ename).fillna(df2_method2.loc[mask, "Equipment name"])
df2_method2.loc[mask, "Branch name"] = df2_method2.loc[mask, "Equipment ID"].fillna("").astype(str).str.strip().map(
    mapping_bname).fillna(df2_method2.loc[mask, "Branch name"])

# 使用原始第二文件数据进行对比 
# 排除Api order类型的记录
df2_original_finish = df2_original[(df2_original["Order status"].str.strip().str.lower() == "finish") & 
                                 ((df2_original["Order types"].str.strip().str.lower() != "api order") | 
                                  (df2_original["Order types"].isna()))] 
original_total = df2_original_finish["Order price"].sum()
original_freq = df2_original_finish["Order price"].round(2).value_counts().to_dict() 

# 生成频率比较日志 
freq_compare_logs = [] 
freq_compare_logs.append(("", f"{df1_filtered['DateTime'].min().strftime('%Y-%m-%d %H:%M:%S')}")) 
freq_compare_logs.append(("", f"RAZER : RM{total_bill_amt:.2f}")) 
freq_compare_logs.append(("", f"CHINA : RM{original_total:.2f}")) 

# 添加空行 
freq_compare_logs.append(("", "")) 

# 添加验证信息 
freq_compare_logs.append(("", f"First file total (settled): RM{total_bill_amt:.2f}")) 

# 添加验证通过信息 
if abs(total_bill_amt - after_total) < 0.01:
    freq_compare_logs.append(("", f"Verification passed: Final total matches first file total RM{total_bill_amt:.2f}"))
else:
    freq_compare_logs.append(("", f"WARNING: Final total RM{after_total:.2f} does not match first file total RM{total_bill_amt:.2f}, difference: RM{abs(after_total - total_bill_amt):.2f}"))

# 添加空行 
freq_compare_logs.append(("", "")) 

# 合并所有金额 
all_amounts = sorted(set(list(freq_bill_amt.keys()) + list(original_freq.keys()))) 

# 先生成所有金额的汇总信息
for amt in all_amounts: 
    first_count = freq_bill_amt.get(amt, 0) 
    second_count = original_freq.get(amt, 0) 
    diff = second_count - first_count 
    
    # 移除过滤条件，显示所有金额的汇总信息
    if diff == 0: 
        msg = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count}" 
    elif diff > 0: 
        msg = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (MORE: {diff})" 
    else:  # diff < 0 
        msg = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (LESS: {abs(diff)})" 
    
    freq_compare_logs.append(("", msg))

# 添加空行
freq_compare_logs.append(("", ""))

# 然后再显示每个金额下的详细修改日志，并在每个金额前添加标题
for amt in all_amounts: 
    first_count = freq_bill_amt.get(amt, 0) 
    second_count = original_freq.get(amt, 0) 
    diff = second_count - first_count 
    
    # 处理所有金额记录，不再只处理有差异的记录
    # 查找与该金额相关的日志
    related_logs = [log for log in note_logs if abs(log[0] - amt) < 0.01]
    
    # 只有当有相关日志时才添加该金额的标题和日志
    if related_logs:
        # 添加金额标题
        if diff == 0: 
            title = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count}" 
        elif diff > 0: 
            title = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (MORE: {diff})" 
        else:  # diff < 0 
            title = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (LESS: {abs(diff)})" 
            
            freq_compare_logs.append(("", title))
            
            # 添加该金额下的所有相关日志
            for log in related_logs:
                freq_compare_logs.append((log[1], log[2]))
            
            # 在每个金额的日志组后添加空行，除非是最后一个金额
            if amt != all_amounts[-1]:
                freq_compare_logs.append(("", ""))

# 创建日志DataFrame，ID列在左边
log_df = pd.DataFrame(freq_compare_logs, columns=["ID", "Log"])

# 先打印第二文件初始总金额
print(f"第二文件初始总金额: RM{after_total:.2f}")

# -----------------------【自动修正函数】-----------------------
def auto_correct_discrepancies():
    """
    自动检测并修正第一文件和第二文件之间的金额差异。
    该函数在数据处理完成后执行，确保两个文件的总金额完全一致。
    
    处理逻辑：
    1. 比较第一文件和第二文件的总金额
    2. 如果存在差异，查找导致差异的订单
    3. 自动修正金额差异，确保两个文件的总金额完全一致
    4. 记录所有修正操作到日志中
    
    返回：
        bool: 是否进行了修正操作
    """
    global df1_filtered, df2, df2_after, after_total, total_bill_amt, note_logs
    
    # 检查是否存在金额差异
    if abs(total_bill_amt - after_total) < 0.01:
        return False  # 无需修正
    
    correction_logs = []
    correction_logs.append((0, "", f"开始自动修正金额差异: 第一文件 RM{total_bill_amt:.2f} vs 第二文件 RM{after_total:.2f}, 差异: RM{abs(total_bill_amt - after_total):.2f}"))
    
    # 创建第一文件和第二文件的订单金额映射
    # 排除Api order和Anomaly order类型的记录
    df1_orders = {}
    for _, row in df1_filtered.iterrows():
        oid = row["Order ID"]
        amt = row["Bill Amt"]
        dt = row["DateTime"]
        id_type = row["OrderID_Type"]
        
        # 对于重复ID，创建包含时间信息的唯一键
        # 检查是否是重复ID
        is_duplicate = False
        time_key = ""
        
        if id_type == "9_digit" and oid in processed_9digit_ids and processed_9digit_ids[oid] > 1:
            is_duplicate = True
            # 查找最接近的时间记录
            closest_time = None
            min_diff = float('inf')
            for recorded_time in processed_9digit_ids_time[oid]:
                diff = abs((recorded_time - dt).total_seconds())
                if diff < min_diff:
                    min_diff = diff
                    closest_time = recorded_time
            
            # 如果时间差超过1秒，添加时间戳到键中
            if min_diff > 1:
                time_key = dt.strftime("%H%M%S")
                print(f"自动修正: 重复9位ID {oid} 时间差较大({min_diff}秒)，添加时间戳到键中: {time_key}")
        
        elif id_type == "over_9" and oid in processed_over9_ids and processed_over9_ids[oid] > 1:
            is_duplicate = True
            # 查找最接近的时间记录
            closest_time = None
            min_diff = float('inf')
            for recorded_time in processed_over9_ids_time[oid]:
                diff = abs((recorded_time - dt).total_seconds())
                if diff < min_diff:
                    min_diff = diff
                    closest_time = recorded_time
            
            # 如果时间差超过1秒，添加时间戳到键中
            if min_diff > 1:
                time_key = dt.strftime("%H%M%S")
                print(f"自动修正: 重复超过9位ID {oid} 时间差较大({min_diff}秒)，添加时间戳到键中: {time_key}")
        
        # 创建唯一键：订单ID + 时间戳(如果是重复ID) + 金额（四舍五入到2位小数）
        if is_duplicate and time_key:
            key = (oid, time_key, round(amt, 2))
        else:
            key = (oid, round(amt, 2))
            
        df1_orders[key] = {
            "datetime": dt,
            "id_type": id_type,
            "processed": False
        }
    
    # 首先统计df2_after中每个ID的出现次数和时间信息
    df2_id_counts = {}
    df2_id_times = {}
    
    for _, row in df2_after.iterrows():
        # 根据ID类型确定使用哪个字段作为订单ID
        if row["Order types"] == "Offline order":  # 9位ID
            oid = row["Equipment ID"]
            id_type = "9_digit"
        elif row["Order types"] == "Normal order":  # 超过9位ID
            oid = row["Order No."]
            id_type = "over_9"
        else:  # 其他情况，尝试两个字段
            oid = row["Equipment ID"] if str(row["Equipment ID"]).strip() else row["Order No."]
            id_type = "other"
        
        dt = row["Order time"]
        
        # 统计ID出现次数和时间
        if oid not in df2_id_counts:
            df2_id_counts[oid] = 0
            df2_id_times[oid] = []
        df2_id_counts[oid] += 1
        if isinstance(dt, pd.Timestamp):
            df2_id_times[oid].append(dt)
    
    # 创建df2_orders，使用与df1_orders一致的键创建逻辑
    df2_orders = {}
    df2_orders_with_time = {}
    
    for _, row in df2_after.iterrows():
        # 根据ID类型确定使用哪个字段作为订单ID
        if row["Order types"] == "Offline order":  # 9位ID
            oid = row["Equipment ID"]
            id_type = "9_digit"
        elif row["Order types"] == "Normal order":  # 超过9位ID
            oid = row["Order No."]
            id_type = "over_9"
        else:  # 其他情况，尝试两个字段
            oid = row["Equipment ID"] if str(row["Equipment ID"]).strip() else row["Order No."]
            id_type = "other"
        
        amt = row["Order price"]
        dt = row["Order time"]
        
        # 检查是否是重复ID并需要添加时间戳
        is_duplicate = df2_id_counts.get(oid, 1) > 1
        time_key = ""
        
        if is_duplicate and isinstance(dt, pd.Timestamp):
            # 计算与其他相同ID记录的时间差
            min_diff = float('inf')
            for other_time in df2_id_times[oid]:
                if other_time != dt:
                    diff = abs((other_time - dt).total_seconds())
                    if diff < min_diff:
                        min_diff = diff
            
            # 如果时间差超过1秒，添加时间戳到键中
            if min_diff > 1:
                time_key = dt.strftime("%H%M%S")
                correction_logs.append((0, "", f"自动修正: 重复ID {oid} 在df2中时间差较大({min_diff:.1f}秒)，添加时间戳: {time_key}"))
        
        # 创建唯一键：订单ID + 时间戳(如果是重复ID) + 金额（四舍五入到2位小数）
        if is_duplicate and time_key:
            key = (oid, time_key, round(amt, 2))
            df2_orders_with_time[key] = True
        else:
            key = (oid, round(amt, 2))
            df2_orders[key] = True
    
    # 查找第一文件中存在但第二文件中不存在的订单
    missing_orders = []
    for key, info in df1_orders.items():
        # 检查是否是带时间的键
        if len(key) == 3:  # 带时间的键 (oid, time_key, amt)
            if key not in df2_orders_with_time:
                missing_orders.append({
                    "oid": key[0],
                    "amt": key[2],  # 注意索引变化
                    "datetime": info["datetime"],
                    "id_type": info["id_type"],
                    "time_key": key[1]  # 保存时间键
                })
        else:  # 基本键 (oid, amt)
            if key not in df2_orders:
                missing_orders.append({
                    "oid": key[0],
                    "amt": key[1],
                    "datetime": info["datetime"],
                    "id_type": info["id_type"]
                })
    
    # 查找第二文件中存在但第一文件中不存在的订单
    extra_orders = []
    
    # 检查基本键
    for key in df2_orders.keys():
        if key not in df1_orders:
            # 查找对应的行以获取更多信息
            for _, row in df2_after.iterrows():
                if ((row["Equipment ID"] == key[0] or row["Order No."] == key[0]) and 
                    abs(row["Order price"] - key[1]) < 0.01):
                    extra_orders.append({
                        "oid": key[0],
                        "amt": key[1],
                        "index": row.name,
                        "key_type": "basic"
                    })
                    break
    
    # 检查带时间戳的键
    for key in df2_orders_with_time.keys():
        if key not in df1_orders:
            # 查找对应的行以获取更多信息
            for _, row in df2_after.iterrows():
                if ((row["Equipment ID"] == key[0] or row["Order No."] == key[0]) and 
                    abs(row["Order price"] - key[2]) < 0.01):  # 注意索引变化
                    extra_orders.append({
                        "oid": key[0],
                        "amt": key[2],  # 注意索引变化
                        "index": row.name,
                        "key_type": "time_based",
                        "time_key": key[1]
                    })
                    break
    
    # 计算当前差异
    current_diff = total_bill_amt - after_total
    correction_logs.append((0, "", f"找到 {len(missing_orders)} 个第一文件中存在但第二文件中不存在的订单"))
    correction_logs.append((0, "", f"找到 {len(extra_orders)} 个第二文件中存在但第一文件中不存在的订单"))
    
    # 详细显示缺失的订单
    if missing_orders:
        correction_logs.append((0, "", "=== 第一文件中存在但第二文件中不存在的订单 ==="))
        for i, order in enumerate(missing_orders[:10]):  # 只显示前10个
            if "time_key" in order:
                correction_logs.append((0, "", f"  {i+1}. ID: {order['oid']}, 金额: RM{order['amt']:.2f}, 时间戳: {order['time_key']}, 类型: {order['id_type']}"))
            else:
                correction_logs.append((0, "", f"  {i+1}. ID: {order['oid']}, 金额: RM{order['amt']:.2f}, 类型: {order['id_type']}"))
        if len(missing_orders) > 10:
            correction_logs.append((0, "", f"  ... 还有 {len(missing_orders) - 10} 个订单"))
    
    # 详细显示多余的订单
    if extra_orders:
        correction_logs.append((0, "", "=== 第二文件中存在但第一文件中不存在的订单 ==="))
        for i, order in enumerate(extra_orders[:10]):  # 只显示前10个
            if order.get("key_type") == "time_based":
                correction_logs.append((0, "", f"  {i+1}. ID: {order['oid']}, 金额: RM{order['amt']:.2f}, 时间戳: {order['time_key']}, 键类型: {order['key_type']}"))
            else:
                correction_logs.append((0, "", f"  {i+1}. ID: {order['oid']}, 金额: RM{order['amt']:.2f}, 键类型: {order.get('key_type', 'basic')}"))
        if len(extra_orders) > 10:
            correction_logs.append((0, "", f"  ... 还有 {len(extra_orders) - 10} 个订单"))
    
    # 处理缺失的订单（第一文件有但第二文件没有）
    orders_to_add = []
    for order in missing_orders:
        # 检查添加此订单是否有助于减少差异
        if (current_diff > 0 and order["amt"] > 0) or (current_diff < 0 and order["amt"] < 0):
            orders_to_add.append(order)
            current_diff -= order["amt"]
            
            # 如果差异已经足够小，可以停止添加
            if abs(current_diff) < 0.01:
                break
    
    # 处理多余的订单（第二文件有但第一文件没有）
    indices_to_remove = []
    if abs(current_diff) >= 0.01 and extra_orders:
        for order in extra_orders:
            # 检查删除此订单是否有助于减少差异
            if (current_diff < 0 and order["amt"] > 0) or (current_diff > 0 and order["amt"] < 0):
                indices_to_remove.append(order["index"])
                current_diff += order["amt"]
                
                # 如果差异已经足够小，可以停止删除
                if abs(current_diff) < 0.01:
                    break
    
    # 执行修正操作
    modified = False
    
    # 添加缺失的订单
    for order in orders_to_add:
        oid = order["oid"]
        amt = order["amt"]
        dt = order["datetime"]
        id_type = order["id_type"]
        t_val = dt.strftime("%H:%M:%S")
        
        # 根据ID类型设置Order types和搜索字段
        if id_type == "9_digit":
            search_field = "Equipment ID"
            default_eq = oid
            order_type = "Offline order"  # 9位ID对应Offline order
        elif id_type == "over_9":
            search_field = "Order No."
            default_eq = ""
            order_type = "Normal order"   # 超过9位ID对应Normal order
        else:  # anomaly
            search_field = "Equipment ID"
            default_eq = oid
            order_type = "Anomaly order"  # 异常ID使用特殊标记
        
        # 创建新行
        new_row = {
            search_field: oid,
            "Order price": amt,
            "Order status": "Finish",
            "Order time": dt,
            "Time": t_val,
            "Equipment ID": default_eq if search_field != "Equipment ID" else oid,
            "Matched Order ID": oid if search_field != "Equipment ID" else "",
            "Matched_Flag": True,
            "Order types": order_type
        }
        
        # 获取详细信息
        eq_id = default_eq if search_field != "Equipment ID" else oid
        order_no = oid if search_field == "Order No." else ""
        
        # 创建详细的日志记录
        detail_info = f"自动修正: 添加缺失订单 {oid}, 金额 RM{amt:.2f}, 类型 {order_type}\n"
        detail_info += f"  - Equipment ID: {eq_id}\n"
        detail_info += f"  - Order No.: {order_no}\n"
        detail_info += f"  - Order price: RM{amt:.2f}\n"
        detail_info += f"  - Order time: {dt}\n"
        detail_info += f"  - Order type: {order_type}"
        
        # 添加新行
        df2 = pd.concat([df2, pd.DataFrame([new_row])], ignore_index=True)
        correction_logs.append((amt, oid, detail_info))
        modified = True
    
    # 删除多余的订单
    if indices_to_remove:
        for idx in indices_to_remove:
            row = df2.loc[idx]
            oid = row["Equipment ID"] if str(row["Equipment ID"]).strip() else row["Order No."]
            amt = row["Order price"]
            # 获取更多详细信息
            eq_id = str(row["Equipment ID"]).strip()
            order_no = str(row["Order No."]).strip()
            order_time = str(row["Order time"]).strip() if "Order time" in row else ""
            payment_date = str(row["Payment date"]).strip() if "Payment date" in row else ""
            equipment_name = str(row["Equipment name"]).strip() if "Equipment name" in row else ""
            branch_name = str(row["Branch name"]).strip() if "Branch name" in row else ""
            
            # 查找对应的extra_order信息
            extra_info = ""
            for order in extra_orders:
                if order["index"] == idx:
                    if order.get("key_type") == "time_based":
                        extra_info = f", 时间戳: {order['time_key']}"
                    break
            
            # 创建详细的日志记录
            detail_info = f"自动修正: 删除多余订单 {oid}, 金额 RM{amt:.2f}{extra_info}\n"
            detail_info += f"  - Equipment ID: {eq_id}\n"
            detail_info += f"  - Order No.: {order_no}\n"
            detail_info += f"  - Order price: RM{amt:.2f}\n"
            if order_time: detail_info += f"  - Order time: {order_time}\n"
            if payment_date: detail_info += f"  - Payment date: {payment_date}\n"
            if equipment_name: detail_info += f"  - Equipment name: {equipment_name}\n"
            if branch_name: detail_info += f"  - Branch name: {branch_name}"
            
            correction_logs.append((amt, oid, detail_info))
        
        # 删除指定索引的行
        df2 = df2.drop(indices_to_remove).reset_index(drop=True)
        modified = True
    
    # 如果进行了修改，重新计算总金额
    if modified:
        # 重新计算修正后的频率和总金额
        # 排除Api order类型的记录
        df2_after = df2[(df2["Order status"].str.strip().str.lower() == "finish") & 
                       ((df2["Order types"].str.strip().str.lower() != "api order") | 
                        (df2["Order types"].isna()))].copy()
        
        # 更新全局变量
        globals()['df2_after'] = df2_after
        globals()['after_total'] = df2_after["Order price"].sum()
        after_total = globals()['after_total']
        
        # 计算添加和删除的订单数量
        added_orders = len([log for log in correction_logs if "添加缺失订单" in log[2]])
        removed_orders = len([log for log in correction_logs if "删除多余订单" in log[2]])
        
        # 添加修正结果到日志
        summary_info = f"自动修正完成: 第一文件 RM{total_bill_amt:.2f} vs 修正后第二文件 RM{after_total:.2f}, 剩余差异: RM{abs(total_bill_amt - after_total):.2f}\n"
        summary_info += f"  - 添加订单数量: {added_orders}\n"
        summary_info += f"  - 删除订单数量: {removed_orders}\n"
        summary_info += f"  - 总修正订单数量: {added_orders + removed_orders}"
        
        correction_logs.append((0, "", summary_info))
        
        # 将修正日志添加到主日志
        note_logs.extend(correction_logs)
    
    return modified

# 在验证总金额后调用自动修正函数
if abs(total_bill_amt - after_total) >= 0.01:
    print("检测到金额差异，启动自动修正...")    
    if auto_correct_discrepancies():
        print(f"自动修正完成，修正后总金额: RM{after_total:.2f}")
        # 在日志中添加修正记录
        note_logs.append((0, "", f"自动修正后总金额从 RM{total_bill_amt:.2f} 变更为 RM{after_total:.2f}"))
        
        # 更新验证通过信息
        for i, (id_val, log_val) in enumerate(freq_compare_logs):
            if log_val.startswith("WARNING: Final total"):
                if abs(total_bill_amt - after_total) < 0.01:
                    freq_compare_logs[i] = ("", f"Verification passed: Final total matches first file total RM{total_bill_amt:.2f} (after auto-correction)")
                else:
                    freq_compare_logs[i] = ("", f"WARNING: Final total RM{after_total:.2f} does not match first file total RM{total_bill_amt:.2f}, difference: RM{abs(after_total - total_bill_amt):.2f} (after auto-correction)")
                break
    else:
        print("无法自动修正金额差异，请手动检查数据")

# 创建日志DataFrame，ID列在左边
log_df = pd.DataFrame(freq_compare_logs, columns=["ID", "Log"])

# -----------------------【输出结果】-----------------------
# 使用ExcelWriter写入多个sheet
with pd.ExcelWriter(output_file_path, engine="openpyxl", mode="a", if_sheet_exists="replace") as writer:
    # 写入数据sheet
    df2.to_excel(writer, sheet_name=output_data_sheet, index=False)
    # 写入日志sheet
    log_df.to_excel(writer, sheet_name=output_log_sheet, index=False)

print(f"处理完成！结果已写入 {output_file_path}")
print(f"- 数据已写入 {output_data_sheet} sheet")
print(f"- 日志已写入 {output_log_sheet} sheet")