#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入问题诊断脚本 - 专门诊断 'NoneType' object has no attribute 'columns' 错误
"""

import pandas as pd
import sys
import os
from pathlib import Path

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(os.path.join(parent_dir, '01_主程序'))

def test_file_loading(file_path, platform):
    """测试文件加载过程"""
    print(f"🔍 诊断文件加载过程")
    print(f"文件: {file_path}")
    print(f"平台: {platform}")
    print("=" * 60)
    
    try:
        # 步骤1: 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return False
        
        print(f"✅ 文件存在")
        
        # 步骤2: 检查Excel文件结构
        try:
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            print(f"✅ Excel文件可读，工作表: {sheet_names}")
        except Exception as e:
            print(f"❌ Excel文件读取失败: {e}")
            return False
        
        # 步骤3: 检测正确的sheet
        platform_upper = platform.upper()
        target_sheet = None
        
        for sheet_name in sheet_names:
            if platform_upper in sheet_name.upper():
                target_sheet = sheet_name
                break
        
        if target_sheet is None:
            print(f"❌ 未找到包含 '{platform}' 标识的工作表")
            print(f"可用工作表: {sheet_names}")
            return False
        
        print(f"✅ 找到目标工作表: {target_sheet}")
        
        # 步骤4: 读取数据
        try:
            df = pd.read_excel(file_path, sheet_name=target_sheet, engine='openpyxl')
            print(f"✅ 数据读取成功")
            print(f"   数据形状: {df.shape}")
            print(f"   列名: {list(df.columns)}")
            
            if df is None:
                print(f"❌ DataFrame 为 None")
                return False
            
            if df.empty:
                print(f"⚠️ DataFrame 为空")
                return False
                
        except Exception as e:
            print(f"❌ 数据读取失败: {e}")
            return False
        
        # 步骤5: 测试数据清理
        try:
            # 删除完全空白的行
            df_cleaned = df.dropna(how='all')
            print(f"✅ 数据清理成功，清理后形状: {df_cleaned.shape}")
            
            if df_cleaned is None:
                print(f"❌ 清理后 DataFrame 为 None")
                return False
                
        except Exception as e:
            print(f"❌ 数据清理失败: {e}")
            return False
        
        # 步骤6: 测试智能列名映射
        try:
            # 尝试导入智能映射器
            from intelligent_column_mapper import IntelligentColumnMapper
            
            mapper = IntelligentColumnMapper()
            df_mapped, analysis = mapper.apply_intelligent_mapping(df_cleaned)
            
            print(f"✅ 智能列名映射成功")
            print(f"   映射后形状: {df_mapped.shape}")
            print(f"   映射后列名: {list(df_mapped.columns)}")
            
            if df_mapped is None:
                print(f"❌ 映射后 DataFrame 为 None")
                return False
                
        except ImportError as e:
            print(f"⚠️ 智能映射器导入失败: {e}")
            print(f"   这是正常的，将使用传统映射")
            df_mapped = df_cleaned
            
        except Exception as e:
            print(f"❌ 智能列名映射失败: {e}")
            return False
        
        print(f"🎉 所有步骤都成功完成！")
        return True
        
    except Exception as e:
        print(f"❌ 诊断过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 导入问题诊断工具")
    print("=" * 60)
    
    # 测试文件路径（请根据实际情况修改）
    test_file = "030725 CHINA ZERO.xlsx"
    test_platform = "ZERO"
    
    # 如果命令行提供了参数，使用命令行参数
    if len(sys.argv) >= 3:
        test_file = sys.argv[1]
        test_platform = sys.argv[2]
    
    print(f"测试文件: {test_file}")
    print(f"测试平台: {test_platform}")
    print()
    
    # 执行诊断
    success = test_file_loading(test_file, test_platform)
    
    if success:
        print(f"\n✅ 诊断结果: 文件加载流程正常")
        print(f"   问题可能出现在其他地方，建议检查:")
        print(f"   1. 数据库连接")
        print(f"   2. 重复检测逻辑")
        print(f"   3. 数据插入过程")
    else:
        print(f"\n❌ 诊断结果: 文件加载流程存在问题")
        print(f"   请根据上述错误信息进行修复")

if __name__ == "__main__":
    main()
