# -*- coding: utf-8 -*-
"""
测试数据导入修复
验证智能列名匹配集成到data_import_optimized.py后的效果
"""

import pandas as pd
import tempfile
import os
import sys
from datetime import datetime

# 添加scripts目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'scripts'))

def create_test_data_with_extra_columns():
    """创建包含额外列的测试数据"""
    # 模拟您遇到的包含Serial number等额外列的数据
    test_data = {
        'Serial number': ['001', '002', '003'],  # 额外列 - 应该被忽略
        'Copartner name': ['商户A', '商户B', '商户C'],
        'Merchant name': ['Merchant A', 'Merchant B', 'Merchant C'],  # 额外列
        'Order No.': ['ORD001', 'ORD002', 'ORD003'],
        'Transaction Num': ['TXN001', 'TXN002', 'TXN003'],
        'Order types': ['Normal', 'Api order', 'Normal'],
        'Ticke code': ['TIC001', 'TIC002', 'TIC003'],  # 额外列 - 应该被忽略
        'Order status': ['Finished', 'Finished', 'Pending'],
        'Payment type': ['Card', 'Cash', 'Card'],  # 额外列 - 应该被忽略
        'Order price': [10.50, 25.00, 15.75],
        'Payment': [10.50, 25.00, 15.75],
        'Monetary unit': ['RM', 'RM', 'RM'],  # 额外列 - 应该被忽略
        'Order time': ['2025-06-30 10:00:00', '2025-06-30 11:00:00', '2025-06-30 12:00:00'],
        'Equipment type': ['Type A', 'Type B', 'Type A'],  # 额外列 - 应该被忽略
        'Equipment Model': ['Model X', 'Model Y', 'Model X'],  # 额外列 - 应该被忽略
        'Equipment ID': ['EQ001', 'EQ002', 'EQ003'],
        'Equipment name': ['设备A', '设备B', '设备C'],
        'Branch name': ['分店A', '分店B', '分店C'],
        'Area type': ['Area 1', 'Area 2', 'Area 1'],  # 额外列 - 应该被忽略
        'Payment date': ['2025-06-30', '2025-06-30', '2025-06-30'],
        'User name': ['用户A', '用户B', '用户C'],
        'Time': ['10:00', '11:00', '12:00'],
        'Matched_Flag': [True, True, False],
        'Matched Order ID': ['MATCH001', 'MATCH002', None],
        'Transaction ID': ['2951553687', '2951553688', '2951553689'],
        'OrderTime_dt': ['2025-06-30 10:00:00', '2025-06-30 11:00:00', '2025-06-30 12:00:00']
    }
    
    df = pd.DataFrame(test_data)
    
    # 创建临时Excel文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    df.to_excel(temp_file.name, index=False, sheet_name='TestData')
    temp_file.close()
    
    return temp_file.name, df

def test_data_import_processor():
    """测试DataImportProcessor的列名处理"""
    print("🔍 数据导入处理器列名修复测试")
    print("=" * 80)
    
    # 创建测试数据
    test_file, original_df = create_test_data_with_extra_columns()
    
    try:
        # 导入DataImportProcessor
        from data_import_optimized import DataImportProcessor
        
        # 创建处理器实例
        processor = DataImportProcessor()
        
        print(f"📋 原始测试数据 ({len(original_df.columns)} 列):")
        for i, col in enumerate(original_df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        print(f"\n🔧 测试列名标准化处理...")
        
        # 测试列名标准化方法
        df_standardized = processor._standardize_column_names(original_df)
        
        print(f"\n📊 列名标准化结果:")
        print(f"  原始列数: {len(original_df.columns)}")
        print(f"  处理后列数: {len(df_standardized.columns)}")
        print(f"  列数减少: {(1 - len(df_standardized.columns) / len(original_df.columns)) * 100:.1f}%")
        
        print(f"\n📋 处理后的列名:")
        for i, col in enumerate(df_standardized.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # 检查是否过滤掉了额外列
        extra_columns_in_original = [
            'Serial number', 'Ticke code', 'Payment type', 'Monetary unit',
            'Equipment type', 'Equipment Model', 'Area type'
        ]
        
        filtered_out = [col for col in extra_columns_in_original if col not in df_standardized.columns]
        still_present = [col for col in extra_columns_in_original if col in df_standardized.columns]
        
        print(f"\n✅ 过滤效果验证:")
        print(f"  应该被过滤的额外列: {len(extra_columns_in_original)}")
        print(f"  成功过滤的列: {len(filtered_out)}")
        print(f"  仍然存在的额外列: {len(still_present)}")
        
        if filtered_out:
            print(f"\n🚫 成功过滤的额外列:")
            for col in filtered_out:
                print(f"  - {col}")
        
        if still_present:
            print(f"\n⚠️ 仍然存在的额外列:")
            for col in still_present:
                print(f"  - {col}")
        
        # 检查核心业务列是否保留
        core_columns = ['Order_No', 'Transaction_Num', 'Order_price', 'Order_time', 'Equipment_ID', 'Equipment_name', 'Branch_name']
        missing_core = [col for col in core_columns if col not in df_standardized.columns]
        present_core = [col for col in core_columns if col in df_standardized.columns]
        
        print(f"\n✅ 核心业务列验证:")
        print(f"  核心列总数: {len(core_columns)}")
        print(f"  保留的核心列: {len(present_core)}")
        print(f"  缺失的核心列: {len(missing_core)}")
        
        if present_core:
            print(f"\n✅ 保留的核心业务列:")
            for col in present_core:
                print(f"  - {col}")
        
        if missing_core:
            print(f"\n❌ 缺失的核心业务列:")
            for col in missing_core:
                print(f"  - {col}")
        
        # 数据完整性检查
        print(f"\n📊 数据完整性检查:")
        print(f"  原始数据行数: {len(original_df)}")
        print(f"  处理后行数: {len(df_standardized)}")
        
        if len(original_df) == len(df_standardized):
            print(f"  ✅ 数据行数保持一致")
        else:
            print(f"  ❌ 数据行数不一致")
        
        # 检查关键数据是否保留
        if 'Order_price' in df_standardized.columns and 'Order price' in original_df.columns:
            original_sum = original_df['Order price'].sum()
            processed_sum = df_standardized['Order_price'].sum()
            if abs(original_sum - processed_sum) < 0.01:
                print(f"  ✅ 订单金额数据完整: RM{processed_sum:.2f}")
            else:
                print(f"  ❌ 订单金额数据不一致: {original_sum} → {processed_sum}")
        
        # 模拟数据库插入测试
        print(f"\n🔧 模拟数据库插入测试:")
        
        # 检查是否还有会导致数据库错误的列
        problematic_columns = []
        for col in df_standardized.columns:
            # 检查列名中是否包含空格或特殊字符
            if ' ' in col or any(char in col for char in ['(', ')', '.', '-']):
                problematic_columns.append(col)
        
        if not problematic_columns:
            print(f"  ✅ 所有列名都符合数据库要求")
        else:
            print(f"  ⚠️ 发现可能有问题的列名: {problematic_columns}")
        
        # 总体评估
        success_rate = (len(filtered_out) / len(extra_columns_in_original)) * 100 if extra_columns_in_original else 100
        core_retention_rate = (len(present_core) / len(core_columns)) * 100
        
        print(f"\n🎯 修复效果评估:")
        print(f"  额外列过滤成功率: {success_rate:.1f}%")
        print(f"  核心列保留率: {core_retention_rate:.1f}%")
        print(f"  数据完整性: {'✅ 保持' if len(original_df) == len(df_standardized) else '❌ 丢失'}")
        
        if success_rate >= 80 and core_retention_rate >= 90:
            print(f"  🎉 修复效果: 优秀")
            return True
        elif success_rate >= 60 and core_retention_rate >= 80:
            print(f"  👍 修复效果: 良好")
            return True
        else:
            print(f"  ⚠️ 修复效果: 需要改进")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时文件
        if os.path.exists(test_file):
            os.unlink(test_file)

def test_specific_error_case():
    """测试特定的错误案例：Serial number列"""
    print(f"\n🔧 特定错误案例测试: Serial number列")
    print("=" * 50)
    
    # 创建包含Serial number的最小测试数据
    minimal_data = {
        'Serial number': ['001', '002'],  # 这个列导致了错误
        'Order No.': ['ORD001', 'ORD002'],
        'Order price': [10.50, 25.00],
        'Equipment ID': ['EQ001', 'EQ002'],
        'Equipment name': ['设备A', '设备B'],
        'Branch name': ['分店A', '分店B'],
    }
    
    df_minimal = pd.DataFrame(minimal_data)
    
    try:
        from data_import_optimized import DataImportProcessor
        processor = DataImportProcessor()
        
        print(f"📋 测试数据列名: {list(df_minimal.columns)}")
        
        # 处理数据
        df_processed = processor._standardize_column_names(df_minimal)
        
        print(f"📋 处理后列名: {list(df_processed.columns)}")
        
        # 检查Serial number是否被过滤
        if 'Serial number' not in df_processed.columns:
            print(f"✅ Serial number列已被成功过滤")
        else:
            print(f"❌ Serial number列仍然存在")
        
        # 检查核心列是否保留
        expected_columns = ['Order_No', 'Order_price', 'Equipment_ID', 'Equipment_name', 'Branch_name']
        missing = [col for col in expected_columns if col not in df_processed.columns]
        
        if not missing:
            print(f"✅ 所有核心列都已保留")
        else:
            print(f"❌ 缺失核心列: {missing}")
        
        return 'Serial number' not in df_processed.columns and not missing
        
    except Exception as e:
        print(f"❌ 特定测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 数据导入修复验证测试")
    print("=" * 80)
    
    # 测试1: 完整的列名处理测试
    success1 = test_data_import_processor()
    
    # 测试2: 特定错误案例测试
    success2 = test_specific_error_case()
    
    print(f"\n📄 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 测试目标: 验证Serial number等额外列不再导致数据库插入错误")
    
    overall_success = success1 and success2
    print(f"✅ 测试结果: {'成功' if overall_success else '失败'}")
    
    if overall_success:
        print(f"\n🎉 数据导入修复验证成功！")
        print(f"  📋 特性: 自动过滤额外列，保留核心业务列")
        print(f"  🔧 修复: Serial number等列不再导致数据库错误")
        print(f"  🎯 效果: 数据导入将正常进行，不会因额外列报错")
    else:
        print(f"\n⚠️ 修复验证失败，需要进一步调试")

if __name__ == "__main__":
    main()
