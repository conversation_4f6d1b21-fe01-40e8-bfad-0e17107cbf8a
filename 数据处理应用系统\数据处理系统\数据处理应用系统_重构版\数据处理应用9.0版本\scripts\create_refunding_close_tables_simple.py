# -*- coding: utf-8 -*-
"""
简化版Refunding和Close表创建脚本
去掉GUI对话框，直接执行操作
"""

import os
import sys
import sqlite3
from datetime import datetime
from pathlib import Path

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class SimpleRefundingCloseTableCreator:
    """简化版Refunding和Close表创建器"""
    
    def __init__(self, db_path: str = None):
        """初始化表创建器"""
        if db_path:
            self.db_path = db_path
        else:
            self.db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
        
        print(f"🔧 简化版Refunding和Close表创建器已初始化")
        print(f"📁 数据库路径: {self.db_path}")
    
    def check_source_tables(self) -> bool:
        """检查源表是否存在"""
        try:
            print("🔍 检查源表...")
            
            # 确保数据库目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查IOT_Sales表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='IOT_Sales'")
            iot_exists = cursor.fetchone() is not None
            
            # 检查ZERO_Sales表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ZERO_Sales'")
            zero_exists = cursor.fetchone() is not None
            
            if not iot_exists:
                print("❌ IOT_Sales表不存在")
                conn.close()
                return False
            
            if not zero_exists:
                print("❌ ZERO_Sales表不存在")
                conn.close()
                return False
            
            # 检查表中是否有数据
            cursor.execute("SELECT COUNT(*) FROM IOT_Sales")
            iot_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM ZERO_Sales")
            zero_count = cursor.fetchone()[0]
            
            print(f"✅ 源表检查完成:")
            print(f"  • IOT_Sales: {iot_count} 条记录")
            print(f"  • ZERO_Sales: {zero_count} 条记录")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ 检查源表失败: {e}")
            return False
    
    def create_refunding_close_tables(self) -> bool:
        """创建Refunding和Close表"""
        try:
            print("🏗️ 创建Refunding和Close表...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取IOT_Sales表结构
            cursor.execute("PRAGMA table_info(IOT_Sales)")
            iot_columns = cursor.fetchall()
            
            # 构建列定义
            column_definitions = []
            for col in iot_columns:
                col_name = col[1]
                col_type = col[2]
                not_null = "NOT NULL" if col[3] else ""
                default_val = f"DEFAULT {col[4]}" if col[4] is not None else ""
                pk = "PRIMARY KEY AUTOINCREMENT" if col[5] and col_name == "ID" else ""
                
                col_def = f"{col_name} {col_type} {pk} {not_null} {default_val}".strip()
                column_definitions.append(col_def)
            
            columns_sql = ",\n    ".join(column_definitions)
            
            # 创建四个新表
            tables_to_create = [
                "IOT_Sales_Refunding",
                "IOT_Sales_Close", 
                "ZERO_Sales_Refunding",
                "ZERO_Sales_Close"
            ]
            
            for table_name in tables_to_create:
                print(f"  📋 创建表: {table_name}")
                create_sql = f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    {columns_sql}
                )
                """
                
                cursor.execute(create_sql)
                print(f"  ✅ 表 {table_name} 创建完成")
            
            conn.commit()
            conn.close()
            print("🎉 所有Refunding和Close表创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 创建表失败: {e}")
            return False
    
    def migrate_existing_data(self) -> dict:
        """迁移现有数据到对应的表"""
        print("📊 开始迁移现有数据...")
        
        migration_stats = {
            'IOT_Sales_Refunding': 0,
            'IOT_Sales_Close': 0,
            'ZERO_Sales_Refunding': 0,
            'ZERO_Sales_Close': 0
        }
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 迁移IOT_Sales中的Refunding数据
            print("  🔄 迁移IOT_Sales中的Refunding数据...")
            cursor.execute("""
                INSERT INTO IOT_Sales_Refunding 
                SELECT * FROM IOT_Sales 
                WHERE LOWER(Order_status) IN ('refunded', 'refunding')
            """)
            migration_stats['IOT_Sales_Refunding'] = cursor.rowcount
            print(f"    ✅ 迁移 {cursor.rowcount} 条记录到 IOT_Sales_Refunding")
            
            # 迁移IOT_Sales中的Close数据
            print("  🔄 迁移IOT_Sales中的Close数据...")
            cursor.execute("""
                INSERT INTO IOT_Sales_Close 
                SELECT * FROM IOT_Sales 
                WHERE LOWER(Order_status) IN ('close', 'closed')
            """)
            migration_stats['IOT_Sales_Close'] = cursor.rowcount
            print(f"    ✅ 迁移 {cursor.rowcount} 条记录到 IOT_Sales_Close")
            
            # 迁移ZERO_Sales中的Refunding数据
            print("  🔄 迁移ZERO_Sales中的Refunding数据...")
            cursor.execute("""
                INSERT INTO ZERO_Sales_Refunding 
                SELECT * FROM ZERO_Sales 
                WHERE LOWER(Order_status) IN ('refunded', 'refunding')
            """)
            migration_stats['ZERO_Sales_Refunding'] = cursor.rowcount
            print(f"    ✅ 迁移 {cursor.rowcount} 条记录到 ZERO_Sales_Refunding")
            
            # 迁移ZERO_Sales中的Close数据
            print("  🔄 迁移ZERO_Sales中的Close数据...")
            cursor.execute("""
                INSERT INTO ZERO_Sales_Close 
                SELECT * FROM ZERO_Sales 
                WHERE LOWER(Order_status) IN ('close', 'closed')
            """)
            migration_stats['ZERO_Sales_Close'] = cursor.rowcount
            print(f"    ✅ 迁移 {cursor.rowcount} 条记录到 ZERO_Sales_Close")
            
            conn.commit()
            conn.close()
            
            # 输出迁移统计
            total_migrated = sum(migration_stats.values())
            print(f"📊 数据迁移完成，总计迁移: {total_migrated} 条记录")
            for table_name, count in migration_stats.items():
                if count > 0:
                    print(f"  • {table_name}: {count} 条记录")
            
            return migration_stats
            
        except Exception as e:
            print(f"❌ 数据迁移失败: {e}")
            return migration_stats
    
    def create_simple_views(self) -> bool:
        """创建简化版统计视图"""
        try:
            print("📈 创建统计视图...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 删除现有视图（如果存在）
            cursor.execute("DROP VIEW IF EXISTS Refunding_Close_Summary")
            
            # 创建简化的汇总统计视图
            summary_view_sql = """
            CREATE VIEW Refunding_Close_Summary AS
            SELECT
              'IOT_Sales_Refunding' AS Table_Name,
              'IOT' AS Platform,
              'Refunding' AS Status_Type,
              COUNT(*) AS Record_Count,
              SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
              AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
              COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count
            FROM IOT_Sales_Refunding
            WHERE Order_price IS NOT NULL AND Order_price != ''
            
            UNION ALL
            
            SELECT
              'IOT_Sales_Close' AS Table_Name,
              'IOT' AS Platform,
              'Close' AS Status_Type,
              COUNT(*) AS Record_Count,
              SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
              AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
              COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count
            FROM IOT_Sales_Close
            WHERE Order_price IS NOT NULL AND Order_price != ''
            
            UNION ALL
            
            SELECT
              'ZERO_Sales_Refunding' AS Table_Name,
              'ZERO' AS Platform,
              'Refunding' AS Status_Type,
              COUNT(*) AS Record_Count,
              SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
              AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
              COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count
            FROM ZERO_Sales_Refunding
            WHERE Order_price IS NOT NULL AND Order_price != ''
            
            UNION ALL
            
            SELECT
              'ZERO_Sales_Close' AS Table_Name,
              'ZERO' AS Platform,
              'Close' AS Status_Type,
              COUNT(*) AS Record_Count,
              SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
              AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
              COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count
            FROM ZERO_Sales_Close
            WHERE Order_price IS NOT NULL AND Order_price != ''
            
            ORDER BY Platform, Status_Type
            """
            
            cursor.execute(summary_view_sql)
            conn.commit()
            
            print("✅ Refunding_Close_Summary 汇总视图创建完成")
            
            # 测试视图
            cursor.execute("SELECT COUNT(*) FROM Refunding_Close_Summary")
            summary_count = cursor.fetchone()[0]
            print(f"📊 汇总视图包含 {summary_count} 条统计记录")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ 创建视图失败: {e}")
            return False
    
    def run_complete_setup(self) -> dict:
        """运行完整的设置流程"""
        print("🚀 开始创建Refunding和Close表系统")
        
        # 1. 检查源表
        if not self.check_source_tables():
            print("❌ 源表检查失败，无法继续")
            return {}
        
        # 2. 创建新表
        if not self.create_refunding_close_tables():
            print("❌ 创建表失败，无法继续")
            return {}
        
        # 3. 迁移现有数据
        migration_stats = self.migrate_existing_data()
        
        # 4. 创建统计视图
        if not self.create_simple_views():
            print("❌ 创建视图失败")
        
        print("✅ Refunding和Close表系统创建完成")
        return migration_stats


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='简化版创建Refunding和Close表脚本')
    parser.add_argument('--db_path', help='数据库路径')
    
    args = parser.parse_args()
    
    try:
        print("=" * 60)
        print("🎯 简化版Refunding和Close表创建脚本")
        print("=" * 60)
        
        # 创建表创建器
        creator = SimpleRefundingCloseTableCreator(args.db_path)
        
        # 运行完整设置
        migration_stats = creator.run_complete_setup()
        
        # 输出结果
        print("\n" + "=" * 60)
        print("✅ Refunding和Close表系统创建成功")
        print("=" * 60)
        
        if migration_stats:
            print(f"📊 数据迁移统计:")
            total_migrated = sum(migration_stats.values())
            print(f"  总计迁移: {total_migrated} 条记录")
            
            for table_name, count in migration_stats.items():
                if count > 0:
                    print(f"  • {table_name}: {count} 条记录")
        
        print(f"\n🎯 创建的表:")
        print(f"  • IOT_Sales_Refunding")
        print(f"  • IOT_Sales_Close")
        print(f"  • ZERO_Sales_Refunding")
        print(f"  • ZERO_Sales_Close")
        
        print(f"\n📋 创建的视图:")
        print(f"  • Refunding_Close_Summary")
        
        print(f"\n🎉 所有操作完成！")
        return 0
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
