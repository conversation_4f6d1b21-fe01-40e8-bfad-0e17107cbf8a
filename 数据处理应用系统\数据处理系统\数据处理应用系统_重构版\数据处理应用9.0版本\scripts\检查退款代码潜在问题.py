#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
退款代码潜在问题检查脚本
全面检查退款脚本的代码质量、安全性和潜在问题
"""

import os
import sys
import ast
import re
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Tuple

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class RefundCodeAnalyzer:
    """退款代码分析器"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.suggestions = []
        
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """分析退款脚本文件"""
        print(f"🔍 分析文件: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 执行各种检查
        self._check_sql_injection_risks(content)
        self._check_error_handling(content)
        self._check_transaction_safety(content)
        self._check_data_validation(content)
        self._check_logging_practices(content)
        self._check_performance_issues(content)
        self._check_transaction_id_logic(content)
        self._check_database_operations(content)
        
        return {
            'issues': self.issues,
            'warnings': self.warnings,
            'suggestions': self.suggestions
        }
    
    def _check_sql_injection_risks(self, content: str):
        """检查SQL注入风险"""
        print("🔒 检查SQL注入风险...")
        
        # 检查字符串拼接的SQL
        sql_concat_patterns = [
            r'f".*{.*}.*".*execute',
            r'".*%.*".*execute',
            r"'.*%.*'.*execute",
            r'f".*UPDATE.*{.*}.*"',
            r'f".*SELECT.*{.*}.*"',
            r'f".*INSERT.*{.*}.*"',
            r'f".*DELETE.*{.*}.*"'
        ]
        
        for pattern in sql_concat_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            if matches:
                self.issues.append({
                    'type': 'SQL注入风险',
                    'severity': 'HIGH',
                    'description': f'发现可能的SQL注入风险: 使用字符串拼接构建SQL语句',
                    'matches': len(matches),
                    'recommendation': '使用参数化查询（?占位符）代替字符串拼接'
                })
        
        # 检查参数化查询的使用
        parameterized_queries = re.findall(r'execute\([^)]*\?\s*[,)]', content)
        if parameterized_queries:
            self.suggestions.append({
                'type': '安全实践',
                'description': f'发现{len(parameterized_queries)}处正确使用参数化查询',
                'status': 'GOOD'
            })
    
    def _check_error_handling(self, content: str):
        """检查错误处理"""
        print("⚠️ 检查错误处理...")
        
        # 检查裸露的except语句
        bare_except = re.findall(r'except\s*:', content)
        if bare_except:
            self.warnings.append({
                'type': '错误处理',
                'severity': 'MEDIUM',
                'description': f'发现{len(bare_except)}处裸露的except语句',
                'recommendation': '使用具体的异常类型，避免捕获所有异常'
            })
        
        # 检查异常重新抛出
        reraise_patterns = re.findall(r'raise\s*$', content, re.MULTILINE)
        if reraise_patterns:
            self.suggestions.append({
                'type': '错误处理',
                'description': f'发现{len(reraise_patterns)}处正确的异常重新抛出',
                'status': 'GOOD'
            })
        
        # 检查自定义异常的使用
        custom_exceptions = re.findall(r'raise\s+\w*Error\(', content)
        if custom_exceptions:
            self.suggestions.append({
                'type': '错误处理',
                'description': f'发现{len(custom_exceptions)}处使用自定义异常',
                'status': 'GOOD'
            })
    
    def _check_transaction_safety(self, content: str):
        """检查事务安全性"""
        print("🔄 检查事务安全性...")
        
        # 检查事务开始
        begin_transactions = re.findall(r'BEGIN|begin\(\)', content, re.IGNORECASE)
        commits = re.findall(r'commit\(\)', content, re.IGNORECASE)
        rollbacks = re.findall(r'rollback\(\)', content, re.IGNORECASE)
        
        if begin_transactions:
            if not commits and not rollbacks:
                self.issues.append({
                    'type': '事务安全',
                    'severity': 'HIGH',
                    'description': '发现事务开始但没有对应的commit或rollback',
                    'recommendation': '确保每个事务都有对应的commit或rollback'
                })
            elif commits and rollbacks:
                self.suggestions.append({
                    'type': '事务安全',
                    'description': '事务处理完整：包含commit和rollback',
                    'status': 'GOOD'
                })
        
        # 检查with语句的使用（自动资源管理）
        with_statements = re.findall(r'with\s+get_connection\(\)', content)
        if with_statements:
            self.suggestions.append({
                'type': '资源管理',
                'description': f'发现{len(with_statements)}处使用with语句进行连接管理',
                'status': 'GOOD'
            })
    
    def _check_data_validation(self, content: str):
        """检查数据验证"""
        print("✅ 检查数据验证...")
        
        # 检查输入验证
        validation_patterns = [
            r'pd\.notna\(',
            r'\.strip\(\)',
            r'isinstance\(',
            r'validate_\w+',
            r'if\s+not\s+\w+:',
            r'\.empty\s*:'
        ]
        
        validation_count = 0
        for pattern in validation_patterns:
            matches = re.findall(pattern, content)
            validation_count += len(matches)
        
        if validation_count > 10:
            self.suggestions.append({
                'type': '数据验证',
                'description': f'发现{validation_count}处数据验证，验证覆盖良好',
                'status': 'GOOD'
            })
        elif validation_count < 5:
            self.warnings.append({
                'type': '数据验证',
                'severity': 'MEDIUM',
                'description': f'数据验证较少（{validation_count}处），可能存在数据安全风险',
                'recommendation': '增加更多的输入数据验证'
            })
    
    def _check_logging_practices(self, content: str):
        """检查日志记录实践"""
        print("📝 检查日志记录...")
        
        # 检查日志级别的使用
        log_levels = {
            'debug': len(re.findall(r'\.debug\(', content)),
            'info': len(re.findall(r'\.info\(', content)),
            'warning': len(re.findall(r'\.warning\(', content)),
            'error': len(re.findall(r'\.error\(', content))
        }
        
        total_logs = sum(log_levels.values())
        if total_logs > 20:
            self.suggestions.append({
                'type': '日志记录',
                'description': f'日志记录丰富（{total_logs}条），包含多种级别',
                'details': log_levels,
                'status': 'GOOD'
            })
        
        # 检查敏感信息泄露
        sensitive_patterns = [
            r'password',
            r'token',
            r'secret',
            r'key'
        ]
        
        for pattern in sensitive_patterns:
            matches = re.findall(f'log.*{pattern}', content, re.IGNORECASE)
            if matches:
                self.warnings.append({
                    'type': '信息安全',
                    'severity': 'MEDIUM',
                    'description': f'可能在日志中记录敏感信息: {pattern}',
                    'recommendation': '避免在日志中记录敏感信息'
                })
    
    def _check_performance_issues(self, content: str):
        """检查性能问题"""
        print("⚡ 检查性能问题...")
        
        # 检查iterrows的使用
        iterrows_usage = re.findall(r'\.iterrows\(\)', content)
        if iterrows_usage:
            self.warnings.append({
                'type': '性能问题',
                'severity': 'MEDIUM',
                'description': f'发现{len(iterrows_usage)}处使用iterrows()，可能影响性能',
                'recommendation': '考虑使用向量化操作或apply()方法'
            })
        
        # 检查批量操作
        batch_patterns = [
            r'batch_size',
            r'executemany',
            r'to_sql.*chunksize'
        ]
        
        batch_count = 0
        for pattern in batch_patterns:
            matches = re.findall(pattern, content)
            batch_count += len(matches)
        
        if batch_count > 0:
            self.suggestions.append({
                'type': '性能优化',
                'description': f'发现{batch_count}处批量操作优化',
                'status': 'GOOD'
            })
    
    def _check_transaction_id_logic(self, content: str):
        """检查Transaction ID逻辑"""
        print("🆔 检查Transaction ID逻辑...")
        
        # 检查Transaction ID处理逻辑
        transaction_id_checks = [
            r'Transaction ID.*in.*columns',
            r'pd\.notna.*Transaction ID',
            r'transaction_id.*strip',
            r'Transaction_Num.*astype.*str'
        ]
        
        logic_count = 0
        for pattern in transaction_id_checks:
            matches = re.findall(pattern, content, re.IGNORECASE)
            logic_count += len(matches)
        
        if logic_count >= 3:
            self.suggestions.append({
                'type': 'Transaction ID逻辑',
                'description': f'Transaction ID处理逻辑完整（{logic_count}处检查）',
                'status': 'GOOD'
            })
        else:
            self.warnings.append({
                'type': 'Transaction ID逻辑',
                'severity': 'LOW',
                'description': f'Transaction ID处理逻辑可能不够完整（{logic_count}处检查）',
                'recommendation': '确保Transaction ID的存在性、格式和匹配逻辑都有适当的处理'
            })
    
    def _check_database_operations(self, content: str):
        """检查数据库操作"""
        print("🗄️ 检查数据库操作...")
        
        # 检查UPDATE操作的安全性
        update_operations = re.findall(r'UPDATE.*WHERE', content, re.IGNORECASE | re.DOTALL)
        if update_operations:
            # 检查是否有WHERE子句
            updates_without_where = re.findall(r'UPDATE.*(?!WHERE)', content, re.IGNORECASE)
            if len(updates_without_where) > len(update_operations):
                self.issues.append({
                    'type': '数据库安全',
                    'severity': 'HIGH',
                    'description': '发现可能没有WHERE子句的UPDATE操作',
                    'recommendation': '确保所有UPDATE操作都有适当的WHERE条件'
                })
            else:
                self.suggestions.append({
                    'type': '数据库安全',
                    'description': f'所有UPDATE操作都包含WHERE子句（{len(update_operations)}处）',
                    'status': 'GOOD'
                })
        
        # 检查连接池的使用
        connection_pool_usage = re.findall(r'get_connection', content)
        if connection_pool_usage:
            self.suggestions.append({
                'type': '数据库连接',
                'description': f'使用连接池管理数据库连接（{len(connection_pool_usage)}处）',
                'status': 'GOOD'
            })

def main():
    """主函数"""
    print("🔍 退款代码潜在问题检查")
    print("=" * 60)
    
    # 分析退款脚本
    refund_script_path = "refund_process_optimized.py"
    
    if not os.path.exists(refund_script_path):
        print(f"❌ 找不到退款脚本: {refund_script_path}")
        return
    
    analyzer = RefundCodeAnalyzer()
    results = analyzer.analyze_file(refund_script_path)
    
    # 输出分析结果
    print("\n" + "=" * 60)
    print("📊 代码质量分析结果")
    print("=" * 60)
    
    # 严重问题
    if results['issues']:
        print(f"\n🚨 发现 {len(results['issues'])} 个严重问题:")
        for i, issue in enumerate(results['issues'], 1):
            print(f"\n{i}. {issue['type']} ({issue['severity']})")
            print(f"   描述: {issue['description']}")
            if 'matches' in issue:
                print(f"   出现次数: {issue['matches']}")
            print(f"   建议: {issue['recommendation']}")
    else:
        print("\n✅ 未发现严重问题")
    
    # 警告
    if results['warnings']:
        print(f"\n⚠️ 发现 {len(results['warnings'])} 个警告:")
        for i, warning in enumerate(results['warnings'], 1):
            print(f"\n{i}. {warning['type']} ({warning['severity']})")
            print(f"   描述: {warning['description']}")
            print(f"   建议: {warning['recommendation']}")
    else:
        print("\n✅ 未发现警告问题")
    
    # 良好实践
    if results['suggestions']:
        print(f"\n💡 发现 {len(results['suggestions'])} 个良好实践:")
        for i, suggestion in enumerate(results['suggestions'], 1):
            print(f"\n{i}. {suggestion['type']}")
            print(f"   {suggestion['description']}")
            if 'details' in suggestion:
                print(f"   详情: {suggestion['details']}")
    
    # 总体评估
    print("\n" + "=" * 60)
    print("🎯 总体评估")
    print("=" * 60)
    
    total_issues = len(results['issues'])
    total_warnings = len(results['warnings'])
    total_good_practices = len(results['suggestions'])
    
    if total_issues == 0 and total_warnings <= 2:
        print("✅ 代码质量良好")
        print("🔒 安全性: 优秀")
        print("⚡ 性能: 良好")
        print("🛡️ 错误处理: 完善")
    elif total_issues <= 1 and total_warnings <= 5:
        print("⚠️ 代码质量一般")
        print("🔒 安全性: 良好")
        print("⚡ 性能: 一般")
        print("🛡️ 错误处理: 基本完善")
    else:
        print("❌ 代码质量需要改进")
        print("🔒 安全性: 需要关注")
        print("⚡ 性能: 需要优化")
        print("🛡️ 错误处理: 需要加强")
    
    print(f"\n📊 统计:")
    print(f"  严重问题: {total_issues}")
    print(f"  警告: {total_warnings}")
    print(f"  良好实践: {total_good_practices}")
    
    # 优先修复建议
    if total_issues > 0:
        print(f"\n🔧 优先修复建议:")
        high_priority = [issue for issue in results['issues'] if issue['severity'] == 'HIGH']
        if high_priority:
            print("  1. 优先修复高严重性问题")
            for issue in high_priority:
                print(f"     - {issue['type']}: {issue['recommendation']}")
    
    return total_issues == 0 and total_warnings <= 2

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 检查{'通过' if success else '发现问题'}")
    input("按回车键退出...")
