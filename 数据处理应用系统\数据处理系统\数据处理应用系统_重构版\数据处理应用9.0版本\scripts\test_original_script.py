#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试原始脚本的模块导入问题
"""

print("TEST_START", flush=True)

import sys
import os

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print("PATH_SET", flush=True)

# 测试重构模块导入
modules_to_test = [
    "utils.standard_utils",
    "utils.logger", 
    "utils.exceptions",
    "utils.validators",
    "database.connection_pool",
    "database.models",
    "database.smart_backup_manager"
]

for module_name in modules_to_test:
    try:
        print(f"TESTING_{module_name.replace('.', '_').upper()}", flush=True)
        __import__(module_name)
        print(f"SUCCESS_{module_name.replace('.', '_').upper()}", flush=True)
    except Exception as e:
        print(f"FAILED_{module_name.replace('.', '_').upper()}_{str(e)}", flush=True)

print("TEST_END", flush=True)
