#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准化工具模块
提供统一的编码处理和配置管理功能
确保所有脚本使用一致的标准
"""

import os
import sys
import configparser
import codecs
from typing import Optional, Dict, Any

class StandardEncodingManager:
    """统一的编码管理器"""
    
    @staticmethod
    def setup_console_encoding():
        """设置控制台编码为UTF-8 - 统一标准"""
        try:
            if sys.platform.startswith('win'):
                # Windows系统使用codecs方式
                if hasattr(sys.stdout, 'detach'):
                    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
                    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
            else:
                # 非Windows系统使用reconfigure方式
                if hasattr(sys.stdout, 'reconfigure'):
                    sys.stdout.reconfigure(encoding='utf-8')
                    sys.stderr.reconfigure(encoding='utf-8')
        except Exception as e:
            # 如果设置失败，记录但不中断程序
            print(f"Warning: Failed to setup console encoding: {e}")
    
    @staticmethod
    def get_file_encoding() -> str:
        """获取标准文件编码"""
        return 'utf-8'
    
    @staticmethod
    def open_file(file_path: str, mode: str = 'r', **kwargs):
        """使用标准编码打开文件"""
        if 'encoding' not in kwargs:
            kwargs['encoding'] = StandardEncodingManager.get_file_encoding()
        return open(file_path, mode, **kwargs)

class StandardConfigManager:
    """统一的配置管理器"""
    
    def __init__(self, config_file: str = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，如果为None则自动查找
        """
        self.config = configparser.ConfigParser()
        self.config_file = config_file or self._find_config_file()
        self._load_config()
    
    def _find_config_file(self) -> str:
        """自动查找配置文件"""
        # 获取当前脚本的目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 可能的配置文件位置
        possible_paths = [
            # 当前目录
            os.path.join(current_dir, "config.ini"),
            # 父目录
            os.path.join(os.path.dirname(current_dir), "config.ini"),
            # 01_主程序目录
            os.path.join(os.path.dirname(current_dir), "01_主程序", "config.ini"),
            # 03_配置文件目录
            os.path.join(os.path.dirname(current_dir), "03_配置文件", "config.ini"),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 如果都不存在，返回默认路径
        return os.path.join(os.path.dirname(current_dir), "01_主程序", "config.ini")
    
    def _load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file, encoding=StandardEncodingManager.get_file_encoding())
            except Exception as e:
                print(f"Warning: Failed to load config file {self.config_file}: {e}")
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            if self.config.has_section(section) and self.config.has_option(section, key):
                return self.config.get(section, key)
            return default
        except Exception:
            return default
    
    def get_bool(self, section: str, key: str, default: bool = False) -> bool:
        """获取布尔配置值"""
        try:
            if self.config.has_section(section) and self.config.has_option(section, key):
                return self.config.getboolean(section, key)
            return default
        except Exception:
            return default
    
    def get_int(self, section: str, key: str, default: int = 0) -> int:
        """获取整数配置值"""
        try:
            if self.config.has_section(section) and self.config.has_option(section, key):
                return self.config.getint(section, key)
            return default
        except Exception:
            return default
    
    def get_db_path(self) -> str:
        """获取数据库路径 - 统一接口"""
        db_path = self.get('Database', 'db_path', '')
        
        if not db_path:
            # 使用默认路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            db_path = os.path.join(os.path.dirname(current_dir), "database", "sales_reports.db")
        
        # 如果是相对路径，转换为绝对路径
        if not os.path.isabs(db_path):
            config_dir = os.path.dirname(self.config_file)
            db_path = os.path.abspath(os.path.join(config_dir, db_path))
        
        return db_path
    
    def get_postgresql_config(self) -> Dict[str, Any]:
        """获取PostgreSQL配置"""
        return {
            'host': self.get('PostgreSQL', 'host', 'localhost'),
            'port': self.get_int('PostgreSQL', 'port', 5432),
            'database': self.get('PostgreSQL', 'database', 'sales_reports'),
            'username': self.get('PostgreSQL', 'username', 'postgres'),
            'password': self.get('PostgreSQL', 'password', ''),
        }
    
    def get_backup_config(self) -> Dict[str, Any]:
        """获取备份配置"""
        return {
            'auto_backup': self.get_bool('Backup', 'auto_backup', True),
            'backup_before_import': self.get_bool('Backup', 'backup_before_import', True),
            'backup_before_refund': self.get_bool('Backup', 'backup_before_refund', True),
            'max_backup_files': self.get_int('Backup', 'max_backup_files', 10),
        }

class StandardDatabaseManager:
    """统一的数据库连接管理器"""
    
    @staticmethod
    def setup_sqlite_connection(conn):
        """设置SQLite连接的标准配置"""
        # 设置文本工厂为str，确保UTF-8处理
        conn.text_factory = str
        # 关闭外键约束（如果需要）
        conn.execute("PRAGMA foreign_keys = OFF")
        # 设置同步模式为NORMAL，平衡性能和安全性
        conn.execute("PRAGMA synchronous = NORMAL")
        # 设置日志模式为WAL，提高并发性能
        conn.execute("PRAGMA journal_mode = WAL")
        return conn

# 创建全局实例
standard_encoding = StandardEncodingManager()
standard_config = StandardConfigManager()

def setup_standard_environment():
    """设置标准环境 - 所有脚本都应该调用此函数"""
    # 设置控制台编码
    standard_encoding.setup_console_encoding()
    
    # 返回配置管理器实例
    return standard_config

def get_standard_config() -> StandardConfigManager:
    """获取标准配置管理器实例"""
    return standard_config

def get_standard_db_path() -> str:
    """获取标准数据库路径"""
    return standard_config.get_db_path()

# 兼容性函数 - 保持向后兼容
def get_db_path_from_config(cli_db_path: Optional[str] = None) -> str:
    """
    兼容性函数：获取数据库路径
    保持与现有代码的兼容性
    """
    if cli_db_path:
        return cli_db_path
    
    return get_standard_db_path()

# 使用示例和文档
"""
使用示例：

1. 在脚本开头设置标准环境：
```python
from utils.standard_utils import setup_standard_environment
config = setup_standard_environment()
```

2. 获取配置值：
```python
db_path = config.get_db_path()
backup_enabled = config.get_bool('Backup', 'auto_backup', True)
```

3. 打开文件：
```python
from utils.standard_utils import StandardEncodingManager
with StandardEncodingManager.open_file('data.txt', 'r') as f:
    content = f.read()
```

4. 设置数据库连接：
```python
from utils.standard_utils import StandardDatabaseManager
import sqlite3

conn = sqlite3.connect(db_path)
StandardDatabaseManager.setup_sqlite_connection(conn)
```

这样可以确保所有脚本使用一致的编码和配置处理方式。
"""
