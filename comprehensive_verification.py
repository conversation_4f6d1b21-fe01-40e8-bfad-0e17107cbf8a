#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面验证脚本 - 检查所有修复是否正确
"""

import os
import sys
import configparser
import subprocess
import traceback
from pathlib import Path

def test_main_app_import():
    """测试main_app.py导入"""
    print("🔍 测试main_app.py导入...")
    
    try:
        # 测试导入
        import main_app
        print("✅ main_app.py导入成功")
        return True
    except Exception as e:
        print(f"❌ main_app.py导入失败: {e}")
        traceback.print_exc()
        return False

def test_config_files():
    """测试配置文件"""
    print("\n🔍 测试配置文件...")
    
    config_files = [
        "config.ini",
        "config.json",
        "数据处理应用系统/config.ini"
    ]
    
    results = {}
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ 配置文件存在: {config_file}")
            
            if config_file.endswith('.ini'):
                try:
                    config = configparser.ConfigParser()
                    config.read(config_file, encoding='utf-8')
                    
                    # 检查数据库路径
                    if config.has_section('Database'):
                        db_path = config.get('Database', 'db_path', fallback='')
                        if 'Day report 3' in db_path:
                            print(f"  ✅ 数据库路径正确: {db_path}")
                            results[config_file] = True
                        else:
                            print(f"  ❌ 数据库路径错误: {db_path}")
                            results[config_file] = False
                    else:
                        print(f"  ⚠️ 缺少Database配置段")
                        results[config_file] = False
                        
                except Exception as e:
                    print(f"  ❌ 配置文件解析失败: {e}")
                    results[config_file] = False
            else:
                results[config_file] = True
        else:
            print(f"❌ 配置文件不存在: {config_file}")
            results[config_file] = False
    
    return results

def test_script_paths():
    """测试脚本路径"""
    print("\n🔍 测试关键脚本文件...")
    
    scripts = [
        "report 三天报告 3.0.py",
        "数据导入脚本_完整版.py",
        "数据处理应用系统/数据处理系统/report 模块化设计 7.0.py",
        "数据处理应用系统/数据处理系统/数据处理应用系统_重构版/01_主程序/report 模块化设计 7.0.py"
    ]
    
    results = {}
    
    for script in scripts:
        if os.path.exists(script):
            print(f"✅ 脚本存在: {script}")
            
            # 检查脚本中的硬编码路径
            try:
                with open(script, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否还有旧的硬编码路径
                old_paths = [
                    'C:\\Users\\<USER>\\Desktop\\June\\IOT',
                    'C:/Users/<USER>/Desktop/June/IOT',
                    'C:\\Users\\<USER>\\Desktop\\Day Report',
                    'C:/Users/<USER>/Desktop/Day Report'
                ]
                
                has_old_path = False
                for old_path in old_paths:
                    if old_path in content:
                        print(f"  ⚠️ 发现旧路径: {old_path}")
                        has_old_path = True
                
                if not has_old_path:
                    print(f"  ✅ 路径检查通过")
                    results[script] = True
                else:
                    results[script] = False
                    
            except Exception as e:
                print(f"  ❌ 脚本检查失败: {e}")
                results[script] = False
        else:
            print(f"❌ 脚本不存在: {script}")
            results[script] = False
    
    return results

def test_directory_structure():
    """测试目录结构"""
    print("\n🔍 测试目录结构...")
    
    expected_dirs = [
        "backup_versions",
        "src",
        "config", 
        "logs",
        "data",
        "scripts"
    ]
    
    results = {}
    
    for directory in expected_dirs:
        if os.path.exists(directory):
            print(f"✅ 目录存在: {directory}")
            results[directory] = True
        else:
            print(f"❌ 目录不存在: {directory}")
            results[directory] = False
    
    return results

def test_backup_files():
    """测试备份文件"""
    print("\n🔍 测试备份文件...")
    
    backup_dir = "backup_versions"
    if not os.path.exists(backup_dir):
        print(f"❌ 备份目录不存在: {backup_dir}")
        return False
    
    backup_files = os.listdir(backup_dir)
    print(f"📦 备份目录包含 {len(backup_files)} 个文件")
    
    for file in backup_files[:5]:  # 显示前5个文件
        print(f"  📄 {file}")
    
    if len(backup_files) > 5:
        print(f"  ... 还有 {len(backup_files) - 5} 个文件")
    
    return len(backup_files) > 0

def test_main_app_startup():
    """测试main_app启动"""
    print("\n🔍 测试main_app启动...")
    
    try:
        # 使用subprocess测试启动，但立即终止
        cmd = [sys.executable, "-c", "import main_app; print('启动测试成功')"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ main_app启动测试成功")
            if "启动测试成功" in result.stdout:
                print("✅ 导入和初始化正常")
            return True
        else:
            print(f"❌ main_app启动失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ main_app启动超时（可能是GUI界面）")
        return True  # GUI应用超时是正常的
    except Exception as e:
        print(f"❌ main_app启动测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始全面验证...")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("main_app导入测试", test_main_app_import),
        ("配置文件测试", test_config_files),
        ("脚本路径测试", test_script_paths),
        ("目录结构测试", test_directory_structure),
        ("备份文件测试", test_backup_files),
        ("main_app启动测试", test_main_app_startup)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 验证结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        if isinstance(result, dict):
            # 对于返回字典的测试，检查是否所有项都通过
            all_passed = all(result.values()) if result else False
            status = "✅ 通过" if all_passed else "❌ 失败"
            print(f"  {status} {test_name}")
            if all_passed:
                passed += 1
        elif result:
            print(f"  ✅ 通过 {test_name}")
            passed += 1
        else:
            print(f"  ❌ 失败 {test_name}")
    
    print(f"\n📈 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有验证都通过！项目重构成功！")
        return True
    else:
        print("⚠️ 部分验证失败，请检查上述问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
