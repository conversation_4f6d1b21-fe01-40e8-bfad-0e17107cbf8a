[Database]
db_type = postgresql
db_host = localhost
db_port = 5432
db_name = postgres
db_user = postgres
db_password = zerochon
db_path = C:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\数据处理应用9.0版本\database\sales_reports.db

[Scripts]
intelligent_processor = report 脚本 3.0.py
modular_processor_9_0 = report 模块化设计 9.0.py
modular_processor_8_0 = report 模块化设计 8.0.py
modular_processor = report 模块化设计 7.0.py
refund_script = Refund_process_修复版.py
refund_script_optimized = scripts/refund_process_optimized.py
data_import_script = 数据导入脚本.py
data_import_script_optimized = scripts/data_import_optimized.py
dual_database_import = scripts/dual_database_import.py
dual_database_refund = scripts/dual_database_refund.py

[UI]
window_width = 900
window_height = 700
theme = iphone_style

[SQLite]
db_path = database\sales_reports.db
enabled = True

[Files]
file_separator = ||
temp_dir = temp_data
backup_dir = backups

[Backup]
auto_backup = true
backup_before_import = true
backup_before_refund = true
max_backup_files = 10

[PostgreSQL]
host = localhost
port = 5432
database = postgres
user = postgres
password = zerochon
enabled = True

