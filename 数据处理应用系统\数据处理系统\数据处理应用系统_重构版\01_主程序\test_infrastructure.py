# -*- coding: utf-8 -*-
"""
基础设施测试脚本 - 架构优化步骤1验证
测试服务容器、事件总线和特性开关的基本功能

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import sys
import os
import time
import threading
from typing import Any, Dict

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def test_service_container():
    """测试服务容器功能"""
    print("\n🧪 测试服务容器...")
    
    try:
        from infrastructure.service_container import ServiceContainer, ServiceLifetime
        
        # 创建服务容器
        container = ServiceContainer()
        
        # 测试1：注册和获取简单服务
        def create_test_service():
            return {"name": "test_service", "value": 42}
            
        container.register("test_service", create_test_service, ServiceLifetime.SINGLETON)
        
        service1 = container.get("test_service")
        service2 = container.get("test_service")
        
        assert service1 is service2, "单例服务应该返回相同实例"
        assert service1["value"] == 42, "服务数据应该正确"
        
        # 测试2：依赖注入
        def create_dependent_service(test_service):
            return {"dependency": test_service, "processed": True}
            
        container.register("dependent_service", create_dependent_service, 
                         ServiceLifetime.SINGLETON, dependencies=["test_service"])
        
        dependent = container.get("dependent_service")
        assert dependent["dependency"] is service1, "依赖注入应该正确"
        assert dependent["processed"] is True, "依赖服务应该正确处理"
        
        # 测试3：性能统计
        stats = container.get_performance_stats()
        assert stats["total_resolutions"] >= 3, "应该记录服务解析次数"
        assert stats["cache_hits"] >= 1, "应该有缓存命中"
        
        print("✅ 服务容器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 服务容器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_event_bus():
    """测试事件总线功能"""
    print("\n🧪 测试事件总线...")
    
    try:
        from infrastructure.event_bus import EventBus, EventPriority
        
        # 创建事件总线
        event_bus = EventBus(max_queue_size=100, worker_threads=1)
        
        # 测试1：基本事件发布和订阅
        received_events = []
        
        def event_handler(event):
            received_events.append(event)
            
        subscription_id = event_bus.subscribe("test_event", event_handler)
        
        # 发布事件
        event_bus.publish("test_event", {"message": "Hello World"})
        
        # 等待事件处理
        time.sleep(0.1)
        
        assert len(received_events) == 1, "应该接收到一个事件"
        assert received_events[0].data["message"] == "Hello World", "事件数据应该正确"
        
        # 测试2：事件优先级
        priority_events = []
        
        def priority_handler(event):
            priority_events.append(event.priority)
            
        event_bus.subscribe("priority_test", priority_handler)
        
        # 发布不同优先级的事件
        event_bus.publish("priority_test", "low", EventPriority.LOW)
        event_bus.publish("priority_test", "high", EventPriority.HIGH)
        event_bus.publish("priority_test", "normal", EventPriority.NORMAL)
        
        time.sleep(0.2)
        
        # 测试3：一次性订阅
        once_events = []
        
        def once_handler(event):
            once_events.append(event)
            
        event_bus.subscribe("once_test", once_handler, once=True)
        
        event_bus.publish("once_test", "first")
        event_bus.publish("once_test", "second")
        
        time.sleep(0.1)
        
        assert len(once_events) == 1, "一次性订阅应该只接收一个事件"
        
        # 测试4：统计信息
        stats = event_bus.get_stats()
        assert stats["events_published"] > 0, "应该记录发布的事件数"
        assert stats["events_processed"] > 0, "应该记录处理的事件数"
        
        # 关闭事件总线
        event_bus.shutdown()
        
        print("✅ 事件总线测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 事件总线测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_feature_flags():
    """测试特性开关功能"""
    print("\n🧪 测试特性开关...")
    
    try:
        from infrastructure.feature_flags import FeatureFlags, FeatureState
        
        # 创建特性开关管理器
        flags = FeatureFlags("test_feature_flags.json")
        
        # 测试1：基本启用/禁用
        flags.enable("test_feature", "测试特性")
        assert flags.is_enabled("test_feature"), "启用的特性应该返回True"
        
        flags.disable("test_feature")
        assert not flags.is_enabled("test_feature"), "禁用的特性应该返回False"
        
        # 测试2：测试模式
        flags.set_testing("test_feature", "测试模式特性")
        assert not flags.is_enabled("test_feature"), "测试模式对普通用户应该返回False"
        assert flags.is_enabled("test_feature", "test_user"), "测试模式对测试用户应该返回True"
        
        # 测试3：渐进推出
        flags.set_rollout("rollout_feature", 50.0, "渐进推出特性")
        
        # 测试多个用户ID
        enabled_count = 0
        for i in range(100):
            if flags.is_enabled("rollout_feature", f"user_{i}"):
                enabled_count += 1
                
        # 应该大约有50%的用户启用
        assert 30 <= enabled_count <= 70, f"50%推出应该有约50个用户启用，实际: {enabled_count}"
        
        # 测试4：依赖关系
        flags.enable("base_feature", "基础特性")
        flags.enable("dependent_feature", "依赖特性")
        flags.add_dependency("dependent_feature", "base_feature")
        
        assert flags.is_enabled("dependent_feature"), "依赖特性应该启用"
        
        flags.disable("base_feature")
        assert not flags.is_enabled("dependent_feature"), "依赖特性应该因为基础特性禁用而禁用"
        
        # 测试5：统计信息
        stats = flags.get_stats()
        assert stats["total_flags"] > 0, "应该有注册的特性"
        assert stats["flag_checks"] > 0, "应该记录特性检查次数"
        
        # 清理测试文件
        if os.path.exists("test_feature_flags.json"):
            os.remove("test_feature_flags.json")
            
        print("✅ 特性开关测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 特性开关测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_infrastructure_integration():
    """测试基础设施集成"""
    print("\n🧪 测试基础设施集成...")
    
    try:
        from infrastructure import initialize_infrastructure, get_infrastructure, shutdown_infrastructure
        
        # 初始化基础设施
        config = {
            "event_bus_queue_size": 100,
            "event_bus_workers": 1,
            "feature_flags_config": "test_integration_flags.json"
        }
        
        success = initialize_infrastructure(config)
        assert success, "基础设施初始化应该成功"
        
        infrastructure = get_infrastructure()
        assert infrastructure.initialized, "基础设施应该已初始化"
        
        # 测试组件获取
        container = infrastructure.get_container()
        event_bus = infrastructure.get_event_bus()
        feature_flags = infrastructure.get_feature_flags()
        
        assert container is not None, "应该能获取服务容器"
        assert event_bus is not None, "应该能获取事件总线"
        assert feature_flags is not None, "应该能获取特性开关"
        
        # 测试统计信息
        stats = infrastructure.get_stats()
        assert stats["initialized"], "统计信息应该显示已初始化"
        assert "initialization_time" in stats, "应该记录初始化时间"
        
        # 关闭基础设施
        shutdown_infrastructure()
        
        # 清理测试文件
        if os.path.exists("test_integration_flags.json"):
            os.remove("test_integration_flags.json")
            
        print("✅ 基础设施集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基础设施集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_compatibility_adapter():
    """测试兼容性适配器"""
    print("\n🧪 测试兼容性适配器...")
    
    try:
        from infrastructure import initialize_infrastructure, create_compatibility_adapter, shutdown_infrastructure
        
        # 初始化基础设施
        config = {"feature_flags_config": "test_adapter_flags.json"}
        initialize_infrastructure(config)
        
        # 创建兼容性适配器
        adapter = create_compatibility_adapter()
        
        # 测试配置管理器获取
        config_manager = adapter.get_config_manager()
        assert config_manager is not None, "应该能获取配置管理器"
        
        # 测试特性开关控制
        feature_flags = adapter.infrastructure.get_feature_flags()
        
        # 禁用服务容器，应该回退到旧系统
        feature_flags.disable("use_service_container")
        config_manager_old = adapter.get_config_manager()
        assert config_manager_old is not None, "回退模式应该能获取配置管理器"
        
        # 关闭基础设施
        shutdown_infrastructure()
        
        # 清理测试文件
        if os.path.exists("test_adapter_flags.json"):
            os.remove("test_adapter_flags.json")
            
        print("✅ 兼容性适配器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 兼容性适配器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始基础设施测试...")
    print("=" * 60)
    
    tests = [
        ("服务容器", test_service_container),
        ("事件总线", test_event_bus),
        ("特性开关", test_feature_flags),
        ("基础设施集成", test_infrastructure_integration),
        ("兼容性适配器", test_compatibility_adapter)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            failed += 1
            
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}个通过, {failed}个失败")
    
    if failed == 0:
        print("🎉 所有测试通过！基础设施架构优化步骤1完成")
        return True
    else:
        print("⚠️ 部分测试失败，需要修复问题")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
