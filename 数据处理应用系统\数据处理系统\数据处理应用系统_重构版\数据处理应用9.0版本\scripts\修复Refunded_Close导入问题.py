#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Refunded/Close导入问题 - 诊断并修复状态路由问题
"""

import sqlite3
import os
import sys
from pathlib import Path

def check_database_status():
    """检查数据库状态"""
    print("🔍 检查数据库状态")
    print("=" * 60)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [table[0] for table in cursor.fetchall()]
        
        required_tables = [
            'IOT_Sales', 'IOT_Sales_Refunding', 'IOT_Sales_Close',
            'ZERO_Sales', 'ZERO_Sales_Refunding', 'ZERO_Sales_Close'
        ]
        
        print("📋 表存在性检查:")
        missing_tables = []
        existing_tables = []
        
        for table in required_tables:
            if table in tables:
                print(f"   ✅ {table}: 存在")
                existing_tables.append(table)
            else:
                print(f"   ❌ {table}: 不存在")
                missing_tables.append(table)
        
        # 检查主表中的状态分布
        print(f"\n📊 主表状态分布:")
        for main_table in ['IOT_Sales', 'ZERO_Sales']:
            if main_table in existing_tables:
                try:
                    cursor.execute(f"""
                        SELECT Order_status, COUNT(*) as count 
                        FROM {main_table} 
                        WHERE Order_status IS NOT NULL AND Order_status != ''
                        GROUP BY Order_status 
                        ORDER BY count DESC
                    """)
                    
                    status_distribution = cursor.fetchall()
                    print(f"\n   {main_table}:")
                    
                    refunded_count = 0
                    close_count = 0
                    
                    for status, count in status_distribution:
                        status_lower = status.lower()
                        if any(keyword in status_lower for keyword in ['refund', 'cancel']):
                            refunded_count += count
                            print(f"     🚨 {status}: {count:,} 条 (应该在Refunding表)")
                        elif any(keyword in status_lower for keyword in ['close', 'end', 'stop']):
                            close_count += count
                            print(f"     🚨 {status}: {count:,} 条 (应该在Close表)")
                        else:
                            print(f"     ✅ {status}: {count:,} 条")
                    
                    if refunded_count > 0 or close_count > 0:
                        print(f"     ⚠️ 需要迁移: Refunded类型 {refunded_count} 条, Close类型 {close_count} 条")
                        
                except Exception as e:
                    print(f"     ❌ 查询失败: {e}")
        
        conn.close()
        
        return {
            'missing_tables': missing_tables,
            'existing_tables': existing_tables
        }
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def create_missing_tables():
    """创建缺失的表"""
    print("\n🔧 创建缺失的表")
    print("=" * 60)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取主表结构
        cursor.execute("PRAGMA table_info(IOT_Sales)")
        iot_columns = cursor.fetchall()
        
        if not iot_columns:
            print("❌ IOT_Sales表不存在，无法获取表结构")
            return False
        
        # 构建CREATE TABLE语句
        column_definitions = []
        for col in iot_columns:
            col_name = col[1]
            col_type = col[2]
            not_null = "NOT NULL" if col[3] else ""
            default_val = f"DEFAULT {col[4]}" if col[4] is not None else ""
            primary_key = "PRIMARY KEY" if col[5] else ""
            
            col_def = f"{col_name} {col_type} {not_null} {default_val} {primary_key}".strip()
            column_definitions.append(col_def)
        
        columns_sql = ", ".join(column_definitions)
        
        # 创建Refunding和Close表
        tables_to_create = [
            'IOT_Sales_Refunding', 'IOT_Sales_Close',
            'ZERO_Sales_Refunding', 'ZERO_Sales_Close',
            'APP_Sales_Refunding', 'APP_Sales_Close'
        ]
        
        created_tables = []
        
        for table_name in tables_to_create:
            try:
                # 检查表是否已存在
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                if cursor.fetchone():
                    print(f"   ✅ {table_name}: 已存在")
                    continue
                
                # 创建表
                create_sql = f"CREATE TABLE {table_name} ({columns_sql})"
                cursor.execute(create_sql)
                created_tables.append(table_name)
                print(f"   ✅ {table_name}: 创建成功")
                
            except Exception as e:
                print(f"   ❌ {table_name}: 创建失败 - {e}")
        
        conn.commit()
        conn.close()
        
        if created_tables:
            print(f"\n🎉 成功创建 {len(created_tables)} 个表: {created_tables}")
        else:
            print(f"\n✅ 所有必需的表都已存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def migrate_existing_data():
    """迁移现有数据到正确的表"""
    print("\n🔄 迁移现有数据到正确的表")
    print("=" * 60)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        migration_stats = {}
        
        # 迁移IOT_Sales中的数据
        print("📊 迁移IOT_Sales数据:")
        
        # 迁移Refunding数据
        cursor.execute("""
            INSERT INTO IOT_Sales_Refunding 
            SELECT * FROM IOT_Sales 
            WHERE LOWER(Order_status) IN ('refunded', 'refunding', 'cancelled', 'cancel')
               OR Order_status LIKE '%refund%' 
               OR Order_status LIKE '%cancel%'
        """)
        refunding_count = cursor.rowcount
        migration_stats['IOT_Sales_Refunding'] = refunding_count
        print(f"   ✅ Refunding: {refunding_count} 条记录")
        
        # 迁移Close数据
        cursor.execute("""
            INSERT INTO IOT_Sales_Close 
            SELECT * FROM IOT_Sales 
            WHERE LOWER(Order_status) IN ('close', 'closed', 'end', 'ended', 'stop', 'stopped')
               OR Order_status LIKE '%close%' 
               OR Order_status LIKE '%end%'
               OR Order_status LIKE '%stop%'
        """)
        close_count = cursor.rowcount
        migration_stats['IOT_Sales_Close'] = close_count
        print(f"   ✅ Close: {close_count} 条记录")
        
        # 迁移ZERO_Sales中的数据
        print("\n📊 迁移ZERO_Sales数据:")
        
        # 检查ZERO_Sales是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ZERO_Sales'")
        if cursor.fetchone():
            # 迁移Refunding数据
            cursor.execute("""
                INSERT INTO ZERO_Sales_Refunding 
                SELECT * FROM ZERO_Sales 
                WHERE LOWER(Order_status) IN ('refunded', 'refunding', 'cancelled', 'cancel')
                   OR Order_status LIKE '%refund%' 
                   OR Order_status LIKE '%cancel%'
            """)
            zero_refunding_count = cursor.rowcount
            migration_stats['ZERO_Sales_Refunding'] = zero_refunding_count
            print(f"   ✅ Refunding: {zero_refunding_count} 条记录")
            
            # 迁移Close数据
            cursor.execute("""
                INSERT INTO ZERO_Sales_Close 
                SELECT * FROM ZERO_Sales 
                WHERE LOWER(Order_status) IN ('close', 'closed', 'end', 'ended', 'stop', 'stopped')
                   OR Order_status LIKE '%close%' 
                   OR Order_status LIKE '%end%'
                   OR Order_status LIKE '%stop%'
            """)
            zero_close_count = cursor.rowcount
            migration_stats['ZERO_Sales_Close'] = zero_close_count
            print(f"   ✅ Close: {zero_close_count} 条记录")
        else:
            print("   ⚠️ ZERO_Sales表不存在，跳过迁移")
        
        conn.commit()
        conn.close()
        
        # 显示迁移统计
        print(f"\n📊 迁移统计:")
        total_migrated = sum(migration_stats.values())
        for table, count in migration_stats.items():
            if count > 0:
                print(f"   ✅ {table}: {count:,} 条记录")
        
        print(f"\n🎉 总计迁移: {total_migrated:,} 条记录")
        
        return migration_stats
        
    except Exception as e:
        print(f"❌ 数据迁移失败: {e}")
        return {}

def verify_migration():
    """验证迁移结果"""
    print("\n✅ 验证迁移结果")
    print("=" * 60)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        tables_to_check = [
            'IOT_Sales_Refunding', 'IOT_Sales_Close',
            'ZERO_Sales_Refunding', 'ZERO_Sales_Close'
        ]
        
        print("📊 各表数据统计:")
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                
                if count > 0:
                    print(f"   ✅ {table}: {count:,} 条记录")
                    
                    # 显示状态分布
                    cursor.execute(f"""
                        SELECT Order_status, COUNT(*) as count 
                        FROM {table} 
                        GROUP BY Order_status 
                        ORDER BY count DESC 
                        LIMIT 5
                    """)
                    status_dist = cursor.fetchall()
                    for status, status_count in status_dist:
                        print(f"       - {status}: {status_count} 条")
                else:
                    print(f"   ⚠️ {table}: 0 条记录")
                    
            except Exception as e:
                print(f"   ❌ {table}: 查询失败 - {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

def main():
    """主函数"""
    print("🔧 修复Refunded/Close导入问题")
    print("=" * 60)
    
    try:
        # 1. 检查数据库状态
        db_status = check_database_status()
        if not db_status:
            return 1
        
        # 2. 创建缺失的表
        if db_status['missing_tables']:
            print(f"\n🚨 发现缺失的表: {db_status['missing_tables']}")
            if not create_missing_tables():
                return 1
        
        # 3. 迁移现有数据
        migration_stats = migrate_existing_data()
        
        # 4. 验证迁移结果
        verify_migration()
        
        # 5. 提供使用建议
        print("\n" + "=" * 60)
        print("🎯 修复完成！使用建议")
        print("=" * 60)
        
        print("1. 📥 今后导入数据时，请确保:")
        print("   - 选择'智能识别导入'模式")
        print("   - 确认Excel文件中有Order_status列")
        print("   - 状态值包含Refunded、Close等关键词")
        
        print("2. 🔍 验证导入结果:")
        print("   - 检查对应的Refunding和Close表是否有新数据")
        print("   - 确认主表中不再有Refunded/Close状态的记录")
        
        print("3. 📊 数据查询:")
        print("   - Refunded数据: 查询 *_Sales_Refunding 表")
        print("   - Close数据: 查询 *_Sales_Close 表")
        print("   - 正常数据: 查询 *_Sales 主表")
        
        total_migrated = sum(migration_stats.values()) if migration_stats else 0
        if total_migrated > 0:
            print(f"\n🎉 修复成功！已迁移 {total_migrated:,} 条记录到正确的表中。")
        else:
            print(f"\n✅ 表结构已修复，可以正常导入Refunded/Close数据。")
        
        return 0
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
