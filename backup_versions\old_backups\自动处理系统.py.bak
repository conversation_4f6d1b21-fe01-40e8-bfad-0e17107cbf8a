# coding: utf-8
import os
import re
import pandas as pd
import numpy as np
import shutil
import time
import logging
import configparser
from datetime import datetime, timedelta
import warnings

# 抑制 openpyxl 警告
warnings.simplefilter("ignore", category=UserWarning)

# 配置日志
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"file_processing_{datetime.now().strftime('%Y%m%d')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger()

# 配置文件路径
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")

# 默认配置
DEFAULT_CONFIG = {
    "Paths": {
        "iot_folder": r"C:\Users\<USER>\Desktop\Day report 3\IOT",
        "zero_folder": r"C:\Users\<USER>\Desktop\Day report 3\ZERO",
        "processed_folder": r"C:\Users\<USER>\Desktop\Day report 3\已处理",
        "error_folder": r"C:\Users\<USER>\Desktop\Day report 3\需人工检查"
    },
    "FilePatterns": {
        "settlement_iot_pattern": r"SETTLEMENT_REPORT.*_zeroiot\.xlsx$",
        "settlement_zero_pattern": r"SETTLEMENT_REPORT.*_zeropowerstatio\.xlsx$",
        "china_iot_pattern": r"\d{6}\s+CHINA\s+IOT\.xlsx$",
        "china_zero_pattern": r"\d{6}\s+CHINA\s+ZERO\.xlsx$"
    },
    "Processing": {
        "check_interval": "60",  # 检查间隔（秒）
        "max_retries": "3",      # 最大重试次数
        "retry_delay": "5"       # 重试延迟（秒）
    }
}

# 创建或加载配置文件
def create_or_load_config():
    config = configparser.ConfigParser()
    
    if os.path.exists(config_path):
        config.read(config_path, encoding='utf-8')
        logger.info("已加载配置文件")
    else:
        # 创建默认配置
        for section, options in DEFAULT_CONFIG.items():
            if not config.has_section(section):
                config.add_section(section)
            for option, value in options.items():
                config.set(section, option, value)
        
        # 保存配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            config.write(f)
        logger.info("已创建默认配置文件")
    
    return config

# 创建必要的文件夹
def create_folders(config):
    folders = [
        config.get('Paths', 'iot_folder'),
        config.get('Paths', 'zero_folder'),
        config.get('Paths', 'processed_folder'),
        config.get('Paths', 'error_folder')
    ]
    
    for folder in folders:
        os.makedirs(folder, exist_ok=True)
        logger.info(f"确保文件夹存在: {folder}")

# 识别文件类型
def identify_file_type(filename, config):
    if re.match(config.get('FilePatterns', 'settlement_iot_pattern'), filename, re.IGNORECASE):
        return "SETTLEMENT_IOT"
    elif re.match(config.get('FilePatterns', 'settlement_zero_pattern'), filename, re.IGNORECASE):
        return "SETTLEMENT_ZERO"
    elif re.match(config.get('FilePatterns', 'china_iot_pattern'), filename, re.IGNORECASE):
        return "CHINA_IOT"
    elif re.match(config.get('FilePatterns', 'china_zero_pattern'), filename, re.IGNORECASE):
        return "CHINA_ZERO"
    else:
        return "UNKNOWN"

# 从文件名提取日期
def extract_date_from_filename(filename, file_type):
    try:
        if file_type in ["CHINA_IOT", "CHINA_ZERO"]:
            # 格式如: 110525 CHINA IOT.xlsx
            match = re.search(r'(\d{6})', filename)
            if match:
                date_str = match.group(1)
                # 假设格式为 DDMMYY
                day = date_str[:2]
                month = date_str[2:4]
                year = "20" + date_str[4:6]  # 假设是21世纪
                return f"{year}-{month}-{day}"
        
        elif file_type in ["SETTLEMENT_IOT", "SETTLEMENT_ZERO"]:
            # 尝试多种格式匹配
            # 格式1: SETTLEMENT_REPORT_FROM_DATE_13052025_TO_13052025_zeroiot.xlsx
            match1 = re.search(r'FROM_DATE_(\d{8})_TO_(\d{8})', filename)
            if match1:
                start_date = match1.group(1)
                end_date = match1.group(2)
                # 假设格式为 DDMMYYYY
                start_day = start_date[:2]
                start_month = start_date[2:4]
                start_year = start_date[4:8]
                return f"{start_year}-{start_month}-{start_day}"
            
            # 格式2: SETTLEMENT_REPORT_FROM_08 TO 11052025_zeroiot.xlsx
            match2 = re.search(r'FROM_(\d{2})\s+TO\s+(\d{8})', filename)
            if match2:
                start_day = match2.group(1)
                end_date = match2.group(2)
                # 假设格式为 DDMMYYYY
                end_day = end_date[:2]
                end_month = end_date[2:4]
                end_year = end_date[4:8]
                return f"{end_year}-{end_month}-{end_day}"
        
        # 处理线上订单流水信息文件格式 (如: 线上订单流水信息20250514.xlsx)
        match_order = re.search(r'线上订单流水信息(\d{8})', filename)
        if match_order:
            date_str = match_order.group(1)
            # 格式为 YYYYMMDD
            year = date_str[:4]
            month = date_str[4:6]
            day = date_str[6:8]
            return f"{year}-{month}-{day}"
        
        # 如果无法匹配，尝试从文件名中提取任何可能的日期
        # 尝试匹配YYYYMMDD格式
        match_yyyymmdd = re.search(r'(\d{8})', filename)
        if match_yyyymmdd:
            date_str = match_yyyymmdd.group(1)
            # 检查是否可能是DDMMYYYY格式
            if int(date_str[4:8]) >= 2000 and int(date_str[4:8]) <= 2100:
                # 可能是DDMMYYYY格式
                day = date_str[:2]
                month = date_str[2:4]
                year = date_str[4:8]
                return f"{year}-{month}-{day}"
            elif int(date_str[:4]) >= 2000 and int(date_str[:4]) <= 2100:
                # 可能是YYYYMMDD格式
                year = date_str[:4]
                month = date_str[4:6]
                day = date_str[6:8]
                return f"{year}-{month}-{day}"
        
        # 尝试匹配DDMMYY格式
        match_ddmmyy = re.search(r'(\d{6})', filename)
        if match_ddmmyy:
            date_str = match_ddmmyy.group(1)
            day = date_str[:2]
            month = date_str[2:4]
            year = "20" + date_str[4:6]  # 假设是21世纪
            return f"{year}-{month}-{day}"
        
        # 如果无法匹配，返回None
        logger.warning(f"无法从文件名提取日期: {filename}")
        return None
    
    except Exception as e:
        logger.error(f"从文件名提取日期时出错: {filename}, 错误: {str(e)}")
        return None

# 检查文件是否已处理
def is_file_processed(filename, processed_folder):
    return os.path.exists(os.path.join(processed_folder, filename))

# 移动文件到指定文件夹
def move_file(src_path, dest_folder, filename=None):
    if filename is None:
        filename = os.path.basename(src_path)
    
    dest_path = os.path.join(dest_folder, filename)
    
    # 如果目标文件已存在，添加时间戳
    if os.path.exists(dest_path):
        name, ext = os.path.splitext(filename)
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        new_filename = f"{name}_{timestamp}{ext}"
        dest_path = os.path.join(dest_folder, new_filename)
    
    try:
        shutil.move(src_path, dest_path)
        logger.info(f"已移动文件: {src_path} -> {dest_path}")
        return True
    except Exception as e:
        logger.error(f"移动文件失败: {src_path}, 错误: {str(e)}")
        return False

# 处理SETTLEMENT文件和CHINA文件的匹配
def match_settlement_with_china(settlement_file, china_file, settlement_type, config):
    try:
        # 提取日期
        settlement_date = extract_date_from_filename(settlement_file, settlement_type)
        
        # 检查china_file是否是线上订单流水信息文件
        if "线上订单流水信息" in china_file:
            china_type = "线上订单流水信息"
        else:
            china_type = "CHINA_IOT" if settlement_type == "SETTLEMENT_IOT" else "CHINA_ZERO"
        
        china_date = extract_date_from_filename(china_file, china_type)
        
        if settlement_date and china_date:
            logger.info(f"提取的日期 - SETTLEMENT: {settlement_date}, 流水/CHINA: {china_date}")
            
            # 检查日期是否精确匹配
            if settlement_date == china_date:
                logger.info(f"找到精确匹配: {settlement_file} 与 {china_file} (日期: {settlement_date})")
                return True
            
            # 尝试更灵活的匹配 - 转换为日期对象进行比较
            try:
                from datetime import datetime
                settlement_dt = datetime.strptime(settlement_date, "%Y-%m-%d")
                china_dt = datetime.strptime(china_date, "%Y-%m-%d")
                
                # 检查日期是否相近（允许1天的误差）
                delta = abs((settlement_dt - china_dt).days)
                if delta <= 1:
                    logger.info(f"找到近似匹配: {settlement_file} 与 {china_file} (日期相差 {delta} 天)")
                    return True
            except Exception as e:
                logger.error(f"日期转换出错: {str(e)}")
        
        return False
    
    except Exception as e:
        logger.error(f"匹配文件时出错: {settlement_file} 与 {china_file}, 错误: {str(e)}")
        return False

# 执行处理脚本
def execute_processing_script(settlement_file, china_file, file_type, config):
    try:
        # 根据文件类型选择合适的处理脚本
        if file_type == "SETTLEMENT_IOT":
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "report 脚本 3.0.py")
        elif file_type == "SETTLEMENT_ZERO":
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "report 三天报告 3.0.py")
        else:
            logger.error(f"未知的文件类型: {file_type}")
            return False
        
        # 获取文件的完整路径
        settlement_path = os.path.join(
            config.get('Paths', 'iot_folder' if file_type == "SETTLEMENT_IOT" else 'zero_folder'),
            settlement_file
        )
        
        china_path = os.path.join(
            config.get('Paths', 'iot_folder' if file_type == "SETTLEMENT_IOT" else 'zero_folder'),
            china_file
        )
        
        # 创建临时配置文件
        temp_config = configparser.ConfigParser()
        temp_config.add_section('Files')
        temp_config.set('Files', 'settlement_file', settlement_path)
        temp_config.set('Files', 'china_file', china_path)
        
        temp_config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_config.ini")
        with open(temp_config_path, 'w', encoding='utf-8') as f:
            temp_config.write(f)
        
        # 执行脚本适配器
        adapter_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "脚本适配器.py")
        import sys
        import subprocess
        cmd = [sys.executable, adapter_path]
        
        max_retries = int(config.get('Processing', 'max_retries', fallback=3))
        retry_delay = int(config.get('Processing', 'retry_delay', fallback=5))
        
        for attempt in range(max_retries):
            try:
                logger.info(f"执行处理脚本 (尝试 {attempt+1}/{max_retries})")
                result = subprocess.run(cmd, check=True, capture_output=True, text=True)
                logger.info(f"脚本执行成功: {result.stdout}")
                
                # 清理临时配置文件
                if os.path.exists(temp_config_path):
                    os.remove(temp_config_path)
                
                # 执行数据验证和处理
                logger.info("开始执行数据验证和处理...")
                try:
                    # 导入数据验证处理器
                    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
                    from 数据验证处理器 import verify_and_process_data
                    
                    # 设置容差值（可以从配置文件中读取）
                    tolerance = float(config.get('Processing', 'amount_tolerance', fallback=0.01))
                    
                    # 执行验证和处理
                    verification_result = verify_and_process_data(settlement_path, china_path, tolerance)
                    
                    if verification_result:
                        logger.info("数据验证和处理成功完成")
                    else:
                        logger.error("数据验证和处理失败，请检查日志了解详情")
                        return False
                    
                except Exception as e:
                    logger.error(f"执行数据验证和处理时出错: {str(e)}")
                    import traceback
                    logger.error(traceback.format_exc())
                    return False
                
                # 处理完成后，移动文件到已处理文件夹
                processed_folder = config.get('Paths', 'processed_folder')
                move_file(settlement_path, processed_folder)
                move_file(china_path, processed_folder)
                
                logger.info(f"处理完成: {settlement_file} 和 {china_file}")
                return True
            except subprocess.CalledProcessError as e:
                logger.error(f"脚本执行失败 (尝试 {attempt+1}/{max_retries}): {e}")
                logger.error(f"错误输出: {e.stderr}")
                
                if attempt < max_retries - 1:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"达到最大重试次数，放弃处理")
                    
                    # 清理临时配置文件
                    if os.path.exists(temp_config_path):
                        os.remove(temp_config_path)
                    
                    return False
        
        return False
    
    except Exception as e:
        logger.error(f"执行处理脚本时出错: {str(e)}")
        return False

# 监控文件夹并处理文件
def monitor_and_process(config):
    iot_folder = config.get('Paths', 'iot_folder')
    zero_folder = config.get('Paths', 'zero_folder')
    processed_folder = config.get('Paths', 'processed_folder')
    error_folder = config.get('Paths', 'error_folder')
    
    check_interval = int(config.get('Processing', 'check_interval'))
    
    logger.info(f"开始监控文件夹: {iot_folder} 和 {zero_folder}")
    
    while True:
        try:
            # 处理IOT文件夹
            process_folder(iot_folder, "IOT", processed_folder, error_folder, config)
            
            # 处理ZERO文件夹
            process_folder(zero_folder, "ZERO", processed_folder, error_folder, config)
            
            # 等待下一次检查
            logger.info(f"等待 {check_interval} 秒后进行下一次检查...")
            time.sleep(check_interval)
        
        except KeyboardInterrupt:
            logger.info("监控已停止")
            break
        
        except Exception as e:
            logger.error(f"监控过程中出错: {str(e)}")
            time.sleep(check_interval)

# 处理单个文件夹
def process_folder(folder_path, folder_type, processed_folder, error_folder, config):
    logger.info(f"检查 {folder_type} 文件夹: {folder_path}")
    
    # 获取文件列表
    try:
        files = [f for f in os.listdir(folder_path) if f.lower().endswith('.xlsx') and not f.startswith('~$')]
    except Exception as e:
        logger.error(f"读取文件夹失败: {folder_path}, 错误: {str(e)}")
        return
    
    if not files:
        logger.info(f"{folder_type} 文件夹中没有Excel文件")
        return
    
    # 分类文件
    settlement_files = []
    china_files = []
    
    for file in files:
        file_type = identify_file_type(file, config)
        
        if file_type in ["SETTLEMENT_IOT", "SETTLEMENT_ZERO"] and folder_type == ("IOT" if file_type == "SETTLEMENT_IOT" else "ZERO"):
            settlement_files.append(file)
        
        elif file_type in ["CHINA_IOT", "CHINA_ZERO"] and folder_type == ("IOT" if file_type == "CHINA_IOT" else "ZERO"):
            china_files.append(file)
        
        elif file_type == "UNKNOWN":
            logger.warning(f"未知文件类型: {file}")
    
    logger.info(f"找到 {len(settlement_files)} 个SETTLEMENT文件和 {len(china_files)} 个CHINA文件")
    
    # 尝试匹配文件并处理
    for settlement_file in settlement_files:
        settlement_type = identify_file_type(settlement_file, config)
        matched = False
        
        for china_file in china_files:
            if match_settlement_with_china(settlement_file, china_file, settlement_type, config):
                # 执行处理脚本
                if execute_processing_script(settlement_file, china_file, settlement_type, config):
                    matched = True
                    break
        
        if not matched:
            logger.warning(f"未找到匹配的CHINA文件: {settlement_file}")
            # 可以选择移动到错误文件夹或保留在原处等待匹配

# 主函数
def main():
    logger.info("启动自动处理系统")
    
    # 加载配置
    config = create_or_load_config()
    
    # 创建必要的文件夹
    create_folders(config)
    
    # 开始监控和处理
    monitor_and_process(config)

if __name__ == "__main__":
    main()