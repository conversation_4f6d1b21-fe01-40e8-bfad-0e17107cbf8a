#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库修复工具
专门用于修复SQLite数据库损坏问题
"""

import os
import sys
import sqlite3
import shutil
import json
from datetime import datetime
from pathlib import Path
import traceback

class DatabaseRepairTool:
    """数据库修复工具"""
    
    def __init__(self):
        self.config_path = "config.json"
        self.db_path = None
        self.backup_dir = None
        self.repair_log = []
        
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.db_path = config.get('database_path', 'C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db')
            else:
                self.db_path = 'C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db'
                
            # 设置备份目录
            self.backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            os.makedirs(self.backup_dir, exist_ok=True)
            
            self.log_message(f"数据库路径: {self.db_path}")
            self.log_message(f"备份目录: {self.backup_dir}")
            
        except Exception as e:
            self.log_message(f"❌ 加载配置失败: {e}")
            
    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)
        self.repair_log.append(log_entry)
        
    def diagnose_database(self):
        """诊断数据库状态"""
        self.log_message("🔍 开始诊断数据库状态...")
        
        if not os.path.exists(self.db_path):
            self.log_message(f"❌ 数据库文件不存在: {self.db_path}")
            return False
            
        # 检查文件大小
        file_size = os.path.getsize(self.db_path)
        self.log_message(f"📊 数据库文件大小: {file_size:,} 字节")
        
        if file_size == 0:
            self.log_message("❌ 数据库文件为空")
            return False
            
        # 尝试连接数据库
        try:
            with sqlite3.connect(self.db_path, timeout=5) as conn:
                cursor = conn.cursor()
                
                # 执行完整性检查
                self.log_message("🔧 执行数据库完整性检查...")
                cursor.execute("PRAGMA integrity_check")
                integrity_result = cursor.fetchone()
                
                if integrity_result[0] == 'ok':
                    self.log_message("✅ 数据库完整性检查通过")
                    return True
                else:
                    self.log_message(f"❌ 数据库完整性检查失败: {integrity_result[0]}")
                    return False
                    
        except sqlite3.DatabaseError as e:
            error_msg = str(e).lower()
            if 'malformed' in error_msg or 'corrupt' in error_msg:
                self.log_message(f"🚨 数据库损坏: {e}")
                return False
            else:
                self.log_message(f"❌ 数据库连接失败: {e}")
                return False
        except Exception as e:
            self.log_message(f"❌ 诊断过程中出错: {e}")
            return False
            
    def find_latest_backup(self):
        """查找最新的备份文件"""
        self.log_message("🔍 查找最新备份文件...")
        
        if not os.path.exists(self.backup_dir):
            self.log_message(f"❌ 备份目录不存在: {self.backup_dir}")
            return None
            
        backup_files = []
        for file in os.listdir(self.backup_dir):
            if file.endswith('.db') and 'backup' in file:
                file_path = os.path.join(self.backup_dir, file)
                file_time = os.path.getmtime(file_path)
                backup_files.append((file_path, file_time))
                
        if not backup_files:
            self.log_message("❌ 没有找到备份文件")
            return None
            
        # 按时间排序，最新的在前
        backup_files.sort(key=lambda x: x[1], reverse=True)
        latest_backup = backup_files[0][0]
        
        self.log_message(f"📁 找到最新备份: {os.path.basename(latest_backup)}")
        
        # 验证备份文件完整性
        if self.verify_backup_integrity(latest_backup):
            self.log_message("✅ 备份文件完整性验证通过")
            return latest_backup
        else:
            self.log_message("❌ 备份文件损坏，查找下一个备份...")
            # 尝试下一个备份
            for backup_path, _ in backup_files[1:]:
                if self.verify_backup_integrity(backup_path):
                    self.log_message(f"✅ 找到可用备份: {os.path.basename(backup_path)}")
                    return backup_path
            
            self.log_message("❌ 所有备份文件都已损坏")
            return None
            
    def verify_backup_integrity(self, backup_path):
        """验证备份文件完整性"""
        try:
            with sqlite3.connect(backup_path, timeout=5) as conn:
                cursor = conn.cursor()
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                return result[0] == 'ok'
        except Exception:
            return False
            
    def create_emergency_backup(self):
        """创建紧急备份（即使数据库损坏）"""
        self.log_message("🚨 创建紧急备份...")
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            emergency_backup = os.path.join(self.backup_dir, f"emergency_backup_{timestamp}.db")
            shutil.copy2(self.db_path, emergency_backup)
            
            self.log_message(f"✅ 紧急备份已创建: {os.path.basename(emergency_backup)}")
            return emergency_backup
            
        except Exception as e:
            self.log_message(f"❌ 创建紧急备份失败: {e}")
            return None
            
    def attempt_sqlite_repair(self):
        """尝试使用SQLite内置修复功能"""
        self.log_message("🔧 尝试SQLite内置修复...")
        
        try:
            # 创建修复后的数据库文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            repaired_db = os.path.join(self.backup_dir, f"repaired_{timestamp}.db")
            
            # 使用.dump和.read命令修复
            dump_file = os.path.join(self.backup_dir, f"dump_{timestamp}.sql")
            
            # 导出数据
            with sqlite3.connect(self.db_path) as source_conn:
                with open(dump_file, 'w', encoding='utf-8') as f:
                    for line in source_conn.iterdump():
                        f.write('%s\n' % line)
                        
            self.log_message("✅ 数据导出完成")
            
            # 创建新数据库并导入数据
            with sqlite3.connect(repaired_db) as new_conn:
                with open(dump_file, 'r', encoding='utf-8') as f:
                    new_conn.executescript(f.read())
                    
            self.log_message("✅ 数据导入完成")
            
            # 验证修复结果
            if self.verify_backup_integrity(repaired_db):
                self.log_message("✅ 数据库修复成功")
                
                # 清理临时文件
                os.remove(dump_file)
                
                return repaired_db
            else:
                self.log_message("❌ 修复后的数据库仍有问题")
                return None
                
        except Exception as e:
            self.log_message(f"❌ SQLite修复失败: {e}")
            return None
            
    def restore_from_backup(self, backup_path):
        """从备份恢复数据库"""
        self.log_message(f"🔄 从备份恢复数据库: {os.path.basename(backup_path)}")
        
        try:
            # 创建当前数据库的紧急备份
            self.create_emergency_backup()
            
            # 恢复备份
            shutil.copy2(backup_path, self.db_path)
            
            # 验证恢复结果
            if self.diagnose_database():
                self.log_message("✅ 数据库恢复成功")
                return True
            else:
                self.log_message("❌ 数据库恢复失败")
                return False
                
        except Exception as e:
            self.log_message(f"❌ 恢复过程中出错: {e}")
            return False
            
    def save_repair_log(self):
        """保存修复日志"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = os.path.join(self.backup_dir, f"repair_log_{timestamp}.txt")
            
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("数据库修复日志\n")
                f.write("=" * 50 + "\n")
                for entry in self.repair_log:
                    f.write(entry + "\n")
                    
            self.log_message(f"📝 修复日志已保存: {os.path.basename(log_file)}")
            
        except Exception as e:
            self.log_message(f"❌ 保存日志失败: {e}")
            
    def run_repair(self):
        """执行完整的修复流程"""
        self.log_message("🚀 开始数据库修复流程...")
        self.log_message("=" * 60)
        
        # 加载配置
        self.load_config()
        
        # 诊断数据库
        if self.diagnose_database():
            self.log_message("✅ 数据库状态正常，无需修复")
            return True
            
        # 查找备份
        backup_path = self.find_latest_backup()
        
        if backup_path:
            # 尝试从备份恢复
            if self.restore_from_backup(backup_path):
                self.log_message("🎉 数据库修复完成！")
                self.save_repair_log()
                return True
        
        # 尝试SQLite内置修复
        repaired_db = self.attempt_sqlite_repair()
        if repaired_db:
            # 用修复后的数据库替换原数据库
            if self.restore_from_backup(repaired_db):
                self.log_message("🎉 数据库修复完成！")
                self.save_repair_log()
                return True
                
        self.log_message("❌ 所有修复方法都失败了")
        self.log_message("💡 建议：")
        self.log_message("   1. 检查是否有其他备份文件")
        self.log_message("   2. 联系技术支持")
        self.log_message("   3. 考虑重新创建数据库")
        
        self.save_repair_log()
        return False

def main():
    """主函数"""
    print("🔧 数据库修复工具")
    print("=" * 60)
    
    try:
        repair_tool = DatabaseRepairTool()
        success = repair_tool.run_repair()
        
        if success:
            print("\n🎉 修复完成！您现在可以重新尝试数据导入。")
            return 0
        else:
            print("\n❌ 修复失败！请查看日志了解详情。")
            return 1
            
    except Exception as e:
        print(f"\n💥 修复工具执行失败: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
