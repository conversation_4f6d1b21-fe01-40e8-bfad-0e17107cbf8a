#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化显示效果脚本 - 简化终端输出，提升用户体验
"""

import os
import sys

def create_optimized_launcher():
    """创建优化的启动脚本"""
    print("🔧 创建优化的启动脚本")
    
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版应用启动器 - 简洁的终端输出
"""

import os
import sys
import subprocess

def main():
    """主函数 - 启动应用程序并优化输出"""
    print("🚀 启动数据处理与导入应用...")
    
    # 设置环境变量，减少调试输出
    env = os.environ.copy()
    env['PYTHONUNBUFFERED'] = '1'  # 确保输出实时显示
    env['APP_DEBUG'] = '0'  # 禁用调试模式
    
    # 应用程序路径
    app_path = os.path.join(
        os.path.dirname(__file__),
        "01_主程序",
        "数据处理与导入应用_完整版.py"
    )
    
    try:
        # 启动应用程序
        process = subprocess.Popen(
            [sys.executable, app_path],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 过滤输出，只显示重要信息
        important_keywords = [
            "启动", "成功", "失败", "错误", "完成", "进度",
            "导入", "备份", "恢复", "✅", "❌", "🔍", "📋"
        ]
        
        debug_keywords = [
            "[DEBUG]", "DEBUG:", "safe_log调用", "_update_log_widget调用",
            "显示初始消息", "ModernProcessingTab", "已注册", "不存在"
        ]
        
        print("应用程序已启动，正在过滤输出...")
        print("-" * 50)
        
        for line in process.stdout:
            line = line.strip()
            if not line:
                continue
            
            # 跳过调试信息
            if any(keyword in line for keyword in debug_keywords):
                continue
            
            # 显示重要信息
            if any(keyword in line for keyword in important_keywords):
                print(line)
            elif line.startswith(("🔍", "✅", "❌", "📋", "🔄", "导入:", "使用导入脚本:")):
                print(line)
            elif "备份" in line and ("完成" in line or "验证" in line):
                # 简化备份信息
                if "完成" in line:
                    print("✅ 数据库备份完成")
                elif "验证通过" in line:
                    print("✅ 备份验证通过")
        
        # 等待进程结束
        return_code = process.wait()
        
        if return_code == 0:
            print("-" * 50)
            print("🎉 应用程序正常退出")
        else:
            print("-" * 50)
            print(f"⚠️ 应用程序退出，返回码: {return_code}")
        
        return return_code
        
    except KeyboardInterrupt:
        print("\\n⚠️ 用户中断操作")
        if 'process' in locals():
            process.terminate()
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
    
    launcher_path = "../启动应用_简洁版.py"
    try:
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        print(f"✅ 创建启动脚本: {launcher_path}")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def create_environment_config():
    """创建环境配置文件"""
    print("🔧 创建环境配置文件")
    
    env_content = '''# 数据处理应用环境配置
# 设置这些环境变量来控制应用行为

# 调试模式 (0=关闭, 1=开启)
APP_DEBUG=0

# 强制命令行模式 (避免GUI卡住)
FORCE_CONSOLE=1

# 自动处理重复数据
AUTO_DUPLICATE_HANDLING=skip

# Python输出缓冲
PYTHONUNBUFFERED=1

# 编码设置
PYTHONIOENCODING=utf-8
'''
    
    env_path = "../.env"
    try:
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(env_content)
        print(f"✅ 创建环境配置: {env_path}")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def create_batch_launcher():
    """创建批处理启动文件"""
    print("🔧 创建批处理启动文件")
    
    batch_content = '''@echo off
chcp 65001 >nul
title 数据处理与导入应用

echo 🚀 启动数据处理与导入应用...
echo.

REM 设置环境变量
set APP_DEBUG=0
set FORCE_CONSOLE=1
set AUTO_DUPLICATE_HANDLING=skip
set PYTHONUNBUFFERED=1
set PYTHONIOENCODING=utf-8

REM 启动应用
python "01_主程序\\数据处理与导入应用_完整版.py"

echo.
echo 按任意键退出...
pause >nul
'''
    
    batch_path = "../启动应用.bat"
    try:
        with open(batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        print(f"✅ 创建批处理文件: {batch_path}")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("📋 优化后的使用方法")
    print("=" * 60)
    
    print("\n🚀 推荐的启动方式:")
    print("1. 双击 启动应用.bat (Windows批处理)")
    print("   - 自动设置环境变量")
    print("   - 简洁的输出显示")
    print("   - 避免调试信息干扰")
    print("")
    
    print("2. 运行 启动应用_简洁版.py (Python脚本)")
    print("   - 过滤调试输出")
    print("   - 只显示重要信息")
    print("   - 实时进度显示")
    print("")
    
    print("3. 直接运行原程序 (如果需要完整输出)")
    print("   - python \"01_主程序\\数据处理与导入应用_完整版.py\"")
    print("")
    
    print("🔧 环境变量说明:")
    print("- APP_DEBUG=0: 关闭调试输出")
    print("- FORCE_CONSOLE=1: 强制命令行模式")
    print("- AUTO_DUPLICATE_HANDLING=skip: 自动跳过重复数据")
    print("")
    
    print("📊 优化效果:")
    print("- ✅ 终端输出更简洁")
    print("- ✅ 减少调试信息干扰")
    print("- ✅ 保留重要的状态信息")
    print("- ✅ 避免GUI相关的卡住问题")
    print("- ✅ 更好的用户体验")

def main():
    """主函数"""
    print("🔧 优化显示效果")
    print("=" * 60)
    
    # 执行优化任务
    tasks = [
        ("创建优化启动脚本", create_optimized_launcher),
        ("创建环境配置文件", create_environment_config),
        ("创建批处理启动文件", create_batch_launcher),
    ]
    
    results = []
    for task_name, task_func in tasks:
        try:
            result = task_func()
            results.append((task_name, result))
        except Exception as e:
            print(f"❌ {task_name} 失败: {e}")
            results.append((task_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 优化结果总结:")
    
    passed = 0
    for task_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {task_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n总体结果: {passed}/{total} 任务完成 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 显示效果优化完成！")
    else:
        print("⚠️ 部分优化任务失败")
    
    # 显示使用说明
    show_usage_instructions()

if __name__ == "__main__":
    main()
