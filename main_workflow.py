# main_workflow.py

import os
import pandas as pd
import shutil
from datetime import datetime, timedelta
import logging
import re
import subprocess
import openpyxl # For direct Excel manipulation
import time
import sys

# --- Configuration --- #
import configparser

# 读取配置文件
config_parser = configparser.ConfigParser()
# 从环境变量获取配置文件路径，如果不存在则使用默认路径
config_path = os.environ.get('CONFIG_PATH', os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.ini'))
logging.info(f"使用配置文件: {config_path}")
config_parser.read(config_path, encoding='utf-8')

# 从配置文件获取路径，如果不存在则使用默认值（动态计算）
script_dir = os.path.dirname(os.path.abspath(__file__))
CONFIG = {
    "iot_folder": config_parser.get('Paths', 'iot_folder', fallback=os.path.join(script_dir, "IOT")),
    "zero_folder": config_parser.get('Paths', 'zero_folder', fallback=os.path.join(script_dir, "ZERO")),
    "processed_folder": config_parser.get('Paths', 'processed_folder', fallback=os.path.join(script_dir, "已处理")),
    "error_folder": config_parser.get('Paths', 'error_folder', fallback=os.path.join(script_dir, "需人工检查")),
    "log_folder": os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs'),
    "database_folder": os.path.join(os.path.dirname(os.path.abspath(__file__)), 'database'),
    "temp_folder": os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp_refund_data'), # For temporary refund files
    "workspace_dir": os.path.dirname(os.path.abspath(__file__)), # For calling report scripts
    "settlement_iot_pattern": config_parser.get('FilePatterns', 'settlement_iot_pattern', fallback=r"SETTLEMENT_REPORT.*_zeroiot\.xlsx$"),
    "settlement_zero_pattern": config_parser.get('FilePatterns', 'settlement_zero_pattern', fallback=r"SETTLEMENT_REPORT.*_zeropowerstatio\.xlsx$"),
    "raw_order_flow_pattern": r"线上订单流水信息\d{8}\.xlsx",
    "processed_second_file_pattern_iot": config_parser.get('FilePatterns', 'china_iot_pattern', fallback=r"\d{6}\s+CHINA\s+IOT\.xlsx$"),
    "processed_second_file_pattern_zero": config_parser.get('FilePatterns', 'china_zero_pattern', fallback=r"\d{6}\s+CHINA\s+ZERO\.xlsx$"),
    "processed_first_file_pattern_iot_single": r"SETTLEMENT_REPORT_\d{8}_IOT\.xlsx",
    "processed_first_file_pattern_zero_single": r"SETTLEMENT_REPORT_\d{8}_ZERO\.xlsx",
    "processed_first_file_pattern_iot_multi": r"SETTLEMENT_REPORT_FROM_\d{2}_TO_\d{8}_IOT\.xlsx",
    "processed_first_file_pattern_zero_multi": r"SETTLEMENT_REPORT_FROM_\d{2}_TO_\d{8}_ZERO\.xlsx",
}

# --- Logging Setup --- #
LOG_FILE = os.path.join(CONFIG["log_folder"], f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(filename=LOG_FILE, level=logging.INFO,                    format='%(asctime)s - %(levelname)s - %(message)s')

# Ensure directories exist
for folder_key in ["iot_folder", "zero_folder", "processed_folder", "error_folder", "log_folder", "database_folder", "temp_folder"]:
    folder_path = CONFIG[folder_key]
    os.makedirs(folder_path, exist_ok=True)
    logging.info(f"Ensured directory exists: {folder_path}")

# --- Helper Functions --- # 
def move_to_error(file_path, reason, associated_file_path=None):
    """Moves a file to the error folder and logs the reason."""
    try:
        if file_path and os.path.exists(file_path):
            error_file_path = os.path.join(CONFIG["error_folder"], os.path.basename(file_path))
            shutil.move(file_path, error_file_path)
            logging.error(f"Moved {file_path} to {error_file_path} due to: {reason}")
        elif file_path:
            logging.error(f"Attempted to move non-existent file {file_path} to error folder. Reason: {reason}")
        
        if associated_file_path and os.path.exists(associated_file_path):
            error_assoc_path = os.path.join(CONFIG["error_folder"], os.path.basename(associated_file_path))
            shutil.move(associated_file_path, error_assoc_path)
            logging.error(f"Moved associated file {associated_file_path} to {error_assoc_path} due to: {reason}")
        elif associated_file_path:
             logging.error(f"Attempted to move non-existent associated file {associated_file_path} to error folder. Reason: {reason}")

    except Exception as e:
        logging.error(f"Failed to move file(s) to error folder: {e}")

def get_file_type_from_cell(file_path):
    try:
        df = pd.read_excel(file_path, sheet_name="Sheet0", header=0, engine='openpyxl')
        copartner_name_value = df.iloc[0, df.columns.get_loc("Copartner name")]
        if copartner_name_value == "ZERO2": return "IOT"
        elif copartner_name_value == "ZERO": return "ZERO"
        else:
            logging.warning(f"Unknown Copartner name: {copartner_name_value} in file: {file_path}")
            return "UNKNOWN"
    except Exception as e:
        logging.error(f"Error reading file type from {file_path} (Sheet0, Copartner name): {e}")
        return "ERROR"

def get_order_date_from_cell(file_path):
    try:
        df = pd.read_excel(file_path, sheet_name="Sheet0", header=0, engine='openpyxl')
        order_time_value = df.iloc[0, df.columns.get_loc("Order time")]
        if isinstance(order_time_value, datetime):
            return order_time_value.strftime("%Y-%m-%d")
        elif isinstance(order_time_value, str):
            return datetime.strptime(order_time_value.split()[0], "%Y-%m-%d").strftime("%Y-%m-%d")
        else:
            logging.warning(f"Unexpected Order time format in {file_path}: {order_time_value}")
            return None
    except Exception as e:
        logging.error(f"Error reading order date from {file_path} (Sheet0, Order time): {e}")
        return None

# --- File Processing Functions --- #
def process_second_file(file_path, expected_folder_type):
    logging.info(f"Processing second file: {file_path} for type {expected_folder_type}")
    actual_file_type = get_file_type_from_cell(file_path)
    
    if actual_file_type == "ERROR":
        move_to_error(file_path, "Error getting file type from cell.")
        return None
    if actual_file_type == "UNKNOWN":
        move_to_error(file_path, f"Unknown file type based on Copartner name.")
        return None
    if actual_file_type != expected_folder_type:
        move_to_error(file_path, f"File type mismatch: Expected {expected_folder_type}, found {actual_file_type}.")
        return None

    order_date_str = get_order_date_from_cell(file_path)
    if not order_date_str:
        move_to_error(file_path, "Could not determine order date from cell.")
        return None

    try:
        order_date_obj = datetime.strptime(order_date_str, "%Y-%m-%d")
        new_file_name = f"{order_date_obj.strftime('%d%m%y')} CHINA {actual_file_type}.xlsx"
        new_file_path = os.path.join(os.path.dirname(file_path), new_file_name)
        
        if os.path.exists(new_file_path) and file_path != new_file_path:
            logging.warning(f"Target file {new_file_path} already exists. Moving original to error folder.")
            move_to_error(file_path, f"Renamed file {new_file_name} already exists.")
            return None
        
        os.rename(file_path, new_file_path)
        logging.info(f"Renamed {file_path} to {new_file_path}")
        return {"path": new_file_path, "category": "second_file", "status": "preprocessed", "file_type": actual_file_type, "date_obj": order_date_obj, "date_str_dmy": order_date_obj.strftime('%d%m%y'), "date_str_ymd": order_date_obj.strftime('%Y-%m-%d')}
    except Exception as e:
        logging.error(f"Error renaming or processing second file {file_path}: {e}")
        move_to_error(file_path, f"Exception during renaming/processing: {e}")
        return None

def process_first_file(file_path, file_type_from_folder):
    logging.info(f"Processing first file: {file_path} as type {file_type_from_folder}")
    try:
        xls = pd.ExcelFile(file_path, engine='openpyxl')
        if "TRANSACTION_LIST" not in xls.sheet_names:
            move_to_error(file_path, "Sheet 'TRANSACTION_LIST' not found.")
            return None
        
        df = pd.read_excel(xls, sheet_name="TRANSACTION_LIST", header=0)
        if "Date" not in df.columns:
            move_to_error(file_path, "Column 'Date' not found in 'TRANSACTION_LIST'.")
            return None

        df["Date_dt"] = pd.to_datetime(df["Date"], errors='coerce')
        df.dropna(subset=["Date_dt"], inplace=True)
        unique_dates = sorted(df["Date_dt"].dt.date.unique())

        if not unique_dates:
            move_to_error(file_path, "No valid dates found in 'Date' column.")
            return None

        original_dir = os.path.dirname(file_path)
        
        if len(unique_dates) == 1:
            report_date = unique_dates[0]
            new_file_name = f"SETTLEMENT_REPORT_{report_date.strftime('%d%m%Y')}_{file_type_from_folder}.xlsx"
            script_to_use = "report 脚本 3.0.py"
            new_file_path = os.path.join(original_dir, new_file_name)
            
            if os.path.exists(new_file_path) and file_path != new_file_path:
                logging.warning(f"Target file {new_file_path} already exists. Moving original to error folder.")
                move_to_error(file_path, f"Renamed file {new_file_name} already exists.")
                return None
            if file_path != new_file_path: os.rename(file_path, new_file_path)
            logging.info(f"Processed single-date first file: {new_file_path}, uses {script_to_use}")
            return {"path": new_file_path, "category": "first_file", "status": "preprocessed", "file_type": file_type_from_folder, "dates": [report_date], "script": script_to_use, "date_str_dmy_list": [d.strftime('%d%m%y') for d in [report_date]]}
        else: 
            start_date, end_date = unique_dates[0], unique_dates[-1]
            new_file_name = f"SETTLEMENT_REPORT_FROM_{start_date.strftime('%d')}_TO_{end_date.strftime('%d%m%Y')}_{file_type_from_folder}.xlsx"
            script_to_use = "report 三天报告 3.0.py"
            new_file_path = os.path.join(original_dir, new_file_name)

            if os.path.exists(new_file_path) and file_path != new_file_path:
                logging.warning(f"Target file {new_file_path} for multi-date report already exists. Moving original to error folder.")
                move_to_error(file_path, f"Renamed multi-date file {new_file_name} already exists.")
                return None
            
            temp_file_path = file_path + ".tmp"
            with pd.ExcelWriter(temp_file_path, engine='openpyxl') as writer:
                processed_sheets_data = {}
                for report_date_obj in unique_dates:
                    df_date_specific = df[df["Date_dt"].dt.date == report_date_obj].copy()
                    df_date_specific.insert(df_date_specific.columns.get_loc("Date") + 1, "Time", df_date_specific["Date_dt"].dt.strftime('%H:%M:%S'))
                    sheet_name = report_date_obj.strftime("%d%m%y")
                    processed_sheets_data[sheet_name] = df_date_specific
                
                first_date_sheet_name = unique_dates[0].strftime("%d%m%y")
                processed_sheets_data[first_date_sheet_name].to_excel(writer, sheet_name=first_date_sheet_name, index=False)
                logging.info(f"Wrote sheet {first_date_sheet_name} to {temp_file_path}")

                for report_date_obj in unique_dates[1:]:
                    sheet_name = report_date_obj.strftime("%d%m%y")
                    processed_sheets_data[sheet_name].to_excel(writer, sheet_name=sheet_name, index=False)
                    logging.info(f"Wrote sheet {sheet_name} to {temp_file_path}")
            
            if file_path != new_file_path:
                if os.path.exists(new_file_path): os.remove(new_file_path)
                os.rename(temp_file_path, new_file_path)
                if os.path.exists(file_path): os.remove(file_path)
            else:
                os.remove(file_path)
                os.rename(temp_file_path, new_file_path)

            logging.info(f"Processed multi-date first file: {new_file_path}, uses {script_to_use}")
            return {"path": new_file_path, "category": "first_file", "status": "preprocessed", "file_type": file_type_from_folder, "dates": unique_dates, "script": script_to_use, "date_str_dmy_list": [d.strftime('%d%m%y') for d in unique_dates]}

    except Exception as e:
        logging.error(f"Error processing first file {file_path}: {e}")
        move_to_error(file_path, f"Exception during processing: {e}")
        return None

def run_report_script(script_name, first_file_path, second_file_path):
    logging.info(f"Running script: {script_name} with F1={first_file_path}, F2={second_file_path}")
    script_full_path = os.path.join(CONFIG["workspace_dir"], script_name)
    try:
        process = subprocess.run(["python", script_full_path, first_file_path, second_file_path],
                                 capture_output=True, text=True, check=True, timeout=300)
        logging.info(f"Script {script_name} STDOUT: {process.stdout}")
        if process.stderr:
            logging.warning(f"Script {script_name} STDERR: {process.stderr}")
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"Script {script_name} failed with error: {e.stderr}")
        return False
    except subprocess.TimeoutExpired:
        logging.error(f"Script {script_name} timed out.")
        return False
    except Exception as e:
        logging.error(f"Error running script {script_name}: {e}")
        return False

def validate_amounts(first_file_info, second_file_info):
    logging.info(f"Validating amounts between {first_file_info['path']} and {second_file_info['path']}")
    logging.warning("Amount validation is a placeholder and currently always returns True.")
    return True 

def process_second_file_deep(second_file_info):
    file_path = second_file_info["path"]
    file_type = second_file_info["file_type"]
    date_dmy = second_file_info["date_str_dmy"]
    logging.info(f"Deep processing second file: {file_path}")
    try:
        xls = pd.ExcelFile(file_path, engine='openpyxl')
        if "DATA" not in xls.sheet_names:
            logging.error(f"Sheet 'DATA' not found in {file_path} after report script processing.")
            move_to_error(file_path, "Sheet DATA not found post-script.")
            return False

        df_data = pd.read_excel(xls, sheet_name="DATA")
        df_data_filtered = df_data[df_data["Order status"] == "Finish"].copy()
        logging.info(f"Filtered DATA sheet by 'Order status' == 'Finish'. Rows before: {len(df_data)}, after: {len(df_data_filtered)}")

        columns_to_keep = [
            "Copartner name", "Order No.", "Order types", "Order status", "Order price", 
            "Payment", "Order time", "Equipment ID", "Equipment name", "Branch name", 
            "Payment date", "User name", "Time", "Matched Order ID", "OrderTime_dt"
        ]
        actual_columns_to_keep = [col for col in columns_to_keep if col in df_data_filtered.columns]
        if len(actual_columns_to_keep) != len(columns_to_keep):
            missing_cols = set(columns_to_keep) - set(actual_columns_to_keep)
            logging.warning(f"Missing expected columns in DATA sheet of {file_path}: {missing_cols}")
        
        df_data_final = df_data_filtered[actual_columns_to_keep]
        logging.info(f"Selected {len(actual_columns_to_keep)} columns for DATA sheet.")

        new_sheet_name = f"{file_type}{date_dmy}"
        sheets_to_write = {new_sheet_name: df_data_final}
        if "LOG" in xls.sheet_names:
            df_log = pd.read_excel(xls, sheet_name="LOG")
            sheets_to_write["LOG"] = df_log
        
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            for sheet_name_to_write, df_sheet in sheets_to_write.items():
                df_sheet.to_excel(writer, sheet_name=sheet_name_to_write, index=False)
        logging.info(f"DATA sheet processed and renamed to {new_sheet_name} in {file_path}. LOG sheet preserved.")
        return True

    except Exception as e:
        logging.error(f"Error during deep processing of {file_path}: {e}")
        move_to_error(file_path, f"Deep processing failed: {e}")
        return False

def format_and_copy_log_summary(second_file_info):
    file_path = second_file_info["path"]
    file_type = second_file_info["file_type"]
    summary_header_date_str = second_file_info["date_str_ymd"] 
    logging.info(f"Formatting LOG summary for {file_path}")

    try:
        workbook = openpyxl.load_workbook(file_path)
        if "LOG" not in workbook.sheetnames:
            logging.warning(f"Sheet 'LOG' not found in {file_path} for summary formatting.")
            return True 

        sheet = workbook["LOG"]
        start_col_idx = sheet.max_column + 2 
        original_log_lines = []
        for row in sheet.iter_rows(values_only=True):
            if row[0] is not None: 
                original_log_lines.append(str(row[0]))
        
        if not original_log_lines:
            logging.warning(f"No log lines found in LOG sheet of {file_path}")
            return True
            
        # 提取并格式化日志信息
        formatted_summary = f"{summary_header_date_str} {file_type}\n"
        razer_amount = None
        first_file_amounts = []
        
        for line in original_log_lines:
            if line.startswith("From") and "RAZER" in line:
                parts = line.split("RAZER : ")
                if len(parts) > 1:
                    razer_amount = parts[1].strip()
                    formatted_summary += f"RAZER : {razer_amount}\n"
            elif "(First file)" in line and line.startswith("RM"):
                amount = line.split(" (First file)")[0].strip()
                first_file_amounts.append(amount)
                formatted_summary += f"{amount}\n"
        
        # 写入格式化的摘要
        start_row = 1
        sheet.cell(row=start_row, column=start_col_idx, value="格式化摘要")
        for i, line in enumerate(formatted_summary.split("\n")):
            if line:
                sheet.cell(row=start_row + i + 1, column=start_col_idx, value=line)
        
        workbook.save(file_path)
        logging.info(f"LOG summary formatted and added to {file_path}")
        return True
        
    except Exception as e:
        logging.error(f"Error formatting LOG summary for {file_path}: {e}")
        return False  # 不移动到错误文件夹，因为这只是一个格式化功能

# --- 主函数 --- #
def main(test_mode=False, db_handler=None):
    logging.info("开始自动处理系统")
    print("开始自动处理系统...")
    
    # 检查是否使用测试数据库
    if test_mode and db_handler:
        logging.info("使用测试模式和测试数据库")
    
    # 处理的文件信息列表
    processed_files = []
    
    # 检查IOT文件夹
    iot_folder = CONFIG["iot_folder"]
    logging.info(f"检查IOT文件夹: {iot_folder}")
    if os.path.exists(iot_folder):
        iot_files = [f for f in os.listdir(iot_folder) if os.path.isfile(os.path.join(iot_folder, f))]
        for file in iot_files:
            file_path = os.path.join(iot_folder, file)
            logging.info(f"处理IOT文件: {file}")
            
            # 识别文件类型
            if re.search(CONFIG["raw_order_flow_pattern"], file):
                logging.info(f"识别为订单流水文件: {file}")
                file_info = process_second_file(file_path, "IOT")
                if file_info:
                    processed_files.append(file_info)
                    # 如果使用测试数据库，记录处理信息
                    if test_mode and db_handler:
                        db_handler.log_processing(file_path, "process_second_file", f"处理IOT订单流水文件: {file}")
            elif re.search(CONFIG["settlement_iot_pattern"], file):
                logging.info(f"识别为结算报告文件: {file}")
                file_info = process_first_file(file_path, "IOT")
                if file_info:
                    processed_files.append(file_info)
                    # 如果使用测试数据库，记录处理信息
                    if test_mode and db_handler:
                        db_handler.log_processing(file_path, "process_first_file", f"处理IOT结算报告文件: {file}")
            else:
                logging.warning(f"未识别的文件类型: {file}")
    else:
        logging.error(f"IOT文件夹不存在: {iot_folder}")
    
    # 检查ZERO文件夹
    zero_folder = CONFIG["zero_folder"]
    logging.info(f"检查ZERO文件夹: {zero_folder}")
    if os.path.exists(zero_folder):
        zero_files = [f for f in os.listdir(zero_folder) if os.path.isfile(os.path.join(zero_folder, f))]
        for file in zero_files:
            file_path = os.path.join(zero_folder, file)
            logging.info(f"处理ZERO文件: {file}")
            
            # 识别文件类型
            if re.search(CONFIG["raw_order_flow_pattern"], file):
                logging.info(f"识别为订单流水文件: {file}")
                file_info = process_second_file(file_path, "ZERO")
                if file_info:
                    processed_files.append(file_info)
                    # 如果使用测试数据库，记录处理信息
                    if test_mode and db_handler:
                        db_handler.log_processing(file_path, "process_second_file", f"处理ZERO订单流水文件: {file}")
            elif re.search(CONFIG["settlement_zero_pattern"], file):
                logging.info(f"识别为结算报告文件: {file}")
                file_info = process_first_file(file_path, "ZERO")
                if file_info:
                    processed_files.append(file_info)
                    # 如果使用测试数据库，记录处理信息
                    if test_mode and db_handler:
                        db_handler.log_processing(file_path, "process_first_file", f"处理ZERO结算报告文件: {file}")
            else:
                logging.warning(f"未识别的文件类型: {file}")
    else:
        logging.error(f"ZERO文件夹不存在: {zero_folder}")
    
    # 处理匹配的文件对
    if processed_files:
        logging.info(f"找到 {len(processed_files)} 个已处理文件")
        # 按日期和类型匹配第一类和第二类文件
        match_and_process_files(processed_files, test_mode, db_handler)
    
    logging.info("自动处理系统完成")
    print("自动处理系统完成")
    return processed_files

# 匹配并处理文件对
def match_and_process_files(processed_files, test_mode=False, db_handler=None):
    # 分类文件
    first_files = [f for f in processed_files if f["category"] == "first_file"]
    second_files = [f for f in processed_files if f["category"] == "second_file"]
    
    # 按日期和类型匹配
    for first_file in first_files:
        file_type = first_file["file_type"]
        date_list = first_file.get("date_str_dmy_list", [])
        
        for date_str in date_list:
            # 查找匹配的第二类文件
            matching_second_files = [f for f in second_files 
                                    if f["file_type"] == file_type and 
                                    f["date_str_dmy"] == date_str]
            
            if matching_second_files:
                second_file = matching_second_files[0]  # 取第一个匹配的文件
                logging.info(f"找到匹配的文件对: {first_file['path']} 和 {second_file['path']}")
                
                # 验证金额
                if validate_amounts(first_file, second_file):
                    # 运行报告脚本
                    script_success = run_report_script(first_file["script"], first_file["path"], second_file["path"])
                    
                    if script_success:
                        # 深度处理第二类文件
                        if process_second_file_deep(second_file):
                            # 格式化日志摘要
                            format_and_copy_log_summary(second_file)
                            
                            # 如果使用测试数据库，导入处理后的数据
                            if test_mode and db_handler:
                                try:
                                    # 获取处理后的sheet名称
                                    sheet_name = f"{second_file['file_type']}{second_file['date_str_dmy']}"
                                    db_handler.insert_orders_from_excel(second_file["path"], sheet_name, second_file["file_type"])
                                    db_handler.log_processing(second_file["path"], "database_import", 
                                                            f"导入{second_file['file_type']}数据到测试数据库")
                                except Exception as e:
                                    logging.error(f"导入数据到测试数据库时出错: {e}")
                    else:
                        logging.error(f"运行报告脚本失败: {first_file['script']}")
                        move_to_error(first_file["path"], "报告脚本运行失败", second_file["path"])
                else:
                    logging.error(f"金额验证失败: {first_file['path']} 和 {second_file['path']}")
                    move_to_error(first_file["path"], "金额验证失败", second_file["path"])
            else:
                logging.warning(f"未找到与 {first_file['path']} ({date_str}) 匹配的第二类文件")

# 如果直接运行此脚本，则执行主函数
if __name__ == "__main__":
    main()