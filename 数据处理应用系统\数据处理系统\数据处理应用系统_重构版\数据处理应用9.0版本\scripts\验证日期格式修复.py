#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证日期格式修复 - 测试Order_time只保留日期部分的修复
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_standardize_date_method():
    """测试standardize_date方法"""
    print("🔧 1. 测试standardize_date方法")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试各种日期格式
        test_cases = [
            ("完整日期时间", "2025-07-07 00:00:00", "2025-07-07"),
            ("完整日期时间2", "2025-07-07 12:30:45", "2025-07-07"),
            ("只有日期", "2025-07-07", "2025-07-07"),
            ("斜杠分隔", "2025/07/07", "2025-07-07"),
            ("点分隔", "2025.07.07", "2025-07-07"),
            ("带T的ISO格式", "2025-07-07T12:30:45", "2025-07-07"),
            ("空值", None, None),
            ("空字符串", "", None),
        ]
        
        passed = 0
        total = len(test_cases)
        
        for case_name, input_date, expected_output in test_cases:
            try:
                result = processor.standardize_date(input_date)
                if result == expected_output:
                    print(f"✅ {case_name}: '{input_date}' → '{result}'")
                    passed += 1
                else:
                    print(f"❌ {case_name}: '{input_date}' → '{result}' (期望: '{expected_output}')")
            except Exception as e:
                print(f"❌ {case_name}: '{input_date}' → 错误: {e}")
        
        print(f"\n📊 standardize_date测试结果: {passed}/{total} 通过")
        return passed == total
        
    except Exception as e:
        print(f"❌ standardize_date方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_duplicate_detection_date_format():
    """测试重复检测中的日期格式"""
    print("\n🔧 2. 测试重复检测中的日期格式")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据：新数据包含完整时间，现有数据只有日期
        new_data = pd.DataFrame({
            'Transaction_Num': ['TXN001', 'TXN002'],
            'Order_time': ['2025-07-07 00:00:00', '2025-07-08 12:30:45'],  # 包含时间
            'Order_price': [100.0, 200.0],
            'Equipment_ID': ['EQ001', 'EQ002']
        })
        
        existing_data = pd.DataFrame({
            'Transaction_Num': ['TXN001', 'TXN003'],
            'Order_time': ['2025-07-07', '2025-07-09'],  # 只有日期
            'Order_price': [100.0, 300.0],
            'Equipment_ID': ['EQ001', 'EQ003']
        })
        
        print(f"📊 新数据: {len(new_data)} 条记录")
        print(f"📊 现有数据: {len(existing_data)} 条记录")
        print("📋 新数据Order_time格式: 包含时间")
        print("📋 现有数据Order_time格式: 只有日期")
        
        # 执行重复检测
        try:
            result = processor._detect_by_transaction_num(new_data, existing_data)
            fully_dup, partial_dup, new_records = result
            
            print(f"\n✅ 重复检测成功:")
            print(f"  完全重复: {len(fully_dup)} 条")
            print(f"  部分重复: {len(partial_dup)} 条")
            print(f"  新记录: {len(new_records)} 条")
            
            # 验证TXN001应该被识别为重复
            if len(fully_dup) > 0 or len(partial_dup) > 0:
                print("✅ 正确识别了重复记录（TXN001应该匹配）")
                return True
            else:
                print("⚠️ 没有识别到重复记录，可能仍有格式问题")
                return False
                
        except Exception as e:
            print(f"❌ 重复检测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 重复检测日期格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_traditional_detection_date_format():
    """测试传统检测中的日期格式"""
    print("\n🔧 3. 测试传统检测中的日期格式")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        new_data = pd.DataFrame({
            'Order_time': ['2025-07-07 00:00:00', '2025-07-08 12:30:45'],
            'Equipment_ID': ['EQ001', 'EQ002'],
            'Payment': [100.0, 200.0]
        })
        
        existing_data = pd.DataFrame({
            'Order_time': ['2025-07-07', '2025-07-09'],
            'Equipment_ID': ['EQ001', 'EQ003'],
            'Payment': [100.0, 300.0]
        })
        
        print(f"📊 新数据: {len(new_data)} 条记录")
        print(f"📊 现有数据: {len(existing_data)} 条记录")
        
        # 执行传统检测
        try:
            result = processor._detect_by_traditional_method(new_data, existing_data)
            fully_dup, partial_dup, new_records = result
            
            print(f"\n✅ 传统检测成功:")
            print(f"  完全重复: {len(fully_dup)} 条")
            print(f"  部分重复: {len(partial_dup)} 条")
            print(f"  新记录: {len(new_records)} 条")
            
            # 验证应该识别重复
            if len(fully_dup) > 0 or len(partial_dup) > 0:
                print("✅ 正确识别了重复记录")
                return True
            else:
                print("⚠️ 没有识别到重复记录")
                return False
                
        except Exception as e:
            print(f"❌ 传统检测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 传统检测日期格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_processing_pipeline():
    """测试完整的数据处理流程"""
    print("\n🔧 4. 测试完整的数据处理流程")
    print("-" * 60)
    
    try:
        # 设置非交互模式
        os.environ['NON_INTERACTIVE'] = '1'
        os.environ['AUTO_DUPLICATE_HANDLING'] = 'overwrite'
        os.environ['AUTO_MISSING_HANDLING'] = 'ignore'
        
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建包含时间的测试数据
        test_data = pd.DataFrame({
            'Copartner name': ['Partner1', 'Partner2'],
            'Order No.': ['ORD001', 'ORD002'],
            'Transaction Num': ['TXN001', 'TXN002'],
            'Order types': ['普通', '普通'],
            'Order price': [100.0, 200.0],
            'Payment': [100.0, 200.0],
            'Order time': ['2025-07-07 00:00:00', '2025-07-08 12:30:45'],  # 包含时间
            'Equipment ID': ['EQ001', 'EQ002'],
            'Equipment name': ['Device1', 'Device2'],
            'Branch name': ['Branch1', 'Branch2'],
            'Payment date': ['2025-07-07', '2025-07-08'],
            'User name': ['User1', 'User2'],
            'Time': ['10:00:00', '11:00:00'],
            'Matched Order ID': ['', ''],
            'OrderTime_dt': ['2025-07-07 10:00:00', '2025-07-08 11:00:00']
        })
        
        print(f"📊 测试数据: {len(test_data)} 条记录")
        print("📋 Order time格式: 包含完整时间")
        
        # 测试数据处理流程
        try:
            # 数据清洗
            cleaned_data = processor._clean_data(test_data.copy())
            print(f"✅ 数据清洗成功: {len(cleaned_data)} 条记录")
            
            # 数据标准化
            standardized_data = processor._standardize_data_types(cleaned_data.copy())
            print(f"✅ 数据标准化成功: {len(standardized_data)} 条记录")
            
            # 检查Order_time格式
            if 'Order_time' in standardized_data.columns:
                sample_order_time = standardized_data['Order_time'].iloc[0]
                print(f"📋 标准化后的Order_time格式: '{sample_order_time}'")
                
                # 验证格式是否为YYYY-MM-DD
                if len(str(sample_order_time)) == 10 and str(sample_order_time).count('-') == 2:
                    print("✅ Order_time格式正确：只包含日期部分")
                    return True
                else:
                    print(f"❌ Order_time格式错误：仍包含时间部分")
                    return False
            else:
                print("❌ Order_time列不存在")
                return False
                
        except Exception as e:
            print(f"❌ 数据处理流程失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Order_time日期格式修复验证")
    print("=" * 80)
    
    tests = [
        ("standardize_date方法", test_standardize_date_method),
        ("重复检测日期格式", test_duplicate_detection_date_format),
        ("传统检测日期格式", test_traditional_detection_date_format),
        ("完整数据处理流程", test_data_processing_pipeline)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 日期格式修复验证结果")
    print("=" * 80)
    
    print(f"📊 通过测试: {passed}/{total}")
    success_rate = (passed / total) * 100
    print(f"📊 成功率: {success_rate:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过")
        print("✅ Order_time日期格式修复成功")
        print("✅ 只保留日期部分，去除时间信息")
        print("✅ 新旧数据格式统一")
        print("✅ 重复检测正常工作")
        print("\n💡 修复内容:")
        print("  - standardize_date方法只返回日期部分")
        print("  - 重复检测使用统一的日期格式")
        print("  - 传统检测使用统一的日期格式")
        print("  - 数据处理流程保持一致性")
    elif passed >= total * 0.75:
        print("✅ 大部分测试通过")
        print("⚠️ 少量问题可能仍存在")
    else:
        print("❌ 多个测试失败")
        print("🔧 日期格式问题可能仍然存在")
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 修复验证{'成功' if success else '需要改进'}")
    input("按回车键退出...")
