# -*- coding: utf-8 -*-
"""
输入验证模块
提供文件路径、数据格式、配置等验证功能
"""

import os
import re
from pathlib import Path
from typing import List, Tuple, Optional, Union, Any
import pandas as pd

from .exceptions import ValidationError, FileValidationError
from .config_manager import config_manager


class InputValidator:
    """输入验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.config = config_manager.get_section('files')
        self.allowed_extensions = self.config.get('allowed_extensions', ['.xlsx', '.xls', '.csv'])
        self.max_file_size = self.config.get('max_file_size', 104857600)  # 100MB
    
    def validate_file_path(self, file_path: str, check_exists: bool = True) -> Tuple[bool, Optional[str]]:
        """
        验证文件路径
        
        Args:
            file_path: 文件路径
            check_exists: 是否检查文件存在性
            
        Returns:
            (是否有效, 错误消息)
        """
        if not file_path:
            return False, "文件路径不能为空"
        
        # 检查路径遍历攻击
        if '..' in file_path or file_path.startswith('/'):
            return False, "文件路径包含非法字符"
        
        # 转换为Path对象
        try:
            path = Path(file_path)
        except Exception as e:
            return False, f"无效的文件路径: {e}"
        
        # 检查文件扩展名
        if path.suffix.lower() not in self.allowed_extensions:
            return False, f"不支持的文件类型: {path.suffix}。支持的类型: {', '.join(self.allowed_extensions)}"
        
        # 检查文件是否存在
        if check_exists and not path.exists():
            return False, f"文件不存在: {file_path}"
        
        # 检查文件大小
        if check_exists and path.exists():
            try:
                file_size = path.stat().st_size
                if file_size > self.max_file_size:
                    max_size_mb = self.max_file_size / 1024 / 1024
                    actual_size_mb = file_size / 1024 / 1024
                    return False, f"文件过大: {actual_size_mb:.1f}MB，最大允许: {max_size_mb:.1f}MB"
            except Exception as e:
                return False, f"无法获取文件大小: {e}"
        
        return True, None
    
    def validate_file_paths(self, file_paths: List[str], check_exists: bool = True) -> Tuple[bool, List[str]]:
        """
        验证多个文件路径
        
        Args:
            file_paths: 文件路径列表
            check_exists: 是否检查文件存在性
            
        Returns:
            (是否全部有效, 错误消息列表)
        """
        if not file_paths:
            return False, ["文件路径列表不能为空"]
        
        errors = []
        for file_path in file_paths:
            is_valid, error_msg = self.validate_file_path(file_path, check_exists)
            if not is_valid:
                errors.append(f"{file_path}: {error_msg}")
        
        return len(errors) == 0, errors
    
    def validate_excel_file(self, file_path: str, required_sheets: List[str] = None) -> Tuple[bool, Optional[str]]:
        """
        验证Excel文件格式
        
        Args:
            file_path: Excel文件路径
            required_sheets: 必需的工作表名称列表
            
        Returns:
            (是否有效, 错误消息)
        """
        # 首先验证文件路径
        is_valid, error_msg = self.validate_file_path(file_path)
        if not is_valid:
            return False, error_msg
        
        try:
            # 尝试读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            # 检查必需的工作表
            if required_sheets:
                missing_sheets = set(required_sheets) - set(sheet_names)
                if missing_sheets:
                    return False, f"缺少必需的工作表: {', '.join(missing_sheets)}"
            
            # 检查是否有工作表
            if not sheet_names:
                return False, "Excel文件中没有工作表"
            
            return True, None
            
        except Exception as e:
            return False, f"无法读取Excel文件: {e}"
    
    def validate_dataframe_columns(self, df: pd.DataFrame, required_columns: List[str], 
                                 file_name: str = "") -> Tuple[bool, Optional[str]]:
        """
        验证DataFrame列
        
        Args:
            df: 要验证的DataFrame
            required_columns: 必需的列名列表
            file_name: 文件名（用于错误消息）
            
        Returns:
            (是否有效, 错误消息)
        """
        if df.empty:
            return False, f"数据为空{f': {file_name}' if file_name else ''}"
        
        # 检查必需的列
        missing_columns = set(required_columns) - set(df.columns)
        if missing_columns:
            return False, f"缺少必需的列{f' ({file_name})' if file_name else ''}: {', '.join(missing_columns)}"
        
        return True, None
    
    def validate_transaction_data(self, df: pd.DataFrame, file_name: str = "") -> Tuple[bool, List[str]]:
        """
        验证交易数据格式
        
        Args:
            df: 交易数据DataFrame
            file_name: 文件名
            
        Returns:
            (是否有效, 错误消息列表)
        """
        errors = []
        
        # 检查基本列存在性（使用用户实际的列名）
        required_columns = ['Order_price', 'Order_time']  # 使用用户实际有的列
        is_valid, error_msg = self.validate_dataframe_columns(df, required_columns, file_name)
        if not is_valid:
            errors.append(error_msg)
            return False, errors

        # 检查Transaction Num格式（如果存在）
        if 'Transaction_Num' in df.columns:
            invalid_transaction_nums = df[df['Transaction_Num'].isna() | (df['Transaction_Num'] == '')].index
            if len(invalid_transaction_nums) > 0:
                errors.append(f"Found {len(invalid_transaction_nums)} records with empty Transaction_Num")
        
        # 检查金额格式
        if 'Order_price' in df.columns:
            try:
                # 尝试转换为数值
                pd.to_numeric(df['Order_price'], errors='coerce')
            except Exception:
                errors.append("Order price column contains invalid numeric format")

        # 检查Order No格式（如果存在）
        if 'Order_No' in df.columns:
            invalid_order_nos = df[df['Order_No'].isna() | (df['Order_No'] == '')].index
            if len(invalid_order_nos) > 0:
                errors.append(f"Found {len(invalid_order_nos)} records with empty Order_No")
        
        return len(errors) == 0, errors
    
    def sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除危险字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # 移除危险字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # 限制长度
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        return filename
    
    def validate_config_value(self, section: str, key: str, value: Any, 
                            expected_type: type = None, min_value: Union[int, float] = None,
                            max_value: Union[int, float] = None) -> Tuple[bool, Optional[str]]:
        """
        验证配置值
        
        Args:
            section: 配置节
            key: 配置键
            value: 配置值
            expected_type: 期望的数据类型
            min_value: 最小值（用于数值类型）
            max_value: 最大值（用于数值类型）
            
        Returns:
            (是否有效, 错误消息)
        """
        # 检查类型
        if expected_type and not isinstance(value, expected_type):
            return False, f"配置项 {section}.{key} 类型错误，期望 {expected_type.__name__}，实际 {type(value).__name__}"
        
        # 检查数值范围
        if isinstance(value, (int, float)):
            if min_value is not None and value < min_value:
                return False, f"配置项 {section}.{key} 值过小，最小值: {min_value}，实际值: {value}"
            if max_value is not None and value > max_value:
                return False, f"配置项 {section}.{key} 值过大，最大值: {max_value}，实际值: {value}"
        
        # 检查字符串长度
        if isinstance(value, str):
            if not value.strip():
                return False, f"配置项 {section}.{key} 不能为空字符串"
        
        return True, None
    
    def validate_database_path(self, db_path: str) -> Tuple[bool, Optional[str]]:
        """
        验证数据库路径
        
        Args:
            db_path: 数据库路径
            
        Returns:
            (是否有效, 错误消息)
        """
        if not db_path:
            return False, "数据库路径不能为空"
        
        try:
            path = Path(db_path)
            
            # 检查目录是否存在，如果不存在尝试创建
            if not path.parent.exists():
                try:
                    path.parent.mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    return False, f"无法创建数据库目录: {e}"
            
            # 检查文件扩展名
            if path.suffix.lower() != '.db':
                return False, "数据库文件必须是.db格式"
            
            return True, None
            
        except Exception as e:
            return False, f"无效的数据库路径: {e}"


# 全局验证器实例
input_validator = InputValidator()
