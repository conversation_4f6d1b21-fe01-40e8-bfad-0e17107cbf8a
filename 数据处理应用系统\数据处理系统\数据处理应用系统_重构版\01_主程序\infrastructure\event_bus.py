# -*- coding: utf-8 -*-
"""
事件总线系统 - 架构优化步骤1
实现松耦合的事件驱动通信，解决循环依赖问题

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import threading
import time
import queue
from typing import Any, Callable, Dict, List, Optional, Set
from collections import defaultdict
from dataclasses import dataclass, field
from enum import Enum
import traceback
import weakref


class EventPriority(Enum):
    """事件优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Event:
    """事件数据结构"""
    type: str
    data: Any
    timestamp: float = field(default_factory=time.time)
    priority: EventPriority = EventPriority.NORMAL
    source: Optional[str] = None
    correlation_id: Optional[str] = None
    
    def __post_init__(self):
        if self.correlation_id is None:
            self.correlation_id = f"{self.type}_{int(self.timestamp * 1000000)}"


@dataclass
class EventSubscription:
    """事件订阅信息"""
    handler: Callable[[Event], None]
    priority: EventPriority
    filter_func: Optional[Callable[[Event], bool]]
    once: bool  # 是否只执行一次
    weak_ref: bool  # 是否使用弱引用
    subscription_time: float = field(default_factory=time.time)


class EventBus:
    """
    事件总线 - 松耦合通信核心
    
    功能：
    - 事件发布和订阅
    - 异步事件处理
    - 事件过滤和优先级
    - 弱引用支持（防止内存泄漏）
    - 性能监控
    """
    
    def __init__(self, max_queue_size: int = 1000, worker_threads: int = 2):
        self._subscribers: Dict[str, List[EventSubscription]] = defaultdict(list)
        self._event_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self._worker_threads: List[threading.Thread] = []
        self._running = False
        self._lock = threading.RLock()
        
        # 性能统计
        self._stats = {
            "events_published": 0,
            "events_processed": 0,
            "events_failed": 0,
            "subscribers_count": 0,
            "average_processing_time": 0.0,
            "total_processing_time": 0.0
        }
        
        # 事件历史（用于调试）
        self._event_history: List[Event] = []
        self._max_history_size = 100
        
        # 启动工作线程
        self._start_workers(worker_threads)
        
    def subscribe(self, 
                 event_type: str, 
                 handler: Callable[[Event], None],
                 priority: EventPriority = EventPriority.NORMAL,
                 filter_func: Optional[Callable[[Event], bool]] = None,
                 once: bool = False,
                 weak_ref: bool = False) -> str:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
            priority: 处理优先级
            filter_func: 事件过滤函数
            once: 是否只处理一次
            weak_ref: 是否使用弱引用
            
        Returns:
            str: 订阅ID，用于取消订阅
        """
        with self._lock:
            # 创建订阅信息
            subscription = EventSubscription(
                handler=handler,
                priority=priority,
                filter_func=filter_func,
                once=once,
                weak_ref=weak_ref
            )
            
            # 如果使用弱引用，包装处理函数
            if weak_ref and hasattr(handler, '__self__'):
                obj_ref = weakref.ref(handler.__self__)
                method_name = handler.__name__
                
                def weak_handler(event: Event):
                    obj = obj_ref()
                    if obj is not None:
                        getattr(obj, method_name)(event)
                    else:
                        # 对象已被回收，自动取消订阅
                        self._remove_dead_subscriptions(event_type)
                        
                subscription.handler = weak_handler
                
            self._subscribers[event_type].append(subscription)
            self._stats["subscribers_count"] += 1
            
            # 按优先级排序
            self._subscribers[event_type].sort(
                key=lambda s: s.priority.value, 
                reverse=True
            )
            
            # 返回订阅ID
            subscription_id = f"{event_type}_{id(subscription)}"
            return subscription_id
            
    def unsubscribe(self, event_type: str, subscription_id: str) -> bool:
        """
        取消订阅
        
        Args:
            event_type: 事件类型
            subscription_id: 订阅ID
            
        Returns:
            bool: 是否成功取消
        """
        with self._lock:
            if event_type not in self._subscribers:
                return False
                
            # 查找并移除订阅
            for i, subscription in enumerate(self._subscribers[event_type]):
                if f"{event_type}_{id(subscription)}" == subscription_id:
                    del self._subscribers[event_type][i]
                    self._stats["subscribers_count"] -= 1
                    return True
                    
            return False
            
    def publish(self, 
               event_type: str, 
               data: Any = None,
               priority: EventPriority = EventPriority.NORMAL,
               source: Optional[str] = None,
               sync: bool = False) -> Optional[Event]:
        """
        发布事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            priority: 事件优先级
            source: 事件源
            sync: 是否同步处理
            
        Returns:
            Optional[Event]: 如果同步处理，返回事件对象
        """
        event = Event(
            type=event_type,
            data=data,
            priority=priority,
            source=source
        )
        
        with self._lock:
            self._stats["events_published"] += 1
            
            # 添加到历史记录
            self._add_to_history(event)
            
            if sync:
                # 同步处理
                self._process_event(event)
                return event
            else:
                # 异步处理
                try:
                    # 使用负优先级值，因为PriorityQueue是最小堆
                    priority_value = -priority.value
                    self._event_queue.put((priority_value, time.time(), event), timeout=1)
                except queue.Full:
                    print(f"⚠️ Event queue is full, dropping event: {event_type}")
                    
        return event
        
    def publish_and_wait(self, 
                        event_type: str, 
                        data: Any = None,
                        timeout: float = 5.0) -> bool:
        """
        发布事件并等待处理完成
        
        Args:
            event_type: 事件类型
            data: 事件数据
            timeout: 超时时间
            
        Returns:
            bool: 是否处理成功
        """
        # 创建完成事件
        completion_event = threading.Event()
        result = {"success": False, "error": None}
        
        def completion_handler(event: Event):
            result["success"] = True
            completion_event.set()
            
        def error_handler(event: Event):
            result["error"] = event.data
            completion_event.set()
            
        # 订阅完成和错误事件
        completion_type = f"{event_type}_completed"
        error_type = f"{event_type}_error"
        
        self.subscribe(completion_type, completion_handler, once=True)
        self.subscribe(error_type, error_handler, once=True)
        
        # 发布原始事件
        self.publish(event_type, data)
        
        # 等待完成
        if completion_event.wait(timeout):
            return result["success"]
        else:
            print(f"⚠️ Event processing timeout: {event_type}")
            return False
            
    def get_subscribers_count(self, event_type: Optional[str] = None) -> int:
        """获取订阅者数量"""
        with self._lock:
            if event_type:
                return len(self._subscribers.get(event_type, []))
            else:
                return sum(len(subs) for subs in self._subscribers.values())
                
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            stats = self._stats.copy()
            stats["queue_size"] = self._event_queue.qsize()
            stats["event_types"] = list(self._subscribers.keys())
            return stats
            
    def get_event_history(self, limit: int = 50) -> List[Event]:
        """获取事件历史"""
        with self._lock:
            return self._event_history[-limit:].copy()
            
    def clear_history(self):
        """清空事件历史"""
        with self._lock:
            self._event_history.clear()
            
    def shutdown(self, timeout: float = 5.0):
        """关闭事件总线"""
        print("🔄 [EVENT] 关闭事件总线...")

        self._running = False

        # 等待工作线程结束
        for thread in self._worker_threads:
            if thread.is_alive():
                thread.join(timeout)
                if thread.is_alive():
                    print(f"⚠️ [EVENT] 工作线程 {thread.name} 未能在 {timeout} 秒内停止")

        # 清空队列和订阅
        try:
            while not self._event_queue.empty():
                self._event_queue.get_nowait()
        except:
            pass

        with self._lock:
            self._subscribers.clear()
            self._event_history.clear()

        self._worker_threads.clear()

        print("✅ [EVENT] 事件总线已关闭")
            
    def _start_workers(self, worker_count: int):
        """启动工作线程"""
        self._running = True
        
        for i in range(worker_count):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"EventBus-Worker-{i}",
                daemon=True
            )
            worker.start()
            self._worker_threads.append(worker)
            
    def _worker_loop(self):
        """工作线程主循环"""
        while self._running:
            try:
                # 获取事件（带超时）
                priority, timestamp, event = self._event_queue.get(timeout=1)
                
                # 处理事件
                self._process_event(event)
                
                # 标记任务完成
                self._event_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Event worker error: {e}")
                traceback.print_exc()
                
    def _process_event(self, event: Event):
        """处理单个事件"""
        start_time = time.time()
        
        try:
            with self._lock:
                subscribers = self._subscribers.get(event.type, []).copy()
                
            processed_count = 0
            
            for subscription in subscribers:
                try:
                    # 应用过滤器
                    if subscription.filter_func and not subscription.filter_func(event):
                        continue
                        
                    # 调用处理函数
                    subscription.handler(event)
                    processed_count += 1
                    
                    # 如果是一次性订阅，移除它
                    if subscription.once:
                        with self._lock:
                            if subscription in self._subscribers[event.type]:
                                self._subscribers[event.type].remove(subscription)
                                self._stats["subscribers_count"] -= 1
                                
                except Exception as e:
                    print(f"❌ Event handler error for {event.type}: {e}")
                    self._stats["events_failed"] += 1
                    
            # 更新统计信息
            processing_time = time.time() - start_time
            with self._lock:
                self._stats["events_processed"] += 1
                self._stats["total_processing_time"] += processing_time
                self._stats["average_processing_time"] = (
                    self._stats["total_processing_time"] / self._stats["events_processed"]
                )
                
        except Exception as e:
            print(f"❌ Event processing error: {e}")
            self._stats["events_failed"] += 1
            
    def _add_to_history(self, event: Event):
        """添加事件到历史记录"""
        self._event_history.append(event)
        
        # 保持历史记录大小
        if len(self._event_history) > self._max_history_size:
            self._event_history.pop(0)
            
    def _remove_dead_subscriptions(self, event_type: str):
        """移除失效的弱引用订阅"""
        with self._lock:
            if event_type in self._subscribers:
                # 这里可以添加清理逻辑
                pass


# 全局事件总线实例
_global_event_bus: Optional[EventBus] = None


def get_global_event_bus() -> EventBus:
    """获取全局事件总线实例"""
    global _global_event_bus
    if _global_event_bus is None:
        _global_event_bus = EventBus()
    return _global_event_bus


def set_global_event_bus(event_bus: EventBus):
    """设置全局事件总线实例"""
    global _global_event_bus
    _global_event_bus = event_bus
