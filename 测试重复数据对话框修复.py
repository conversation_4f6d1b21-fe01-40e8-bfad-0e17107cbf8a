#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重复数据对话框修复效果
"""

import tkinter as tk
from tkinter import ttk
import pandas as pd

def test_duplicate_dialog():
    """测试修复后的重复数据对话框"""
    
    # 模拟重复数据
    fully_duplicate = pd.DataFrame({
        'Transaction_Num': ['T001', 'T002'],
        'Order_No': ['O001', 'O002'],
        'Order_price': [100, 200]
    })
    
    partial_different = pd.DataFrame({
        'Transaction_Num': ['T003'],
        'Order_No': ['O003'],
        'Order_price': [300]
    })
    
    platform = "IOT"
    
    # 创建自定义对话框类
    class DuplicateDataDialog:
        def __init__(self):
            self.result = None
            self.root = tk.Tk()
            self.setup_dialog()
        
        def setup_dialog(self):
            # 窗口基本设置
            self.root.title("🔍 检测到重复数据")
            self.root.geometry("600x500")
            self.root.resizable(False, False)
            
            # 居中显示
            self.center_window()
            
            # 设置为置顶
            self.root.attributes('-topmost', True)
            self.root.focus_force()
            
            # 创建主框架
            main_frame = ttk.Frame(self.root, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 标题
            title_label = ttk.Label(
                main_frame, 
                text="🔍 检测到重复数据", 
                font=("Arial", 14, "bold")
            )
            title_label.pack(pady=(0, 15))
            
            # 详细信息显示
            info_text = f"""平台: {platform}
完全重复记录: {len(fully_duplicate)} 条
部分重复记录: {len(partial_different)} 条

请选择处理方式："""
            
            info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
            info_label.pack(pady=(0, 15))
            
            # 选项框架
            options_frame = ttk.LabelFrame(main_frame, text="处理选项", padding="10")
            options_frame.pack(fill=tk.X, pady=(0, 15))
            
            # 选项变量
            self.choice_var = tk.StringVar(value="skip")
            
            # 选项按钮
            options = [
                ("overwrite", "📝 覆盖更新 - 用新数据覆盖现有重复数据"),
                ("incremental", "🔧 增量更新 - 只更新不同的字段"),
                ("skip", "🔄 跳过重复 - 只导入新数据，跳过重复数据"),
                ("refresh_daily", "🗑️ 重新更新 - 删除当天数据后重新导入"),
                ("cancel", "❌ 取消导入 - 停止导入操作")
            ]
            
            for value, text in options:
                rb = ttk.Radiobutton(
                    options_frame,
                    text=text,
                    variable=self.choice_var,
                    value=value
                )
                rb.pack(anchor=tk.W, pady=2)
            
            # 按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(15, 0))
            
            # 确认按钮
            confirm_btn = ttk.Button(
                button_frame,
                text="✅ 确认",
                command=self.confirm_choice
            )
            confirm_btn.pack(side=tk.RIGHT, padx=(5, 0))
            
            # 取消按钮
            cancel_btn = ttk.Button(
                button_frame,
                text="❌ 取消",
                command=self.cancel_choice
            )
            cancel_btn.pack(side=tk.RIGHT)
            
            # 绑定键盘事件
            self.root.bind('<Return>', lambda e: self.confirm_choice())
            self.root.bind('<Escape>', lambda e: self.cancel_choice())
            
            # 设置默认焦点
            confirm_btn.focus_set()
        
        def center_window(self):
            self.root.update_idletasks()
            width = 600
            height = 500
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f"{width}x{height}+{x}+{y}")
        
        def confirm_choice(self):
            self.result = self.choice_var.get()
            self.root.destroy()
        
        def cancel_choice(self):
            self.result = "cancel"
            self.root.destroy()
        
        def show(self):
            self.root.mainloop()
            return self.result
    
    # 显示对话框
    print("🧪 测试重复数据对话框...")
    dialog = DuplicateDataDialog()
    choice = dialog.show()
    
    print(f"✅ 用户选择: {choice}")
    return choice

def main():
    """主函数"""
    print("🔧 重复数据对话框修复测试")
    print("="*50)
    
    print("\n📋 修复内容:")
    print("• 替换 simpledialog.askstring() 为自定义对话框")
    print("• 添加明确的确认和取消按钮")
    print("• 改善用户界面布局和体验")
    print("• 支持键盘快捷键操作")
    
    print("\n🧪 开始测试...")
    
    try:
        result = test_duplicate_dialog()
        
        print(f"\n✅ 测试成功！")
        print(f"📊 测试结果:")
        print(f"   用户选择: {result}")
        
        # 解释选择
        choice_explanations = {
            "overwrite": "覆盖更新 - 用新数据覆盖现有重复数据",
            "incremental": "增量更新 - 只更新不同的字段",
            "skip": "跳过重复 - 只导入新数据，跳过重复数据",
            "refresh_daily": "重新更新 - 删除当天数据后重新导入",
            "cancel": "取消导入 - 停止导入操作"
        }
        
        if result in choice_explanations:
            print(f"   说明: {choice_explanations[result]}")
        
        print(f"\n💡 修复效果:")
        print(f"• ✅ 对话框有明确的确认和取消按钮")
        print(f"• ✅ 用户界面清晰易懂")
        print(f"• ✅ 支持键盘操作（Enter确认，Escape取消）")
        print(f"• ✅ 窗口居中显示，置顶显示")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎉 重复数据对话框修复成功！")
        print(f"💡 现在数据导入时会显示带有明确按钮的对话框")
    else:
        print(f"\n⚠️ 测试未通过，请检查tkinter环境")
