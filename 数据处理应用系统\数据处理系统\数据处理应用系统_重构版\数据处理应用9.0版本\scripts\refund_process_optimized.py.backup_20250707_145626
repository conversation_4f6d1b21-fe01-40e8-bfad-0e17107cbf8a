# -*- coding: utf-8 -*-
"""
优化版退款处理脚本
使用重构后的基础模块，提供更好的性能、错误处理和日志记录
"""

import os
import sys
import argparse
import pandas as pd
import sqlite3
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# 添加父目录到路径，以便导入重构的模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger
from utils.exceptions import *
from utils.validators import input_validator
from database.connection_pool import get_connection, reinitialize_connection_pool


class RefundProcessor:
    """优化版退款处理器"""
    
    def __init__(self, db_path: Optional[str] = None):
        """
        初始化退款处理器
        
        Args:
            db_path: 数据库路径，如果为None则使用配置中的路径
        """
        self.logger = get_logger('refund_process')

        # 设置数据库路径
        if db_path:
            self.db_path = db_path
        else:
            # 从配置管理器获取数据库路径
            try:
                from utils.config_manager import config_manager
                self.db_path = config_manager.get_db_path()
            except ImportError:
                # 如果配置管理器不可用，使用默认路径
                self.db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "database", "sales_reports.db")

        self.batch_size = 1000
        
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        # 确保连接池使用正确的数据库路径
        reinitialize_connection_pool(self.db_path)

        self.logger.info(f"退款处理器已初始化，数据库路径: {self.db_path}")
    
    def validate_file(self, file_path: str) -> bool:
        """
        🔧 修复：验证文件 - 智能识别退款工作表

        Args:
            file_path: 文件路径

        Returns:
            是否验证通过
        """
        is_valid, error_msg = input_validator.validate_file_path(file_path)
        if not is_valid:
            raise FileValidationError(file_path, error_msg)

        # 🔧 修复：智能检测退款工作表，不再硬编码要求REFUND_LIST
        refund_sheet = self._detect_refund_sheet(file_path)
        if not refund_sheet:
            raise FileValidationError(file_path, "未找到包含退款数据的工作表")

        self.logger.info(f"检测到退款工作表: {refund_sheet}")
        return True

    def _detect_refund_sheet(self, file_path: str) -> str:
        """
        🔧 修复：智能检测退款工作表

        Args:
            file_path: Excel文件路径

        Returns:
            退款工作表名称，如果找不到则返回None
        """
        try:
            # 获取所有工作表名称
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names

            self.logger.info(f"文件包含的工作表: {sheet_names}")

            # 退款工作表的可能名称（按优先级排序）
            refund_keywords = [
                'REFUND_LIST',      # 标准名称
                'REFUND LIST',      # 带空格
                'REFUNDLIST',       # 无分隔符
                'REFUND',           # 简化名称
                'SETTLEMENT_REPORT', # 结算报告
                'SETTLEMENT REPORT', # 带空格的结算报告
                'SETTLEMENT',       # 简化结算
                'REPORT',           # 报告
                'LIST',             # 列表
                'DATA',             # 数据
                'SHEET1',           # 默认工作表
                'SHEET 1'           # 带空格的默认工作表
            ]

            # 按优先级查找匹配的工作表
            for keyword in refund_keywords:
                for sheet_name in sheet_names:
                    if keyword.upper() in sheet_name.upper():
                        self.logger.info(f"找到匹配的退款工作表: {sheet_name} (匹配关键词: {keyword})")
                        return sheet_name

            # 如果没有找到匹配的关键词，使用第一个工作表
            if sheet_names:
                default_sheet = sheet_names[0]
                self.logger.warning(f"未找到明确的退款工作表，使用第一个工作表: {default_sheet}")
                return default_sheet

            # 如果没有工作表，返回None
            self.logger.error("文件中没有找到任何工作表")
            return None

        except Exception as e:
            self.logger.error(f"检测退款工作表时出错: {e}")
            return None
    
    def load_refund_data(self, file_path: str) -> pd.DataFrame:
        """
        加载退款数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            退款数据DataFrame
        """
        try:
            # 🔧 修复：使用智能检测的退款工作表
            refund_sheet = self._detect_refund_sheet(file_path)
            if not refund_sheet:
                raise DataProcessingError("未找到退款工作表", file_path=file_path, stage="退款数据加载")

            # 读取检测到的退款工作表
            df = pd.read_excel(file_path, sheet_name=refund_sheet, engine='openpyxl')
            self.logger.info(f"成功读取退款文件: {file_path}, 工作表: {refund_sheet}, 数据行数: {len(df)}")

            # 🔧 修复：使用正确的退款文件列名检查
            required_columns = ['Transaction Date', 'Order ID', 'Refund']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                self.logger.error(f"❌ 缺少必需的列: {missing_columns}")
                self.logger.error(f"📋 可用列: {list(df.columns)}")
                raise DataProcessingError(f"缺少必需的列: {missing_columns}", file_path=file_path, stage="退款数据验证")

            self.logger.info(f"✅ 找到所有必需列: {required_columns}")

            # 数据清洗
            df = self._clean_data(df)

            self.logger.info(f"退款数据验证和清洗完成，有效数据行数: {len(df)}")
            return df
            
        except Exception as e:
            if isinstance(e, (DataProcessingError, FileValidationError)):
                raise
            raise DataProcessingError(f"加载退款数据失败: {e}", file_path=file_path, stage="数据加载")

    # 🔧 修复：删除复杂的列名检测函数，使用简单直接的列名检查
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗数据
        
        Args:
            df: 原始DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        # 删除完全空白的行
        df = df.dropna(how='all')
        
        # 填充空值
        df = df.fillna('')
        
        # 清理字符串列的空白字符
        for col in df.select_dtypes(include=['object']).columns:
            df[col] = df[col].astype(str).str.strip()
        
        # 🔧 修复：移除Order ID为空的行
        df = df[df['Order ID'] != '']
        
        return df
    
    def find_matching_records(self, refund_df: pd.DataFrame, platform: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        查找匹配的销售记录
        
        Args:
            refund_df: 退款数据DataFrame
            platform: 平台类型
            
        Returns:
            (匹配的记录, 未匹配的记录)
        """
        try:
            with get_connection() as conn:
                table_name = f"{platform}_Sales"
                
                # 查询销售数据
                try:
                    sales_df = pd.read_sql(
                        f"SELECT * FROM {table_name}",
                        conn.connection
                    )
                except Exception:
                    self.logger.warning(f"表 {table_name} 不存在或为空")
                    return pd.DataFrame(), refund_df
                
                if sales_df.empty:
                    self.logger.warning(f"销售表 {table_name} 为空")
                    return pd.DataFrame(), refund_df
                
                # 🔧 修复：基于Order ID匹配（退款文件只有Order ID，没有Equipment_ID）
                matched_records = []
                unmatched_records = []

                for _, refund_row in refund_df.iterrows():
                    order_id = refund_row['Order ID']

                    # 🔧 修复：查找匹配的销售记录（使用Order ID匹配Order_No）
                    matching_sales = sales_df[sales_df['Order_No'] == order_id]

                    if not matching_sales.empty:
                        # 找到匹配记录
                        for _, sales_row in matching_sales.iterrows():
                            matched_record = sales_row.copy()
                            matched_record['Refund_Amount'] = refund_row['Refund']
                            matched_record['Refund_Date'] = datetime.now().strftime("%Y-%m-%d")
                            matched_record['Original_Order_Status'] = sales_row['Order_status']
                            matched_records.append(matched_record)
                    else:
                        # 未找到匹配记录
                        unmatched_records.append(refund_row)
                
                matched_df = pd.DataFrame(matched_records) if matched_records else pd.DataFrame()
                unmatched_df = pd.DataFrame(unmatched_records) if unmatched_records else pd.DataFrame()
                
                self.logger.info(f"记录匹配完成 - 匹配: {len(matched_df)}, 未匹配: {len(unmatched_df)}")
                return matched_df, unmatched_df
                
        except Exception as e:
            raise DatabaseError(f"查找匹配记录失败: {e}", operation="记录匹配", table=f"{platform}_Sales")
    
    def verify_update_operation(self, cursor, order_no: str, equipment_id: str, platform: str) -> bool:
        """
        验证更新操作是否成功（使用同一个事务的cursor）

        Args:
            cursor: 数据库游标（同一事务）
            order_no: 订单号
            equipment_id: 设备ID
            platform: 平台类型

        Returns:
            验证是否成功
        """
        try:
            table_name = f"{platform}_Sales"

            # 验证状态是否真的变成了Refund（在同一事务中查询）
            cursor.execute(f"""
                SELECT Order_status, Order_types FROM {table_name}
                WHERE Order_No = ? AND Equipment_ID = ?
            """, (order_no, equipment_id))

            result = cursor.fetchone()
            if result:
                status, types = result
                if status == 'Refund' and types == 'Refund':
                    self.logger.debug(f"✅ 验证成功: {order_no} 状态已更新为Refund")
                    return True
                else:
                    self.logger.error(f"❌ 验证失败: {order_no} 状态={status}, 类型={types}")
                    return False
            else:
                self.logger.error(f"❌ 验证失败: 找不到记录 {order_no}")
                return False

        except Exception as e:
            self.logger.error(f"验证操作失败: {e}")
            return False

    def update_order_status(self, matched_df: pd.DataFrame, platform: str) -> Dict[str, int]:
        """
        更新订单状态为退款（带验证）

        Args:
            matched_df: 匹配的记录DataFrame
            platform: 平台类型

        Returns:
            更新结果字典
        """
        result = {
            'attempted': 0,
            'successful': 0,
            'failed': 0,
            'verification_failed': 0
        }

        if matched_df.empty:
            return result

        try:
            with get_connection() as conn:
                table_name = f"{platform}_Sales"
                cursor = conn.connection.cursor()

                # 开始事务
                conn.connection.execute("BEGIN")

                # 🔧 修复：删除不需要的验证结果变量

                try:
                    for _, row in matched_df.iterrows():
                        result['attempted'] += 1
                        order_id = row['Order_No']  # 🔧 修复：使用正确的列名（销售数据中是Order_No）

                        # 🔧 修复：简化更新逻辑，只使用Order_ID
                        cursor.execute(f"""
                            UPDATE {table_name}
                            SET Order_status = 'Refund',
                                Order_types = 'Refund'
                            WHERE Order_No = ?
                        """, (order_id,))

                        if cursor.rowcount > 0:
                            result['successful'] += 1
                            self.logger.info(f"✅ 成功更新: {order_id}")
                        else:
                            result['failed'] += 1
                            self.logger.error(f"❌ 更新失败: {order_id}")

                    # 🔧 修复：简化提交逻辑
                    if result['failed'] == 0:
                        conn.connection.commit()
                        self.logger.info(f"✅ 所有更新已提交: 成功{result['successful']}条")
                    else:
                        conn.connection.rollback()
                        self.logger.error(f"❌ 存在失败操作，已回滚所有更改")
                        # 🔧 修复：简化错误记录
                        result['successful'] = 0  # 回滚后没有成功的更新

                except Exception as e:
                    conn.connection.rollback()
                    self.logger.error(f"❌ 更新过程中出错，已回滚: {e}")
                    result['successful'] = 0
                    raise

                return result

        except Exception as e:
            raise DatabaseError(f"更新订单状态失败: {e}", operation="状态更新", table=f"{platform}_Sales")
    
    def create_refund_log(self, matched_df: pd.DataFrame, unmatched_df: pd.DataFrame, 
                         file_path: str, platform: str) -> str:
        """
        创建退款处理日志
        
        Args:
            matched_df: 匹配的记录
            unmatched_df: 未匹配的记录
            file_path: 处理的文件路径
            platform: 平台类型
            
        Returns:
            日志文件路径
        """
        try:
            # 创建日志目录
            log_dir = Path(self.db_path).parent / "refund_logs"
            log_dir.mkdir(exist_ok=True)
            
            # 生成日志文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = log_dir / f"refund_log_{platform}_{timestamp}.xlsx"
            
            # 创建Excel写入器
            with pd.ExcelWriter(log_file, engine='openpyxl') as writer:
                # 写入匹配的记录
                if not matched_df.empty:
                    matched_df.to_excel(writer, sheet_name='已处理退款', index=False)
                
                # 写入未匹配的记录
                if not unmatched_df.empty:
                    unmatched_df.to_excel(writer, sheet_name='未找到匹配记录', index=False)
                
                # 写入处理摘要
                summary_data = {
                    '处理时间': [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                    '源文件': [os.path.basename(file_path)],
                    '平台': [platform],
                    '总退款记录': [len(matched_df) + len(unmatched_df)],
                    '成功处理': [len(matched_df)],
                    '未找到匹配': [len(unmatched_df)]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='处理摘要', index=False)
            
            self.logger.info(f"退款处理日志已创建: {log_file}")
            return str(log_file)
            
        except Exception as e:
            self.logger.error(f"创建退款日志失败: {e}")
            return ""
    
    def process_file(self, file_path: str, platform: str) -> Dict[str, Any]:
        """
        处理退款文件
        
        Args:
            file_path: 文件路径
            platform: 平台类型
            
        Returns:
            处理结果字典
        """
        result = {
            'success': False,
            'file_path': file_path,
            'platform': platform,
            'total_refunds': 0,
            'processed_refunds': 0,
            'unmatched_refunds': 0,
            'updated_records': 0,
            'log_file': '',
            'errors': []
        }
        
        try:
            self.logger.info(f"开始处理退款文件: {file_path}, 平台: {platform}")

            # 🔧 修复：添加UI进度条支持
            # 步骤1: 验证文件 (15%)
            print("进度: 15%")
            self.validate_file(file_path)

            # 步骤2: 加载退款数据 (35%)
            print("进度: 35%")
            refund_df = self.load_refund_data(file_path)
            result['total_refunds'] = len(refund_df)

            # 步骤3: 查找匹配记录 (65%)
            print("进度: 65%")
            matched_df, unmatched_df = self.find_matching_records(refund_df, platform)
            result['processed_refunds'] = len(matched_df)
            result['unmatched_refunds'] = len(unmatched_df)

            # 步骤4: 更新订单状态 (90%)
            if not matched_df.empty:
                print("进度: 90%")
                update_result = self.update_order_status(matched_df, platform)
                result['updated_records'] = update_result['successful']
                result['attempted_updates'] = update_result['attempted']
                result['failed_updates'] = update_result['failed']
                result['verification_failed'] = update_result['verification_failed']
            
            # 步骤5: 创建处理日志 (100%)
            print("进度: 95%")
            log_file = self.create_refund_log(matched_df, unmatched_df, file_path, platform)
            result['log_file'] = log_file

            print("进度: 100%")
            result['success'] = True
            self.logger.info(f"退款文件处理完成: {file_path}")
            
        except Exception as e:
            error_msg = str(e)
            result['errors'].append(error_msg)
            self.logger.error(f"处理退款文件失败: {file_path}, 错误: {error_msg}")
        
        return result


def main():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description='优化版退款处理脚本')
    parser.add_argument('--file', required=True, help='要处理的退款文件路径')
    parser.add_argument('--platform', required=True, choices=['IOT', 'ZERO', 'APP'], help='平台类型')
    parser.add_argument('--db_path', help='数据库路径')
    
    args = parser.parse_args()
    
    try:
        # 创建处理器
        processor = RefundProcessor(args.db_path)
        
        # 处理文件
        result = processor.process_file(args.file, args.platform)
        
        # 输出结果
        if result['success']:
            print(f"✅ 退款处理成功")
            print(f"📁 文件: {os.path.basename(args.file)}")
            print(f"🏷️ 平台: {args.platform}")
            print(f"📊 总退款记录: {result['total_refunds']}")
            print(f"✅ 成功处理: {result['processed_refunds']}")
            print(f"❌ 未找到匹配: {result['unmatched_refunds']}")
            print(f"💾 更新记录数: {result['updated_records']}")

            # 显示详细的验证结果
            if 'attempted_updates' in result:
                print(f"🔍 验证详情:")
                print(f"  • 尝试更新: {result['attempted_updates']} 条")
                print(f"  • 成功验证: {result['updated_records']} 条")
                if result.get('failed_updates', 0) > 0:
                    print(f"  • 更新失败: {result['failed_updates']} 条")
                if result.get('verification_failed', 0) > 0:
                    print(f"  • 验证失败: {result['verification_failed']} 条")

            if result['log_file']:
                print(f"📋 日志文件: {os.path.basename(result['log_file'])}")
            return 0
        else:
            print(f"❌ 退款处理失败")
            for error in result['errors']:
                print(f"错误: {error}")
            return 1
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
