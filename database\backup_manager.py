#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备份管理器
管理数据库备份和恢复操作
"""

import os
import shutil
import logging
import sqlite3
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class BackupManager:
    """备份管理器"""
    
    def __init__(self, db_path=None):
        self.db_path = db_path
        if db_path:
            self.backup_dir = os.path.join(os.path.dirname(db_path), "backups")
        else:
            self.backup_dir = None
    
    def set_database_path(self, db_path):
        """设置数据库路径"""
        self.db_path = db_path
        self.backup_dir = os.path.join(os.path.dirname(db_path), "backups")
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def create_backup(self, reason="manual"):
        """创建数据库备份"""
        if not self.db_path or not os.path.exists(self.db_path):
            logger.error("数据库文件不存在，无法创建备份")
            return None
        
        try:
            os.makedirs(self.backup_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"sales_reports_backup_{timestamp}.db"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            # 创建备份
            shutil.copy2(self.db_path, backup_path)
            
            logger.info(f"备份创建成功: {backup_path} (原因: {reason})")
            return backup_path
            
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return None
    
    def list_backups(self):
        """列出所有备份文件"""
        if not self.backup_dir or not os.path.exists(self.backup_dir):
            return []
        
        try:
            backup_files = []
            for file in os.listdir(self.backup_dir):
                if file.endswith('.db'):
                    file_path = os.path.join(self.backup_dir, file)
                    stat = os.stat(file_path)
                    backup_files.append({
                        'filename': file,
                        'path': file_path,
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_ctime),
                        'modified': datetime.fromtimestamp(stat.st_mtime)
                    })
            
            # 按创建时间排序
            backup_files.sort(key=lambda x: x['created'], reverse=True)
            return backup_files
            
        except Exception as e:
            logger.error(f"列出备份文件失败: {e}")
            return []
    
    def restore_backup(self, backup_path):
        """恢复备份"""
        if not os.path.exists(backup_path):
            logger.error(f"备份文件不存在: {backup_path}")
            return False
        
        if not self.db_path:
            logger.error("未设置数据库路径")
            return False
        
        try:
            # 验证备份文件
            if not self._validate_backup(backup_path):
                logger.error("备份文件验证失败")
                return False
            
            # 创建当前数据库的备份
            current_backup = self.create_backup("restore_safety")
            
            # 恢复备份
            shutil.copy2(backup_path, self.db_path)
            
            logger.info(f"备份恢复成功: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"恢复备份失败: {e}")
            return False
    
    def _validate_backup(self, backup_path):
        """验证备份文件的完整性"""
        try:
            conn = sqlite3.connect(backup_path)
            cursor = conn.cursor()
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()
            conn.close()
            
            return result[0] == 'ok'
            
        except Exception as e:
            logger.error(f"验证备份文件失败: {e}")
            return False
    
    def cleanup_old_backups(self, keep_days=30, keep_count=10):
        """清理旧备份文件"""
        if not self.backup_dir or not os.path.exists(self.backup_dir):
            return
        
        try:
            backups = self.list_backups()
            
            # 按时间清理
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            old_backups = [b for b in backups if b['created'] < cutoff_date]
            
            # 按数量清理
            if len(backups) > keep_count:
                excess_backups = backups[keep_count:]
                old_backups.extend(excess_backups)
            
            # 去重
            old_backups = list({b['path']: b for b in old_backups}.values())
            
            for backup in old_backups:
                try:
                    os.remove(backup['path'])
                    logger.info(f"删除旧备份: {backup['filename']}")
                except Exception as e:
                    logger.error(f"删除备份文件失败 {backup['filename']}: {e}")
                    
        except Exception as e:
            logger.error(f"清理旧备份失败: {e}")

# 全局备份管理器实例
_backup_manager = None

def get_backup_manager():
    """获取备份管理器实例"""
    global _backup_manager
    if _backup_manager is None:
        _backup_manager = BackupManager()
    return _backup_manager

def initialize_backup_manager(db_path):
    """初始化备份管理器"""
    global _backup_manager
    _backup_manager = BackupManager(db_path)
    return _backup_manager
