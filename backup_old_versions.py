#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备份旧版本文件脚本
"""

import os
import shutil
from datetime import datetime

def backup_old_versions():
    """备份旧版本的应用文件"""
    
    # 要备份的文件列表
    files_to_backup = [
        "数据处理与导入应用_改进版.py",
        "数据处理与导入应用 - C.py", 
        "数据处理与导入工具.py",
        "数据导入脚本_完整版.py",
        "启动改进版应用.py",
        "启动数据处理与导入应用.bat"
    ]
    
    backup_dir = "backup_versions"
    
    # 确保备份目录存在
    os.makedirs(backup_dir, exist_ok=True)
    
    print("🔄 开始备份旧版本文件...")
    
    backed_up_count = 0
    for file_name in files_to_backup:
        if os.path.exists(file_name):
            try:
                # 复制文件到备份目录
                dest_path = os.path.join(backup_dir, file_name)
                shutil.copy2(file_name, dest_path)
                print(f"✅ 已备份: {file_name}")
                backed_up_count += 1
            except Exception as e:
                print(f"❌ 备份失败: {file_name} - {e}")
        else:
            print(f"⚠️ 文件不存在: {file_name}")
    
    print(f"\n📊 备份完成！共备份了 {backed_up_count} 个文件")
    
    # 显示备份目录内容
    print(f"\n📁 备份目录内容 ({backup_dir}):")
    if os.path.exists(backup_dir):
        for item in os.listdir(backup_dir):
            item_path = os.path.join(backup_dir, item)
            if os.path.isfile(item_path):
                size = os.path.getsize(item_path)
                print(f"  📄 {item} ({size:,} bytes)")
            else:
                print(f"  📁 {item}/")

if __name__ == "__main__":
    backup_old_versions()
