# -*- coding: utf-8 -*-
"""
数据库安全工具模块
提供数据库操作的安全验证和防护功能
"""

import re
import sqlite3
from typing import List, Set, Optional, Any
from pathlib import Path

class DatabaseSecurityError(Exception):
    """数据库安全相关异常"""
    pass

class SQLSecurityValidator:
    """SQL安全验证器"""
    
    # 允许的表名白名单（基于项目实际表结构）
    ALLOWED_TABLES = {
        'IOT_Sales', 'IOT_Refunding', 'IOT_Close',
        'ZERO_Sales', 'ZERO_Refunding', 'ZERO_Close',
        'APP_Sales', 'APP_Refunding', 'APP_Close',
        'sqlite_master', 'sqlite_sequence'
    }
    
    # 允许的列名模式（基于项目实际列结构）
    ALLOWED_COLUMN_PATTERNS = {
        r'^[a-zA-Z][a-zA-Z0-9_]*$',  # 标准列名模式
        r'^Order[_\s]?[a-zA-Z0-9_]*$',  # Order相关列
        r'^Transaction[_\s]?[a-zA-Z0-9_]*$',  # Transaction相关列
        r'^Equipment[_\s]?[a-zA-Z0-9_]*$',  # Equipment相关列
        r'^Payment[_\s]?[a-zA-Z0-9_]*$',  # Payment相关列
        r'^Copartner[_\s]?[a-zA-Z0-9_]*$',  # Copartner相关列
        r'^Branch[_\s]?[a-zA-Z0-9_]*$',  # Branch相关列
        r'^User[_\s]?[a-zA-Z0-9_]*$',  # User相关列
        r'^Time$', r'^is_deleted$', r'^deleted_at$', r'^delete_reason$'  # 特殊列
    }
    
    # 危险的SQL关键字
    DANGEROUS_KEYWORDS = {
        'DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'EXEC', 'EXECUTE',
        'UNION', 'SCRIPT', 'JAVASCRIPT', 'VBSCRIPT', 'ONLOAD', 'ONERROR'
    }
    
    @classmethod
    def validate_table_name(cls, table_name: str) -> bool:
        """
        验证表名是否安全
        
        Args:
            table_name: 要验证的表名
            
        Returns:
            bool: 表名是否安全
            
        Raises:
            DatabaseSecurityError: 表名不安全时抛出异常
        """
        if not table_name:
            raise DatabaseSecurityError("表名不能为空")
        
        # 检查是否在白名单中
        if table_name not in cls.ALLOWED_TABLES:
            raise DatabaseSecurityError(f"表名 '{table_name}' 不在允许的表名白名单中")
        
        # 检查表名格式
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', table_name):
            raise DatabaseSecurityError(f"表名 '{table_name}' 格式不符合安全要求")
        
        # 检查长度限制
        if len(table_name) > 64:
            raise DatabaseSecurityError(f"表名 '{table_name}' 长度超过限制")
        
        return True
    
    @classmethod
    def validate_column_name(cls, column_name: str) -> bool:
        """
        验证列名是否安全
        
        Args:
            column_name: 要验证的列名
            
        Returns:
            bool: 列名是否安全
            
        Raises:
            DatabaseSecurityError: 列名不安全时抛出异常
        """
        if not column_name:
            raise DatabaseSecurityError("列名不能为空")
        
        # 检查列名格式
        is_valid = any(re.match(pattern, column_name) for pattern in cls.ALLOWED_COLUMN_PATTERNS)
        if not is_valid:
            raise DatabaseSecurityError(f"列名 '{column_name}' 格式不符合安全要求")
        
        # 检查长度限制
        if len(column_name) > 64:
            raise DatabaseSecurityError(f"列名 '{column_name}' 长度超过限制")
        
        return True
    
    @classmethod
    def validate_sql_content(cls, sql_content: str) -> bool:
        """
        验证SQL内容是否包含危险关键字
        
        Args:
            sql_content: 要验证的SQL内容
            
        Returns:
            bool: SQL内容是否安全
            
        Raises:
            DatabaseSecurityError: SQL内容不安全时抛出异常
        """
        if not sql_content:
            return True
        
        # 转换为大写进行检查
        sql_upper = sql_content.upper()
        
        # 检查危险关键字
        for keyword in cls.DANGEROUS_KEYWORDS:
            if keyword in sql_upper:
                # 排除一些合法的使用场景
                if keyword == 'DELETE' and 'DELETE FROM' not in sql_upper:
                    continue
                if keyword == 'CREATE' and ('CREATE TABLE IF NOT EXISTS' in sql_upper or 'CREATE INDEX' in sql_upper):
                    continue
                    
                raise DatabaseSecurityError(f"SQL内容包含危险关键字: {keyword}")
        
        return True
    
    @classmethod
    def sanitize_table_name(cls, table_name: str) -> str:
        """
        安全地处理表名，添加引号保护
        
        Args:
            table_name: 原始表名
            
        Returns:
            str: 安全的表名（带引号）
        """
        # 先验证表名
        cls.validate_table_name(table_name)
        
        # 添加双引号保护
        return f'"{table_name}"'
    
    @classmethod
    def sanitize_column_name(cls, column_name: str) -> str:
        """
        安全地处理列名，添加引号保护
        
        Args:
            column_name: 原始列名
            
        Returns:
            str: 安全的列名（带引号）
        """
        # 先验证列名
        cls.validate_column_name(column_name)
        
        # 添加双引号保护
        return f'"{column_name}"'

class SafeSQLBuilder:
    """安全的SQL构建器"""
    
    def __init__(self):
        self.validator = SQLSecurityValidator()
    
    def build_select_query(self, table_name: str, columns: List[str] = None, 
                          where_conditions: List[str] = None, 
                          order_by: str = None, limit: int = None) -> tuple:
        """
        构建安全的SELECT查询
        
        Args:
            table_name: 表名
            columns: 列名列表
            where_conditions: WHERE条件列表
            order_by: 排序字段
            limit: 限制条数
            
        Returns:
            tuple: (SQL查询字符串, 参数列表)
        """
        # 验证表名
        safe_table = self.validator.sanitize_table_name(table_name)
        
        # 处理列名
        if columns:
            safe_columns = [self.validator.sanitize_column_name(col) for col in columns]
            columns_str = ', '.join(safe_columns)
        else:
            columns_str = '*'
        
        # 构建基础查询
        query = f"SELECT {columns_str} FROM {safe_table}"
        params = []
        
        # 添加WHERE条件
        if where_conditions:
            query += " WHERE " + " AND ".join(where_conditions)
        
        # 添加ORDER BY
        if order_by:
            safe_order_by = self.validator.sanitize_column_name(order_by)
            query += f" ORDER BY {safe_order_by}"
        
        # 添加LIMIT
        if limit:
            query += " LIMIT ?"
            params.append(limit)
        
        return query, params
    
    def build_insert_query(self, table_name: str, columns: List[str]) -> tuple:
        """
        构建安全的INSERT查询
        
        Args:
            table_name: 表名
            columns: 列名列表
            
        Returns:
            tuple: (SQL查询字符串, 占位符数量)
        """
        # 验证表名和列名
        safe_table = self.validator.sanitize_table_name(table_name)
        safe_columns = [self.validator.sanitize_column_name(col) for col in columns]
        
        columns_str = ', '.join(safe_columns)
        placeholders = ', '.join(['?' for _ in columns])
        
        query = f"INSERT INTO {safe_table} ({columns_str}) VALUES ({placeholders})"
        
        return query, len(columns)
    
    def build_update_query(self, table_name: str, set_columns: List[str], 
                          where_conditions: List[str]) -> str:
        """
        构建安全的UPDATE查询
        
        Args:
            table_name: 表名
            set_columns: 要更新的列名列表
            where_conditions: WHERE条件列表
            
        Returns:
            str: SQL查询字符串
        """
        # 验证表名和列名
        safe_table = self.validator.sanitize_table_name(table_name)
        safe_set_columns = [f"{self.validator.sanitize_column_name(col)} = ?" for col in set_columns]
        
        set_str = ', '.join(safe_set_columns)
        where_str = ' AND '.join(where_conditions)
        
        query = f"UPDATE {safe_table} SET {set_str} WHERE {where_str}"
        
        return query
    
    def build_delete_query(self, table_name: str, where_conditions: List[str]) -> str:
        """
        构建安全的DELETE查询
        
        Args:
            table_name: 表名
            where_conditions: WHERE条件列表
            
        Returns:
            str: SQL查询字符串
        """
        # 验证表名
        safe_table = self.validator.sanitize_table_name(table_name)
        where_str = ' AND '.join(where_conditions)
        
        query = f"DELETE FROM {safe_table} WHERE {where_str}"
        
        return query

def get_safe_sql_builder() -> SafeSQLBuilder:
    """获取安全SQL构建器实例"""
    return SafeSQLBuilder()

def validate_database_path(db_path: str) -> bool:
    """
    验证数据库路径是否安全
    
    Args:
        db_path: 数据库文件路径
        
    Returns:
        bool: 路径是否安全
        
    Raises:
        DatabaseSecurityError: 路径不安全时抛出异常
    """
    if not db_path:
        raise DatabaseSecurityError("数据库路径不能为空")
    
    path = Path(db_path)
    
    # 检查路径是否存在父目录遍历攻击
    if '..' in str(path):
        raise DatabaseSecurityError("数据库路径包含不安全的父目录引用")
    
    # 检查文件扩展名
    if path.suffix.lower() not in ['.db', '.sqlite', '.sqlite3']:
        raise DatabaseSecurityError(f"不支持的数据库文件类型: {path.suffix}")
    
    # 检查路径长度
    if len(str(path)) > 260:  # Windows路径长度限制
        raise DatabaseSecurityError("数据库路径长度超过限制")
    
    return True

# 导出主要接口
__all__ = [
    'DatabaseSecurityError',
    'SQLSecurityValidator', 
    'SafeSQLBuilder',
    'get_safe_sql_builder',
    'validate_database_path'
]
