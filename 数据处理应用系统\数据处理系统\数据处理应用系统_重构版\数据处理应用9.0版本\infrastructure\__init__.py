# -*- coding: utf-8 -*-
"""
基础设施模块初始化 - 架构优化步骤1
整合服务容器、事件总线和特性开关

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import os
import sys
import time
from typing import Optional, Dict, Any

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入基础设施组件
from .service_container import ServiceContainer, ServiceContainerBuilder, ServiceLifetime
from .event_bus import EventBus, Event, EventPriority, get_global_event_bus, set_global_event_bus
from .feature_flags import FeatureFlags, FeatureState, get_feature_flags, is_feature_enabled
from .config_service import CachedConfigManager, ConfigServiceFactory, ConfigPreloader, get_config_service, set_config_service
from .logging_service import LoggingService, LoggingServiceFactory, LogLevel, TabType, get_logging_service, set_logging_service, log_message
from .gui_service import GUIUpdateService, GUIServiceFactory, GUIUpdateType, get_gui_service, set_gui_service
from .gui_compatibility import SafeGUIUpdaterCompatible, SafeGUIUpdaterFactory, get_compatible_gui_updater, set_compatible_gui_updater
from .file_service import FileService, FileServiceFactory, FileInfo, FileStatus, FileType, get_file_service, set_file_service
from .backup_service import BackupService, BackupServiceFactory, BackupTask, BackupType, BackupStatus, BackupPriority, BackupPolicy, get_backup_service, set_backup_service


class InfrastructureManager:
    """
    基础设施管理器
    
    负责初始化和管理所有基础设施组件：
    - 服务容器
    - 事件总线
    - 特性开关
    """
    
    def __init__(self):
        self.container: Optional[ServiceContainer] = None
        self.event_bus: Optional[EventBus] = None
        self.feature_flags: Optional[FeatureFlags] = None
        self.initialized = False
        self.initialization_time = 0.0
        
    def initialize(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """
        初始化基础设施
        
        Args:
            config: 配置参数
            
        Returns:
            bool: 是否初始化成功
        """
        start_time = time.time()
        
        try:
            print("🚀 初始化基础设施组件...")
            
            # 1. 初始化特性开关（最先初始化，用于控制其他组件）
            self._init_feature_flags(config)
            
            # 2. 初始化事件总线
            self._init_event_bus(config)
            
            # 3. 初始化服务容器
            self._init_service_container(config)
            
            # 4. 注册核心服务
            self._register_core_services()
            
            self.initialization_time = time.time() - start_time
            self.initialized = True
            
            print(f"✅ 基础设施初始化完成，耗时: {self.initialization_time:.3f}秒")
            
            # 发布初始化完成事件
            if self.event_bus:
                self.event_bus.publish("infrastructure_initialized", {
                    "initialization_time": self.initialization_time,
                    "components": ["service_container", "event_bus", "feature_flags"]
                })
                
            return True
            
        except Exception as e:
            print(f"❌ 基础设施初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    def shutdown(self):
        """关闭基础设施"""
        print("🔄 关闭基础设施组件...")
        
        try:
            # 发布关闭事件
            if self.event_bus:
                self.event_bus.publish("infrastructure_shutdown", {
                    "shutdown_time": time.time()
                })
                
            # 关闭事件总线
            if self.event_bus:
                self.event_bus.shutdown()
                
            # 清理服务容器
            if self.container:
                self.container.clear()
                
            self.initialized = False
            print("✅ 基础设施关闭完成")
            
        except Exception as e:
            print(f"❌ 基础设施关闭失败: {e}")
            
    def get_container(self) -> ServiceContainer:
        """获取服务容器"""
        if not self.initialized or not self.container:
            raise RuntimeError("Infrastructure not initialized")
        return self.container
        
    def get_event_bus(self) -> EventBus:
        """获取事件总线"""
        if not self.initialized or not self.event_bus:
            raise RuntimeError("Infrastructure not initialized")
        return self.event_bus
        
    def get_feature_flags(self) -> FeatureFlags:
        """获取特性开关"""
        if not self.initialized or not self.feature_flags:
            raise RuntimeError("Infrastructure not initialized")
        return self.feature_flags

    def get_compatibility_adapter(self):
        """获取兼容性适配器"""
        if not hasattr(self, '_compatibility_adapter') or self._compatibility_adapter is None:
            self._compatibility_adapter = LegacyCompatibilityAdapter(self)
        return self._compatibility_adapter

    def get_stats(self) -> Dict[str, Any]:
        """获取基础设施统计信息"""
        if not self.initialized:
            return {"initialized": False}
            
        stats = {
            "initialized": True,
            "initialization_time": self.initialization_time,
            "container_stats": self.container.get_performance_stats() if self.container else {},
            "event_bus_stats": self.event_bus.get_stats() if self.event_bus else {},
            "feature_flags_stats": self.feature_flags.get_stats() if self.feature_flags else {}
        }
        
        return stats
        
    def _init_feature_flags(self, config: Optional[Dict[str, Any]]):
        """初始化特性开关"""
        print("  📋 初始化特性开关...")
        
        config_file = "feature_flags.json"
        if config and "feature_flags_config" in config:
            config_file = config["feature_flags_config"]
            
        self.feature_flags = FeatureFlags(config_file)
        
        # 设置全局实例
        import infrastructure.feature_flags as ff_module
        ff_module._global_feature_flags = self.feature_flags
        
        print("  ✅ 特性开关初始化完成")
        
    def _init_event_bus(self, config: Optional[Dict[str, Any]]):
        """初始化事件总线"""
        print("  📡 初始化事件总线...")
        
        # 从配置获取参数
        max_queue_size = 1000
        worker_threads = 2
        
        if config:
            max_queue_size = config.get("event_bus_queue_size", max_queue_size)
            worker_threads = config.get("event_bus_workers", worker_threads)
            
        self.event_bus = EventBus(
            max_queue_size=max_queue_size,
            worker_threads=worker_threads
        )
        
        # 设置全局实例
        set_global_event_bus(self.event_bus)
        
        print("  ✅ 事件总线初始化完成")
        
    def _init_service_container(self, config: Optional[Dict[str, Any]]):
        """初始化服务容器"""
        print("  🏗️ 初始化服务容器...")
        
        self.container = ServiceContainer()
        
        print("  ✅ 服务容器初始化完成")
        
    def _register_core_services(self):
        """注册核心服务"""
        print("  🔧 注册核心服务...")

        # 注册基础设施服务（检查是否已注册）
        if not self.container.has("event_bus"):
            self.container.register(
                "event_bus",
                lambda: self.event_bus,
                ServiceLifetime.SINGLETON
            )

        if not self.container.has("feature_flags"):
            self.container.register(
                "feature_flags",
                lambda: self.feature_flags,
                ServiceLifetime.SINGLETON
            )

        if not self.container.has("infrastructure_manager"):
            self.container.register(
                "infrastructure_manager",
                lambda: self,
                ServiceLifetime.SINGLETON
            )

        # 🔧 步骤2：注册配置服务
        if self.feature_flags.is_enabled("use_cached_config") and not self.container.has("config_manager"):
            print("  📋 注册缓存配置服务...")
            from .config_service import ConfigServiceFactory

            self.container.register(
                "config_manager",
                lambda: ConfigServiceFactory.create_legacy_compatible_service(),
                ServiceLifetime.SINGLETON
            )
        elif not self.container.has("config_manager"):
            print("  📋 配置服务未启用，将使用传统配置管理器")

        # 🔧 步骤3：注册日志和GUI服务
        if self.feature_flags.is_enabled("use_async_logging") and not self.container.has("logging_service"):
            print("  📝 注册异步日志服务...")
            from .logging_service import LoggingServiceFactory

            self.container.register(
                "logging_service",
                lambda: LoggingServiceFactory.create_logging_service(self.event_bus),
                ServiceLifetime.SINGLETON
            )
        elif not self.container.has("logging_service"):
            print("  📝 异步日志服务未启用，将使用传统日志")

        # 🔧 步骤4：注册文件服务
        if self.feature_flags.is_enabled("use_file_service") and not self.container.has("file_manager"):
            print("  📁 注册异步文件服务...")
            from .file_service import FileServiceFactory

            def create_file_service():
                config_service = self.container.get("config_manager") if self.container.has("config_manager") else None
                logging_service = self.container.get("logging_service") if self.container.has("logging_service") else None
                return FileServiceFactory.create_legacy_compatible_service(config_service, logging_service)

            self.container.register(
                "file_manager",
                create_file_service,
                ServiceLifetime.SINGLETON
            )
        elif not self.container.has("file_manager"):
            print("  📁 文件服务未启用，将使用传统文件管理器")

        # 🔧 步骤5：注册备份服务
        if self.feature_flags.is_enabled("use_backup_service") and not self.container.has("backup_manager"):
            print("  💾 注册异步备份服务...")
            from .backup_service import BackupServiceFactory

            def create_backup_service():
                config_service = self.container.get("config_manager") if self.container.has("config_manager") else None
                logging_service = self.container.get("logging_service") if self.container.has("logging_service") else None
                gui_service = self.container.get("gui_service") if self.container.has("gui_service") else None
                return BackupServiceFactory.create_legacy_compatible_service(config_service, logging_service, gui_service)

            self.container.register(
                "backup_manager",
                create_backup_service,
                ServiceLifetime.SINGLETON
            )
        elif not self.container.has("backup_manager"):
            print("  💾 备份服务未启用，将使用传统备份管理器")

        print("  ✅ 核心服务注册完成")

    def shutdown(self):
        """关闭基础设施管理器"""
        print("🔄 [INFRA] 关闭基础设施管理器...")

        try:
            # 关闭所有注册的服务
            if self.container:
                services_to_shutdown = [
                    "backup_manager",
                    "file_manager",
                    "gui_service",
                    "logging_service",
                    "config_manager"
                ]

                for service_name in services_to_shutdown:
                    try:
                        if self.container.has(service_name):
                            service = self.container.get(service_name)
                            if hasattr(service, 'shutdown'):
                                service.shutdown()
                                print(f"  ✅ {service_name} 已关闭")
                    except Exception as e:
                        print(f"  ⚠️ 关闭 {service_name} 时出错: {e}")

            # 关闭事件总线
            if self.event_bus:
                self.event_bus.shutdown()
                self.event_bus = None

            # 重置状态
            self.initialized = False
            self.container = None
            self.feature_flags = None

            print("✅ [INFRA] 基础设施管理器已关闭")

        except Exception as e:
            print(f"❌ [INFRA] 关闭基础设施管理器时出错: {e}")


class LegacyCompatibilityAdapter:
    """
    遗留系统兼容性适配器
    
    确保在架构优化过程中，新旧系统可以平滑过渡
    """
    
    def __init__(self, infrastructure_manager: InfrastructureManager):
        self.infrastructure = infrastructure_manager
        self.feature_flags = infrastructure_manager.get_feature_flags()
        
    def get_config_manager(self):
        """获取配置管理器（兼容旧接口）"""
        # 🔧 步骤2：优先使用缓存配置服务
        if self.feature_flags.is_enabled("use_cached_config"):
            try:
                return self.infrastructure.get_container().get("config_manager")
            except Exception as e:
                print(f"⚠️ [CONFIG] 缓存配置服务获取失败，回退到传统模式: {e}")

        elif self.feature_flags.is_enabled("use_service_container"):
            # 使用新的服务容器
            try:
                return self.infrastructure.get_container().get("config_manager")
            except:
                # 回退到旧系统
                pass

        # 使用旧的配置管理器
        try:
            # 🔧 修复：使用动态导入避免相对导入问题
            import sys
            import os

            # 获取主程序模块
            main_module_name = None
            for name, module in sys.modules.items():
                if hasattr(module, 'ConfigManager') and 'DataProcessingApp' in str(module):
                    main_module_name = name
                    break

            if main_module_name:
                main_module = sys.modules[main_module_name]
                return main_module.ConfigManager()
            else:
                # 尝试直接导入
                current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                sys.path.insert(0, current_dir)

                import importlib.util
                spec = importlib.util.spec_from_file_location(
                    "main_app",
                    os.path.join(current_dir, "数据处理与导入应用_完整版.py")
                )
                if spec and spec.loader:
                    main_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(main_module)
                    return main_module.ConfigManager()

        except Exception as e:
            print(f"⚠️ [CONFIG] 传统配置管理器导入失败: {e}")

        # 如果导入失败，创建一个基本的配置管理器
        from .config_service import ConfigServiceFactory
        return ConfigServiceFactory.create_legacy_compatible_service()
        
    def get_gui_updater(self, root):
        """获取GUI更新器（兼容旧接口）"""
        # 🔧 步骤3：优先使用异步日志和GUI服务
        if self.feature_flags.is_enabled("use_async_logging"):
            try:
                # 创建兼容性GUI更新器
                container = self.infrastructure.get_container()

                # 检查是否已有GUI服务
                if not container.has("gui_service"):
                    from .gui_service import GUIServiceFactory
                    event_bus = self.infrastructure.get_event_bus()

                    container.register(
                        "gui_service",
                        lambda: GUIServiceFactory.create_gui_service(root, event_bus),
                        ServiceLifetime.SINGLETON
                    )

                # 创建兼容性包装器
                from .gui_compatibility import SafeGUIUpdaterFactory
                return SafeGUIUpdaterFactory.create_from_services(root, container)

            except Exception as e:
                print(f"⚠️ [GUI] 异步GUI服务获取失败，回退到传统模式: {e}")

        elif self.feature_flags.is_enabled("use_event_bus"):
            # 使用新的事件驱动GUI更新器
            try:
                return self.infrastructure.get_container().get("gui_updater")
            except:
                # 回退到旧系统
                pass

        # 使用旧的GUI更新器
        try:
            # 🔧 修复：使用动态导入避免相对导入问题
            import sys
            import os

            # 获取主程序模块
            main_module_name = None
            for name, module in sys.modules.items():
                if hasattr(module, 'SafeGUIUpdater') and 'DataProcessingApp' in str(module):
                    main_module_name = name
                    break

            if main_module_name:
                main_module = sys.modules[main_module_name]
                return main_module.SafeGUIUpdater(root)
            else:
                # 尝试直接导入
                current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                sys.path.insert(0, current_dir)

                import importlib.util
                spec = importlib.util.spec_from_file_location(
                    "main_app",
                    os.path.join(current_dir, "数据处理与导入应用_完整版.py")
                )
                if spec and spec.loader:
                    main_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(main_module)
                    return main_module.SafeGUIUpdater(root)

        except Exception as e:
            print(f"⚠️ [GUI] 传统GUI更新器导入失败: {e}")

        # 如果导入失败，创建一个基本的GUI更新器
        return self._create_fallback_gui_updater(root)

    def _create_fallback_gui_updater(self, root):
        """创建回退的GUI更新器"""
        class FallbackGUIUpdater:
            def __init__(self, root):
                self.root = root

            def log_message(self, message, tab_type="general"):
                print(f"[GUI] {message}")

            def safe_update(self, update_func, *args, **kwargs):
                try:
                    if self.root:
                        self.root.after(0, update_func, *args, **kwargs)
                except Exception as e:
                    print(f"❌ GUI更新失败: {e}")

        return FallbackGUIUpdater(root)

    def get_file_manager(self, config):
        """获取文件管理器（兼容旧接口）"""
        # 🔧 步骤4：优先使用文件服务
        if self.feature_flags.is_enabled("use_file_service"):
            try:
                return self.infrastructure.get_container().get("file_manager")
            except Exception as e:
                print(f"⚠️ [FILE] 文件服务获取失败，回退到传统模式: {e}")

        elif self.feature_flags.is_enabled("use_service_container"):
            # 使用新的文件服务
            try:
                return self.infrastructure.get_container().get("file_manager")
            except:
                # 回退到旧系统
                pass
                
        # 使用旧的文件管理器
        try:
            # 🔧 修复：使用动态导入避免相对导入问题
            import sys

            # 获取主程序模块
            main_module_name = None
            for name, module in sys.modules.items():
                if hasattr(module, 'FileManager') and 'DataProcessingApp' in str(module):
                    main_module_name = name
                    break

            if main_module_name:
                main_module = sys.modules[main_module_name]
                return main_module.FileManager(config)

        except Exception as e:
            print(f"⚠️ [FILE] 传统文件管理器导入失败: {e}")

        # 如果导入失败，创建一个基本的文件管理器
        return self._create_fallback_file_manager(config)

    def _create_fallback_file_manager(self, config):
        """创建回退的文件管理器"""
        class FallbackFileManager:
            def __init__(self, config):
                self.config = config

            def validate_files(self, file_paths):
                return True, "文件验证通过"

            def get_file_info(self, file_path):
                return {"path": file_path, "exists": os.path.exists(file_path)}

        return FallbackFileManager(config)

    def get_backup_manager(self, config, gui_updater):
        """获取备份管理器（兼容旧接口）"""
        # 🔧 步骤5：优先使用备份服务
        if self.feature_flags.is_enabled("use_backup_service"):
            try:
                return self.infrastructure.get_container().get("backup_manager")
            except Exception as e:
                print(f"⚠️ [BACKUP] 备份服务获取失败，回退到传统模式: {e}")

        elif self.feature_flags.is_enabled("use_service_container"):
            # 使用新的备份服务
            try:
                return self.infrastructure.get_container().get("backup_manager")
            except:
                # 回退到旧系统
                pass
                
        # 使用旧的备份管理器
        try:
            # 🔧 修复：使用动态导入避免相对导入问题
            import sys

            # 获取主程序模块
            main_module_name = None
            for name, module in sys.modules.items():
                if hasattr(module, 'DatabaseBackupManager') and 'DataProcessingApp' in str(module):
                    main_module_name = name
                    break

            if main_module_name:
                main_module = sys.modules[main_module_name]
                return main_module.DatabaseBackupManager(config, gui_updater)

        except Exception as e:
            print(f"⚠️ [BACKUP] 传统备份管理器导入失败: {e}")

        # 如果导入失败，创建一个基本的备份管理器
        return self._create_fallback_backup_manager(config, gui_updater)

    def _create_fallback_backup_manager(self, config, gui_updater):
        """创建回退的备份管理器"""
        class FallbackBackupManager:
            def __init__(self, config, gui_updater):
                self.config = config
                self.gui_updater = gui_updater
                self._backup_dir_cache = None  # 缓存计算的备份目录
                print(f"🔧 [ERROR_CHECK] FallbackBackupManager初始化完成，backup_dir将动态计算", flush=True)

            def _get_backup_dir(self):
                """获取备份目录，恢复原来的正确逻辑"""
                try:
                    # 🔧 恢复原来的正确备份逻辑：数据库目录下的backups文件夹
                    import os

                    # 获取数据库路径
                    if hasattr(self.config, 'get'):
                        db_path = self.config.get('Database', 'db_path', '')
                    elif hasattr(self.config, 'get_db_path'):
                        db_path = self.config.get_db_path()
                    else:
                        # 使用默认路径
                        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "database", "sales_reports.db")

                    print(f"🔧 [ERROR_CHECK] 数据库路径: {db_path}", flush=True)

                    # 🔧 恢复原来的正确逻辑：在数据库目录下创建backups子文件夹
                    backup_dir = os.path.join(os.path.dirname(db_path), "backups")
                    print(f"🔧 [ERROR_CHECK] 恢复正确的备份目录: {backup_dir}", flush=True)

                    return backup_dir

                except Exception as e:
                    print(f"❌ [IMMEDIATE_ERROR] _get_backup_dir失败: {e}", flush=True)
                    # 最后的回退选项
                    import os
                    default_backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "database", "backups")
                    print(f"🔧 [ERROR_CHECK] _get_backup_dir - 使用最终默认备份目录: {default_backup_dir}", flush=True)
                    return default_backup_dir

            @property
            def backup_dir(self):
                """backup_dir属性，动态计算以确保路径一致性"""
                if self._backup_dir_cache is None:
                    self._backup_dir_cache = self._get_backup_dir()
                return self._backup_dir_cache

            def backup_database(self, backup_name=None):
                """真正的备份实现，而不是回退模式"""
                try:
                    print(f"🔧 [ERROR_CHECK] FallbackBackupManager开始真正的备份操作", flush=True)

                    # 获取数据库路径
                    if hasattr(self.config, 'get'):
                        db_path = self.config.get('Database', 'db_path', '')
                    elif hasattr(self.config, 'get_db_path'):
                        db_path = self.config.get_db_path()
                    else:
                        # 使用默认路径
                        import os
                        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "database", "sales_reports.db")

                    print(f"🔧 [ERROR_CHECK] 数据库路径: {db_path}", flush=True)

                    # 检查数据库文件是否存在
                    import os
                    if not os.path.exists(db_path):
                        error_msg = f"❌ [IMMEDIATE_ERROR] 数据库文件不存在: {db_path}"
                        print(error_msg, flush=True)
                        self.gui_updater.safe_log(error_msg, "general")
                        return None

                    # 检查数据库文件大小
                    db_size = os.path.getsize(db_path)
                    print(f"🔧 [ERROR_CHECK] 数据库文件大小: {db_size:,} bytes", flush=True)

                    if db_size == 0:
                        error_msg = f"❌ [IMMEDIATE_ERROR] 数据库文件为空: {db_path}"
                        print(error_msg, flush=True)
                        self.gui_updater.safe_log(error_msg, "general")
                        return None

                    # 🔧 恢复原来的正确备份逻辑：数据库目录下的backups文件夹
                    backup_dir = self.backup_dir  # 使用property方法，现在会返回正确的路径
                    os.makedirs(backup_dir, exist_ok=True)
                    print(f"🔧 [ERROR_CHECK] 使用正确的备份目录: {backup_dir}", flush=True)

                    # 生成备份文件名
                    from datetime import datetime
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    backup_filename = f"sales_reports_backup_{timestamp}.db"
                    backup_path = os.path.join(backup_dir, backup_filename)
                    print(f"🔧 [ERROR_CHECK] 备份文件路径: {backup_path}", flush=True)

                    # 执行备份
                    import shutil
                    print(f"🔧 [ERROR_CHECK] 开始复制文件...", flush=True)
                    shutil.copy2(db_path, backup_path)
                    print(f"✅ [ERROR_CHECK] 文件复制完成", flush=True)

                    # 验证备份文件
                    if not os.path.exists(backup_path):
                        error_msg = f"❌ [IMMEDIATE_ERROR] 备份文件创建失败: {backup_path}"
                        print(error_msg, flush=True)
                        self.gui_updater.safe_log(error_msg, "general")
                        return None

                    backup_size = os.path.getsize(backup_path)
                    print(f"🔧 [ERROR_CHECK] 备份文件大小: {backup_size:,} bytes", flush=True)

                    if backup_size != db_size:
                        error_msg = f"❌ [IMMEDIATE_ERROR] 备份文件大小不匹配，原文件: {db_size:,} bytes，备份: {backup_size:,} bytes"
                        print(error_msg, flush=True)
                        self.gui_updater.safe_log(error_msg, "general")
                        return None

                    success_msg = f"✅ 数据库备份成功: {backup_filename} ({backup_size/1024/1024:.1f} MB)"
                    print(f"✅ [ERROR_CHECK] {success_msg}", flush=True)
                    self.gui_updater.safe_log(success_msg, "general")

                    return backup_path

                except Exception as e:
                    error_msg = f"❌ [IMMEDIATE_ERROR] 备份过程中出错: {e}"
                    print(error_msg, flush=True)
                    self.gui_updater.safe_log(error_msg, "general")
                    import traceback
                    traceback.print_exc()
                    return None

            def restore_database(self, backup_file):
                """恢复数据库（兼容旧接口）"""
                return self.restore_from_backup(backup_file)

            def restore_from_backup(self, backup_file):
                """从备份恢复数据库"""
                try:
                    print(f"🔧 [ERROR_CHECK] FallbackBackupManager开始恢复操作", flush=True)
                    print(f"🔧 [ERROR_CHECK] 备份文件: {backup_file}", flush=True)

                    # 检查备份文件是否存在
                    import os
                    if not os.path.exists(backup_file):
                        error_msg = f"❌ [IMMEDIATE_ERROR] 备份文件不存在: {backup_file}"
                        print(error_msg, flush=True)
                        self.gui_updater.safe_log(error_msg, "general")
                        return False

                    # 检查备份文件大小
                    backup_size = os.path.getsize(backup_file)
                    print(f"🔧 [ERROR_CHECK] 备份文件大小: {backup_size:,} bytes", flush=True)

                    if backup_size == 0:
                        error_msg = f"❌ [IMMEDIATE_ERROR] 备份文件为空: {backup_file}"
                        print(error_msg, flush=True)
                        self.gui_updater.safe_log(error_msg, "general")
                        return False

                    # 获取数据库路径
                    if hasattr(self.config, 'get'):
                        db_path = self.config.get('Database', 'db_path', '')
                    elif hasattr(self.config, 'get_db_path'):
                        db_path = self.config.get_db_path()
                    else:
                        # 使用默认路径
                        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "database", "sales_reports.db")

                    print(f"🔧 [ERROR_CHECK] 目标数据库路径: {db_path}", flush=True)

                    # 创建数据库目录（如果不存在）
                    db_dir = os.path.dirname(db_path)
                    os.makedirs(db_dir, exist_ok=True)

                    # 备份当前数据库（如果存在）
                    if os.path.exists(db_path):
                        from datetime import datetime
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        current_backup = f"{db_path}.restore_backup_{timestamp}"
                        print(f"🔧 [ERROR_CHECK] 备份当前数据库到: {current_backup}", flush=True)

                        import shutil
                        shutil.copy2(db_path, current_backup)

                    # 执行恢复
                    print(f"🔧 [ERROR_CHECK] 开始恢复数据库...", flush=True)
                    import shutil
                    shutil.copy2(backup_file, db_path)
                    print(f"✅ [ERROR_CHECK] 数据库恢复完成", flush=True)

                    # 验证恢复结果
                    if not os.path.exists(db_path):
                        error_msg = f"❌ [IMMEDIATE_ERROR] 恢复后数据库文件不存在: {db_path}"
                        print(error_msg, flush=True)
                        self.gui_updater.safe_log(error_msg, "general")
                        return False

                    restored_size = os.path.getsize(db_path)
                    print(f"🔧 [ERROR_CHECK] 恢复后数据库大小: {restored_size:,} bytes", flush=True)

                    if restored_size != backup_size:
                        error_msg = f"❌ [IMMEDIATE_ERROR] 恢复后文件大小不匹配，备份: {backup_size:,} bytes，恢复: {restored_size:,} bytes"
                        print(error_msg, flush=True)
                        self.gui_updater.safe_log(error_msg, "general")
                        return False

                    success_msg = f"✅ 数据库恢复成功: {os.path.basename(backup_file)} ({restored_size/1024/1024:.1f} MB)"
                    print(f"✅ [ERROR_CHECK] {success_msg}", flush=True)
                    self.gui_updater.safe_log(success_msg, "general")

                    return True

                except Exception as e:
                    error_msg = f"❌ [IMMEDIATE_ERROR] 恢复过程中出错: {e}"
                    print(error_msg, flush=True)
                    self.gui_updater.safe_log(error_msg, "general")
                    import traceback
                    traceback.print_exc()
                    return False

        return FallbackBackupManager(config, gui_updater)

    def get_process_runner(self, gui_updater):
        """获取进程运行器（兼容旧接口）"""
        if self.feature_flags.is_enabled("use_service_container"):
            # 使用新的进程服务
            try:
                return self.infrastructure.get_container().get("process_runner")
            except:
                # 回退到旧系统
                pass
                
        # 使用旧的进程运行器
        try:
            # 🔧 修复：使用动态导入避免相对导入问题
            import sys

            # 获取主程序模块
            main_module_name = None
            for name, module in sys.modules.items():
                if hasattr(module, 'ProcessRunner') and 'DataProcessingApp' in str(module):
                    main_module_name = name
                    break

            if main_module_name:
                main_module = sys.modules[main_module_name]
                return main_module.ProcessRunner(gui_updater)

        except Exception as e:
            print(f"⚠️ [PROCESS] 传统进程运行器导入失败: {e}")

        # 如果导入失败，创建一个基本的进程运行器
        return self._create_fallback_process_runner(gui_updater)

    def _create_fallback_process_runner(self, gui_updater):
        """创建真正的进程运行器（不再是回退模式）"""
        try:
            # 尝试导入真正的ProcessRunner
            import sys
            import os

            # 添加主程序目录到路径
            main_dir = os.path.dirname(os.path.abspath(__file__))
            if main_dir not in sys.path:
                sys.path.insert(0, main_dir)

            # 导入真正的ProcessRunner
            from 数据处理与导入应用_完整版 import ProcessRunner

            print("✅ [INFRA] 使用真正的ProcessRunner")
            return ProcessRunner(gui_updater)

        except ImportError as e:
            print(f"⚠️ [INFRA] 无法导入ProcessRunner，使用回退模式: {e}")

            class FallbackProcessRunner:
                def __init__(self, gui_updater):
                    self.gui_updater = gui_updater

                def run_script(self, script_path, *args, **kwargs):
                    self.gui_updater.safe_log("❌ 脚本运行功能暂不可用（回退模式）", "general")
                    return True

            return FallbackProcessRunner(gui_updater)


# 全局基础设施管理器实例
_global_infrastructure: Optional[InfrastructureManager] = None


def get_infrastructure() -> InfrastructureManager:
    """获取全局基础设施管理器"""
    global _global_infrastructure
    if _global_infrastructure is None:
        _global_infrastructure = InfrastructureManager()
    return _global_infrastructure


def initialize_infrastructure(config: Optional[Dict[str, Any]] = None) -> bool:
    """初始化全局基础设施"""
    infrastructure = get_infrastructure()
    return infrastructure.initialize(config)


def shutdown_infrastructure():
    """关闭全局基础设施"""
    global _global_infrastructure
    if _global_infrastructure:
        _global_infrastructure.shutdown()
        _global_infrastructure = None


def create_compatibility_adapter() -> LegacyCompatibilityAdapter:
    """创建兼容性适配器"""
    infrastructure = get_infrastructure()
    if not infrastructure.initialized:
        raise RuntimeError("Infrastructure must be initialized first")
    return LegacyCompatibilityAdapter(infrastructure)


# 导出主要组件
__all__ = [
    # 核心组件
    "ServiceContainer", "ServiceContainerBuilder", "ServiceLifetime",
    "EventBus", "Event", "EventPriority",
    "FeatureFlags", "FeatureState",

    # 🔧 步骤2：配置服务组件
    "CachedConfigManager", "ConfigServiceFactory", "ConfigPreloader",
    "get_config_service", "set_config_service",

    # 🔧 步骤3：日志和GUI服务组件
    "LoggingService", "LoggingServiceFactory", "LogLevel", "TabType",
    "get_logging_service", "set_logging_service", "log_message",
    "GUIUpdateService", "GUIServiceFactory", "GUIUpdateType",
    "get_gui_service", "set_gui_service",
    "SafeGUIUpdaterCompatible", "SafeGUIUpdaterFactory",
    "get_compatible_gui_updater", "set_compatible_gui_updater",

    # 🔧 步骤4：文件服务组件
    "FileService", "FileServiceFactory", "FileInfo", "FileStatus", "FileType",
    "get_file_service", "set_file_service",

    # 🔧 步骤5：备份服务组件
    "BackupService", "BackupServiceFactory", "BackupTask", "BackupType", "BackupStatus", "BackupPriority", "BackupPolicy",
    "get_backup_service", "set_backup_service",

    # 管理器
    "InfrastructureManager", "LegacyCompatibilityAdapter",

    # 全局函数
    "get_infrastructure", "initialize_infrastructure", "shutdown_infrastructure",
    "create_compatibility_adapter", "get_feature_flags", "is_feature_enabled",
    "get_global_event_bus"
]
