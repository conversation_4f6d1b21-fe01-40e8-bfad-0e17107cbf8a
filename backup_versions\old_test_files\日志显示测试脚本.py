#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志显示测试脚本
用于诊断应用日志显示问题
"""

import sys
import os
import tkinter as tk
from datetime import datetime

# 添加路径以导入主应用模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_log_display():
    """测试日志显示功能"""
    print("🔍 开始日志显示测试...")
    
    try:
        # 导入主应用
        from 数据处理与导入应用_完整版 import SafeGUIUpdater, LogProcessor
        
        print("✅ 成功导入主应用模块")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("日志显示测试")
        root.geometry("800x600")
        
        # 创建GUI更新器
        gui_updater = SafeGUIUpdater(root)
        print("✅ 成功创建SafeGUIUpdater")
        
        # 创建测试日志控件
        log_frame = tk.Frame(root)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        log_text = tk.Text(log_frame, wrap=tk.WORD, font=("STKaiti", 12))
        log_text.pack(fill=tk.BOTH, expand=True)
        
        # 注册日志控件
        gui_updater.register_log_widget("processing", log_text)
        print("✅ 成功注册日志控件")
        
        # 创建测试按钮
        button_frame = tk.Frame(root)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        def test_basic_log():
            """测试基本日志"""
            gui_updater.safe_log("这是一条基本测试日志", "processing")
            print("📝 发送基本测试日志")
        
        def test_processing_log():
            """测试数据处理日志"""
            test_messages = [
                "开始数据处理...",
                "正在读取文件...",
                "Transaction ID统计: 成功 5, 失败 0",
                "🔍 第二文件Transaction Num样本: ['T123', 'T456']",
                "🎯 RM5.00记录更新结果: 成功",
                "处理完成，耗时: 2.5秒",
                "📊 transaction_id 模式统计:",
                "   成功匹配: 100条",
                "   失败匹配: 5条",
                "   总计处理: 105条",
                "============================================================",
                "📊 数据处理完成总结",
                "   第一文件总金额: RM 1,250.00",
                "   第二文件处理总金额: RM 1,200.00",
                "   匹配成功率: 95.2%"
            ]
            
            for i, msg in enumerate(test_messages):
                root.after(i * 500, lambda m=msg: gui_updater.safe_log(m, "processing"))
            print("📝 发送数据处理测试日志序列")
        
        def test_filter_logic():
            """测试过滤逻辑"""
            processor = LogProcessor()
            
            test_cases = [
                "Transaction ID统计: 成功 1, 失败 0",
                "🔍 第二文件Transaction Num样本",
                "处理完成，耗时: 1.5秒",
                "📊 transaction_id 模式统计:",
                "普通日志消息"
            ]
            
            print("\n🔍 测试过滤逻辑:")
            for msg in test_cases:
                formatted = processor.format_log_message(msg)
                print(f"   输入: {msg}")
                print(f"   输出: {formatted}")
                print()
        
        def check_widget_registration():
            """检查控件注册状态"""
            print(f"\n🔍 检查控件注册状态:")
            print(f"   注册的日志控件: {list(gui_updater.log_widgets.keys())}")
            print(f"   processing控件存在: {'processing' in gui_updater.log_widgets}")
            if 'processing' in gui_updater.log_widgets:
                widget = gui_updater.log_widgets['processing']
                print(f"   控件类型: {type(widget)}")
                print(f"   控件状态: {widget.cget('state')}")
        
        # 创建测试按钮
        tk.Button(button_frame, text="基本日志测试", command=test_basic_log).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="数据处理日志测试", command=test_processing_log).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="过滤逻辑测试", command=test_filter_logic).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="检查注册状态", command=check_widget_registration).pack(side=tk.LEFT, padx=5)
        
        # 添加说明标签
        info_label = tk.Label(root, text="点击按钮测试不同的日志功能，观察日志显示效果", 
                             font=("Microsoft YaHei", 10), fg="blue")
        info_label.pack(pady=5)
        
        # 初始检查
        root.after(1000, check_widget_registration)
        
        print("✅ 测试界面创建完成")
        print("💡 请在GUI中点击按钮进行测试")
        
        # 启动GUI
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_log_processor():
    """单独测试LogProcessor"""
    print("\n🔍 单独测试LogProcessor...")
    
    try:
        from 数据处理与导入应用_完整版 import LogProcessor
        
        processor = LogProcessor()
        
        test_messages = [
            "Transaction ID统计: 成功 1, 失败 0",
            "🔍 第二文件Transaction Num样本: ['T123']",
            "🎯 RM5.00记录更新结果: 成功",
            "处理完成，耗时: 1.5秒",
            "📊 transaction_id 模式统计:",
            "   成功匹配: 50条",
            "============================================================",
            "📊 数据处理完成总结",
            "普通日志消息"
        ]
        
        print("测试消息处理结果:")
        for msg in test_messages:
            result = processor.format_log_message(msg)
            print(f"输入: {msg}")
            print(f"输出: {result}")
            print("-" * 50)
            
    except Exception as e:
        print(f"❌ LogProcessor测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 日志显示诊断工具")
    print("=" * 50)
    
    # 先测试LogProcessor
    test_log_processor()
    
    # 然后测试完整的日志显示
    test_log_display()
