#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接全面测试脚本
验证所有应用和脚本的数据库连接是否正确
"""

import os
import sys
import sqlite3
import configparser
import subprocess
from datetime import datetime

def test_config_db_path():
    """测试配置文件中的数据库路径"""
    print("🔍 测试配置文件数据库路径...")
    
    config_files = [
        "config.ini",
        "数据处理应用系统/config.ini",
        "数据处理应用系统/数据处理系统/数据处理应用系统_重构版/03_配置文件/config.ini",
        "数据处理应用系统/数据处理系统/数据处理应用系统_重构版/01_主程序/config.ini"
    ]
    
    results = {}
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                config = configparser.ConfigParser()
                config.read(config_file, encoding='utf-8')
                
                if config.has_section('Database'):
                    db_path = config.get('Database', 'db_path', fallback='')
                    if db_path:
                        print(f"  📄 {config_file}: {db_path}")
                        results[config_file] = db_path
                    else:
                        print(f"  ❌ {config_file}: 数据库路径为空")
                        results[config_file] = None
                else:
                    print(f"  ❌ {config_file}: 缺少Database配置段")
                    results[config_file] = None
                    
            except Exception as e:
                print(f"  ❌ {config_file}: 读取失败 - {e}")
                results[config_file] = None
        else:
            print(f"  ⚠️ {config_file}: 文件不存在")
            results[config_file] = None
    
    return results

def test_direct_db_connection(db_path):
    """直接测试数据库连接"""
    print(f"\n🔗 测试数据库连接: {db_path}")
    
    if not db_path:
        print("  ❌ 数据库路径为空")
        return False
    
    if not os.path.exists(db_path):
        print(f"  ❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        with sqlite3.connect(db_path, timeout=10) as conn:
            cursor = conn.cursor()
            
            # 测试基本查询
            cursor.execute("SELECT sqlite_version()")
            version = cursor.fetchone()[0]
            print(f"  ✅ SQLite版本: {version}")
            
            # 获取表列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"  ✅ 数据库表数量: {len(tables)}")
            
            if tables:
                print("  📊 数据库表:")
                for table in tables[:5]:  # 显示前5个表
                    cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                    count = cursor.fetchone()[0]
                    print(f"    - {table[0]}: {count} 条记录")
                if len(tables) > 5:
                    print(f"    ... 还有 {len(tables) - 5} 个表")
            
            return True
            
    except Exception as e:
        print(f"  ❌ 连接失败: {e}")
        return False

def test_connection_pool():
    """测试连接池模块"""
    print("\n🔧 测试连接池模块...")
    
    try:
        # 添加路径
        sys.path.insert(0, "数据处理应用系统/数据处理系统/数据处理应用系统_重构版")
        
        from database.connection_pool import initialize_connection_pool, get_connection
        
        # 初始化连接池
        pool = initialize_connection_pool()
        print(f"  ✅ 连接池初始化成功")
        
        # 测试获取连接
        with get_connection() as conn:
            cursor = conn.execute("SELECT sqlite_version()")
            version = cursor.fetchone()[0]
            print(f"  ✅ 通过连接池连接成功，SQLite版本: {version}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 连接池测试失败: {e}")
        return False

def test_main_app_config():
    """测试main_app.py的配置管理"""
    print("\n📱 测试main_app.py配置管理...")
    
    try:
        # 模拟ConfigManager的get_db_path方法
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')
        
        if config.has_section('Database'):
            db_path = config.get('Database', 'db_path', fallback=None)
            if db_path:
                print(f"  ✅ main_app配置路径: {db_path}")
                
                # 验证路径是否可访问
                if os.path.exists(db_path):
                    print(f"  ✅ 数据库文件存在")
                    return True
                else:
                    print(f"  ❌ 数据库文件不存在")
                    return False
            else:
                print(f"  ❌ 配置中没有数据库路径")
                return False
        else:
            print(f"  ❌ 配置中没有Database段")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_script_connections():
    """测试各个脚本的数据库连接"""
    print("\n📜 测试脚本数据库连接...")
    
    scripts_to_test = [
        {
            'name': '数据导入脚本',
            'file': '数据导入脚本_完整版.py',
            'test_cmd': [sys.executable, '数据导入脚本_完整版.py', '--help']
        },
        {
            'name': '退款处理脚本',
            'file': 'Refund_process 脚本.py',
            'test_cmd': [sys.executable, 'Refund_process 脚本.py', '--help']
        }
    ]
    
    results = {}
    
    for script in scripts_to_test:
        print(f"  🔍 测试 {script['name']}...")
        
        if not os.path.exists(script['file']):
            print(f"    ❌ 脚本文件不存在: {script['file']}")
            results[script['name']] = False
            continue
        
        try:
            # 测试脚本是否能正常启动（显示帮助）
            result = subprocess.run(
                script['test_cmd'],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8'
            )
            
            if result.returncode == 0 or 'usage:' in result.stdout.lower() or 'help' in result.stdout.lower():
                print(f"    ✅ 脚本可以正常启动")
                results[script['name']] = True
            else:
                print(f"    ⚠️ 脚本启动异常: {result.stderr}")
                results[script['name']] = False
                
        except subprocess.TimeoutExpired:
            print(f"    ⚠️ 脚本启动超时")
            results[script['name']] = False
        except Exception as e:
            print(f"    ❌ 测试失败: {e}")
            results[script['name']] = False
    
    return results

def generate_connection_report(config_results, db_test_result, pool_test_result, 
                             main_app_result, script_results):
    """生成连接测试报告"""
    print("\n" + "="*60)
    print("📊 数据库连接测试报告")
    print("="*60)
    
    # 配置文件测试结果
    print("\n📄 配置文件测试:")
    config_ok = 0
    config_total = 0
    for config_file, db_path in config_results.items():
        if db_path is not None:
            status = "✅" if db_path else "❌"
            print(f"  {status} {config_file}")
            if db_path:
                config_ok += 1
        config_total += 1
    
    print(f"  📊 配置文件通过率: {config_ok}/{config_total}")
    
    # 数据库连接测试
    print(f"\n🔗 数据库连接测试:")
    print(f"  {'✅' if db_test_result else '❌'} 直接数据库连接")
    print(f"  {'✅' if pool_test_result else '❌'} 连接池测试")
    print(f"  {'✅' if main_app_result else '❌'} 主应用配置")
    
    # 脚本测试结果
    print(f"\n📜 脚本连接测试:")
    script_ok = 0
    script_total = len(script_results)
    for script_name, result in script_results.items():
        status = "✅" if result else "❌"
        print(f"  {status} {script_name}")
        if result:
            script_ok += 1
    
    print(f"  📊 脚本通过率: {script_ok}/{script_total}")
    
    # 总体评估
    print(f"\n🎯 总体评估:")
    total_tests = 3 + script_total  # 直接连接 + 连接池 + 主应用 + 脚本数量
    passed_tests = (1 if db_test_result else 0) + \
                   (1 if pool_test_result else 0) + \
                   (1 if main_app_result else 0) + \
                   script_ok
    
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    if success_rate >= 90:
        print(f"  🎉 优秀！通过率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        print(f"  ✅ 所有数据库连接配置正确！")
    elif success_rate >= 70:
        print(f"  ✅ 良好！通过率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        print(f"  ⚠️ 大部分连接正常，少数需要检查")
    else:
        print(f"  ⚠️ 需要改进！通过率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        print(f"  🔧 多个连接存在问题，需要修复")
    
    return success_rate >= 80

def main():
    """主函数"""
    print("🚀 开始数据库连接全面测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 测试配置文件
    config_results = test_config_db_path()
    
    # 2. 获取主数据库路径进行测试
    main_db_path = config_results.get("config.ini")
    db_test_result = test_direct_db_connection(main_db_path)
    
    # 3. 测试连接池
    pool_test_result = test_connection_pool()
    
    # 4. 测试主应用配置
    main_app_result = test_main_app_config()
    
    # 5. 测试脚本连接
    script_results = test_script_connections()
    
    # 6. 生成报告
    all_ok = generate_connection_report(
        config_results, db_test_result, pool_test_result,
        main_app_result, script_results
    )
    
    if all_ok:
        print("\n🎊 恭喜！所有数据库连接都正确配置！")
    else:
        print("\n🔧 请根据上述报告修复发现的连接问题")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
