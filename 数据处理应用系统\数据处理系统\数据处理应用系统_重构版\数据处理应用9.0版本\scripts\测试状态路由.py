#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试状态路由 - 验证智能状态检测和表路由是否正常工作
"""

import sqlite3
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.models import SMART_STATUS_PATTERNS, STATUS_TABLE_MAPPING

def test_status_detection():
    """测试状态检测逻辑"""
    print("🔍 测试智能状态检测")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        ("Close", "CLOSED"),
        ("Closed", "CLOSED"),
        ("close", "CLOSED"),
        ("Refunded", "REFUNDING"),
        ("refunded", "REFUNDING"),
        ("Refunding", "REFUNDING"),
        ("refunding", "REFUNDING"),
        ("Finish", "FINISHED"),
        ("Finished", "FINISHED"),
        ("Complete", "FINISHED"),
        ("Success", "FINISHED"),
        ("Unknown", None)
    ]
    
    print("📋 状态检测测试:")
    for status, expected in test_cases:
        detected = smart_status_detection(status)
        result = "✅" if detected == expected else "❌"
        print(f"   {result} '{status}' -> {detected} (期望: {expected})")

def smart_status_detection(status: str) -> str:
    """智能状态检测（复制自原代码）"""
    status_lower = status.lower().strip()
    
    # 遍历所有状态模式
    for category, pattern_info in SMART_STATUS_PATTERNS.items():
        keywords = pattern_info['keywords']
        
        # 检查是否包含任何关键词
        for keyword in keywords:
            if keyword.lower() in status_lower:
                return category
    
    return None

def determine_target_table(platform: str, order_status: str) -> str:
    """确定目标表（复制自原代码）"""
    # 清理和标准化状态
    status = str(order_status).strip().lower() if order_status else ""
    
    if not status:
        return f"{platform}_Sales"
    
    # 1. 精确匹配
    status_mapping = STATUS_TABLE_MAPPING.get(platform, {})
    for status_key, table_name in status_mapping.items():
        if status.lower() == status_key.lower():
            return table_name
    
    # 2. 智能模糊匹配
    matched_category = smart_status_detection(status)
    if matched_category:
        table_name = f"{platform}_Sales{SMART_STATUS_PATTERNS[matched_category]['table_suffix']}"
        return table_name
    
    # 3. 默认主表
    return f"{platform}_Sales"

def test_table_routing():
    """测试表路由逻辑"""
    print("\n🔍 测试表路由逻辑")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        ("IOT", "Close", "IOT_Sales_Close"),
        ("IOT", "Closed", "IOT_Sales_Close"),
        ("IOT", "Refunded", "IOT_Sales_Refunding"),
        ("IOT", "Refunding", "IOT_Sales_Refunding"),
        ("IOT", "Finish", "IOT_Sales"),
        ("IOT", "Finished", "IOT_Sales"),
        ("ZERO", "Close", "ZERO_Sales_Close"),
        ("ZERO", "Refunded", "ZERO_Sales_Refunding"),
        ("ZERO", "Finish", "ZERO_Sales"),
    ]
    
    print("📋 表路由测试:")
    for platform, status, expected in test_cases:
        result_table = determine_target_table(platform, status)
        result = "✅" if result_table == expected else "❌"
        print(f"   {result} {platform} + '{status}' -> {result_table} (期望: {expected})")

def check_database_tables():
    """检查数据库表是否存在"""
    print("\n🔍 检查数据库表存在性")
    print("=" * 60)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        # 需要检查的表
        required_tables = [
            'IOT_Sales', 'IOT_Sales_Refunding', 'IOT_Sales_Close',
            'ZERO_Sales', 'ZERO_Sales_Refunding', 'ZERO_Sales_Close',
            'APP_Sales', 'APP_Sales_Refunding', 'APP_Sales_Close'
        ]
        
        print("📋 表存在性检查:")
        all_exist = True
        for table in required_tables:
            if table in existing_tables:
                print(f"   ✅ {table}: 存在")
            else:
                print(f"   ❌ {table}: 不存在")
                all_exist = False
        
        # 检查表结构
        if all_exist:
            print("\n📋 表结构检查:")
            for table in ['IOT_Sales_Refunding', 'IOT_Sales_Close']:
                if table in existing_tables:
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [col[1] for col in cursor.fetchall()]
                    print(f"   📊 {table}: {len(columns)} 列")
                    print(f"       列名: {', '.join(columns[:5])}{'...' if len(columns) > 5 else ''}")
        
        conn.close()
        return all_exist
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def test_insert_simulation():
    """模拟插入测试"""
    print("\n🔍 模拟数据插入测试")
    print("=" * 60)
    
    # 模拟数据
    test_data = [
        {"platform": "IOT", "Order_status": "Close", "Order_No": "TEST001"},
        {"platform": "IOT", "Order_status": "Refunded", "Order_No": "TEST002"},
        {"platform": "IOT", "Order_status": "Refunding", "Order_No": "TEST003"},
        {"platform": "IOT", "Order_status": "Finish", "Order_No": "TEST004"},
    ]
    
    print("📋 数据路由模拟:")
    for data in test_data:
        platform = data["platform"]
        status = data["Order_status"]
        order_no = data["Order_No"]
        
        target_table = determine_target_table(platform, status)
        print(f"   📊 {order_no} ({status}) -> {target_table}")

def main():
    """主函数"""
    print("🔧 状态路由测试工具")
    print("=" * 60)
    
    try:
        # 1. 测试状态检测
        test_status_detection()
        
        # 2. 测试表路由
        test_table_routing()
        
        # 3. 检查数据库表
        tables_exist = check_database_tables()
        
        # 4. 模拟插入测试
        test_insert_simulation()
        
        # 5. 总结
        print("\n" + "=" * 60)
        print("🎯 测试总结")
        print("=" * 60)
        
        if tables_exist:
            print("✅ 数据库表结构正常")
            print("✅ 状态检测逻辑正常")
            print("✅ 表路由逻辑正常")
            print("\n🔍 如果导入仍有问题，可能的原因:")
            print("1. 数据过滤逻辑问题")
            print("2. 事务提交问题")
            print("3. 重复数据检测过于严格")
            print("4. 列过滤导致数据被丢弃")
        else:
            print("❌ 数据库表结构不完整")
            print("🔧 需要运行表创建脚本")
        
        return 0
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
