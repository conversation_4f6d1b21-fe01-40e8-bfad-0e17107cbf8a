#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查配置文件一致性 - 验证所有引用的文件是否存在
"""

import os
import sys
from pathlib import Path

def check_script_files():
    """检查配置中引用的脚本文件是否存在"""
    print("🔧 检查配置文件一致性")
    print("=" * 60)
    
    # 获取主程序目录
    main_dir = Path(__file__).parent.parent / "01_主程序"
    scripts_dir = Path(__file__).parent
    
    # 配置中的脚本文件映射
    script_config = {
        'intelligent_processor': main_dir / 'report 脚本 3.0.py',
        'modular_processor': main_dir / 'report 模块化设计 7.0.py',
        'refund_script': main_dir / 'Refund_process_修复版.py',
        'refund_script_optimized': scripts_dir / 'refund_process_optimized.py',
        'data_import_script': scripts_dir / 'data_import_optimized.py',
        'data_import_script_optimized': scripts_dir / 'data_import_optimized.py',
        'data_import_script_legacy': main_dir / '数据导入脚本.py',
        'dual_database_import': scripts_dir / 'dual_database_import.py'
    }
    
    print("📋 检查脚本文件存在性:")
    missing_files = []
    existing_files = []
    
    for script_name, script_path in script_config.items():
        if script_path.exists():
            print(f"   ✅ {script_name}: {script_path.name}")
            existing_files.append(script_name)
        else:
            print(f"   ❌ {script_name}: {script_path} (文件不存在)")
            missing_files.append((script_name, script_path))
    
    return missing_files, existing_files

def check_database_path():
    """检查数据库路径配置"""
    print("\n📊 检查数据库路径配置:")
    
    # 配置中的数据库路径
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    if os.path.exists(db_path):
        print(f"   ✅ 数据库文件存在: {db_path}")
        
        # 检查文件大小
        file_size = os.path.getsize(db_path)
        print(f"   📊 数据库大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
        
        return True
    else:
        print(f"   ❌ 数据库文件不存在: {db_path}")
        return False

def check_directory_structure():
    """检查目录结构"""
    print("\n📁 检查目录结构:")
    
    base_dir = Path(__file__).parent.parent
    required_dirs = [
        "01_主程序",
        "scripts", 
        "database",
        "utils",
        "logs"
    ]
    
    missing_dirs = []
    existing_dirs = []
    
    for dir_name in required_dirs:
        dir_path = base_dir / dir_name
        if dir_path.exists() and dir_path.is_dir():
            print(f"   ✅ {dir_name}/")
            existing_dirs.append(dir_name)
        else:
            print(f"   ❌ {dir_name}/ (目录不存在)")
            missing_dirs.append(dir_name)
    
    return missing_dirs, existing_dirs

def check_import_dependencies():
    """检查导入依赖"""
    print("\n📦 检查关键模块导入:")
    
    critical_modules = [
        'pandas',
        'numpy', 
        'openpyxl',
        'sqlite3',
        'tkinter',
        'customtkinter'
    ]
    
    missing_modules = []
    available_modules = []
    
    for module in critical_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
            available_modules.append(module)
        except ImportError:
            print(f"   ❌ {module} (模块未安装)")
            missing_modules.append(module)
    
    return missing_modules, available_modules

def generate_consistency_report():
    """生成一致性检查报告"""
    print("\n" + "=" * 60)
    print("📋 配置文件一致性检查报告")
    print("=" * 60)
    
    # 检查脚本文件
    missing_scripts, existing_scripts = check_script_files()
    
    # 检查数据库
    db_exists = check_database_path()
    
    # 检查目录结构
    missing_dirs, existing_dirs = check_directory_structure()
    
    # 检查模块依赖
    missing_modules, available_modules = check_import_dependencies()
    
    # 生成总结
    print(f"\n📊 检查结果统计:")
    print(f"   脚本文件: {len(existing_scripts)}/{len(existing_scripts) + len(missing_scripts)} 存在")
    print(f"   数据库: {'✅ 存在' if db_exists else '❌ 缺失'}")
    print(f"   目录结构: {len(existing_dirs)}/{len(existing_dirs) + len(missing_dirs)} 完整")
    print(f"   模块依赖: {len(available_modules)}/{len(available_modules) + len(missing_modules)} 可用")
    
    # 问题总结
    total_issues = len(missing_scripts) + (0 if db_exists else 1) + len(missing_dirs) + len(missing_modules)
    
    if total_issues == 0:
        print(f"\n🎉 配置检查结果: 完美！")
        print("   所有配置项都正确，系统可以正常运行。")
    else:
        print(f"\n⚠️ 发现 {total_issues} 个配置问题:")
        
        if missing_scripts:
            print(f"\n   📄 缺失的脚本文件:")
            for script_name, script_path in missing_scripts:
                print(f"      - {script_name}: {script_path}")
        
        if not db_exists:
            print(f"\n   📊 数据库问题:")
            print(f"      - 数据库文件不存在")
        
        if missing_dirs:
            print(f"\n   📁 缺失的目录:")
            for dir_name in missing_dirs:
                print(f"      - {dir_name}/")
        
        if missing_modules:
            print(f"\n   📦 缺失的模块:")
            for module in missing_modules:
                print(f"      - {module}")
    
    return total_issues == 0

def suggest_fixes():
    """提供修复建议"""
    print(f"\n🔧 修复建议:")
    print("1. 确保所有脚本文件都在正确的位置")
    print("2. 检查数据库文件路径是否正确")
    print("3. 创建缺失的目录结构")
    print("4. 安装缺失的Python模块: pip install pandas numpy openpyxl customtkinter")
    print("5. 检查文件权限，确保应用程序可以访问所有文件")

def main():
    """主函数"""
    print("🔧 配置文件一致性检查工具")
    print("=" * 60)
    
    try:
        # 执行检查
        is_consistent = generate_consistency_report()
        
        if not is_consistent:
            suggest_fixes()
        
        print("\n" + "=" * 60)
        if is_consistent:
            print("✅ 配置文件一致性检查完成！系统配置正确。")
        else:
            print("⚠️ 配置文件一致性检查完成！发现问题需要修复。")
        
        return 0 if is_consistent else 1
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
