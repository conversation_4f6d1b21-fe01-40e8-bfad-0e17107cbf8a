#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置对话框
用于配置数据库连接设置
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import logging

logger = logging.getLogger(__name__)

class DatabaseConfigDialog:
    """数据库配置对话框"""
    
    def __init__(self, parent=None, current_db_path=""):
        self.parent = parent
        self.current_db_path = current_db_path
        self.result = None
        self.dialog = None
        
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.dialog.title("数据库配置")
        self.dialog.geometry("500x300")
        self.dialog.resizable(False, False)
        
        # 使对话框模态
        if self.parent:
            self.dialog.transient(self.parent)
            self.dialog.grab_set()
        
        self._create_widgets()
        self._center_dialog()
        
        # 等待对话框关闭
        self.dialog.wait_window()
        return self.result
    
    def _create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 当前数据库路径
        ttk.Label(main_frame, text="当前数据库路径:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        self.current_path_var = tk.StringVar(value=self.current_db_path)
        current_path_entry = ttk.Entry(main_frame, textvariable=self.current_path_var, width=60, state='readonly')
        current_path_entry.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 新数据库路径
        ttk.Label(main_frame, text="选择新的数据库路径:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        
        path_frame = ttk.Frame(main_frame)
        path_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.new_path_var = tk.StringVar()
        self.new_path_entry = ttk.Entry(path_frame, textvariable=self.new_path_var, width=50)
        self.new_path_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        browse_btn = ttk.Button(path_frame, text="浏览...", command=self._browse_database)
        browse_btn.grid(row=0, column=1, padx=(5, 0))
        
        path_frame.columnconfigure(0, weight=1)
        
        # 选项
        options_frame = ttk.LabelFrame(main_frame, text="选项", padding="5")
        options_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.backup_var = tk.BooleanVar(value=True)
        backup_check = ttk.Checkbutton(options_frame, text="更改前自动备份当前数据库", variable=self.backup_var)
        backup_check.grid(row=0, column=0, sticky=tk.W)
        
        self.create_dir_var = tk.BooleanVar(value=True)
        create_dir_check = ttk.Checkbutton(options_frame, text="如果目录不存在则自动创建", variable=self.create_dir_var)
        create_dir_check.grid(row=1, column=0, sticky=tk.W)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="确定", command=self._on_ok).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="重置为默认", command=self._reset_to_default).pack(side=tk.LEFT, padx=(10, 0))
        
        main_frame.columnconfigure(0, weight=1)
        self.dialog.columnconfigure(0, weight=1)
        self.dialog.rowconfigure(0, weight=1)
    
    def _browse_database(self):
        """浏览数据库文件"""
        initial_dir = os.path.dirname(self.current_db_path) if self.current_db_path else os.getcwd()
        
        file_path = filedialog.asksaveasfilename(
            title="选择数据库文件",
            initialdir=initial_dir,
            defaultextension=".db",
            filetypes=[
                ("SQLite数据库", "*.db"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.new_path_var.set(file_path)
    
    def _reset_to_default(self):
        """重置为默认路径"""
        script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        default_path = os.path.join(script_dir, "database", "sales_reports.db")
        self.new_path_var.set(default_path)
    
    def _on_ok(self):
        """确定按钮处理"""
        new_path = self.new_path_var.get().strip()
        
        if not new_path:
            messagebox.showerror("错误", "请选择数据库路径")
            return
        
        # 验证路径
        try:
            db_dir = os.path.dirname(new_path)
            
            if not os.path.exists(db_dir):
                if self.create_dir_var.get():
                    os.makedirs(db_dir, exist_ok=True)
                else:
                    messagebox.showerror("错误", f"目录不存在: {db_dir}")
                    return
            
            # 检查写入权限
            if not os.access(db_dir, os.W_OK):
                messagebox.showerror("错误", f"没有写入权限: {db_dir}")
                return
            
            self.result = {
                'db_path': new_path,
                'backup_current': self.backup_var.get(),
                'create_directory': self.create_dir_var.get()
            }
            
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"路径验证失败: {e}")
    
    def _on_cancel(self):
        """取消按钮处理"""
        self.result = None
        self.dialog.destroy()
    
    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        
        if self.parent:
            x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - (width // 2)
            y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - (height // 2)
        else:
            x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
            y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

def show_database_config_dialog(parent=None, current_db_path=""):
    """显示数据库配置对话框"""
    dialog = DatabaseConfigDialog(parent, current_db_path)
    return dialog.show()
