# 数据处理应用9.0版本

## 🎉 版本说明

这是数据处理应用的9.0版本，是从原版本完全独立复制而来的版本。

### ✨ 主要特点

- **完全独立运行**：所有配置和脚本都在9.0版本目录内，不会与原版本冲突
- **独立的配置文件**：使用自己的config/config.ini配置文件
- **独立的数据库**：使用自己的database/sales_reports.db数据库
- **独立的日志系统**：日志文件保存在自己的logs目录中
- **独立的备份系统**：备份文件保存在自己的backups目录中

### 📁 目录结构

```
数据处理应用9.0版本/
├── 数据处理应用9.0版本.py          # 主程序文件
├── 启动9.0版本.bat                 # 启动脚本
├── README_9.0版本.md               # 说明文档
├── config/                         # 配置文件目录
│   └── config.ini                  # 主配置文件
├── scripts/                        # 脚本文件目录
│   ├── data_import_optimized.py    # 数据导入脚本
│   ├── refund_process_optimized.py # 退款处理脚本
│   └── ...                         # 其他脚本
├── database/                       # 数据库目录
│   └── sales_reports.db            # SQLite数据库
├── utils/                          # 工具类目录
├── ui/                             # UI组件目录
├── logs/                           # 日志目录
├── backups/                        # 备份目录
├── temp_data/                      # 临时数据目录
└── PostgreSQL database/            # PostgreSQL数据库目录
```

### 🚀 启动方法

#### 方法1：使用批处理文件（推荐）
双击 `启动9.0版本.bat` 文件

#### 方法2：直接运行Python文件
```bash
python "数据处理应用9.0版本.py"
```

### ⚙️ 配置说明

配置文件位于 `config/config.ini`，包含以下主要配置：

- **数据库配置**：SQLite和PostgreSQL数据库连接信息
- **脚本路径**：各种处理脚本的路径
- **UI设置**：窗口大小、主题等
- **备份设置**：自动备份相关配置

### 🔧 与原版本的区别

1. **应用名称**：窗口标题显示为"数据处理应用9.0版本"
2. **配置路径**：所有路径都指向9.0版本目录内的文件
3. **数据库路径**：使用相对路径，指向本目录下的database文件夹
4. **脚本路径**：所有脚本调用都指向本目录下的scripts文件夹
5. **日志路径**：日志文件保存在本目录下的logs文件夹

### 📝 使用注意事项

1. **独立性**：9.0版本与原版本完全独立，可以同时运行
2. **数据隔离**：两个版本使用不同的数据库和配置文件
3. **备份安全**：每个版本都有自己的备份系统
4. **配置修改**：修改9.0版本的配置不会影响原版本

### 🛠️ 故障排除

如果遇到启动问题，请检查：

1. **Python环境**：确保已安装Python 3.7+
2. **依赖包**：确保已安装所需的Python包（tkinter, pandas, sqlite3等）
3. **配置文件**：检查config/config.ini文件是否存在且格式正确
4. **权限问题**：确保对目录有读写权限

### 📞 技术支持

如有问题，请检查logs目录下的日志文件获取详细错误信息。

---

**版本**: 9.0  
**创建日期**: 2025-08-04  
**基于**: 数据处理与导入应用_完整版 v2.0
