#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析文件状态脚本 - 检查Excel文件中的订单状态
"""

import os
import sys
import pandas as pd

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def analyze_excel_file(file_path):
    """分析Excel文件中的订单状态"""
    print(f"🔍 分析文件: {file_path}")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 读取Excel文件
        print("📋 读取Excel文件...")
        
        # 先获取所有工作表名称
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        print(f"工作表: {sheet_names}")
        
        # 查找包含IOT的工作表
        target_sheet = None
        for sheet in sheet_names:
            if 'IOT' in sheet.upper():
                target_sheet = sheet
                break
        
        if not target_sheet:
            target_sheet = sheet_names[0]  # 使用第一个工作表
        
        print(f"使用工作表: {target_sheet}")
        
        # 读取数据
        df = pd.read_excel(file_path, sheet_name=target_sheet, engine='openpyxl')
        
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 查找状态相关的列
        status_columns = []
        for col in df.columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in ['status', '状态', 'state']):
                status_columns.append(col)
        
        print(f"\n找到状态列: {status_columns}")
        
        # 分析每个状态列
        for col in status_columns:
            print(f"\n📊 分析列: {col}")
            print("-" * 40)
            
            # 获取唯一值和计数
            value_counts = df[col].value_counts(dropna=False)
            
            print("状态值分布:")
            for value, count in value_counts.items():
                print(f"  '{value}': {count} 条")
            
            # 检查哪些状态应该路由到不同的表
            print("\n路由分析:")
            for value, count in value_counts.items():
                value_str = str(value).lower().strip() if pd.notna(value) else ""
                
                if any(keyword in value_str for keyword in ['refund', '退款', 'refunding', 'refunded']):
                    print(f"  '{value}' → IOT_Sales_Refunding ({count} 条)")
                elif any(keyword in value_str for keyword in ['close', '关闭', 'closed', 'cancel']):
                    print(f"  '{value}' → IOT_Sales_Close ({count} 条)")
                elif any(keyword in value_str for keyword in ['finish', '完成', 'complete', 'success', 'paid']):
                    print(f"  '{value}' → IOT_Sales ({count} 条)")
                else:
                    print(f"  '{value}' → IOT_Sales (默认) ({count} 条)")
        
        # 测试智能路由逻辑
        print(f"\n🔧 测试智能路由逻辑")
        print("-" * 40)
        
        try:
            from data_import_optimized import DataImportProcessor
            processor = DataImportProcessor()
            
            # 测试每个状态值
            for col in status_columns:
                unique_values = df[col].dropna().unique()
                print(f"\n测试列 {col} 的路由:")
                
                for value in unique_values[:10]:  # 只测试前10个值
                    target_table = processor._determine_target_table('IOT', value)
                    print(f"  '{value}' → {target_table}")
                    
        except Exception as e:
            print(f"❌ 智能路由测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析文件失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_sample_file():
    """使用示例文件测试"""
    print("🔍 查找可用的测试文件")
    print("=" * 60)
    
    # 可能的文件路径
    possible_paths = [
        "C:/Users/<USER>/Desktop/July/IOT/030725 CHINA IOT.xlsx",
        "030725 CHINA IOT.xlsx",
        "../030725 CHINA IOT.xlsx",
        "../../030725 CHINA IOT.xlsx",
        "C:/Users/<USER>/Desktop/030725 CHINA IOT.xlsx",
        "030725 CHINA ZERO.xlsx",  # 备用文件
    ]
    
    for file_path in possible_paths:
        print(f"检查: {file_path}")
        if os.path.exists(file_path):
            print(f"✅ 找到文件: {file_path}")
            return analyze_excel_file(file_path)
    
    print("❌ 没有找到可用的测试文件")
    print("请确保以下文件之一存在:")
    for path in possible_paths:
        print(f"  - {path}")
    
    return False

def check_status_mapping():
    """检查状态映射配置"""
    print("\n🔍 检查状态映射配置")
    print("=" * 60)
    
    try:
        from database.models import STATUS_TABLE_MAPPING, SMART_STATUS_PATTERNS
        
        print("STATUS_TABLE_MAPPING:")
        for platform, mapping in STATUS_TABLE_MAPPING.items():
            print(f"  {platform}:")
            for status, table in mapping.items():
                print(f"    '{status}' → {table}")
        
        print(f"\nSMART_STATUS_PATTERNS:")
        for category, pattern in SMART_STATUS_PATTERNS.items():
            print(f"  {category}:")
            print(f"    keywords: {pattern['keywords']}")
            print(f"    table_suffix: {pattern['table_suffix']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查状态映射失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 分析文件状态和智能路由问题")
    print("=" * 80)
    
    # 检查1: 状态映射配置
    config_result = check_status_mapping()
    
    # 检查2: 分析文件
    file_result = test_with_sample_file()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 分析结果总结:")
    print(f"   状态映射配置: {'✅ 正常' if config_result else '❌ 异常'}")
    print(f"   文件分析: {'✅ 完成' if file_result else '❌ 失败'}")
    
    if not file_result:
        print("\n💡 建议:")
        print("1. 确保文件路径正确")
        print("2. 检查文件是否存在")
        print("3. 确认文件格式为Excel (.xlsx)")

if __name__ == "__main__":
    main()
