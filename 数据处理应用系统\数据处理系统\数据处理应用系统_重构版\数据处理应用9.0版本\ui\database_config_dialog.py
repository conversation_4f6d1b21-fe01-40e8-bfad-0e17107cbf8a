# -*- coding: utf-8 -*-
"""
数据库配置对话框
允许用户配置SQLite和PostgreSQL数据库设置
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.dual_database_manager import get_dual_database_manager, configure_databases

class DatabaseConfigDialog:
    """数据库配置对话框"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.db_manager = get_dual_database_manager()
        self.result = None
        
        # 创建窗口
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("数据库配置")
        self.window.geometry("600x500")
        self.window.resizable(True, True)
        
        # 设置窗口图标和样式
        self.setup_window_style()
        
        # 创建界面
        self.create_widgets()
        
        # 加载当前配置
        self.load_current_config()
        
        # 居中显示
        self.center_window()
    
    def setup_window_style(self):
        """设置窗口样式"""
        self.window.configure(bg='#f0f0f0')
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'), background='#f0f0f0')
        style.configure('Section.TLabel', font=('Arial', 10, 'bold'), background='#f0f0f0')
        style.configure('Config.TFrame', background='#f0f0f0', relief='groove', borderwidth=2)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="数据库配置", style='Title.TLabel')
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # SQLite配置区域
        self.create_sqlite_config(main_frame, row=1)
        
        # PostgreSQL配置区域
        self.create_postgresql_config(main_frame, row=2)
        
        # 状态显示区域
        self.create_status_area(main_frame, row=3)
        
        # 按钮区域
        self.create_buttons(main_frame, row=4)
    
    def create_sqlite_config(self, parent, row):
        """创建SQLite配置区域"""
        # SQLite框架
        sqlite_frame = ttk.LabelFrame(parent, text="SQLite 数据库", style='Config.TFrame', padding="15")
        sqlite_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        sqlite_frame.columnconfigure(1, weight=1)
        
        # 启用复选框
        self.sqlite_enabled_var = tk.BooleanVar()
        sqlite_enabled_cb = ttk.Checkbutton(
            sqlite_frame, 
            text="启用 SQLite 数据库", 
            variable=self.sqlite_enabled_var,
            command=self.on_sqlite_enabled_changed
        )
        sqlite_enabled_cb.grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        # 数据库路径
        ttk.Label(sqlite_frame, text="数据库路径:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        
        self.sqlite_path_var = tk.StringVar()
        self.sqlite_path_entry = ttk.Entry(sqlite_frame, textvariable=self.sqlite_path_var, width=50)
        self.sqlite_path_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        self.sqlite_browse_btn = ttk.Button(
            sqlite_frame, 
            text="浏览...", 
            command=self.browse_sqlite_path
        )
        self.sqlite_browse_btn.grid(row=1, column=2, sticky=tk.W)
        
        # 测试连接按钮
        self.sqlite_test_btn = ttk.Button(
            sqlite_frame, 
            text="测试连接", 
            command=self.test_sqlite_connection
        )
        self.sqlite_test_btn.grid(row=2, column=0, pady=(10, 0), sticky=tk.W)
        
        # 状态标签
        self.sqlite_status_label = ttk.Label(sqlite_frame, text="")
        self.sqlite_status_label.grid(row=2, column=1, pady=(10, 0), sticky=tk.W, padx=(10, 0))
    
    def create_postgresql_config(self, parent, row):
        """创建PostgreSQL配置区域"""
        # PostgreSQL框架
        pg_frame = ttk.LabelFrame(parent, text="PostgreSQL 数据库", style='Config.TFrame', padding="15")
        pg_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        pg_frame.columnconfigure(1, weight=1)
        
        # 启用复选框
        self.pg_enabled_var = tk.BooleanVar()
        pg_enabled_cb = ttk.Checkbutton(
            pg_frame, 
            text="启用 PostgreSQL 数据库", 
            variable=self.pg_enabled_var,
            command=self.on_postgresql_enabled_changed
        )
        pg_enabled_cb.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        # 配置字段
        config_fields = [
            ("主机:", "pg_host_var"),
            ("端口:", "pg_port_var"),
            ("数据库:", "pg_database_var"),
            ("用户名:", "pg_user_var"),
            ("密码:", "pg_password_var")
        ]
        
        self.pg_vars = {}
        self.pg_entries = {}
        
        for i, (label_text, var_name) in enumerate(config_fields):
            # 标签
            ttk.Label(pg_frame, text=label_text).grid(row=i+1, column=0, sticky=tk.W, padx=(0, 10), pady=2)
            
            # 变量和输入框
            var = tk.StringVar()
            self.pg_vars[var_name] = var
            
            if var_name == "pg_password_var":
                entry = ttk.Entry(pg_frame, textvariable=var, show="*", width=30)
            else:
                entry = ttk.Entry(pg_frame, textvariable=var, width=30)
            
            entry.grid(row=i+1, column=1, sticky=(tk.W, tk.E), pady=2)
            self.pg_entries[var_name] = entry
        
        # 测试连接按钮
        self.pg_test_btn = ttk.Button(
            pg_frame, 
            text="测试连接", 
            command=self.test_postgresql_connection
        )
        self.pg_test_btn.grid(row=len(config_fields)+1, column=0, pady=(10, 0), sticky=tk.W)
        
        # 状态标签
        self.pg_status_label = ttk.Label(pg_frame, text="")
        self.pg_status_label.grid(row=len(config_fields)+1, column=1, pady=(10, 0), sticky=tk.W, padx=(10, 0))
    
    def create_status_area(self, parent, row):
        """创建状态显示区域"""
        status_frame = ttk.LabelFrame(parent, text="数据库状态", padding="15")
        status_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        status_frame.columnconfigure(0, weight=1)
        
        # 状态文本框
        self.status_text = tk.Text(status_frame, height=6, width=70, wrap=tk.WORD)
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 滚动条
        status_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        status_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.status_text.configure(yscrollcommand=status_scrollbar.set)
        
        # 刷新状态按钮
        refresh_btn = ttk.Button(status_frame, text="刷新状态", command=self.refresh_status)
        refresh_btn.grid(row=1, column=0, pady=(10, 0), sticky=tk.W)
    
    def create_buttons(self, parent, row):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=row, column=0, pady=(10, 0))
        
        # 保存按钮
        save_btn = ttk.Button(button_frame, text="保存配置", command=self.save_config)
        save_btn.grid(row=0, column=0, padx=(0, 10))
        
        # 取消按钮
        cancel_btn = ttk.Button(button_frame, text="取消", command=self.cancel)
        cancel_btn.grid(row=0, column=1, padx=(0, 10))
        
        # 应用按钮
        apply_btn = ttk.Button(button_frame, text="应用", command=self.apply_config)
        apply_btn.grid(row=0, column=2)
    
    def load_current_config(self):
        """加载当前配置"""
        # 加载SQLite配置
        self.sqlite_enabled_var.set(self.db_manager.sqlite_config['enabled'])
        self.sqlite_path_var.set(self.db_manager.sqlite_config['db_path'])
        
        # 加载PostgreSQL配置
        self.pg_enabled_var.set(self.db_manager.postgresql_config['enabled'])
        self.pg_vars['pg_host_var'].set(self.db_manager.postgresql_config['host'])
        self.pg_vars['pg_port_var'].set(self.db_manager.postgresql_config['port'])
        self.pg_vars['pg_database_var'].set(self.db_manager.postgresql_config['database'])
        self.pg_vars['pg_user_var'].set(self.db_manager.postgresql_config['user'])
        self.pg_vars['pg_password_var'].set(self.db_manager.postgresql_config['password'])
        
        # 更新界面状态
        self.on_sqlite_enabled_changed()
        self.on_postgresql_enabled_changed()
        
        # 刷新状态
        self.refresh_status()
    
    def on_sqlite_enabled_changed(self):
        """SQLite启用状态改变"""
        enabled = self.sqlite_enabled_var.get()
        state = tk.NORMAL if enabled else tk.DISABLED
        
        self.sqlite_path_entry.configure(state=state)
        self.sqlite_browse_btn.configure(state=state)
        self.sqlite_test_btn.configure(state=state)
    
    def on_postgresql_enabled_changed(self):
        """PostgreSQL启用状态改变"""
        enabled = self.pg_enabled_var.get()
        state = tk.NORMAL if enabled else tk.DISABLED
        
        for entry in self.pg_entries.values():
            entry.configure(state=state)
        self.pg_test_btn.configure(state=state)
    
    def browse_sqlite_path(self):
        """浏览SQLite数据库路径"""
        filename = filedialog.asksaveasfilename(
            title="选择SQLite数据库文件",
            defaultextension=".db",
            filetypes=[("SQLite数据库", "*.db"), ("所有文件", "*.*")]
        )
        if filename:
            self.sqlite_path_var.set(filename)
    
    def test_sqlite_connection(self):
        """测试SQLite连接"""
        try:
            # 临时更新配置
            self.db_manager.sqlite_config['db_path'] = self.sqlite_path_var.get()
            self.db_manager.sqlite_config['enabled'] = self.sqlite_enabled_var.get()
            
            if self.db_manager.test_sqlite_connection():
                self.sqlite_status_label.configure(text="✅ 连接成功", foreground="green")
            else:
                self.sqlite_status_label.configure(text="❌ 连接失败", foreground="red")
        except Exception as e:
            self.sqlite_status_label.configure(text=f"❌ 错误: {str(e)}", foreground="red")
    
    def test_postgresql_connection(self):
        """测试PostgreSQL连接"""
        try:
            # 临时更新配置
            self.db_manager.postgresql_config.update({
                'host': self.pg_vars['pg_host_var'].get(),
                'port': self.pg_vars['pg_port_var'].get(),
                'database': self.pg_vars['pg_database_var'].get(),
                'user': self.pg_vars['pg_user_var'].get(),
                'password': self.pg_vars['pg_password_var'].get(),
                'enabled': self.pg_enabled_var.get()
            })
            
            if self.db_manager.test_postgresql_connection():
                self.pg_status_label.configure(text="✅ 连接成功", foreground="green")
            else:
                self.pg_status_label.configure(text="❌ 连接失败", foreground="red")
        except Exception as e:
            self.pg_status_label.configure(text=f"❌ 错误: {str(e)}", foreground="red")
    
    def refresh_status(self):
        """刷新数据库状态"""
        try:
            status = self.db_manager.get_database_status()
            
            self.status_text.delete(1.0, tk.END)
            
            for db_type, info in status.items():
                self.status_text.insert(tk.END, f"{db_type} 数据库:\n")
                self.status_text.insert(tk.END, f"  启用: {'是' if info['enabled'] else '否'}\n")
                self.status_text.insert(tk.END, f"  连接: {'成功' if info['connected'] else '失败'}\n")
                
                if info['connected'] and info['tables']:
                    self.status_text.insert(tk.END, f"  表数量: {len(info['tables'])}\n")
                    total_rows = sum(table['rows'] for table in info['tables'])
                    self.status_text.insert(tk.END, f"  总行数: {total_rows:,}\n")
                
                self.status_text.insert(tk.END, "\n")
            
        except Exception as e:
            self.status_text.delete(1.0, tk.END)
            self.status_text.insert(tk.END, f"获取状态失败: {str(e)}")
    
    def apply_config(self):
        """应用配置"""
        try:
            configure_databases(
                sqlite_path=self.sqlite_path_var.get(),
                sqlite_enabled=self.sqlite_enabled_var.get(),
                pg_host=self.pg_vars['pg_host_var'].get(),
                pg_port=self.pg_vars['pg_port_var'].get(),
                pg_database=self.pg_vars['pg_database_var'].get(),
                pg_user=self.pg_vars['pg_user_var'].get(),
                pg_password=self.pg_vars['pg_password_var'].get(),
                pg_enabled=self.pg_enabled_var.get()
            )
            
            # 重新加载配置
            self.db_manager.load_config()
            
            # 刷新状态
            self.refresh_status()
            
            messagebox.showinfo("成功", "配置已应用")
            
        except Exception as e:
            messagebox.showerror("错误", f"应用配置失败: {str(e)}")
    
    def save_config(self):
        """保存配置并关闭"""
        self.apply_config()
        self.result = "saved"
        self.window.destroy()
    
    def cancel(self):
        """取消并关闭"""
        self.result = "cancelled"
        self.window.destroy()
    
    def center_window(self):
        """居中显示窗口"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def show(self):
        """显示对话框"""
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.wait_window()
        return self.result

def main():
    """测试函数"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    dialog = DatabaseConfigDialog()
    result = dialog.show()
    
    print(f"对话框结果: {result}")

if __name__ == "__main__":
    main()
