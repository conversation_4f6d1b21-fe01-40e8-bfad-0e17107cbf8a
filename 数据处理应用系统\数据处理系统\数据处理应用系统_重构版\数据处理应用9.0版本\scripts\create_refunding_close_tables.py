# -*- coding: utf-8 -*-
"""
创建Refunding和Close表脚本
根据现有的IOT_Sales和ZERO_Sales表创建对应的Refunding和Close表
"""

import os
import sys
import sqlite3
import pandas as pd
from datetime import datetime
from pathlib import Path

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger
from utils.exceptions import DatabaseError
from database.connection_pool import get_connection, reinitialize_connection_pool
from database.smart_backup_manager import get_smart_backup_manager


class RefundingCloseTableCreator:
    """Refunding和Close表创建器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化表创建器
        
        Args:
            db_path: 数据库路径
        """
        self.logger = get_logger('refunding_close_creator')
        
        if db_path:
            self.db_path = db_path
        else:
            self.db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
        
        # 确保连接池使用正确的数据库路径
        reinitialize_connection_pool(self.db_path)

        # 初始化备份管理器
        self.backup_manager = get_smart_backup_manager(self.db_path)

        self.logger.info(f"Refunding和Close表创建器已初始化，数据库路径: {self.db_path}")
    
    def check_source_tables(self) -> bool:
        """检查源表是否存在"""
        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()
                
                # 检查IOT_Sales表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='IOT_Sales'")
                iot_exists = cursor.fetchone() is not None
                
                # 检查ZERO_Sales表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ZERO_Sales'")
                zero_exists = cursor.fetchone() is not None
                
                if not iot_exists:
                    self.logger.error("❌ IOT_Sales表不存在")
                    return False
                
                if not zero_exists:
                    self.logger.error("❌ ZERO_Sales表不存在")
                    return False
                
                # 检查表中是否有数据
                cursor.execute("SELECT COUNT(*) FROM IOT_Sales")
                iot_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM ZERO_Sales")
                zero_count = cursor.fetchone()[0]
                
                self.logger.info(f"✅ 源表检查完成:")
                self.logger.info(f"  • IOT_Sales: {iot_count} 条记录")
                self.logger.info(f"  • ZERO_Sales: {zero_count} 条记录")
                
                return True
                
        except Exception as e:
            self.logger.error(f"检查源表失败: {e}")
            return False
    
    def create_refunding_close_tables(self) -> bool:
        """创建Refunding和Close表"""
        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()
                
                # 获取IOT_Sales表结构
                cursor.execute("PRAGMA table_info(IOT_Sales)")
                iot_columns = cursor.fetchall()
                
                # 构建列定义
                column_definitions = []
                for col in iot_columns:
                    col_name = col[1]
                    col_type = col[2]
                    not_null = "NOT NULL" if col[3] else ""
                    default_val = f"DEFAULT {col[4]}" if col[4] is not None else ""
                    pk = "PRIMARY KEY" if col[5] else ""
                    
                    col_def = f"{col_name} {col_type} {not_null} {default_val} {pk}".strip()
                    column_definitions.append(col_def)
                
                columns_sql = ",\n    ".join(column_definitions)
                
                # 创建四个新表
                tables_to_create = [
                    "IOT_Sales_Refunding",
                    "IOT_Sales_Close", 
                    "ZERO_Sales_Refunding",
                    "ZERO_Sales_Close"
                ]
                
                for table_name in tables_to_create:
                    create_sql = f"""
                    CREATE TABLE IF NOT EXISTS {table_name} (
                        {columns_sql}
                    )
                    """
                    
                    cursor.execute(create_sql)
                    self.logger.info(f"✅ 表 {table_name} 创建完成")
                
                conn.connection.commit()
                self.logger.info("🎉 所有Refunding和Close表创建完成")
                return True
                
        except Exception as e:
            self.logger.error(f"创建表失败: {e}")
            raise DatabaseError(f"创建Refunding和Close表失败: {e}")
    
    def migrate_existing_data(self) -> dict:
        """迁移现有数据到对应的表"""
        migration_stats = {
            'IOT_Sales_Refunding': 0,
            'IOT_Sales_Close': 0,
            'ZERO_Sales_Refunding': 0,
            'ZERO_Sales_Close': 0
        }
        
        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()
                
                # 迁移IOT_Sales中的Refunding数据
                cursor.execute("""
                    INSERT INTO IOT_Sales_Refunding 
                    SELECT * FROM IOT_Sales 
                    WHERE LOWER(Order_status) IN ('refunded', 'refunding')
                """)
                migration_stats['IOT_Sales_Refunding'] = cursor.rowcount
                
                # 迁移IOT_Sales中的Close数据
                cursor.execute("""
                    INSERT INTO IOT_Sales_Close 
                    SELECT * FROM IOT_Sales 
                    WHERE LOWER(Order_status) IN ('close', 'closed')
                """)
                migration_stats['IOT_Sales_Close'] = cursor.rowcount
                
                # 迁移ZERO_Sales中的Refunding数据
                cursor.execute("""
                    INSERT INTO ZERO_Sales_Refunding 
                    SELECT * FROM ZERO_Sales 
                    WHERE LOWER(Order_status) IN ('refunded', 'refunding')
                """)
                migration_stats['ZERO_Sales_Refunding'] = cursor.rowcount
                
                # 迁移ZERO_Sales中的Close数据
                cursor.execute("""
                    INSERT INTO ZERO_Sales_Close 
                    SELECT * FROM ZERO_Sales 
                    WHERE LOWER(Order_status) IN ('close', 'closed')
                """)
                migration_stats['ZERO_Sales_Close'] = cursor.rowcount
                
                conn.connection.commit()
                
                # 输出迁移统计
                total_migrated = sum(migration_stats.values())
                self.logger.info(f"📊 数据迁移完成，总计迁移: {total_migrated} 条记录")
                for table_name, count in migration_stats.items():
                    if count > 0:
                        self.logger.info(f"  • {table_name}: {count} 条记录")
                
                return migration_stats
                
        except Exception as e:
            self.logger.error(f"数据迁移失败: {e}")
            raise DatabaseError(f"迁移现有数据失败: {e}")
    
    def create_refunding_close_view(self) -> bool:
        """创建Refunding和Close统计视图（按您的要求格式）"""
        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                # 删除现有视图（如果存在）
                cursor.execute("DROP VIEW IF EXISTS Refunding_Close_Statistics")

                # 创建新的统计视图，按您的要求格式
                view_sql = """
                CREATE VIEW Refunding_Close_Statistics AS
                WITH
                  -- 1) 预聚合Refunding和Close数据
                  Refunding_Close_Aggs AS (
                    SELECT
                      Equipment_ID AS Chair_Serial_No,
                      DATE(Order_time) AS Sale_Date,
                      SUM(CASE WHEN Source = 'IOT_Refunding' THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS IOT_Price_Refund,
                      SUM(CASE WHEN Source = 'IOT_Close' THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS IOT_Price_Close,
                      SUM(CASE WHEN Source = 'ZERO_Refunding' THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS ZERO_Price_Refund,
                      SUM(CASE WHEN Source = 'ZERO_Close' THEN CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL) ELSE 0 END) AS ZERO_Price_Close,
                      SUM(CASE WHEN Source = 'IOT_Refunding' THEN 1 ELSE 0 END) AS IOT_Count_Refund,
                      SUM(CASE WHEN Source = 'IOT_Close' THEN 1 ELSE 0 END) AS IOT_Count_Close,
                      SUM(CASE WHEN Source = 'ZERO_Refunding' THEN 1 ELSE 0 END) AS ZERO_Count_Refund,
                      SUM(CASE WHEN Source = 'ZERO_Close' THEN 1 ELSE 0 END) AS ZERO_Count_Close,
                      SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Price
                    FROM (
                      SELECT Equipment_ID, Order_time, Order_price, 'IOT_Refunding' AS Source FROM IOT_Sales_Refunding
                      UNION ALL
                      SELECT Equipment_ID, Order_time, Order_price, 'IOT_Close' FROM IOT_Sales_Close
                      UNION ALL
                      SELECT Equipment_ID, Order_time, Order_price, 'ZERO_Refunding' FROM ZERO_Sales_Refunding
                      UNION ALL
                      SELECT Equipment_ID, Order_time, Order_price, 'ZERO_Close' FROM ZERO_Sales_Close
                    ) AS AllRefundingClose
                    WHERE Equipment_ID IS NOT NULL AND Order_time IS NOT NULL
                    GROUP BY Equipment_ID, DATE(Order_time)
                  ),

                  -- 2) 构建日期维度
                  All_Dates AS (
                    SELECT DISTINCT Sale_Date FROM Refunding_Close_Aggs
                  ),

                  -- 3) 主关联逻辑
                  Main_Join AS (
                    SELECT
                      e.Chair_Serial_No,
                      e.STATE,
                      e.Location,
                      e.Quantity,
                      e.Layer,
                      e.Effective_From,
                      e.Effective_To,
                      e.Rental,
                      e.DATE,
                      d.Sale_Date,
                      COALESCE(a.IOT_Price_Refund, 0) AS IOT_Price_Refund,
                      COALESCE(a.IOT_Price_Close, 0) AS IOT_Price_Close,
                      COALESCE(a.ZERO_Price_Refund, 0) AS ZERO_Price_Refund,
                      COALESCE(a.ZERO_Price_Close, 0) AS ZERO_Price_Close,
                      COALESCE(a.IOT_Count_Refund, 0) AS IOT_Count_Refund,
                      COALESCE(a.IOT_Count_Close, 0) AS IOT_Count_Close,
                      COALESCE(a.ZERO_Count_Refund, 0) AS ZERO_Count_Refund,
                      COALESCE(a.ZERO_Count_Close, 0) AS ZERO_Count_Close,
                      COALESCE(a.Total_Price, 0) AS Total_Price
                    FROM (
                      SELECT * FROM Current_Equipment
                      WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='Current_Equipment')
                    ) e
                    CROSS JOIN All_Dates d
                    LEFT JOIN Refunding_Close_Aggs a
                      ON e.Chair_Serial_No = a.Chair_Serial_No
                      AND d.Sale_Date = a.Sale_Date
                    WHERE d.Sale_Date BETWEEN IFNULL(e.Effective_From, '1900-01-01') AND IFNULL(e.Effective_To, '2100-12-31')
                  ),

                  -- 4) 补缺未匹配的Refunding/Close记录
                  Unmatched_Refunding_Close AS (
                    SELECT
                      s.Chair_Serial_No,
                      NULL AS STATE,
                      NULL AS Location,
                      NULL AS Quantity,
                      NULL AS Layer,
                      NULL AS Effective_From,
                      NULL AS Effective_To,
                      NULL AS Rental,
                      NULL AS DATE,
                      s.Sale_Date,
                      COALESCE(a.IOT_Price_Refund, 0) AS IOT_Price_Refund,
                      COALESCE(a.IOT_Price_Close, 0) AS IOT_Price_Close,
                      COALESCE(a.ZERO_Price_Refund, 0) AS ZERO_Price_Refund,
                      COALESCE(a.ZERO_Price_Close, 0) AS ZERO_Price_Close,
                      COALESCE(a.IOT_Count_Refund, 0) AS IOT_Count_Refund,
                      COALESCE(a.IOT_Count_Close, 0) AS IOT_Count_Close,
                      COALESCE(a.ZERO_Count_Refund, 0) AS ZERO_Count_Refund,
                      COALESCE(a.ZERO_Count_Close, 0) AS ZERO_Count_Close,
                      COALESCE(a.Total_Price, 0) AS Total_Price
                    FROM (
                      SELECT Chair_Serial_No, Sale_Date FROM Refunding_Close_Aggs
                    ) AS s
                    LEFT JOIN (
                      SELECT * FROM Current_Equipment
                      WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='Current_Equipment')
                    ) e ON s.Chair_Serial_No = e.Chair_Serial_No
                      AND s.Sale_Date BETWEEN IFNULL(e.Effective_From, '1900-01-01') AND IFNULL(e.Effective_To, '2100-12-31')
                    LEFT JOIN Refunding_Close_Aggs a
                      ON s.Chair_Serial_No = a.Chair_Serial_No
                      AND s.Sale_Date = a.Sale_Date
                    WHERE e.Chair_Serial_No IS NULL
                  )

                -- 合并最终结果
                SELECT * FROM Main_Join
                UNION ALL
                SELECT * FROM Unmatched_Refunding_Close
                ORDER BY Chair_Serial_No, Sale_Date
                """
                
                cursor.execute(view_sql)
                conn.connection.commit()
                
                self.logger.info("✅ Refunding_Close_Statistics 视图创建完成")
                
                # 测试视图
                cursor.execute("SELECT COUNT(*) FROM Refunding_Close_Statistics")
                view_count = cursor.fetchone()[0]
                self.logger.info(f"📊 视图包含 {view_count} 条记录")
                
                return True
                
        except Exception as e:
            self.logger.error(f"创建视图失败: {e}")
            raise DatabaseError(f"创建Refunding和Close统计视图失败: {e}")

    def create_summary_statistics_view(self) -> bool:
        """创建汇总统计视图"""
        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                # 删除现有视图（如果存在）
                cursor.execute("DROP VIEW IF EXISTS Refunding_Close_Summary")

                # 创建汇总统计视图
                summary_view_sql = """
                CREATE VIEW Refunding_Close_Summary AS
                SELECT
                  'IOT_Sales_Refunding' AS Table_Name,
                  'IOT' AS Platform,
                  'Refunding' AS Status_Type,
                  COUNT(*) AS Record_Count,
                  SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
                  AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
                  MIN(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Min_Sales,
                  MAX(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Max_Sales,
                  COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count,
                  COUNT(DISTINCT DATE(Order_time)) AS Unique_Date_Count
                FROM IOT_Sales_Refunding
                WHERE Order_price IS NOT NULL AND Order_price != ''

                UNION ALL

                SELECT
                  'IOT_Sales_Close' AS Table_Name,
                  'IOT' AS Platform,
                  'Close' AS Status_Type,
                  COUNT(*) AS Record_Count,
                  SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
                  AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
                  MIN(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Min_Sales,
                  MAX(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Max_Sales,
                  COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count,
                  COUNT(DISTINCT DATE(Order_time)) AS Unique_Date_Count
                FROM IOT_Sales_Close
                WHERE Order_price IS NOT NULL AND Order_price != ''

                UNION ALL

                SELECT
                  'ZERO_Sales_Refunding' AS Table_Name,
                  'ZERO' AS Platform,
                  'Refunding' AS Status_Type,
                  COUNT(*) AS Record_Count,
                  SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
                  AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
                  MIN(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Min_Sales,
                  MAX(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Max_Sales,
                  COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count,
                  COUNT(DISTINCT DATE(Order_time)) AS Unique_Date_Count
                FROM ZERO_Sales_Refunding
                WHERE Order_price IS NOT NULL AND Order_price != ''

                UNION ALL

                SELECT
                  'ZERO_Sales_Close' AS Table_Name,
                  'ZERO' AS Platform,
                  'Close' AS Status_Type,
                  COUNT(*) AS Record_Count,
                  SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
                  AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
                  MIN(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Min_Sales,
                  MAX(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Max_Sales,
                  COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count,
                  COUNT(DISTINCT DATE(Order_time)) AS Unique_Date_Count
                FROM ZERO_Sales_Close
                WHERE Order_price IS NOT NULL AND Order_price != ''

                UNION ALL

                -- 总计Refunding (IOT + ZERO)
                SELECT
                  'Total_Refunding' AS Table_Name,
                  'ALL' AS Platform,
                  'Refunding' AS Status_Type,
                  SUM(Record_Count) AS Record_Count,
                  SUM(Total_Sales) AS Total_Sales,
                  AVG(Average_Sales) AS Average_Sales,
                  MIN(Min_Sales) AS Min_Sales,
                  MAX(Max_Sales) AS Max_Sales,
                  SUM(Unique_Equipment_Count) AS Unique_Equipment_Count,
                  SUM(Unique_Date_Count) AS Unique_Date_Count
                FROM (
                  SELECT COUNT(*) AS Record_Count, SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
                         AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
                         MIN(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Min_Sales,
                         MAX(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Max_Sales,
                         COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count,
                         COUNT(DISTINCT DATE(Order_time)) AS Unique_Date_Count
                  FROM IOT_Sales_Refunding WHERE Order_price IS NOT NULL AND Order_price != ''
                  UNION ALL
                  SELECT COUNT(*) AS Record_Count, SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
                         AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
                         MIN(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Min_Sales,
                         MAX(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Max_Sales,
                         COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count,
                         COUNT(DISTINCT DATE(Order_time)) AS Unique_Date_Count
                  FROM ZERO_Sales_Refunding WHERE Order_price IS NOT NULL AND Order_price != ''
                )

                UNION ALL

                -- 总计Close (IOT + ZERO)
                SELECT
                  'Total_Close' AS Table_Name,
                  'ALL' AS Platform,
                  'Close' AS Status_Type,
                  SUM(Record_Count) AS Record_Count,
                  SUM(Total_Sales) AS Total_Sales,
                  AVG(Average_Sales) AS Average_Sales,
                  MIN(Min_Sales) AS Min_Sales,
                  MAX(Max_Sales) AS Max_Sales,
                  SUM(Unique_Equipment_Count) AS Unique_Equipment_Count,
                  SUM(Unique_Date_Count) AS Unique_Date_Count
                FROM (
                  SELECT COUNT(*) AS Record_Count, SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
                         AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
                         MIN(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Min_Sales,
                         MAX(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Max_Sales,
                         COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count,
                         COUNT(DISTINCT DATE(Order_time)) AS Unique_Date_Count
                  FROM IOT_Sales_Close WHERE Order_price IS NOT NULL AND Order_price != ''
                  UNION ALL
                  SELECT COUNT(*) AS Record_Count, SUM(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Total_Sales,
                         AVG(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Average_Sales,
                         MIN(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Min_Sales,
                         MAX(CAST(REPLACE(IFNULL(Order_price, '0'), ',', '') AS REAL)) AS Max_Sales,
                         COUNT(DISTINCT Equipment_ID) AS Unique_Equipment_Count,
                         COUNT(DISTINCT DATE(Order_time)) AS Unique_Date_Count
                  FROM ZERO_Sales_Close WHERE Order_price IS NOT NULL AND Order_price != ''
                )

                ORDER BY
                  CASE Platform
                    WHEN 'IOT' THEN 1
                    WHEN 'ZERO' THEN 2
                    WHEN 'ALL' THEN 3
                  END,
                  CASE Status_Type
                    WHEN 'Refunding' THEN 1
                    WHEN 'Close' THEN 2
                  END
                """

                cursor.execute(summary_view_sql)
                conn.connection.commit()

                self.logger.info("✅ Refunding_Close_Summary 汇总视图创建完成")

                # 测试汇总视图
                cursor.execute("SELECT COUNT(*) FROM Refunding_Close_Summary")
                summary_count = cursor.fetchone()[0]
                self.logger.info(f"📊 汇总视图包含 {summary_count} 条统计记录")

                return True

        except Exception as e:
            self.logger.error(f"创建汇总视图失败: {e}")
            raise DatabaseError(f"创建Refunding和Close汇总统计视图失败: {e}")
    
    def run_complete_setup(self) -> dict:
        """运行完整的设置流程"""
        def _setup_operation():
            self.logger.info("🚀 开始创建Refunding和Close表系统")
            
            # 1. 检查源表
            if not self.check_source_tables():
                raise DatabaseError("源表检查失败")
            
            # 2. 创建新表
            self.create_refunding_close_tables()
            
            # 3. 迁移现有数据
            migration_stats = self.migrate_existing_data()
            
            # 4. 创建统计视图
            self.create_refunding_close_view()

            # 5. 创建汇总统计视图
            self.create_summary_statistics_view()

            self.logger.info("✅ Refunding和Close表系统创建完成")
            return migration_stats
        
        # 使用智能备份管理器执行安全操作
        operation_name = "创建Refunding和Close表系统"
        return self.backup_manager.safe_operation_wrapper(operation_name, _setup_operation)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='创建Refunding和Close表脚本')
    parser.add_argument('--db_path', help='数据库路径')
    parser.add_argument('--migrate_data', action='store_true', help='是否迁移现有数据')
    
    args = parser.parse_args()
    
    try:
        # 创建表创建器
        creator = RefundingCloseTableCreator(args.db_path)
        
        # 运行完整设置
        migration_stats = creator.run_complete_setup()
        
        # 输出结果
        print(f"✅ Refunding和Close表系统创建成功")
        print(f"📊 数据迁移统计:")
        total_migrated = sum(migration_stats.values())
        print(f"  总计迁移: {total_migrated} 条记录")
        
        for table_name, count in migration_stats.items():
            if count > 0:
                print(f"  • {table_name}: {count} 条记录")
        
        print(f"\n🎯 创建的表:")
        print(f"  • IOT_Sales_Refunding")
        print(f"  • IOT_Sales_Close")
        print(f"  • ZERO_Sales_Refunding")
        print(f"  • ZERO_Sales_Close")
        
        print(f"\n📋 创建的视图:")
        print(f"  • Refunding_Close_Statistics - 详细统计视图（与Current_Equipment关联）")
        print(f"  • Refunding_Close_Summary - 汇总统计视图（各表统计信息）")
        
        return 0
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
