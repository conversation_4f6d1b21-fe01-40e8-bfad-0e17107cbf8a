# coding: utf-8
import os
import sys
import subprocess
import time
import logging
from datetime import datetime
import webbrowser

# 配置日志
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"test_runner_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger()

# 脚本路径
date_fix_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "修复日期匹配.py")
config_fix_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "修复配置.py")
test_system_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "测试系统.py")
report_generator_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "报告生成器.py")

def run_script(script_path, description):
    """运行指定的Python脚本"""
    try:
        logger.info(f"开始{description}: {script_path}")
        print(f"\n=== 开始{description} ===")
        
        result = subprocess.run([sys.executable, script_path], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"{description}成功")
            print(f"{description}成功")
            return True
        else:
            logger.error(f"{description}失败: {result.stderr}")
            print(f"{description}失败: {result.stderr}")
            return False
    
    except Exception as e:
        logger.error(f"{description}时出错: {str(e)}")
        print(f"{description}时出错: {str(e)}")
        return False

def main():
    print("\n===== 自动处理系统测试流程 =====\n")
    logger.info("===== 开始自动处理系统测试流程 =====")
    
    # 步骤1: 修复日期匹配
    print("步骤1: 修复日期匹配函数")
    if not run_script(date_fix_path, "修复日期匹配函数"):
        print("日期匹配修复失败，但将继续执行后续步骤")
    
    # 步骤2: 修复配置文件
    print("\n步骤2: 修复配置文件")
    if not run_script(config_fix_path, "修复配置文件"):
        print("配置文件修复失败，但将继续执行后续步骤")
    
    # 步骤3: 运行测试系统
    print("\n步骤3: 运行测试系统")
    if not run_script(test_system_path, "运行测试系统"):
        print("测试系统运行失败，但将继续执行后续步骤")
    
    # 等待一段时间，确保测试数据库写入完成
    time.sleep(2)
    
    # 步骤4: 生成系统报告
    print("\n步骤4: 生成系统报告")
    if not run_script(report_generator_path, "生成系统报告"):
        print("系统报告生成失败")
    
    print("\n===== 自动处理系统测试流程完成 =====\n")
    logger.info("===== 自动处理系统测试流程完成 =====")
    
    # 打开测试报告
    report_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "system_report.html")
    if os.path.exists(report_path):
        print(f"测试报告已生成: {report_path}")
        print("正在打开测试报告...")
        webbrowser.open(f"file://{os.path.abspath(report_path)}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())