#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合检查AppLogger修复 - 全面验证数据导入功能
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """测试基本导入"""
    print("🔧 1. 测试基本导入")
    print("-" * 40)
    
    try:
        # 测试utils.logger
        from utils.logger import get_logger, AppLogger
        print("✅ utils.logger 导入成功")
        
        # 测试AppLogger功能
        logger = get_logger('test')
        print(f"✅ get_logger 成功，类型: {type(logger).__name__}")
        
        # 测试addHandler
        import logging
        handler = logging.StreamHandler()
        logger.addHandler(handler)
        print("✅ addHandler 方法调用成功")
        
        # 测试日志记录
        logger.info("测试日志消息")
        print("✅ 日志记录成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本导入测试失败: {e}")
        return False

def test_data_import_processor():
    """测试数据导入处理器"""
    print("\n🔧 2. 测试数据导入处理器")
    print("-" * 40)
    
    try:
        from data_import_optimized import DataImportProcessor
        print("✅ DataImportProcessor 导入成功")
        
        # 创建实例
        processor = DataImportProcessor()
        print("✅ DataImportProcessor 实例创建成功")
        
        # 测试基本方法
        processor.logger.info("测试处理器日志")
        print("✅ 处理器日志记录成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据导入处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_logging():
    """测试文件日志功能"""
    print("\n🔧 3. 测试文件日志功能")
    print("-" * 40)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试文件日志设置
        test_file = "C:/Users/<USER>/Desktop/test_file.xlsx"
        processor.setup_file_based_logging(test_file)
        print("✅ 文件日志设置成功")
        
        if processor.log_file_path:
            print(f"✅ 日志文件路径: {processor.log_file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件日志测试失败: {e}")
        return False

def test_platform_detection():
    """测试平台检测逻辑"""
    print("\n🔧 4. 测试平台检测逻辑")
    print("-" * 40)
    
    try:
        # 模拟平台检测逻辑
        test_files = [
            "040725 CHINA ZERO.xlsx",
            "040725 CHINA IOT.xlsx", 
            "040725 CHINA ZEROIOT.xlsx",
            "040725 CHINA APP.xlsx",
            "040725 CHINA.xlsx"
        ]
        
        for filename in test_files:
            filename_upper = filename.upper()
            
            if 'ZEROIOT' in filename_upper:
                platform = 'IOT'
            elif 'ZERO' in filename_upper and 'IOT' not in filename_upper:
                platform = 'ZERO'
            elif 'IOT' in filename_upper:
                platform = 'IOT'
            elif 'APP' in filename_upper:
                platform = 'APP'
            else:
                platform = 'APP'  # 默认
            
            print(f"✅ {filename} → {platform}")
        
        return True
        
    except Exception as e:
        print(f"❌ 平台检测测试失败: {e}")
        return False

def test_command_line_interface():
    """测试命令行接口"""
    print("\n🔧 5. 测试命令行接口")
    print("-" * 40)
    
    try:
        import subprocess
        
        # 测试帮助命令
        result = subprocess.run([
            sys.executable, 'data_import_optimized.py', '--help'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 命令行帮助正常")
        else:
            print(f"⚠️ 命令行帮助返回码: {result.returncode}")
        
        return True
        
    except Exception as e:
        print(f"❌ 命令行接口测试失败: {e}")
        return False

def test_error_scenarios():
    """测试错误场景"""
    print("\n🔧 6. 测试错误场景")
    print("-" * 40)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试不存在的文件
        try:
            processor.setup_file_based_logging("不存在的文件.xlsx")
            print("✅ 不存在文件处理正常")
        except Exception as e:
            print(f"✅ 不存在文件错误处理: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误场景测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 AppLogger修复综合检查")
    print("=" * 60)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("数据导入处理器", test_data_import_processor),
        ("文件日志功能", test_file_logging),
        ("平台检测逻辑", test_platform_detection),
        ("命令行接口", test_command_line_interface),
        ("错误场景", test_error_scenarios)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 综合检查结果")
    print("=" * 60)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if i < passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过")
        print("✅ AppLogger错误已完全修复")
        print("✅ 数据导入功能应该能正常工作")
        print("✅ 可以安全使用数据导入功能")
    elif passed >= total * 0.8:
        print("✅ 大部分测试通过")
        print("⚠️ 还有少量问题，但基本功能正常")
    else:
        print("❌ 多个测试失败")
        print("🔧 需要进一步检查和修复")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 综合检查{'成功' if success else '失败'}")
    input("按回车键退出...")
