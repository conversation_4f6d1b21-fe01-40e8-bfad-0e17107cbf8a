# 配置指南

## 📋 概述

本指南详细说明了数据处理应用系统的配置参数，包括日志优化配置、数据库配置、界面配置等。通过合理的配置，可以显著改善系统性能和用户体验。

## 🔧 配置文件结构

### 配置文件位置
```
config/
├── app_config.json          # 应用主配置
├── database_config.json     # 数据库配置
├── logging_config.json      # 日志配置
└── app_config.ini          # 兼容性配置(INI格式)
```

### 配置加载顺序
1. 默认配置 (代码中定义)
2. JSON配置文件
3. INI配置文件 (向后兼容)
4. 环境变量覆盖

## 🚀 日志优化配置

### 配置节: log_optimization

这是本次优化新增的配置节，用于控制日志性能优化的各项参数。

#### 完整配置示例
```json
{
  "log_optimization": {
    "batch_update_interval_ms": 100,
    "stdout_batch_size": 20,
    "stderr_batch_size": 10,
    "completion_delay_seconds": 2,
    "enable_transaction_filter": true,
    "enable_batch_update": true
  }
}
```

#### 参数详解

##### batch_update_interval_ms
- **类型**: 整数
- **默认值**: 100
- **单位**: 毫秒
- **说明**: 批量更新的时间间隔
- **影响**: 
  - 值越小，实时性越好，但性能开销越大
  - 值越大，性能越好，但实时性稍差
- **建议范围**: 50-200ms
- **调优指导**:
  ```json
  // 高性能硬件
  "batch_update_interval_ms": 50
  
  // 普通硬件
  "batch_update_interval_ms": 100
  
  // 低端硬件
  "batch_update_interval_ms": 200
  ```

##### stdout_batch_size
- **类型**: 整数
- **默认值**: 20
- **单位**: 条数
- **说明**: 标准输出日志的批量大小
- **影响**:
  - 值越小，实时性越好，但批量效果越差
  - 值越大，批量效果越好，但可能影响实时性
- **建议范围**: 10-50条
- **调优指导**:
  ```json
  // 实时性优先
  "stdout_batch_size": 10
  
  // 平衡配置
  "stdout_batch_size": 20
  
  // 性能优先
  "stdout_batch_size": 50
  ```

##### stderr_batch_size
- **类型**: 整数
- **默认值**: 10
- **单位**: 条数
- **说明**: 错误输出日志的批量大小
- **特点**: 通常比stdout_batch_size小，因为错误信息更重要
- **建议范围**: 5-20条
- **调优指导**:
  ```json
  // 错误信息及时显示
  "stderr_batch_size": 5
  
  // 平衡配置
  "stderr_batch_size": 10
  
  // 批量优化
  "stderr_batch_size": 20
  ```

##### completion_delay_seconds
- **类型**: 数字(支持小数)
- **默认值**: 2
- **单位**: 秒
- **说明**: 完成检测的延迟时间
- **目的**: 等待后台清理任务完成，避免过早显示完成状态
- **建议范围**: 1-5秒
- **调优指导**:
  ```json
  // 快速处理场景
  "completion_delay_seconds": 1
  
  // 标准配置
  "completion_delay_seconds": 2
  
  // 复杂处理场景
  "completion_delay_seconds": 5
  ```

##### enable_transaction_filter
- **类型**: 布尔值
- **默认值**: true
- **说明**: 是否启用Transaction ID日志过滤
- **效果**: 启用后可过滤90%以上的Transaction ID调试日志
- **使用场景**:
  ```json
  // 生产环境 - 启用过滤，提升性能
  "enable_transaction_filter": true
  
  // 调试环境 - 禁用过滤，查看所有日志
  "enable_transaction_filter": false
  ```

##### enable_batch_update
- **类型**: 布尔值
- **默认值**: true
- **说明**: 是否启用批量更新机制
- **效果**: 启用后可显著改善GUI响应性能
- **使用场景**:
  ```json
  // 正常使用 - 启用批量更新
  "enable_batch_update": true
  
  // 调试模式 - 禁用批量更新，实时显示
  "enable_batch_update": false
  ```

## 📊 其他重要配置

### 数据库配置 (database)
```json
{
  "database": {
    "path": "database/sales_reports.db",
    "backup_dir": "database/backups",
    "max_backups": 10,
    "connection_pool_size": 10,
    "timeout": 30.0
  }
}
```

### 处理配置 (processing)
```json
{
  "processing": {
    "batch_size": 1000,
    "timeout": 300,
    "max_workers": 4,
    "chunk_size": 10000,
    "progress_update_interval": 100
  }
}
```

### 界面配置 (ui)
```json
{
  "ui": {
    "window_width": 900,
    "window_height": 700,
    "theme": "default",
    "auto_save_config": true
  }
}
```

### 日志配置 (logging)
```json
{
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "max_size": 10485760,
    "backup_count": 5,
    "log_dir": "logs"
  }
}
```

## 🎯 场景化配置方案

### 生产环境配置
```json
{
  "log_optimization": {
    "batch_update_interval_ms": 100,
    "stdout_batch_size": 20,
    "stderr_batch_size": 10,
    "completion_delay_seconds": 2,
    "enable_transaction_filter": true,
    "enable_batch_update": true
  },
  "logging": {
    "level": "INFO"
  }
}
```

### 开发调试配置
```json
{
  "log_optimization": {
    "batch_update_interval_ms": 50,
    "stdout_batch_size": 5,
    "stderr_batch_size": 1,
    "completion_delay_seconds": 1,
    "enable_transaction_filter": false,
    "enable_batch_update": false
  },
  "logging": {
    "level": "DEBUG"
  }
}
```

### 高性能配置
```json
{
  "log_optimization": {
    "batch_update_interval_ms": 50,
    "stdout_batch_size": 50,
    "stderr_batch_size": 20,
    "completion_delay_seconds": 1,
    "enable_transaction_filter": true,
    "enable_batch_update": true
  }
}
```

### 低端硬件配置
```json
{
  "log_optimization": {
    "batch_update_interval_ms": 200,
    "stdout_batch_size": 10,
    "stderr_batch_size": 5,
    "completion_delay_seconds": 3,
    "enable_transaction_filter": true,
    "enable_batch_update": true
  },
  "processing": {
    "max_workers": 2,
    "batch_size": 500
  }
}
```

## 🔧 配置管理

### 配置文件创建
如果配置文件不存在，系统会使用默认配置。可以手动创建配置文件：

```bash
mkdir config
echo '{"log_optimization": {"batch_update_interval_ms": 100}}' > config/app_config.json
```

### 配置验证
系统启动时会自动验证配置参数：
- 类型检查
- 范围验证
- 默认值回退

### 配置热更新
部分配置支持热更新，无需重启应用：
- 日志级别
- 批量更新参数
- 过滤开关

### 环境变量覆盖
支持通过环境变量覆盖配置：
```bash
# Windows
set LOG_BATCH_SIZE=30
set LOG_UPDATE_INTERVAL=150

# Linux/Mac
export LOG_BATCH_SIZE=30
export LOG_UPDATE_INTERVAL=150
```

## 🐛 配置故障排除

### 常见配置问题

#### 1. 配置文件格式错误
**现象**: 应用启动失败或使用默认配置
**原因**: JSON格式错误
**解决**: 使用JSON验证工具检查格式

#### 2. 参数类型错误
**现象**: 配置不生效，使用默认值
**原因**: 参数类型不匹配
**解决**: 确保数字类型使用数字，布尔类型使用true/false

#### 3. 配置不生效
**现象**: 修改配置后无变化
**原因**: 
- 配置文件路径错误
- 需要重启应用
- 环境变量覆盖

**解决**:
1. 检查配置文件位置
2. 重启应用
3. 检查环境变量

#### 4. 性能配置不当
**现象**: 配置后性能反而下降
**原因**: 参数设置不合理
**解决**: 参考推荐配置，逐步调整

### 配置诊断工具
```python
# 检查当前配置
python -c "
from utils.config_manager import ConfigManager
config = ConfigManager()
print('当前日志优化配置:')
for key in ['batch_update_interval_ms', 'stdout_batch_size', 'stderr_batch_size']:
    print(f'{key}: {config.get(\"log_optimization\", key)}')
"
```

## 📚 相关文档

- [性能优化文档](performance_optimization.md) - 优化技术详解
- [测试文档](../tests/README.md) - 配置效果验证
- [故障排除文档](troubleshooting.md) - 问题诊断和解决

---

**配置是系统性能的关键，建议根据实际使用场景选择合适的配置方案。**
