google_access_context_manager_access_level
google_access_context_manager_access_level_condition
google_access_context_manager_access_levels
google_access_context_manager_access_policy
google_access_context_manager_access_policy_iam
google_access_context_manager_authorized_orgs_desc
google_access_context_manager_egress_policy
google_access_context_manager_gcp_user_access_binding
google_access_context_manager_ingress_policy
google_access_context_manager_service_perimeter
google_access_context_manager_service_perimeter_egress_policy
google_access_context_manager_service_perimeter_ingress_policy
google_access_context_manager_service_perimeter_resource
google_access_context_manager_service_perimeters
google_active_directory_domain
google_active_directory_domain_trust
google_active_directory_peering
google_alloydb_backup
google_alloydb_cluster
google_alloydb_instance
google_alloydb_user
google_api_gateway_api
google_api_gateway_api_config
google_api_gateway_api_config_iam
google_api_gateway_api_iam
google_api_gateway_gateway
google_api_gateway_gateway_iam
google_apigee_addons_config
google_apigee_endpoint_attachment
google_apigee_env_keystore
google_apigee_env_references
google_apigee_envgroup
google_apigee_envgroup_attachment
google_apigee_environment
google_apigee_environment_iam
google_apigee_flowhook
google_apigee_instance
google_apigee_instance_attachment
google_apigee_keystores_aliases_key_cert_file
google_apigee_keystores_aliases_pkcs12
google_apigee_keystores_aliases_self_signed_cert
google_apigee_nat_address
google_apigee_organization
google_apigee_sharedflow
google_apigee_sharedflow_deployment
google_apigee_sync_authorization
google_apigee_target_server
google_apikeys_key
google_app_engine_application
google_app_engine_application_url_dispatch_rules
google_app_engine_domain_mapping
google_app_engine_firewall_rule
google_app_engine_flexible_app_version
google_app_engine_service_network_settings
google_app_engine_service_split_traffic
google_app_engine_standard_app_version
google_artifact_registry_repository
google_artifact_registry_repository_iam
google_artifact_registry_vpcsc_config
google_assured_workloads_workload
google_backup_dr_management_server
google_beyondcorp_app_connection
google_beyondcorp_app_connector
google_beyondcorp_app_gateway
google_biglake_catalog
google_biglake_database
google_biglake_table
google_bigquery_analytics_hub_data_exchange
google_bigquery_analytics_hub_data_exchange_iam
google_bigquery_analytics_hub_listing
google_bigquery_analytics_hub_listing_iam
google_bigquery_bi_reservation
google_bigquery_capacity_commitment
google_bigquery_connection
google_bigquery_connection_iam
google_bigquery_data_transfer_config
google_bigquery_datapolicy_data_policy
google_bigquery_datapolicy_data_policy_iam
google_bigquery_dataset
google_bigquery_dataset_access
google_bigquery_dataset_iam
google_bigquery_job
google_bigquery_reservation
google_bigquery_reservation_assignment
google_bigquery_routine
google_bigquery_table
google_bigquery_table_iam
google_bigtable_app_profile
google_bigtable_gc_policy
google_bigtable_instance
google_bigtable_instance_iam
google_bigtable_table
google_bigtable_table_iam
google_billing_account_iam
google_billing_budget
google_billing_project_info
google_billing_subaccount
google_binary_authorization_attestor
google_binary_authorization_attestor_iam
google_binary_authorization_policy
google_blockchain_node_engine_blockchain_nodes
google_certificate_manager_certificate
google_certificate_manager_certificate_issuance_config
google_certificate_manager_certificate_map
google_certificate_manager_certificate_map_entry
google_certificate_manager_dns_authorization
google_certificate_manager_trust_config
google_cloud_asset_folder_feed
google_cloud_asset_organization_feed
google_cloud_asset_project_feed
google_cloud_identity_group
google_cloud_identity_group_membership
google_cloud_ids_endpoint
google_cloud_run_domain_mapping
google_cloud_run_service
google_cloud_run_service_iam
google_cloud_run_v2_job
google_cloud_run_v2_job_iam
google_cloud_run_v2_service
google_cloud_run_v2_service_iam
google_cloud_scheduler_job
google_cloud_tasks_queue
google_cloud_tasks_queue_iam
google_cloudbuild_bitbucket_server_config
google_cloudbuild_trigger
google_cloudbuild_worker_pool
google_cloudbuildv2_connection
google_cloudbuildv2_connection_iam
google_cloudbuildv2_repository
google_clouddeploy_automation
google_clouddeploy_delivery_pipeline
google_clouddeploy_target
google_clouddomains_registration
google_cloudfunctions2_function
google_cloudfunctions2_function_iam
google_cloudfunctions_function
google_cloudfunctions_function_iam
google_composer_environment
google_compute_address
google_compute_attached_disk
google_compute_autoscaler
google_compute_backend_bucket
google_compute_backend_bucket_iam
google_compute_backend_bucket_signed_url_key
google_compute_backend_service
google_compute_backend_service_iam
google_compute_backend_service_signed_url_key
google_compute_disk
google_compute_disk_async_replication
google_compute_disk_iam
google_compute_disk_resource_policy_attachment
google_compute_external_vpn_gateway
google_compute_firewall
google_compute_firewall_policy
google_compute_firewall_policy_association
google_compute_firewall_policy_rule
google_compute_forwarding_rule
google_compute_global_address
google_compute_global_forwarding_rule
google_compute_global_network_endpoint
google_compute_global_network_endpoint_group
google_compute_ha_vpn_gateway
google_compute_health_check
google_compute_http_health_check
google_compute_https_health_check
google_compute_image
google_compute_image_iam
google_compute_instance
google_compute_instance_from_machine_image
google_compute_instance_from_template
google_compute_instance_group
google_compute_instance_group_manager
google_compute_instance_group_named_port
google_compute_instance_iam
google_compute_instance_settings
google_compute_instance_template
google_compute_interconnect_attachment
google_compute_machine_image
google_compute_machine_image_iam
google_compute_managed_ssl_certificate
google_compute_network
google_compute_network_attachment
google_compute_network_edge_security_service
google_compute_network_endpoint
google_compute_network_endpoint_group
google_compute_network_endpoints
google_compute_network_firewall_policy
google_compute_network_firewall_policy_association
google_compute_network_firewall_policy_rule
google_compute_network_peering
google_compute_network_peering_routes_config
google_compute_node_group
google_compute_node_template
google_compute_organization_security_policy
google_compute_organization_security_policy_association
google_compute_organization_security_policy_rule
google_compute_packet_mirroring
google_compute_per_instance_config
google_compute_project_default_network_tier
google_compute_project_metadata
google_compute_project_metadata_item
google_compute_public_advertised_prefix
google_compute_public_delegated_prefix
google_compute_region_autoscaler
google_compute_region_backend_service
google_compute_region_backend_service_iam
google_compute_region_commitment
google_compute_region_disk
google_compute_region_disk_iam
google_compute_region_disk_resource_policy_attachment
google_compute_region_health_check
google_compute_region_instance_group_manager
google_compute_region_instance_template
google_compute_region_network_endpoint
google_compute_region_network_endpoint_group
google_compute_region_network_firewall_policy
google_compute_region_network_firewall_policy_association
google_compute_region_network_firewall_policy_rule
google_compute_region_per_instance_config
google_compute_region_security_policy
google_compute_region_security_policy_rule
google_compute_region_ssl_certificate
google_compute_region_ssl_policy
google_compute_region_target_http_proxy
google_compute_region_target_https_proxy
google_compute_region_target_tcp_proxy
google_compute_region_url_map
google_compute_reservation
google_compute_resource_policy
google_compute_route
google_compute_router
google_compute_router_interface
google_compute_router_nat
google_compute_router_peer
google_compute_security_policy
google_compute_service_attachment
google_compute_shared_vpc_host_project
google_compute_shared_vpc_service_project
google_compute_snapshot
google_compute_snapshot_iam
google_compute_ssl_certificate
google_compute_ssl_policy
google_compute_subnetwork
google_compute_subnetwork_iam
google_compute_target_grpc_proxy
google_compute_target_http_proxy
google_compute_target_https_proxy
google_compute_target_instance
google_compute_target_pool
google_compute_target_ssl_proxy
google_compute_target_tcp_proxy
google_compute_url_map
google_compute_vpn_gateway
google_compute_vpn_tunnel
google_container_analysis_note
google_container_analysis_note_iam
google_container_analysis_occurrence
google_container_attached_cluster
google_container_aws_cluster
google_container_aws_node_pool
google_container_azure_client
google_container_azure_cluster
google_container_azure_node_pool
google_container_cluster
google_container_node_pool
google_container_registry
google_data_catalog_entry
google_data_catalog_entry_group
google_data_catalog_entry_group_iam
google_data_catalog_policy_tag
google_data_catalog_policy_tag_iam
google_data_catalog_tag
google_data_catalog_tag_template
google_data_catalog_tag_template_iam
google_data_catalog_taxonomy
google_data_catalog_taxonomy_iam
google_data_fusion_instance
google_data_fusion_instance_iam
google_data_loss_prevention_deidentify_template
google_data_loss_prevention_inspect_template
google_data_loss_prevention_job_trigger
google_data_loss_prevention_stored_info_type
google_data_pipeline_pipeline
google_database_migration_service_connection_profile
google_database_migration_service_private_connection
google_dataflow_flex_template_job
google_dataflow_job
google_dataform_repository
google_dataform_repository_iam
google_dataform_repository_release_config
google_dataform_repository_workflow_config
google_dataplex_asset
google_dataplex_asset_iam
google_dataplex_datascan
google_dataplex_datascan_iam
google_dataplex_lake
google_dataplex_lake_iam
google_dataplex_task
google_dataplex_task_iam
google_dataplex_zone
google_dataplex_zone_iam
google_dataproc_autoscaling_policy
google_dataproc_autoscaling_policy_iam
google_dataproc_cluster
google_dataproc_cluster_iam
google_dataproc_job
google_dataproc_job_iam
google_dataproc_metastore_federation
google_dataproc_metastore_federation_iam
google_dataproc_metastore_service
google_dataproc_metastore_service_iam
google_dataproc_workflow_template
google_datastore_index
google_datastream_connection_profile
google_datastream_private_connection
google_datastream_stream
google_deployment_manager_deployment
google_dialogflow_agent
google_dialogflow_cx_agent
google_dialogflow_cx_entity_type
google_dialogflow_cx_environment
google_dialogflow_cx_flow
google_dialogflow_cx_intent
google_dialogflow_cx_page
google_dialogflow_cx_security_settings
google_dialogflow_cx_test_case
google_dialogflow_cx_version
google_dialogflow_cx_webhook
google_dialogflow_entity_type
google_dialogflow_fulfillment
google_dialogflow_intent
google_discovery_engine_chat_engine
google_discovery_engine_data_store
google_discovery_engine_search_engine
google_dns_managed_zone
google_dns_managed_zone_iam
google_dns_policy
google_dns_record_set
google_dns_response_policy
google_dns_response_policy_rule
google_document_ai_processor
google_document_ai_processor_default_version
google_document_ai_warehouse_document_schema
google_document_ai_warehouse_location
google_edgecontainer_cluster
google_edgecontainer_node_pool
google_edgecontainer_vpn_connection
google_edgenetwork_network
google_edgenetwork_subnet
google_endpoints_service
google_endpoints_service_consumers_iam
google_endpoints_service_iam
google_essential_contacts_contact
google_eventarc_channel
google_eventarc_google_channel_config
google_eventarc_trigger
google_filestore_backup
google_filestore_instance
google_filestore_snapshot
google_firebase_android_app
google_firebase_apple_app
google_firebase_database_instance
google_firebase_extensions_instance
google_firebase_hosting_channel
google_firebase_hosting_custom_domain
google_firebase_hosting_release
google_firebase_hosting_site
google_firebase_hosting_version
google_firebase_project
google_firebase_storage_bucket
google_firebase_web_app
google_firebaserules_release
google_firebaserules_ruleset
google_firestore_backup_schedule
google_firestore_database
google_firestore_document
google_firestore_field
google_firestore_index
google_folder
google_folder_access_approval_settings
google_folder_iam
google_folder_organization_policy
google_gke_backup_backup_plan
google_gke_backup_backup_plan_iam
google_gke_backup_restore_plan
google_gke_backup_restore_plan_iam
google_gke_hub_feature
google_gke_hub_feature_iam
google_gke_hub_feature_membership
google_gke_hub_fleet
google_gke_hub_membership
google_gke_hub_membership_binding
google_gke_hub_membership_iam
google_gke_hub_membership_rbac_role_binding
google_gke_hub_namespace
google_gke_hub_scope
google_gke_hub_scope_iam
google_gke_hub_scope_rbac_role_binding
google_gkeonprem_bare_metal_admin_cluster
google_gkeonprem_bare_metal_cluster
google_gkeonprem_bare_metal_node_pool
google_gkeonprem_vmware_cluster
google_gkeonprem_vmware_node_pool
google_healthcare_consent_store
google_healthcare_consent_store_iam
google_healthcare_dataset
google_healthcare_dataset_iam
google_healthcare_dicom_store
google_healthcare_dicom_store_iam
google_healthcare_fhir_store
google_healthcare_fhir_store_iam
google_healthcare_hl7_v2_store
google_healthcare_hl7_v2_store_iam
google_iam_access_boundary_policy
google_iam_deny_policy
google_iam_workforce_pool
google_iam_workforce_pool_provider
google_iam_workload_identity_pool
google_iam_workload_identity_pool_provider
google_iap_app_engine_service_iam
google_iap_app_engine_version_iam
google_iap_brand
google_iap_client
google_iap_tunnel_iam
google_iap_tunnel_instance_iam
google_iap_web_backend_service_iam
google_iap_web_iam
google_iap_web_region_backend_service_iam
google_iap_web_type_app_engine_iam
google_iap_web_type_compute_iam
google_identity_platform_config
google_identity_platform_default_supported_idp_config
google_identity_platform_inbound_saml_config
google_identity_platform_oauth_idp_config
google_identity_platform_project_default_config
google_identity_platform_tenant
google_identity_platform_tenant_default_supported_idp_config
google_identity_platform_tenant_inbound_saml_config
google_identity_platform_tenant_oauth_idp_config
google_integration_connectors_connection
google_integration_connectors_endpoint_attachment
google_kms_crypto_key
google_kms_crypto_key_iam
google_kms_crypto_key_version
google_kms_key_ring
google_kms_key_ring_iam
google_kms_key_ring_import_job
google_kms_secret_ciphertext
google_logging_billing_account_bucket_config
google_logging_billing_account_exclusion
google_logging_billing_account_sink
google_logging_folder_bucket_config
google_logging_folder_exclusion
google_logging_folder_settings
google_logging_folder_sink
google_logging_linked_dataset
google_logging_log_view
google_logging_metric
google_logging_organization_bucket_config
google_logging_organization_exclusion
google_logging_organization_settings
google_logging_organization_sink
google_logging_project_bucket_config
google_logging_project_exclusion
google_logging_project_sink
google_looker_instance
google_memcache_instance
google_migration_center_group
google_ml_engine_model
google_monitoring_alert_policy
google_monitoring_custom_service
google_monitoring_dashboard
google_monitoring_group
google_monitoring_metric_descriptor
google_monitoring_monitored_project
google_monitoring_notification_channel
google_monitoring_service
google_monitoring_slo
google_monitoring_uptime_check_config
google_netapp_active_directory
google_netapp_backup_policy
google_netapp_backup_vault
google_netapp_kmsconfig
google_netapp_storage_pool
google_netapp_volume
google_netapp_volume_snapshot
google_network_connectivity_hub
google_network_connectivity_policy_based_route
google_network_connectivity_service_connection_policy
google_network_connectivity_spoke
google_network_management_connectivity_test_resource
google_network_security_address_group
google_network_security_address_group_iam
google_network_security_authorization_policy
google_network_security_client_tls_policy
google_network_security_gateway_security_policy
google_network_security_gateway_security_policy_rule
google_network_security_security_profile
google_network_security_server_tls_policy
google_network_security_tls_inspection_policy
google_network_security_url_lists
google_network_services_edge_cache_keyset
google_network_services_edge_cache_origin
google_network_services_edge_cache_service
google_network_services_endpoint_policy
google_network_services_gateway
google_network_services_grpc_route
google_network_services_http_route
google_network_services_mesh
google_network_services_service_binding
google_network_services_tcp_route
google_network_services_tls_route
google_notebooks_environment
google_notebooks_instance
google_notebooks_instance_iam
google_notebooks_location
google_notebooks_runtime
google_notebooks_runtime_iam
google_org_policy_custom_constraint
google_org_policy_policy
google_organization_access_approval_settings
google_organization_iam
google_organization_iam_custom_role
google_organization_policy
google_os_config_guest_policies
google_os_config_os_policy_assignment
google_os_config_patch_deployment
google_os_login_ssh_public_key
google_privateca_ca_pool
google_privateca_ca_pool_iam
google_privateca_certificate
google_privateca_certificate_authority
google_privateca_certificate_template
google_privateca_certificate_template_iam
google_project
google_project_access_approval_settings
google_project_default_service_accounts
google_project_iam
google_project_iam_custom_role
google_project_organization_policy
google_project_service
google_project_service_identity
google_public_ca_external_account_key
google_pubsub_lite_reservation
google_pubsub_lite_subscription
google_pubsub_lite_topic
google_pubsub_schema
google_pubsub_schema_iam
google_pubsub_subscription
google_pubsub_subscription_iam
google_pubsub_topic
google_pubsub_topic_iam
google_recaptcha_enterprise_key
google_redis_cluster
google_redis_instance
google_resource_manager_lien
google_runtimeconfig_config
google_runtimeconfig_config_iam
google_runtimeconfig_variable
google_scc_event_threat_detection_custom_module
google_scc_folder_custom_module
google_scc_mute_config
google_scc_notification_config
google_scc_organization_custom_module
google_scc_project_custom_module
google_scc_source
google_scc_source_iam
google_secret_manager_secret
google_secret_manager_secret_iam
google_secret_manager_secret_version
google_secure_source_manager_instance
google_secure_source_manager_instance_iam
google_security_scanner_scan_config
google_securityposture_posture
google_securityposture_posture_deployment
google_service_account
google_service_account_iam
google_service_account_key
google_service_directory_endpoint
google_service_directory_namespace
google_service_directory_namespace_iam
google_service_directory_service
google_service_directory_service_iam
google_service_networking_connection
google_service_networking_peered_dns_domain
google_service_usage_consumer_quota_override
google_sourcerepo_repository
google_sourcerepo_repository_iam
google_spanner_database
google_spanner_database_iam
google_spanner_instance
google_spanner_instance_iam
google_sql_database
google_sql_database_instance
google_sql_source_representation_instance
google_sql_ssl_cert
google_sql_user
google_storage_bucket
google_storage_bucket_access_control
google_storage_bucket_acl
google_storage_bucket_iam
google_storage_bucket_object
google_storage_default_object_access_control
google_storage_default_object_acl
google_storage_hmac_key
google_storage_insights_report_config
google_storage_notification
google_storage_object_access_control
google_storage_object_acl
google_storage_transfer_agent_pool
google_storage_transfer_job
google_tags_location_tag_binding
google_tags_tag_binding
google_tags_tag_key
google_tags_tag_key_iam
google_tags_tag_value
google_tags_tag_value_iam
google_tpu_node
google_tpu_v2_vm
google_usage_export_bucket
google_vertex_ai_dataset
google_vertex_ai_endpoint
google_vertex_ai_endpoint_iam
google_vertex_ai_feature_group
google_vertex_ai_feature_group_feature
google_vertex_ai_feature_online_store
google_vertex_ai_feature_online_store_featureview
google_vertex_ai_featurestore
google_vertex_ai_featurestore_entitytype
google_vertex_ai_featurestore_entitytype_feature
google_vertex_ai_featurestore_entitytype_iam
google_vertex_ai_featurestore_iam
google_vertex_ai_index
google_vertex_ai_index_endpoint
google_vertex_ai_metadata_store
google_vertex_ai_tensorboard
google_vmwareengine_cluster
google_vmwareengine_external_access_rule
google_vmwareengine_external_address
google_vmwareengine_network
google_vmwareengine_network_peering
google_vmwareengine_network_policy
google_vmwareengine_private_cloud
google_vmwareengine_subnet
google_vpc_access_connector
google_workbench_instance
google_workbench_instance_iam
google_workflows_workflow
google_workstations_workstation
google_workstations_workstation_cluster
google_workstations_workstation_config
google_workstations_workstation_config_iam
google_workstations_workstation_iam
