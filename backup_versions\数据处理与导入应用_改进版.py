# coding: utf-8
import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import subprocess
import configparser
from datetime import datetime
import logging
import sqlite3
import shutil
import re
import pandas as pd
import traceback
import queue
import json
from typing import Dict, List, Optional, Callable

# 导入自定义异常处理系统
from 自定义异常处理系统 import (
    DataAppError, FileError, FileNotFoundError, FileValidationError, FileProcessingError,
    DatabaseError, DatabaseConnectionError, DatabaseOperationError,
    ConfigError, ConfigValidationError, MissingConfigError,
    ProcessError, ProcessExecutionError, ScriptNotFoundError,
    ExceptionHandler, exception_handler
)

# 配置日志
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"data_app_{datetime.now().strftime('%Y%m%d')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger()

class SafeGUIUpdater:
    """线程安全的GUI更新器"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.message_queue = queue.Queue()
        self.log_widgets: Dict[str, tk.Text] = {}
        self.status_var: Optional[tk.StringVar] = None
        self._start_queue_processor()
    
    def _start_queue_processor(self):
        """启动队列处理器"""
        self.root.after(100, self._process_queue)
    
    def _process_queue(self):
        """处理消息队列"""
        try:
            while True:
                message_type, data = self.message_queue.get_nowait()
                
                if message_type == "log":
                    self._update_log_widget(data["tab_type"], data["message"])
                elif message_type == "status":
                    self._update_status(data["status"])
                elif message_type == "button_state":
                    self._update_button_state(data["button"], data["state"])
                    
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self._process_queue)
    
    def _update_log_widget(self, tab_type: str, message: str):
        """更新日志控件"""
        widget = self.log_widgets.get(tab_type)
        if widget:
            widget.config(state=tk.NORMAL)
            timestamp = datetime.now().strftime('%H:%M:%S')
            widget.insert(tk.END, f"[{timestamp}] {message}\n")
            widget.see(tk.END)
            widget.config(state=tk.DISABLED)
    
    def _update_status(self, status: str):
        """更新状态栏"""
        if self.status_var:
            self.status_var.set(status)
    
    def _update_button_state(self, button: tk.Button, state: str):
        """更新按钮状态"""
        if button:
            button.config(state=state)
    
    def register_log_widget(self, tab_type: str, widget: tk.Text):
        """注册日志控件"""
        self.log_widgets[tab_type] = widget
    
    def register_status_var(self, status_var: tk.StringVar):
        """注册状态变量"""
        self.status_var = status_var
    
    def safe_log(self, message: str, tab_type: str = "general"):
        """线程安全的日志记录"""
        self.message_queue.put(("log", {"tab_type": tab_type, "message": message}))
        logger.info(f"[{tab_type}] {message}")
    
    def safe_status_update(self, status: str):
        """线程安全的状态更新"""
        self.message_queue.put(("status", {"status": status}))
    
    def safe_button_update(self, button: tk.Button, state: str):
        """线程安全的按钮状态更新"""
        self.message_queue.put(("button_state", {"button": button, "state": state}))

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.ini"):
        self.config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), config_file)
        self.config = configparser.ConfigParser()
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        self.config['Database'] = {
            'db_path': os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db")
        }
        
        self.config['Scripts'] = {
            'report_script': 'report 脚本 3.0.py',
            'report_three_days': 'report 三天报告 3.0.py',
            'refund_script': 'Refund_process_修复版.py',
            'data_import_script': '数据导入脚本.py'
        }
        
        self.config['UI'] = {
            'window_width': '900',
            'window_height': '700',
            'theme': 'iphone_style'
        }
        
        self.config['Files'] = {
            'file_separator': '||',  # 更安全的分隔符
            'temp_dir': 'temp_data',
            'backup_dir': 'backups'
        }
        
        self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def get(self, section: str, key: str, fallback: str = ""):
        """获取配置值"""
        return self.config.get(section, key, fallback=fallback)
    
    def set(self, section: str, key: str, value: str):
        """设置配置值"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
    
    def get_db_path(self) -> str:
        """获取数据库路径"""
        return self.get('Database', 'db_path', 
                       os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db"))
    
    def get_script_path(self, script_key: str) -> str:
        """获取脚本路径"""
        script_name = self.get('Scripts', script_key)
        if not script_name:
            raise MissingConfigError(script_key, 'Scripts')

        script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), script_name)
        if not os.path.exists(script_path):
            raise ScriptNotFoundError(script_path, f"{script_key}脚本")

        return script_path
    
    def get_scripts_list(self) -> List[str]:
        """获取脚本列表"""
        return [
            self.get('Scripts', 'report_script'),
            self.get('Scripts', 'report_three_days')
        ]

class FileManager:
    """文件管理器"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.separator = config.get('Files', 'file_separator', '||')
    
    def join_file_paths(self, file_paths: List[str]) -> str:
        """连接文件路径"""
        return self.separator.join(file_paths)
    
    def split_file_paths(self, file_paths_str: str) -> List[str]:
        """分割文件路径"""
        if not file_paths_str:
            return []
        return [f.strip() for f in file_paths_str.split(self.separator) if f.strip()]
    
    def validate_files(self, file_paths: List[str]) -> tuple[bool, str]:
        """验证文件是否存在"""
        try:
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    raise FileNotFoundError(file_path, "验证")
            return True, ""
        except FileNotFoundError as e:
            return False, e.get_user_message()
    
    def get_temp_dir(self) -> str:
        """获取临时目录"""
        temp_dir = os.path.join(
            os.path.dirname(os.path.abspath(__file__)), 
            self.config.get('Files', 'temp_dir', 'temp_data')
        )
        os.makedirs(temp_dir, exist_ok=True)
        return temp_dir
    
    def create_temp_file(self, suffix: str = '.xlsx') -> str:
        """创建临时文件"""
        temp_dir = self.get_temp_dir()
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        return os.path.join(temp_dir, f"temp_{timestamp}{suffix}")
    
    def cleanup_temp_file(self, file_path: str):
        """清理临时文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            logger.warning(f"清理临时文件失败: {file_path}, 错误: {e}")

class ProcessRunner:
    """进程运行器"""
    
    def __init__(self, gui_updater: SafeGUIUpdater):
        self.gui_updater = gui_updater
    
    def run_process(self, cmd: List[str], tab_type: str, 
                   success_callback: Optional[Callable] = None,
                   error_callback: Optional[Callable] = None) -> bool:
        """运行进程并实时更新GUI"""
        try:
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                universal_newlines=True,
                encoding='utf-8',
                bufsize=1
            )
            
            # 创建线程读取输出
            def read_output(pipe, is_error=False):
                for line in pipe:
                    line = line.strip()
                    if line:
                        if is_error:
                            self.gui_updater.safe_log(f"错误: {line}", tab_type)
                        else:
                            # 检查是否是进度信息
                            if "[" in line and "]" in line and "%" in line:
                                self.gui_updater.safe_status_update(line)
                            self.gui_updater.safe_log(line, tab_type)
            
            # 启动读取线程
            stdout_thread = threading.Thread(target=read_output, args=(process.stdout,))
            stderr_thread = threading.Thread(target=read_output, args=(process.stderr, True))
            stdout_thread.daemon = True
            stderr_thread.daemon = True
            stdout_thread.start()
            stderr_thread.start()
            
            # 等待完成
            process.wait()
            stdout_thread.join()
            stderr_thread.join()
            
            # 处理结果
            if process.returncode == 0:
                if success_callback:
                    success_callback()
                return True
            else:
                if error_callback:
                    error_callback(process.returncode)
                return False
                
        except Exception as e:
            self.gui_updater.safe_log(f"运行进程时出错: {str(e)}", tab_type)
            if error_callback:
                error_callback(-1)
            return False

class BaseTab:
    """选项卡基类"""
    
    def __init__(self, parent, gui_updater: SafeGUIUpdater, config: ConfigManager, 
                 file_manager: FileManager, process_runner: ProcessRunner):
        self.parent = parent
        self.gui_updater = gui_updater
        self.config = config
        self.file_manager = file_manager
        self.process_runner = process_runner
        self.tab_type = "general"
        
        # 创建主框架
        self.frame = ttk.Frame(parent, padding="10")
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建UI
        self.create_ui()
        
        # 注册日志控件
        if hasattr(self, 'log_text'):
            self.gui_updater.register_log_widget(self.tab_type, self.log_text)
    
    def create_ui(self):
        """创建UI - 子类需要实现"""
        pass
    
    def log_message(self, message: str):
        """记录日志消息"""
        self.gui_updater.safe_log(message, self.tab_type)
    
    def create_log_area(self, title: str = "日志") -> tk.Text:
        """创建日志显示区域"""
        log_frame = ttk.LabelFrame(self.frame, text=title, padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建文本框和滚动条
        log_text = tk.Text(log_frame, wrap=tk.WORD, width=80, height=15)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=log_text.yview)
        log_text.configure(yscrollcommand=scrollbar.set)
        
        log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置只读
        log_text.config(state=tk.DISABLED)
        
        return log_text

class ProcessingTab(BaseTab):
    """数据处理选项卡"""

    def __init__(self, parent, gui_updater: SafeGUIUpdater, config: ConfigManager,
                 file_manager: FileManager, process_runner: ProcessRunner):
        self.tab_type = "processing"
        super().__init__(parent, gui_updater, config, file_manager, process_runner)

    def create_ui(self):
        """创建数据处理UI"""
        # 文件选择区域
        file_frame = ttk.LabelFrame(self.frame, text="文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=10)

        # 第一文件（SETTLEMENT文件）
        ttk.Label(file_frame, text="第一文件 (SETTLEMENT):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.file1_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file1_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_file1).grid(row=0, column=2, padx=5, pady=5)

        # 第一文件Sheet名称
        ttk.Label(file_frame, text="第一文件Sheet名称:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.sheet_name_var = tk.StringVar(value="TRANSACTION_LIST")
        ttk.Entry(file_frame, textvariable=self.sheet_name_var, width=50).grid(row=1, column=1, padx=5, pady=5)

        # 第二文件（CHINA文件）
        ttk.Label(file_frame, text="第二文件 (CHINA):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.file2_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file2_var, width=50).grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_file2).grid(row=2, column=2, padx=5, pady=5)

        # 脚本选择区域
        script_frame = ttk.LabelFrame(self.frame, text="处理脚本选择", padding="10")
        script_frame.pack(fill=tk.X, pady=10)

        self.script_var = tk.StringVar()
        scripts = self.config.get_scripts_list()
        if scripts:
            self.script_var.set(scripts[0])

        for i, script in enumerate(scripts):
            ttk.Radiobutton(script_frame, text=script, value=script,
                          variable=self.script_var).grid(row=0, column=i, padx=20, pady=5, sticky=tk.W)

        # 操作按钮区域
        button_frame = ttk.Frame(self.frame, padding="10")
        button_frame.pack(fill=tk.X, pady=10)

        # 处理按钮
        self.process_button = ttk.Button(button_frame, text="开始处理", command=self.process_files)
        self.process_button.pack(side=tk.LEFT, padx=5)

        # 清空按钮
        ttk.Button(button_frame, text="清空选择", command=self.clear_selection).pack(side=tk.LEFT, padx=5)

        # 创建日志区域
        self.log_text = self.create_log_area("处理日志")

        # 显示初始提示信息
        self.log_message("请使用浏览按钮选择文件")

    def browse_file1(self):
        """浏览选择第一文件"""
        filename = filedialog.askopenfilename(
            title="选择第一文件 (SETTLEMENT)",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.file1_var.set(filename)
            basename = os.path.basename(filename)
            self.log_message(f"已选择第一文件: {basename}")

    def browse_file2(self):
        """浏览选择第二文件"""
        filename = filedialog.askopenfilename(
            title="选择第二文件 (CHINA)",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.file2_var.set(filename)
            basename = os.path.basename(filename)
            self.log_message(f"已选择第二文件: {basename}")

    def clear_selection(self):
        """清空文件选择"""
        self.file1_var.set("")
        self.file2_var.set("")
        self.log_message("已清空文件选择")

    @exception_handler("文件处理")
    def process_files(self):
        """处理文件"""
        file1_path = self.file1_var.get().strip()
        file2_path = self.file2_var.get().strip()
        script = self.script_var.get()

        # 验证输入
        if not file1_path:
            raise FileValidationError("", "请选择第一文件 (SETTLEMENT)")
        if not file2_path:
            raise FileValidationError("", "请选择第二文件 (CHINA)")
        if not script:
            raise ConfigValidationError("script", "请选择处理脚本")

        # 验证文件
        valid, error_msg = self.file_manager.validate_files([file1_path, file2_path])
        if not valid:
            # 这里会抛出FileNotFoundError，由装饰器处理
            return

        # 禁用按钮
        self.gui_updater.safe_button_update(self.process_button, tk.DISABLED)
        self.gui_updater.safe_status_update("处理中...")

        # 在新线程中处理
        thread = threading.Thread(target=self._run_processing, args=(file1_path, file2_path, script))
        thread.daemon = True
        thread.start()

    def _run_processing(self, file1_path: str, file2_path: str, script: str):
        """在新线程中运行处理"""
        try:
            self.log_message("开始处理文件...")

            # 构建命令
            try:
                script_path = self.config.get_script_path('report_script' if script == self.config.get('Scripts', 'report_script') else 'report_three_days')
            except (ScriptNotFoundError, MissingConfigError) as e:
                self.log_message(f"脚本配置错误: {e.get_user_message()}")
                self._on_error(-1)
                return

            cmd = [sys.executable, script_path, "--file1", file1_path, "--file2", file2_path]

            # 添加sheet名称参数
            sheet_name = self.sheet_name_var.get().strip()
            if sheet_name:
                cmd.extend(["--sheet_name", sheet_name])
                self.log_message(f"使用指定的Sheet名称: {sheet_name}")

            # 运行进程
            self.process_runner.run_process(
                cmd,
                self.tab_type,
                success_callback=lambda: self._on_success(),
                error_callback=lambda code: self._on_error(code)
            )

        except Exception as e:
            self.log_message(f"处理文件时出错: {str(e)}")
            self._on_error(-1)

    def _on_success(self):
        """处理成功回调"""
        self.log_message("文件处理成功完成")
        self.gui_updater.safe_status_update("处理完成")
        self.gui_updater.safe_button_update(self.process_button, tk.NORMAL)

    def _on_error(self, return_code: int):
        """处理失败回调"""
        self.log_message(f"文件处理失败，返回码: {return_code}")
        self.gui_updater.safe_status_update("处理失败")
        self.gui_updater.safe_button_update(self.process_button, tk.NORMAL)

class ImportTab(BaseTab):
    """数据导入选项卡"""

    def __init__(self, parent, gui_updater: SafeGUIUpdater, config: ConfigManager,
                 file_manager: FileManager, process_runner: ProcessRunner):
        self.tab_type = "import"
        super().__init__(parent, gui_updater, config, file_manager, process_runner)

    def create_ui(self):
        """创建数据导入UI"""
        # 文件选择区域
        file_frame = ttk.LabelFrame(self.frame, text="导入文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=10)

        ttk.Label(file_frame, text="导入文件(可多选):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.import_files_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.import_files_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_files).grid(row=0, column=2, padx=5, pady=5)

        # 已选择文件列表框
        ttk.Label(file_frame, text="已选择的文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.selected_files_text = tk.Text(file_frame, wrap=tk.WORD, width=50, height=5)
        self.selected_files_text.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W+tk.E)
        scrollbar = ttk.Scrollbar(file_frame, orient="vertical", command=self.selected_files_text.yview)
        scrollbar.grid(row=1, column=2, sticky=tk.N+tk.S)
        self.selected_files_text.configure(yscrollcommand=scrollbar.set)
        self.selected_files_text.config(state=tk.DISABLED)

        # 平台类型选择
        platform_frame = ttk.LabelFrame(self.frame, text="平台类型", padding="10")
        platform_frame.pack(fill=tk.X, pady=10)

        self.platform_var = tk.StringVar(value="IOT")
        ttk.Radiobutton(platform_frame, text="IOT", value="IOT", variable=self.platform_var).grid(row=0, column=0, padx=20, pady=5, sticky=tk.W)
        ttk.Radiobutton(platform_frame, text="ZERO", value="ZERO", variable=self.platform_var).grid(row=0, column=1, padx=20, pady=5, sticky=tk.W)

        # 订单类型选择
        order_type_frame = ttk.LabelFrame(self.frame, text="订单类型", padding="10")
        order_type_frame.pack(fill=tk.X, pady=10)

        self.order_type_var = tk.StringVar(value="all")
        ttk.Radiobutton(order_type_frame, text="所有订单", value="all", variable=self.order_type_var).grid(row=0, column=0, padx=20, pady=5, sticky=tk.W)
        ttk.Radiobutton(order_type_frame, text="仅API订单", value="api", variable=self.order_type_var).grid(row=0, column=1, padx=20, pady=5, sticky=tk.W)
        ttk.Radiobutton(order_type_frame, text="仅普通订单", value="normal", variable=self.order_type_var).grid(row=0, column=2, padx=20, pady=5, sticky=tk.W)

        # 操作按钮区域
        button_frame = ttk.Frame(self.frame, padding="10")
        button_frame.pack(fill=tk.X, pady=10)

        # 导入按钮
        self.import_button = ttk.Button(button_frame, text="开始导入", command=self.import_data)
        self.import_button.pack(side=tk.LEFT, padx=5)

        # 清空按钮
        ttk.Button(button_frame, text="清空选择", command=self.clear_selection).pack(side=tk.LEFT, padx=5)

        # 创建日志区域
        self.log_text = self.create_log_area("导入日志")

        # 显示初始提示信息
        self.log_message("请使用浏览按钮选择文件")

    def browse_files(self):
        """浏览选择导入文件"""
        filenames = filedialog.askopenfilenames(
            title="选择导入文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filenames:
            # 获取当前已选择的文件
            current_files = self.file_manager.split_file_paths(self.import_files_var.get())

            # 合并文件
            all_files = current_files + list(filenames)

            # 更新变量
            self.import_files_var.set(self.file_manager.join_file_paths(all_files))

            # 更新显示
            self.update_selected_files_display(all_files)

            # 记录日志
            basenames = [os.path.basename(f) for f in filenames]
            self.log_message(f"已选择{len(filenames)}个导入文件: {', '.join(basenames)}")

            # 自动识别平台类型
            if filenames:
                first_file = filenames[0]
                if "IOT" in first_file.upper():
                    self.platform_var.set("IOT")
                    self.log_message("自动识别为IOT平台")
                elif "ZERO" in first_file.upper():
                    self.platform_var.set("ZERO")
                    self.log_message("自动识别为ZERO平台")

    def clear_selection(self):
        """清空文件选择"""
        self.import_files_var.set("")
        self.update_selected_files_display([])
        self.log_message("已清空文件选择")

    def update_selected_files_display(self, file_paths: List[str]):
        """更新已选择文件的显示"""
        self.selected_files_text.config(state=tk.NORMAL)
        self.selected_files_text.delete(1.0, tk.END)
        for file_path in file_paths:
            self.selected_files_text.insert(tk.END, f"{os.path.basename(file_path)}\n")
        self.selected_files_text.config(state=tk.DISABLED)

    def import_data(self):
        """导入数据"""
        files_str = self.import_files_var.get()
        if not files_str:
            messagebox.showerror("错误", "请选择至少一个导入文件")
            return

        file_paths = self.file_manager.split_file_paths(files_str)
        if not file_paths:
            messagebox.showerror("错误", "请选择至少一个导入文件")
            return

        # 验证文件
        valid, error_msg = self.file_manager.validate_files(file_paths)
        if not valid:
            messagebox.showerror("错误", error_msg)
            return

        platform_type = self.platform_var.get()
        order_type = self.order_type_var.get()

        # 禁用按钮
        self.gui_updater.safe_button_update(self.import_button, tk.DISABLED)
        self.gui_updater.safe_status_update("导入中...")

        # 在新线程中导入
        thread = threading.Thread(target=self._run_import, args=(file_paths, platform_type, order_type))
        thread.daemon = True
        thread.start()

    def _run_import(self, file_paths: List[str], platform_type: str, order_type: str):
        """在新线程中运行导入"""
        try:
            self.log_message(f"开始导入数据，平台类型: {platform_type}, 订单类型: {order_type}")

            # 获取脚本路径
            script_path = self.config.get_script_path('data_import_script')

            all_success = True
            for file_path in file_paths:
                try:
                    self.log_message(f"处理文件: {os.path.basename(file_path)}")

                    # 构建命令
                    cmd = [
                        sys.executable,
                        script_path,
                        "--file", file_path,
                        "--platform", platform_type,
                        "--db_path", self.config.get_db_path()
                    ]

                    if order_type != "all":
                        cmd.extend(["--order_type", order_type])

                    # 运行进程
                    success = self.process_runner.run_process(cmd, self.tab_type)
                    if not success:
                        all_success = False

                except Exception as e:
                    self.log_message(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
                    all_success = False

            # 处理结果
            if all_success:
                self.log_message("所有文件导入成功")
                self.gui_updater.safe_status_update("导入完成")
            else:
                self.log_message("部分或全部文件导入失败")
                self.gui_updater.safe_status_update("导入部分失败")

        except Exception as e:
            self.log_message(f"导入数据时出错: {str(e)}")
            self.gui_updater.safe_status_update("导入出错")

        finally:
            self.gui_updater.safe_button_update(self.import_button, tk.NORMAL)

class RefundTab(BaseTab):
    """退款处理选项卡"""

    def __init__(self, parent, gui_updater: SafeGUIUpdater, config: ConfigManager,
                 file_manager: FileManager, process_runner: ProcessRunner):
        self.tab_type = "refund"
        super().__init__(parent, gui_updater, config, file_manager, process_runner)

    def create_ui(self):
        """创建退款处理UI"""
        # 文件选择区域
        file_frame = ttk.LabelFrame(self.frame, text="退款文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=10)

        ttk.Label(file_frame, text="退款文件(可多选):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.refund_files_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.refund_files_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_files).grid(row=0, column=2, padx=5, pady=5)

        # 已选择文件列表框
        ttk.Label(file_frame, text="已选择的文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.selected_files_text = tk.Text(file_frame, wrap=tk.WORD, width=50, height=5)
        self.selected_files_text.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W+tk.E)
        scrollbar = ttk.Scrollbar(file_frame, orient="vertical", command=self.selected_files_text.yview)
        scrollbar.grid(row=1, column=2, sticky=tk.N+tk.S)
        self.selected_files_text.configure(yscrollcommand=scrollbar.set)
        self.selected_files_text.config(state=tk.DISABLED)

        # 操作按钮区域
        button_frame = ttk.Frame(self.frame, padding="10")
        button_frame.pack(fill=tk.X, pady=10)

        # 处理按钮
        self.refund_button = ttk.Button(button_frame, text="开始处理退款", command=self.process_refunds)
        self.refund_button.pack(side=tk.LEFT, padx=5)

        # 清空按钮
        ttk.Button(button_frame, text="清空选择", command=self.clear_selection).pack(side=tk.LEFT, padx=5)

        # 创建日志区域
        self.log_text = self.create_log_area("退款处理日志")

        # 显示初始提示信息
        self.log_message("请选择包含REFUND_LIST工作表的Excel文件进行退款处理")

    def browse_files(self):
        """浏览选择退款文件"""
        filenames = filedialog.askopenfilenames(
            title="选择退款文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filenames:
            # 获取当前已选择的文件
            current_files = self.file_manager.split_file_paths(self.refund_files_var.get())

            # 合并文件
            all_files = current_files + list(filenames)

            # 更新变量
            self.refund_files_var.set(self.file_manager.join_file_paths(all_files))

            # 更新显示
            self.update_selected_files_display(all_files)

            # 记录日志
            basenames = [os.path.basename(f) for f in filenames]
            self.log_message(f"已选择{len(filenames)}个退款文件: {', '.join(basenames)}")

    def clear_selection(self):
        """清空文件选择"""
        self.refund_files_var.set("")
        self.update_selected_files_display([])
        self.log_message("已清空文件选择")

    def update_selected_files_display(self, file_paths: List[str]):
        """更新已选择文件的显示"""
        self.selected_files_text.config(state=tk.NORMAL)
        self.selected_files_text.delete(1.0, tk.END)
        for file_path in file_paths:
            self.selected_files_text.insert(tk.END, f"{os.path.basename(file_path)}\n")
        self.selected_files_text.config(state=tk.DISABLED)

    def process_refunds(self):
        """处理退款文件"""
        files_str = self.refund_files_var.get()
        if not files_str:
            messagebox.showerror("错误", "请选择至少一个退款文件")
            return

        file_paths = self.file_manager.split_file_paths(files_str)
        if not file_paths:
            messagebox.showerror("错误", "请选择至少一个退款文件")
            return

        # 验证文件
        valid, error_msg = self.file_manager.validate_files(file_paths)
        if not valid:
            messagebox.showerror("错误", error_msg)
            return

        # 禁用按钮
        self.gui_updater.safe_button_update(self.refund_button, tk.DISABLED)
        self.gui_updater.safe_status_update("退款处理中...")

        # 在新线程中处理
        thread = threading.Thread(target=self._run_refund_processing, args=(file_paths,))
        thread.daemon = True
        thread.start()

    def _run_refund_processing(self, file_paths: List[str]):
        """在新线程中运行退款处理"""
        try:
            self.log_message("开始处理退款文件...")

            # 获取脚本路径
            script_path = self.config.get_script_path('refund_script')

            for file_path in file_paths:
                try:
                    self.log_message(f"处理文件: {os.path.basename(file_path)}")

                    # 检测平台类型
                    platform_type = self._detect_platform_type(file_path)
                    self.log_message(f"检测到平台类型: {platform_type}")

                    # 构建命令
                    cmd = [
                        sys.executable,
                        script_path,
                        "--file", file_path,
                        "--platform", platform_type,
                        "--db_path", self.config.get_db_path()
                    ]

                    # 运行进程
                    self.process_runner.run_process(cmd, self.tab_type)

                except Exception as e:
                    self.log_message(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")

            self.log_message("退款处理完成")
            self.gui_updater.safe_status_update("退款处理完成")

        except Exception as e:
            self.log_message(f"退款处理过程中出错: {str(e)}")
            self.gui_updater.safe_status_update("退款处理出错")

        finally:
            self.gui_updater.safe_button_update(self.refund_button, tk.NORMAL)

    def _detect_platform_type(self, file_path: str) -> str:
        """检测平台类型"""
        try:
            import pandas as pd
            df = pd.read_excel(file_path, sheet_name='REFUND_LIST', engine='openpyxl', nrows=10)

            if 'Merchant Ref ID' in df.columns:
                merchant_refs = df['Merchant Ref ID'].dropna()
                if not merchant_refs.empty:
                    first_ref = str(merchant_refs.iloc[0]).upper()
                    if 'ZERO' in first_ref and 'IOT' not in first_ref:
                        return "ZERO"

            return "IOT"
        except Exception:
            return "IOT"  # 默认返回IOT

class ImprovedDataApp(tk.Tk):
    """改进版数据处理与导入应用"""

    def __init__(self):
        super().__init__()

        # 初始化组件
        self.config = ConfigManager()
        self.gui_updater = SafeGUIUpdater(self)
        self.file_manager = FileManager(self.config)
        self.process_runner = ProcessRunner(self.gui_updater)

        # 设置窗口
        self.setup_window()

        # 创建UI
        self.create_ui()

        # 设置样式
        self.set_style()

        logger.info("改进版数据处理与导入应用已启动")

    def setup_window(self):
        """设置窗口属性"""
        self.title("数据处理与导入应用 - 改进版")

        # 从配置获取窗口大小
        width = int(self.config.get('UI', 'window_width', '900'))
        height = int(self.config.get('UI', 'window_height', '700'))
        self.geometry(f"{width}x{height}")
        self.resizable(True, True)

        # 设置应用图标
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "kibdh-cj84b-001.ico")
        if os.path.exists(icon_path):
            self.iconbitmap(icon_path)

    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        self.main_frame = ttk.Frame(self, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题栏
        self.create_title_bar()

        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建各个选项卡
        self.create_tabs()

        # 创建状态栏
        self.create_status_bar()

    def create_title_bar(self):
        """创建标题栏"""
        title_frame = ttk.Frame(self.main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        # 应用标题
        title_label = ttk.Label(title_frame, text="数据处理与导入应用 - 改进版", font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT, padx=10)

        # 当前日期
        date_label = ttk.Label(title_frame, text=datetime.now().strftime("%Y-%m-%d"))
        date_label.pack(side=tk.RIGHT, padx=10)

    def create_tabs(self):
        """创建选项卡"""
        # 数据处理选项卡
        processing_frame = ttk.Frame(self.notebook)
        self.notebook.add(processing_frame, text="数据处理")
        self.processing_tab = ProcessingTab(processing_frame, self.gui_updater, self.config,
                                          self.file_manager, self.process_runner)

        # 数据导入选项卡
        import_frame = ttk.Frame(self.notebook)
        self.notebook.add(import_frame, text="数据导入")
        self.import_tab = ImportTab(import_frame, self.gui_updater, self.config,
                                  self.file_manager, self.process_runner)

        # 退款处理选项卡
        refund_frame = ttk.Frame(self.notebook)
        self.notebook.add(refund_frame, text="退款处理")
        self.refund_tab = RefundTab(refund_frame, self.gui_updater, self.config,
                                  self.file_manager, self.process_runner)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 注册状态变量到GUI更新器
        self.gui_updater.register_status_var(self.status_var)

    def set_style(self):
        """设置iPhone风格的UI"""
        self.style = ttk.Style()

        # 设置整体主题
        if "clam" in self.style.theme_names():
            self.style.theme_use("clam")

        # 设置颜色
        bg_color = "#f5f5f7"  # 浅灰色背景
        accent_color = "#0071e3"  # 蓝色强调色
        text_color = "#1d1d1f"  # 深灰色文本

        # 配置各种元素样式
        self.style.configure("TFrame", background=bg_color)
        self.style.configure("TLabel", background=bg_color, foreground=text_color)
        self.style.configure("TButton", background=accent_color, foreground="white", padding=6)
        self.style.map("TButton", background=[("active", "#0077ed")])
        self.style.configure("TNotebook", background=bg_color, tabmargins=[2, 5, 2, 0])
        self.style.configure("TNotebook.Tab", background=bg_color, padding=[10, 5], font=("Arial", 10))
        self.style.map("TNotebook.Tab", background=[("selected", accent_color)], foreground=[("selected", "white")])

        # 设置窗口背景色
        self.configure(background=bg_color)
        self.main_frame.configure(style="TFrame")

# 主程序入口
if __name__ == "__main__":
    try:
        # 创建应用程序实例
        app = ImprovedDataApp()
        app.mainloop()
    except Exception as e:
        logger.error(f"应用程序启动失败: {str(e)}")
        print(f"错误: {str(e)}")
        traceback.print_exc()
