#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复后代码运行 - 确保所有修复不影响代码正常运行和调用
"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """测试基本导入功能"""
    print("🔧 1. 测试基本导入功能")
    print("-" * 60)
    
    try:
        # 测试主要模块导入
        from data_import_optimized import DataImportProcessor
        print("✅ DataImportProcessor 导入成功")
        
        from utils.logger import get_logger, AppLogger
        print("✅ Logger模块导入成功")
        
        # 测试数据库连接
        from database.connection_pool import get_connection
        print("✅ 数据库连接模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dataprocessor_initialization():
    """测试DataImportProcessor初始化"""
    print("\n🔧 2. 测试DataImportProcessor初始化")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        # 创建处理器实例
        processor = DataImportProcessor()
        print("✅ DataImportProcessor 实例创建成功")
        
        # 检查关键属性
        print(f"✅ 批处理大小: {processor.batch_size}")
        print(f"✅ 最大内存限制: {processor.max_memory_usage // 1024 // 1024}MB")
        print(f"✅ 启用分批处理: {processor.enable_batch_processing}")
        
        # 测试日志功能
        processor.logger.info("测试日志记录")
        print("✅ 日志记录功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ DataImportProcessor初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_monitoring():
    """测试内存监控功能"""
    print("\n🔧 3. 测试内存监控功能")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试内存检查
        memory_usage = processor._check_memory_usage()
        print(f"✅ 内存使用检查: {memory_usage // 1024 // 1024}MB")
        
        # 测试分批处理判断
        test_data = pd.DataFrame({'test': range(100)})
        should_batch = processor._should_use_batch_processing(test_data)
        print(f"✅ 分批处理判断: {should_batch}")
        
        # 测试大数据集
        large_data = pd.DataFrame({'test': range(5000)})
        should_batch_large = processor._should_use_batch_processing(large_data)
        print(f"✅ 大数据集分批判断: {should_batch_large}")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存监控功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_datetime_standardization():
    """测试时间格式标准化"""
    print("\n🔧 4. 测试时间格式标准化")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'Order_time': [
                '2025-01-01 10:30:45',
                '2025/01/02 11:45:30',
                '01-03-2025 12:15:20'
            ],
            'Payment_date': [
                '2025-01-01',
                '2025/01/02 14:30:00',
                None
            ],
            'Order_No': ['ORD001', 'ORD002', 'ORD003']
        })
        
        # 执行时间标准化
        standardized_data = processor._standardize_datetime_format(test_data.copy())
        print("✅ 时间格式标准化成功")
        
        # 验证结果
        for i, row in standardized_data.iterrows():
            print(f"  {row['Order_No']}: {row['Order_time']} | {row['Payment_date']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 时间格式标准化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_validation():
    """测试字段验证功能"""
    print("\n🔧 5. 测试字段验证功能")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'Transaction_Num': ['TXN001', None, 'TXN003'],
            'Order_No': ['ORD001', 'ORD002', 'ORD003'],
            'Order_price': [100.0, 200.0, None],
            'Equipment_ID': ['EQ001', '', 'EQ003'],
            'Order_time': ['2025-01-01 10:00:00', '2025-01-02 11:00:00', '2025-01-03 12:00:00']
        })
        
        # 执行字段验证
        validation_report = processor._validate_critical_fields(test_data)
        print("✅ 字段验证功能成功")
        
        print(f"  整体有效性: {validation_report['is_valid']}")
        print(f"  错误数量: {len(validation_report['errors'])}")
        print(f"  警告数量: {len(validation_report['warnings'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 字段验证功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_missing_records_detection():
    """测试缺失记录检测"""
    print("\n🔧 6. 测试缺失记录检测")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'Transaction_Num': ['TXN001', 'TXN003'],
            'Order_No': ['ORD001', 'ORD003'],
            'Order_time': ['2025-01-01 10:00:00', '2025-01-03 12:00:00'],
            'Order_price': [100.0, 300.0],
            'Order_status': ['Finished', 'Finished'],
            'Equipment_ID': ['EQ001', 'EQ003']
        })
        
        # 执行缺失记录检测
        missing_report = processor._detect_missing_records(test_data, 'IOT')
        print("✅ 缺失记录检测功能成功")
        
        print(f"  是否有缺失: {missing_report['has_missing']}")
        print(f"  缺失记录数: {missing_report['missing_count']}")
        print(f"  数据库总记录: {missing_report['total_db_records']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 缺失记录检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logger_functionality():
    """测试Logger功能"""
    print("\n🔧 7. 测试Logger功能")
    print("-" * 60)
    
    try:
        from utils.logger import get_logger
        import logging
        
        # 创建logger
        logger = get_logger('test_verification')
        print("✅ Logger创建成功")
        
        # 测试基本日志方法
        logger.debug("测试debug消息")
        logger.info("测试info消息")
        logger.warning("测试warning消息")
        logger.error("测试error消息")
        print("✅ 基本日志方法正常")
        
        # 测试处理器方法
        handler = logging.StreamHandler()
        logger.addHandler(handler)
        logger.removeHandler(handler)
        print("✅ 处理器方法正常")
        
        # 测试级别方法
        logger.setLevel(logging.DEBUG)
        level = logger.getEffectiveLevel()
        enabled = logger.isEnabledFor(logging.INFO)
        has_handlers = logger.hasHandlers()
        print(f"✅ 级别方法正常 - 级别:{level}, 启用:{enabled}, 有处理器:{has_handlers}")
        
        return True
        
    except Exception as e:
        print(f"❌ Logger功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """测试数据库操作"""
    print("\n🔧 8. 测试数据库操作")
    print("-" * 60)
    
    try:
        from database.connection_pool import get_connection
        
        # 测试数据库连接
        with get_connection() as conn:
            cursor = conn.connection.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 5")
            tables = cursor.fetchall()
            print(f"✅ 数据库连接成功，找到 {len(tables)} 个表")
            
            for table in tables:
                print(f"  表: {table[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_application():
    """测试主应用程序"""
    print("\n🔧 9. 测试主应用程序")
    print("-" * 60)
    
    try:
        # 测试主应用程序导入
        sys.path.insert(0, str(project_root / "01_主程序"))
        
        # 测试关键组件导入
        from 数据处理与导入应用_完整版 import ProcessRunner, SafeGUIUpdater
        print("✅ 主应用程序组件导入成功")
        
        # 创建测试实例
        gui_updater = SafeGUIUpdater(None)
        process_runner = ProcessRunner(gui_updater)
        print("✅ 主应用程序实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用程序测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 验证修复后代码运行状况")
    print("=" * 80)
    
    tests = [
        ("基本导入功能", test_basic_imports),
        ("DataImportProcessor初始化", test_dataprocessor_initialization),
        ("内存监控功能", test_memory_monitoring),
        ("时间格式标准化", test_datetime_standardization),
        ("字段验证功能", test_field_validation),
        ("缺失记录检测", test_missing_records_detection),
        ("Logger功能", test_logger_functionality),
        ("数据库操作", test_database_operations),
        ("主应用程序", test_main_application)
    ]
    
    passed = 0
    total = len(tests)
    failed_tests = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"\n✅ {test_name} 测试通过")
            else:
                print(f"\n❌ {test_name} 测试失败")
                failed_tests.append(test_name)
        except Exception as e:
            print(f"\n❌ {test_name} 测试异常: {e}")
            failed_tests.append(test_name)
    
    print("\n" + "=" * 80)
    print("🎯 代码运行验证结果")
    print("=" * 80)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if i < passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过")
        print("✅ 修复后的代码完全正常运行")
        print("✅ 所有功能和调用都正常工作")
        print("✅ 没有引入新的问题")
        print("✅ 系统稳定可靠")
    elif passed >= total * 0.8:
        print("✅ 大部分测试通过")
        print("⚠️ 少量功能需要检查")
        if failed_tests:
            print(f"⚠️ 失败的测试: {', '.join(failed_tests)}")
    else:
        print("❌ 多个测试失败")
        print("🔧 需要检查修复是否引入了新问题")
        if failed_tests:
            print(f"❌ 失败的测试: {', '.join(failed_tests)}")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 验证{'成功' if success else '失败'}")
    input("按回车键退出...")
