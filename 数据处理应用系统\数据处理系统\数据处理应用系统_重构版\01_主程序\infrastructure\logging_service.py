# -*- coding: utf-8 -*-
"""
日志服务 - 架构优化步骤3
实现异步日志处理，解决循环依赖问题

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import threading
import time
import queue
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import traceback
from datetime import datetime


class LogLevel(Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class TabType(Enum):
    """选项卡类型"""
    GENERAL = "general"
    PROCESSING = "processing"
    MANAGEMENT = "management"
    BACKUP = "backup"
    IMPORT = "import"
    EXPORT = "export"


@dataclass
class LogMessage:
    """日志消息数据结构"""
    message: str
    level: LogLevel = LogLevel.INFO
    tab_type: TabType = TabType.GENERAL
    timestamp: float = field(default_factory=time.time)
    thread_id: int = field(default_factory=lambda: threading.current_thread().ident)
    source: Optional[str] = None
    correlation_id: Optional[str] = None
    
    def __post_init__(self):
        if self.correlation_id is None:
            self.correlation_id = f"{self.level.value}_{int(self.timestamp * 1000000)}"
            
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "message": self.message,
            "level": self.level.value,
            "tab_type": self.tab_type.value,
            "timestamp": self.timestamp,
            "thread_id": self.thread_id,
            "source": self.source,
            "correlation_id": self.correlation_id,
            "formatted_time": datetime.fromtimestamp(self.timestamp).strftime("%Y-%m-%d %H:%M:%S")
        }


class LoggingService:
    """
    异步日志服务
    
    功能：
    - 异步日志处理
    - 多线程安全
    - 事件驱动通信
    - 性能监控
    """
    
    def __init__(self, event_bus, max_queue_size: int = 1000, worker_threads: int = 1):
        self.event_bus = event_bus
        self.max_queue_size = max_queue_size
        self.worker_threads = worker_threads
        
        # 日志队列
        self.log_queue = queue.Queue(maxsize=max_queue_size)
        self.worker_threads_list: List[threading.Thread] = []
        self.running = False
        
        # 日志处理器
        self.handlers: List[Callable[[LogMessage], None]] = []
        self.handlers_lock = threading.RLock()
        
        # 性能统计
        self.stats = {
            "messages_processed": 0,
            "messages_dropped": 0,
            "average_processing_time": 0.0,
            "total_processing_time": 0.0,
            "queue_high_water_mark": 0,
            "handler_errors": 0
        }
        
        # 日志历史（用于调试）
        self.message_history: List[LogMessage] = []
        self.max_history_size = 100
        
        # 启动工作线程
        self._start_workers()
        
    def log(self, message: str, level: LogLevel = LogLevel.INFO, 
           tab_type: TabType = TabType.GENERAL, source: Optional[str] = None) -> bool:
        """
        记录日志消息
        
        Args:
            message: 日志消息
            level: 日志级别
            tab_type: 选项卡类型
            source: 消息源
            
        Returns:
            bool: 是否成功加入队列
        """
        try:
            log_msg = LogMessage(
                message=message,
                level=level,
                tab_type=tab_type,
                source=source
            )
            
            # 尝试加入队列
            try:
                self.log_queue.put(log_msg, timeout=0.1)
                
                # 更新队列高水位标记
                current_size = self.log_queue.qsize()
                if current_size > self.stats["queue_high_water_mark"]:
                    self.stats["queue_high_water_mark"] = current_size
                    
                return True
                
            except queue.Full:
                self.stats["messages_dropped"] += 1
                print(f"⚠️ [LOGGING] 日志队列已满，丢弃消息: {message[:50]}...")
                return False
                
        except Exception as e:
            print(f"❌ [LOGGING] 日志记录失败: {e}")
            return False
            
    def log_info(self, message: str, tab_type: TabType = TabType.GENERAL, source: Optional[str] = None):
        """记录信息日志"""
        self.log(message, LogLevel.INFO, tab_type, source)
        
    def log_warning(self, message: str, tab_type: TabType = TabType.GENERAL, source: Optional[str] = None):
        """记录警告日志"""
        self.log(message, LogLevel.WARNING, tab_type, source)
        
    def log_error(self, message: str, tab_type: TabType = TabType.GENERAL, source: Optional[str] = None):
        """记录错误日志"""
        self.log(message, LogLevel.ERROR, tab_type, source)
        
    def add_handler(self, handler: Callable[[LogMessage], None]):
        """添加日志处理器"""
        with self.handlers_lock:
            self.handlers.append(handler)
            
    def remove_handler(self, handler: Callable[[LogMessage], None]):
        """移除日志处理器"""
        with self.handlers_lock:
            if handler in self.handlers:
                self.handlers.remove(handler)
                
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "queue_size": self.log_queue.qsize(),
            "handlers_count": len(self.handlers),
            "worker_threads": len(self.worker_threads_list),
            "running": self.running
        }
        
    def get_message_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取消息历史"""
        return [msg.to_dict() for msg in self.message_history[-limit:]]
        
    def clear_history(self):
        """清空消息历史"""
        self.message_history.clear()
        
    def shutdown(self, timeout: float = 5.0):
        """关闭日志服务"""
        print("🔄 [LOGGING] 关闭日志服务...")

        self.running = False

        # 等待工作线程结束
        for thread in self.worker_threads_list:
            if thread.is_alive():
                thread.join(timeout)
                if thread.is_alive():
                    print(f"⚠️ [LOGGING] 工作线程 {thread.name} 未能在 {timeout} 秒内停止")

        # 清空队列和历史
        try:
            while not self.log_queue.empty():
                self.log_queue.get_nowait()
        except:
            pass

        self.message_history.clear()
        self.worker_threads_list.clear()

        print("✅ [LOGGING] 日志服务已关闭")
        
    def _start_workers(self):
        """启动工作线程"""
        self.running = True
        
        for i in range(self.worker_threads):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"LoggingService-Worker-{i}",
                daemon=True
            )
            worker.start()
            self.worker_threads_list.append(worker)
            
        print(f"✅ [LOGGING] 启动 {self.worker_threads} 个日志工作线程")
        
    def _worker_loop(self):
        """工作线程主循环"""
        while self.running:
            try:
                # 获取日志消息（带超时）
                log_msg = self.log_queue.get(timeout=1)
                
                # 处理日志消息
                self._process_log_message(log_msg)
                
                # 标记任务完成
                self.log_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ [LOGGING] 日志工作线程错误: {e}")
                traceback.print_exc()
                
    def _process_log_message(self, log_msg: LogMessage):
        """处理单个日志消息"""
        start_time = time.perf_counter()
        
        try:
            # 添加到历史记录
            self._add_to_history(log_msg)
            
            # 发布日志事件
            if self.event_bus:
                self.event_bus.publish("log_message", log_msg.to_dict())
                
            # 调用处理器
            with self.handlers_lock:
                handlers = self.handlers.copy()
                
            for handler in handlers:
                try:
                    handler(log_msg)
                except Exception as e:
                    self.stats["handler_errors"] += 1
                    print(f"❌ [LOGGING] 日志处理器错误: {e}")
                    
            # 更新统计信息
            processing_time = time.perf_counter() - start_time
            self.stats["messages_processed"] += 1
            self.stats["total_processing_time"] += processing_time
            self.stats["average_processing_time"] = (
                self.stats["total_processing_time"] / self.stats["messages_processed"]
            )
            
        except Exception as e:
            print(f"❌ [LOGGING] 日志消息处理错误: {e}")
            
    def _add_to_history(self, log_msg: LogMessage):
        """添加消息到历史记录"""
        self.message_history.append(log_msg)
        
        # 保持历史记录大小
        if len(self.message_history) > self.max_history_size:
            self.message_history.pop(0)


class ConsoleLogHandler:
    """控制台日志处理器"""
    
    def __init__(self, min_level: LogLevel = LogLevel.INFO):
        self.min_level = min_level
        
    def __call__(self, log_msg: LogMessage):
        """处理日志消息"""
        if self._should_log(log_msg.level):
            formatted_msg = self._format_message(log_msg)
            print(formatted_msg)
            
    def _should_log(self, level: LogLevel) -> bool:
        """检查是否应该记录此级别的日志"""
        level_order = {
            LogLevel.DEBUG: 0,
            LogLevel.INFO: 1,
            LogLevel.WARNING: 2,
            LogLevel.ERROR: 3,
            LogLevel.CRITICAL: 4
        }
        return level_order[level] >= level_order[self.min_level]
        
    def _format_message(self, log_msg: LogMessage) -> str:
        """格式化日志消息"""
        timestamp = datetime.fromtimestamp(log_msg.timestamp).strftime("%H:%M:%S")
        
        # 根据级别选择前缀
        level_prefixes = {
            LogLevel.DEBUG: "🔍",
            LogLevel.INFO: "ℹ️",
            LogLevel.WARNING: "⚠️",
            LogLevel.ERROR: "❌",
            LogLevel.CRITICAL: "🚨"
        }
        
        prefix = level_prefixes.get(log_msg.level, "📝")
        
        return f"{timestamp} {prefix} [{log_msg.tab_type.value.upper()}] {log_msg.message}"


class LoggingServiceFactory:
    """日志服务工厂"""
    
    @staticmethod
    def create_logging_service(event_bus, config: Optional[Dict[str, Any]] = None) -> LoggingService:
        """创建日志服务实例"""
        print("🏗️ [LOGGING] 创建日志服务...")
        
        # 默认配置
        default_config = {
            "max_queue_size": 1000,
            "worker_threads": 1,
            "console_log_level": LogLevel.INFO
        }
        
        if config:
            default_config.update(config)
            
        # 创建日志服务
        logging_service = LoggingService(
            event_bus=event_bus,
            max_queue_size=default_config["max_queue_size"],
            worker_threads=default_config["worker_threads"]
        )
        
        # 添加控制台处理器
        console_handler = ConsoleLogHandler(default_config["console_log_level"])
        logging_service.add_handler(console_handler)
        
        print("✅ [LOGGING] 日志服务创建完成")
        return logging_service


# 全局日志服务实例
_global_logging_service: Optional[LoggingService] = None


def get_logging_service() -> Optional[LoggingService]:
    """获取全局日志服务实例"""
    return _global_logging_service


def set_logging_service(logging_service: LoggingService):
    """设置全局日志服务实例"""
    global _global_logging_service
    _global_logging_service = logging_service


def log_message(message: str, level: LogLevel = LogLevel.INFO, 
               tab_type: TabType = TabType.GENERAL, source: Optional[str] = None):
    """便捷的日志记录函数"""
    service = get_logging_service()
    if service:
        service.log(message, level, tab_type, source)
    else:
        # 回退到控制台输出
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"{timestamp} [{tab_type.value.upper()}] {message}")
