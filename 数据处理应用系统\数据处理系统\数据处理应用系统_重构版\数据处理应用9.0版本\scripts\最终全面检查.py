#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终全面检查 - 验证所有 logger 方法调用
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_all_logger_methods():
    """测试所有 logger 方法"""
    print("🔧 测试所有 logger 方法")
    print("-" * 50)
    
    try:
        from utils.logger import get_logger
        logger = get_logger('test')
        
        # 测试基本日志方法
        logger.debug("测试debug")
        logger.info("测试info")
        logger.warning("测试warning")
        logger.error("测试error")
        logger.critical("测试critical")
        print("✅ 基本日志方法正常")
        
        # 测试处理器方法
        import logging
        handler = logging.StreamHandler()
        logger.addHandler(handler)
        logger.removeHandler(handler)
        print("✅ 处理器方法正常")
        
        # 测试级别方法
        logger.setLevel(logging.DEBUG)
        level = logger.getEffectiveLevel()
        enabled = logger.isEnabledFor(logging.INFO)
        has_handlers = logger.hasHandlers()
        print(f"✅ 级别方法正常 - 级别:{level}, 启用:{enabled}, 有处理器:{has_handlers}")
        
        # 测试扩展方法
        logger.log_exception(Exception("测试异常"), "测试操作")
        logger.log_performance("测试操作", 1.5)
        logger.log_data_processing("测试阶段", 100, 90, 10)
        logger.log_file_operation("测试操作", "test.txt", True)
        print("✅ 扩展方法正常")
        
        return True
        
    except Exception as e:
        print(f"❌ logger方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_import_processor():
    """测试数据导入处理器"""
    print("\n🔧 测试数据导入处理器")
    print("-" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        print("✅ DataImportProcessor 创建成功")
        
        # 测试所有 logger 方法调用
        processor.logger.info("测试导入处理器")
        processor.logger.warning("测试警告")
        processor.logger.error("测试错误")
        processor.logger.debug("测试调试")
        print("✅ 所有 logger 方法调用正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据导入处理器测试失败: {e}")
        return False

def test_refund_processor():
    """测试退款处理器"""
    print("\n🔧 测试退款处理器")
    print("-" * 50)
    
    try:
        from refund_process_optimized import RefundProcessor
        
        processor = RefundProcessor()
        print("✅ RefundProcessor 创建成功")
        
        # 测试所有 logger 方法调用
        processor.logger.info("测试退款处理器")
        processor.logger.warning("测试警告")
        processor.logger.error("测试错误")
        processor.logger.debug("测试调试")
        print("✅ 所有 logger 方法调用正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 退款处理器测试失败: {e}")
        return False

def test_command_line_scripts():
    """测试命令行脚本"""
    print("\n🔧 测试命令行脚本")
    print("-" * 50)
    
    try:
        import subprocess
        
        # 测试数据导入脚本
        result = subprocess.run([
            sys.executable, 'data_import_optimized.py', '--help'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 数据导入脚本命令行正常")
        else:
            print(f"⚠️ 数据导入脚本返回码: {result.returncode}")
        
        # 测试退款处理脚本
        result = subprocess.run([
            sys.executable, 'refund_process_optimized.py', '--help'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 退款处理脚本命令行正常")
        else:
            print(f"⚠️ 退款处理脚本返回码: {result.returncode}")
        
        return True
        
    except Exception as e:
        print(f"❌ 命令行脚本测试失败: {e}")
        return False

def test_logger_compatibility():
    """测试 logger 兼容性"""
    print("\n🔧 测试 logger 兼容性")
    print("-" * 50)
    
    try:
        from utils.logger import get_logger
        import logging
        
        # 创建 AppLogger
        app_logger = get_logger('compatibility_test')
        
        # 创建标准 logger
        std_logger = logging.getLogger('std_test')
        
        # 比较方法
        app_methods = set(dir(app_logger))
        std_methods = set(dir(std_logger))
        
        # 检查关键方法
        critical_methods = {
            'debug', 'info', 'warning', 'error', 'critical',
            'addHandler', 'removeHandler', 'setLevel', 
            'getEffectiveLevel', 'isEnabledFor', 'hasHandlers'
        }
        
        missing_methods = critical_methods - app_methods
        if missing_methods:
            print(f"❌ AppLogger 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ AppLogger 包含所有关键方法")
        
        # 测试方法调用
        for method in critical_methods:
            if method in ['debug', 'info', 'warning', 'error', 'critical']:
                getattr(app_logger, method)("测试消息")
            elif method == 'addHandler':
                handler = logging.StreamHandler()
                app_logger.addHandler(handler)
                app_logger.removeHandler(handler)
            elif method == 'setLevel':
                app_logger.setLevel(logging.INFO)
            elif method == 'getEffectiveLevel':
                app_logger.getEffectiveLevel()
            elif method == 'isEnabledFor':
                app_logger.isEnabledFor(logging.INFO)
            elif method == 'hasHandlers':
                app_logger.hasHandlers()
        
        print("✅ 所有关键方法调用成功")
        return True
        
    except Exception as e:
        print(f"❌ logger兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 最终全面检查 - 所有 logger 调用")
    print("=" * 80)
    
    tests = [
        ("所有logger方法", test_all_logger_methods),
        ("数据导入处理器", test_data_import_processor),
        ("退款处理器", test_refund_processor),
        ("命令行脚本", test_command_line_scripts),
        ("logger兼容性", test_logger_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"\n✅ {test_name} 测试通过")
            else:
                print(f"\n❌ {test_name} 测试失败")
        except Exception as e:
            print(f"\n❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 最终检查结果")
    print("=" * 80)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if i < passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过")
        print("✅ AppLogger 错误已完全修复")
        print("✅ 所有 logger 方法调用正常")
        print("✅ 数据导入和退款处理功能完全正常")
        print("✅ 系统可以安全使用")
    elif passed >= total * 0.8:
        print("✅ 大部分测试通过")
        print("⚠️ 还有少量问题，但核心功能正常")
    else:
        print("❌ 多个测试失败")
        print("🔧 需要进一步检查和修复")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 最终检查{'成功' if success else '失败'}")
    input("按回车键退出...")
