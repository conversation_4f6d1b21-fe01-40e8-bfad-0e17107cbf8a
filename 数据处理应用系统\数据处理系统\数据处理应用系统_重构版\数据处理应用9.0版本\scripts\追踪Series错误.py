#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
追踪Series错误 - 详细追踪数据导入过程中的Series错误
"""

import os
import sys
import pandas as pd
import traceback
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def monkey_patch_series():
    """猴子补丁：拦截Series的错误操作"""
    original_getattr = pd.Series.__getattribute__
    
    def patched_getattr(self, name):
        if name == 'empty':
            print(f"🚨 错误：尝试访问Series.empty属性！")
            print(f"📍 调用栈：")
            for line in traceback.format_stack()[-5:-1]:
                print(f"  {line.strip()}")
            raise AttributeError(f"'Series' object has no attribute 'empty'. 使用 len(series) == 0 或 series.size == 0 代替")
        return original_getattr(self, name)
    
    pd.Series.__getattribute__ = patched_getattr
    print("🔧 已安装Series.empty检测补丁")

def test_actual_import_process():
    """测试实际的导入过程"""
    print("🔧 测试实际导入过程")
    print("-" * 60)
    
    try:
        # 设置非交互模式
        os.environ['NON_INTERACTIVE'] = '1'
        os.environ['AUTO_DUPLICATE_HANDLING'] = 'overwrite'
        os.environ['AUTO_MISSING_HANDLING'] = 'ignore'
        
        # 安装监控补丁
        monkey_patch_series()
        
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建模拟IOT数据
        test_data = pd.DataFrame({
            'Copartner name': ['Partner1', 'Partner2'],
            'Order No.': ['ORD001', 'ORD002'],
            'Transaction Num': ['TXN001', 'TXN002'],
            'Order types': ['普通', '普通'],
            'Order price': [100.0, 200.0],
            'Payment': [100.0, 200.0],
            'Order time': ['2025-01-01 10:00:00', '2025-01-02 11:00:00'],
            'Equipment ID': ['EQ001', 'EQ002'],
            'Equipment name': ['Device1', 'Device2'],
            'Branch name': ['Branch1', 'Branch2'],
            'Payment date': ['2025-01-01', '2025-01-02'],
            'User name': ['User1', 'User2'],
            'Time': ['10:00:00', '11:00:00'],
            'Matched Order ID': ['', ''],
            'OrderTime_dt': ['2025-01-01 10:00:00', '2025-01-02 11:00:00']
        })
        
        print(f"📊 创建测试数据: {len(test_data)} 条记录")
        
        # 逐步测试各个方法
        test_methods = [
            ("数据清洗", lambda: processor._clean_data(test_data.copy())),
            ("数据标准化", lambda: processor._standardize_data_types(test_data.copy())),
            ("列过滤", lambda: processor._filter_columns_for_database(test_data.copy())),
            ("日期提取", lambda: processor.extract_date_from_data(test_data.copy(), "test.xlsx")),
            ("智能增量重复检测", lambda: processor.smart_incremental_duplicate_check(test_data.copy(), 'IOT')),
        ]
        
        for method_name, method_func in test_methods:
            try:
                print(f"\n🧪 测试方法: {method_name}")
                result = method_func()
                print(f"✅ {method_name} 成功")
            except Exception as e:
                print(f"❌ {method_name} 失败: {e}")
                print("📍 错误详情:")
                traceback.print_exc()
                return False
        
        print("\n✅ 所有方法测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("📍 错误详情:")
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边缘情况"""
    print("\n🔧 测试边缘情况")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试各种边缘情况
        edge_cases = [
            ("空DataFrame", pd.DataFrame()),
            ("只有列名的DataFrame", pd.DataFrame(columns=['Order_time', 'Transaction_Num'])),
            ("单行数据", pd.DataFrame({'Order_time': ['2025-01-01'], 'Transaction_Num': ['TXN001']})),
            ("包含NaN的数据", pd.DataFrame({'Order_time': [None, '2025-01-01'], 'Transaction_Num': ['TXN001', None]})),
        ]
        
        for case_name, test_df in edge_cases:
            try:
                print(f"\n🧪 测试边缘情况: {case_name}")
                
                # 测试关键方法
                if not test_df.empty:
                    processor._clean_data(test_df.copy())
                    processor.extract_date_from_data(test_df.copy(), "test.xlsx")
                
                print(f"✅ {case_name} 测试通过")
                
            except Exception as e:
                print(f"❌ {case_name} 测试失败: {e}")
                print("📍 错误详情:")
                traceback.print_exc()
                return False
        
        print("\n✅ 所有边缘情况测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 边缘情况测试失败: {e}")
        print("📍 错误详情:")
        traceback.print_exc()
        return False

def test_series_operations():
    """测试Series操作"""
    print("\n🔧 测试Series操作")
    print("-" * 60)
    
    try:
        # 测试各种Series操作
        test_series = pd.Series([1, 2, None, 4])
        empty_series = pd.Series([])
        all_nan_series = pd.Series([None, None, None])
        
        print("🧪 测试正确的Series操作:")
        
        # 正确的操作
        print(f"  len(test_series): {len(test_series)}")
        print(f"  test_series.size: {test_series.size}")
        print(f"  test_series.notna().any(): {test_series.notna().any()}")
        print(f"  test_series.isna().all(): {test_series.isna().all()}")
        
        print(f"  len(empty_series): {len(empty_series)}")
        print(f"  empty_series.size: {empty_series.size}")
        
        print(f"  len(all_nan_series): {len(all_nan_series)}")
        print(f"  all_nan_series.notna().any(): {all_nan_series.notna().any()}")
        print(f"  all_nan_series.isna().all(): {all_nan_series.isna().all()}")
        
        # 测试错误的操作（应该被补丁拦截）
        print("\n🧪 测试错误的Series操作:")
        try:
            # 这应该触发我们的补丁
            _ = test_series.empty
            print("❌ 错误：Series.empty操作没有被拦截")
            return False
        except AttributeError as e:
            print(f"✅ 正确拦截了Series.empty操作: {e}")
        
        print("\n✅ Series操作测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Series操作测试失败: {e}")
        print("📍 错误详情:")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Series错误追踪测试")
    print("=" * 80)
    
    tests = [
        ("Series操作", test_series_operations),
        ("边缘情况", test_edge_cases),
        ("实际导入过程", test_actual_import_process),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 Series错误追踪结果")
    print("=" * 80)
    
    print(f"📊 通过测试: {passed}/{total}")
    success_rate = (passed / total) * 100
    print(f"📊 成功率: {success_rate:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过")
        print("✅ 没有发现Series错误")
        print("✅ 数据导入逻辑正常")
    else:
        print("❌ 发现Series错误")
        print("🔧 需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 追踪{'成功' if success else '发现问题'}")
    input("按回车键退出...")
