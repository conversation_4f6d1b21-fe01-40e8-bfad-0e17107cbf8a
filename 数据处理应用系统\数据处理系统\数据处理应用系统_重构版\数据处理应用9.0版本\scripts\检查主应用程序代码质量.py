#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查主应用程序代码质量 - 发现并修复潜在问题
"""

import os
import sys
import re
from pathlib import Path
from collections import defaultdict

def check_duplicate_imports():
    """检查重复导入"""
    print("🔍 检查重复导入")
    print("=" * 60)
    
    script_path = Path(__file__).parent.parent / "01_主程序" / "数据处理与导入应用_完整版.py"
    
    if not script_path.exists():
        print(f"❌ 文件不存在: {script_path}")
        return False
    
    with open(script_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    imports = defaultdict(list)
    
    for i, line in enumerate(lines, 1):
        line = line.strip()
        if line.startswith('import ') or line.startswith('from '):
            # 提取导入的模块名
            if line.startswith('import '):
                module = line.split()[1].split('.')[0]
            else:  # from ... import ...
                module = line.split()[1].split('.')[0]
            
            imports[module].append((i, line))
    
    # 检查重复
    duplicates_found = False
    for module, occurrences in imports.items():
        if len(occurrences) > 1:
            duplicates_found = True
            print(f"❌ 重复导入 '{module}':")
            for line_num, line_content in occurrences:
                print(f"   第{line_num}行: {line_content}")
    
    if not duplicates_found:
        print("✅ 没有发现重复导入")
    
    return not duplicates_found

def check_inline_imports():
    """检查函数内部的导入"""
    print("\n🔍 检查函数内部导入")
    print("=" * 60)
    
    script_path = Path(__file__).parent.parent / "01_主程序" / "数据处理与导入应用_完整版.py"
    
    with open(script_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    inline_imports = []
    in_function = False
    current_function = ""
    
    for i, line in enumerate(lines, 1):
        stripped = line.strip()
        
        # 检测函数开始
        if stripped.startswith('def '):
            in_function = True
            current_function = stripped.split('(')[0].replace('def ', '')
        
        # 检测类方法
        elif stripped.startswith('class '):
            in_function = False
            current_function = ""
        
        # 检测导入
        if in_function and (stripped.startswith('import ') or stripped.startswith('from ')):
            inline_imports.append((i, current_function, stripped))
    
    if inline_imports:
        print(f"⚠️ 发现 {len(inline_imports)} 个函数内部导入:")
        for line_num, func_name, import_line in inline_imports:
            print(f"   第{line_num}行 在函数 '{func_name}': {import_line}")
    else:
        print("✅ 没有发现函数内部导入")
    
    return len(inline_imports) == 0

def check_performance_issues():
    """检查性能问题"""
    print("\n🔍 检查性能问题")
    print("=" * 60)
    
    script_path = Path(__file__).parent.parent / "01_主程序" / "数据处理与导入应用_完整版.py"
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    issues = []
    
    # 检查字符串拼接
    string_concat_pattern = r'\+.*["\']'
    for i, line in enumerate(lines, 1):
        if re.search(string_concat_pattern, line) and 'join' not in line:
            issues.append((i, "字符串拼接", line.strip()))
    
    # 检查time.sleep在主线程
    for i, line in enumerate(lines, 1):
        if 'time.sleep' in line and 'thread' not in line.lower():
            issues.append((i, "主线程sleep", line.strip()))
    
    # 检查大循环
    for i, line in enumerate(lines, 1):
        if re.search(r'for.*in.*range\(\d{3,}\)', line):
            issues.append((i, "大循环", line.strip()))
    
    if issues:
        print(f"⚠️ 发现 {len(issues)} 个性能问题:")
        for line_num, issue_type, line_content in issues:
            print(f"   第{line_num}行 [{issue_type}]: {line_content}")
    else:
        print("✅ 没有发现明显的性能问题")
    
    return len(issues) == 0

def check_error_handling():
    """检查错误处理"""
    print("\n🔍 检查错误处理")
    print("=" * 60)
    
    script_path = Path(__file__).parent.parent / "01_主程序" / "数据处理与导入应用_完整版.py"
    
    with open(script_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    try_blocks = 0
    except_blocks = 0
    bare_except = 0
    
    for i, line in enumerate(lines, 1):
        stripped = line.strip()
        if stripped.startswith('try:'):
            try_blocks += 1
        elif stripped.startswith('except:'):
            bare_except += 1
            except_blocks += 1
        elif stripped.startswith('except '):
            except_blocks += 1
    
    print(f"📊 错误处理统计:")
    print(f"   try块: {try_blocks}")
    print(f"   except块: {except_blocks}")
    print(f"   裸except: {bare_except}")
    
    if bare_except > 0:
        print(f"⚠️ 发现 {bare_except} 个裸except块，建议指定具体异常类型")
    
    coverage = (except_blocks / try_blocks * 100) if try_blocks > 0 else 0
    print(f"   错误处理覆盖率: {coverage:.1f}%")
    
    return bare_except == 0 and coverage > 80

def check_memory_leaks():
    """检查潜在的内存泄漏"""
    print("\n🔍 检查潜在内存泄漏")
    print("=" * 60)
    
    script_path = Path(__file__).parent.parent / "01_主程序" / "数据处理与导入应用_完整版.py"
    
    with open(script_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    potential_leaks = []
    
    for i, line in enumerate(lines, 1):
        stripped = line.strip()
        
        # 检查大数据结构创建
        if re.search(r'pd\.read_excel.*engine=', stripped):
            potential_leaks.append((i, "大文件读取", stripped))
        
        # 检查循环中的列表追加
        if '.append(' in stripped and any(keyword in lines[max(0, i-5):i] for keyword in ['for ', 'while ']):
            potential_leaks.append((i, "循环中追加", stripped))
        
        # 检查未关闭的文件
        if 'open(' in stripped and 'with ' not in stripped:
            potential_leaks.append((i, "可能未关闭文件", stripped))
    
    if potential_leaks:
        print(f"⚠️ 发现 {len(potential_leaks)} 个潜在内存问题:")
        for line_num, issue_type, line_content in potential_leaks:
            print(f"   第{line_num}行 [{issue_type}]: {line_content[:80]}...")
    else:
        print("✅ 没有发现明显的内存泄漏风险")
    
    return len(potential_leaks) == 0

def check_thread_safety():
    """检查线程安全问题"""
    print("\n🔍 检查线程安全问题")
    print("=" * 60)
    
    script_path = Path(__file__).parent.parent / "01_主程序" / "数据处理与导入应用_完整版.py"
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    thread_issues = []
    
    # 检查GUI操作在非主线程
    for i, line in enumerate(lines, 1):
        if 'threading.Thread' in line:
            # 检查后续几行是否有GUI操作
            for j in range(i, min(i+10, len(lines))):
                if any(gui_op in lines[j] for gui_op in ['.configure(', '.pack(', '.grid(', 'messagebox.']):
                    thread_issues.append((j+1, "GUI操作在线程中", lines[j].strip()))
    
    # 检查共享变量访问
    shared_vars = ['self.', 'global ']
    for i, line in enumerate(lines, 1):
        if 'threading.Thread' in line:
            for j in range(i, min(i+20, len(lines))):
                if any(var in lines[j] for var in shared_vars) and 'lock' not in lines[j].lower():
                    # 这可能是共享变量访问
                    pass  # 简化检查
    
    if thread_issues:
        print(f"⚠️ 发现 {len(thread_issues)} 个线程安全问题:")
        for line_num, issue_type, line_content in thread_issues:
            print(f"   第{line_num}行 [{issue_type}]: {line_content}")
    else:
        print("✅ 没有发现明显的线程安全问题")
    
    return len(thread_issues) == 0

def provide_optimization_suggestions():
    """提供优化建议"""
    print("\n💡 优化建议")
    print("=" * 60)
    
    suggestions = [
        "1. 🔧 修复重复导入：移除重复的import语句",
        "2. 🚀 优化函数内导入：将导入移到文件顶部",
        "3. 🧵 改进线程安全：使用root.after()调度GUI更新",
        "4. 💾 内存优化：及时释放大数据结构",
        "5. ⚡性能优化：避免在主线程中使用time.sleep",
        "6. 🛡️ 错误处理：使用具体的异常类型而不是裸except",
        "7. 📝 代码清理：移除未使用的导入和变量",
        "8. 🔍 日志优化：使用异步日志写入",
    ]
    
    for suggestion in suggestions:
        print(suggestion)

def main():
    """主函数"""
    print("🔧 检查主应用程序代码质量")
    print("=" * 80)
    
    try:
        # 1. 检查重复导入
        imports_ok = check_duplicate_imports()
        
        # 2. 检查函数内部导入
        inline_imports_ok = check_inline_imports()
        
        # 3. 检查性能问题
        performance_ok = check_performance_issues()
        
        # 4. 检查错误处理
        error_handling_ok = check_error_handling()
        
        # 5. 检查内存泄漏
        memory_ok = check_memory_leaks()
        
        # 6. 检查线程安全
        thread_safety_ok = check_thread_safety()
        
        # 7. 提供优化建议
        provide_optimization_suggestions()
        
        print("\n" + "=" * 80)
        print("🎯 代码质量检查结果")
        print("=" * 80)
        
        total_checks = 6
        passed_checks = sum([
            imports_ok, inline_imports_ok, performance_ok,
            error_handling_ok, memory_ok, thread_safety_ok
        ])
        
        print(f"📊 检查结果: {passed_checks}/{total_checks} 项通过")
        
        if passed_checks == total_checks:
            print("🎉 代码质量良好！")
        elif passed_checks >= total_checks * 0.8:
            print("✅ 代码质量较好，有少量改进空间")
        elif passed_checks >= total_checks * 0.6:
            print("⚠️ 代码质量一般，建议进行优化")
        else:
            print("❌ 代码质量需要改进")
        
        return passed_checks >= total_checks * 0.8
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
