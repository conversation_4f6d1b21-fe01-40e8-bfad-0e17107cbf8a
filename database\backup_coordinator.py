#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备份协调器
协调和管理数据库备份操作
"""

import os
import logging
import threading
from datetime import datetime

logger = logging.getLogger(__name__)

class BackupCoordinator:
    """备份协调器"""
    
    def __init__(self):
        self.backup_lock = threading.Lock()
        self.backup_in_progress = False
        
    def is_backup_in_progress(self):
        """检查是否有备份正在进行"""
        return self.backup_in_progress
    
    def start_backup(self, db_path, backup_reason="manual"):
        """开始备份操作"""
        with self.backup_lock:
            if self.backup_in_progress:
                logger.warning("备份已在进行中，跳过此次备份请求")
                return False
            
            self.backup_in_progress = True
            
        try:
            backup_dir = os.path.join(os.path.dirname(db_path), "backups")
            os.makedirs(backup_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"sales_reports_backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            # 执行备份
            import shutil
            shutil.copy2(db_path, backup_path)
            
            logger.info(f"备份完成: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"备份失败: {e}")
            return False
        finally:
            self.backup_in_progress = False
    
    def cleanup_old_backups(self, backup_dir, keep_count=10):
        """清理旧备份文件"""
        try:
            if not os.path.exists(backup_dir):
                return
            
            backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.db')]
            backup_files.sort(key=lambda x: os.path.getmtime(os.path.join(backup_dir, x)))
            
            if len(backup_files) > keep_count:
                files_to_remove = backup_files[:-keep_count]
                for file in files_to_remove:
                    file_path = os.path.join(backup_dir, file)
                    os.remove(file_path)
                    logger.info(f"删除旧备份: {file}")
                    
        except Exception as e:
            logger.error(f"清理旧备份失败: {e}")

# 全局备份协调器实例
_backup_coordinator = None

def get_backup_coordinator():
    """获取备份协调器实例"""
    global _backup_coordinator
    if _backup_coordinator is None:
        _backup_coordinator = BackupCoordinator()
    return _backup_coordinator
