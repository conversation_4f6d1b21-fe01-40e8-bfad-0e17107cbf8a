# -*- coding: utf-8 -*-
"""
错误检测测试脚本
测试新添加的立即错误检查功能

版本: 1.0
作者: AI Assistant  
日期: 2025-01-18
"""

import os
import sys

def test_error_detection():
    """测试错误检测功能"""
    print("🧪 开始测试错误检测功能...")
    
    try:
        # 导入主应用程序
        from 数据处理与导入应用_完整版 import ProcessRunner, SafeGUIUpdater
        
        # 创建GUI更新器（模拟）
        gui_updater = SafeGUIUpdater(None)
        
        # 创建ProcessRunner实例
        process_runner = ProcessRunner(gui_updater)
        
        print("✅ 模块导入成功")
        
        # 测试1：空脚本路径
        print("\n🧪 测试1：空脚本路径")
        try:
            result = process_runner.run_script("", ["--test"])
            print(f"结果: {result.returncode}")
        except Exception as e:
            print(f"捕获异常: {e}")
        
        # 测试2：不存在的脚本
        print("\n🧪 测试2：不存在的脚本")
        try:
            result = process_runner.run_script("不存在的脚本.py", ["--test"])
            print(f"结果: {result.returncode}")
        except Exception as e:
            print(f"捕获异常: {e}")
        
        # 测试3：无效参数
        print("\n🧪 测试3：无效参数")
        try:
            result = process_runner.run_script("test_error_detection.py", None)
            print(f"结果: {result.returncode}")
        except Exception as e:
            print(f"捕获异常: {e}")
        
        # 测试4：文件路径提取
        print("\n🧪 测试4：文件路径提取")
        try:
            file_paths = process_runner._extract_file_paths_from_args([
                "--file1", "不存在的文件1.xlsx",
                "--file2", "不存在的文件2.xlsx"
            ])
            print(f"提取的文件路径: {file_paths}")
            
            # 测试超时计算
            timeout = process_runner._calculate_smart_timeout(file_paths)
            print(f"计算的超时时间: {timeout}秒")
            
        except Exception as e:
            print(f"捕获异常: {e}")
        
        print("\n✅ 错误检测测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_error_detection()
    sys.exit(0 if success else 1)
