# -*- coding: utf-8 -*-
"""
调试Transaction Num问题
"""

import pandas as pd

def debug_column_mapping():
    """调试列名映射"""
    print("🔍 调试Transaction Num列名映射")
    print("=" * 60)
    
    # 模拟data_import_optimized.py中的column_mapping
    column_mapping = {
        'Copartner name': 'Copartner_name',
        'Order No.': 'Order_No',
        'Transaction Num': 'Transaction_Num',  # 关键映射
        'Order types': 'Order_types',
        'Order status': 'Order_status',
        'Order price': 'Order_price',
        'Order time': 'Order_time',
        'Equipment ID': 'Equipment_ID',
        'Equipment name': 'Equipment_name',
        'Branch name': 'Branch_name',
        'Payment date': 'Payment_date',
        'User name': 'User_name',
        'Time': 'Time',
        'Matched Order ID': 'Matched_Order_ID',
        'OrderTime_dt': 'OrderTime_dt',
        'Payment': 'Payment',
    }
    
    print("📋 列名映射表:")
    for source, target in column_mapping.items():
        highlight = " ← 关键!" if source == 'Transaction Num' else ""
        print(f"  '{source}' → '{target}'{highlight}")
    
    # 创建测试数据
    test_data = {
        'Copartner name': ['商户A', '商户B'],
        'Order No.': ['ORD001', 'ORD002'],
        'Transaction Num': ['TXN001', 'TXN002'],  # 关键数据
        'Order price': [10.50, 25.00],
        'Equipment ID': ['EQ001', 'EQ002'],
        'Serial number': ['001', '002'],  # 额外列
    }
    
    df = pd.DataFrame(test_data)
    
    print(f"\n📊 原始数据:")
    print(f"  列名: {list(df.columns)}")
    print(f"  Transaction Num数据: {list(df['Transaction Num'])}")
    
    # 步骤1: 重命名列
    df_renamed = df.rename(columns=column_mapping)
    
    print(f"\n🔧 重命名后:")
    print(f"  列名: {list(df_renamed.columns)}")
    if 'Transaction_Num' in df_renamed.columns:
        print(f"  ✅ Transaction_Num存在")
        print(f"  Transaction_Num数据: {list(df_renamed['Transaction_Num'])}")
    else:
        print(f"  ❌ Transaction_Num不存在")
    
    # 步骤2: 创建known_columns
    known_columns = set(column_mapping.values())
    useful_extra_columns = {'Transaction ID', 'Matched_Flag'}
    known_columns.update(useful_extra_columns)
    
    print(f"\n📋 已知列集合:")
    for col in sorted(known_columns):
        highlight = " ← 关键!" if col == 'Transaction_Num' else ""
        print(f"  - {col}{highlight}")
    
    print(f"\nTransaction_Num在已知列中: {'✅' if 'Transaction_Num' in known_columns else '❌'}")
    
    # 步骤3: 过滤列
    available_columns = [col for col in df_renamed.columns if col in known_columns]
    df_filtered = df_renamed[available_columns].copy()
    
    print(f"\n🔧 过滤后:")
    print(f"  保留的列: {available_columns}")
    print(f"  Transaction_Num在保留列中: {'✅' if 'Transaction_Num' in available_columns else '❌'}")
    
    if 'Transaction_Num' in df_filtered.columns:
        print(f"  ✅ Transaction_Num最终保留")
        print(f"  Transaction_Num最终数据: {list(df_filtered['Transaction_Num'])}")
        return True
    else:
        print(f"  ❌ Transaction_Num最终丢失")
        return False

def debug_intelligent_mapper():
    """调试智能映射器"""
    print(f"\n🔧 调试智能映射器:")
    print("=" * 40)
    
    try:
        import sys
        import os
        
        # 添加01_主程序目录到路径
        mapper_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '01_主程序')
        sys.path.insert(0, mapper_path)
        
        from intelligent_column_mapper import IntelligentColumnMapper
        
        print(f"✅ 成功导入智能映射器")
        print(f"📁 导入路径: {mapper_path}")
        
        # 测试映射器
        mapper = IntelligentColumnMapper()
        
        test_data = {
            'Copartner name': ['商户A'],
            'Order No.': ['ORD001'],
            'Transaction Num': ['TXN001'],  # 关键测试
            'Order price': [10.50],
            'Equipment ID': ['EQ001'],
            'Serial number': ['001'],  # 应该被忽略
        }
        
        df = pd.DataFrame(test_data)
        df_mapped, analysis = mapper.apply_intelligent_mapping(df)
        
        print(f"\n📊 智能映射结果:")
        print(f"  原始列数: {analysis['total_columns']}")
        print(f"  保留列数: {len(df_mapped.columns)}")
        print(f"  保留的列: {list(df_mapped.columns)}")
        
        if 'Transaction_Num' in df_mapped.columns:
            print(f"  ✅ Transaction_Num正确映射")
            print(f"  Transaction_Num数据: {list(df_mapped['Transaction_Num'])}")
            return True
        else:
            print(f"  ❌ Transaction_Num映射失败")
            return False
            
    except ImportError as e:
        print(f"❌ 导入智能映射器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 智能映射器测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🔍 Transaction Num问题深度调试")
    print("=" * 80)
    
    success1 = debug_column_mapping()
    success2 = debug_intelligent_mapper()
    
    print(f"\n📊 调试结果总结:")
    print(f"  传统映射: {'✅ 正常' if success1 else '❌ 异常'}")
    print(f"  智能映射: {'✅ 正常' if success2 else '❌ 异常'}")
    
    if success1 and success2:
        print(f"\n🎉 Transaction Num处理逻辑正常")
        print(f"  问题可能在于: 数据导入流程的其他环节")
        print(f"  建议检查: 数据库插入逻辑")
    elif success1 and not success2:
        print(f"\n⚠️ 智能映射器有问题，但传统映射正常")
        print(f"  应用会回退到传统映射，Transaction Num应该能正常处理")
    else:
        print(f"\n❌ 存在严重问题，需要进一步修复")

if __name__ == "__main__":
    main()
