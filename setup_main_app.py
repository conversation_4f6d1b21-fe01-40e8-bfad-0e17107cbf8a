#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置主应用程序文件
"""

import os
import shutil

def setup_main_app():
    """将完整版文件复制为主应用程序"""
    
    source_file = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\数据处理与导入应用_完整版.py"
    target_file = "main_app.py"
    
    print("🔄 设置主应用程序文件...")
    
    if os.path.exists(source_file):
        try:
            # 复制文件
            shutil.copy2(source_file, target_file)
            print(f"✅ 成功复制: {source_file} -> {target_file}")
            
            # 检查文件大小
            source_size = os.path.getsize(source_file)
            target_size = os.path.getsize(target_file)
            
            print(f"📊 源文件大小: {source_size:,} bytes")
            print(f"📊 目标文件大小: {target_size:,} bytes")
            
            if source_size == target_size:
                print("✅ 文件复制验证成功！")
            else:
                print("⚠️ 文件大小不匹配，可能复制不完整")
                
        except Exception as e:
            print(f"❌ 复制失败: {e}")
    else:
        print(f"❌ 源文件不存在: {source_file}")

if __name__ == "__main__":
    setup_main_app()
