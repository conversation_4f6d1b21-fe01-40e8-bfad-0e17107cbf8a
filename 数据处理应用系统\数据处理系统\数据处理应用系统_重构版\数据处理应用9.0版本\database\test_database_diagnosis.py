#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 数据库诊断功能验证测试

验证数据库诊断功能的完整性：
1. 基本诊断功能测试 ✅
2. 不同诊断级别测试 ✅
3. 备份前后诊断测试 ✅
4. 恢复前后诊断测试 ✅
5. 健康报告生成测试 ✅
6. 异常数据库诊断测试 ✅

作者: Claude 4.0 sonnet
创建时间: 2025-01-22
"""

import os
import sys
import sqlite3
import tempfile
import time
from datetime import datetime
from pathlib import Path

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟依赖
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")
    def critical(self, msg): print(f"CRITICAL: {msg}")
    def debug(self, msg): print(f"DEBUG: {msg}")

class DatabaseError(Exception): pass
class BackupError(Exception): pass

def get_logger(name): return MockLogger()

# 模拟导入
sys.modules['utils.exceptions'] = type(sys)('utils.exceptions')
sys.modules['utils.exceptions'].DatabaseError = DatabaseError
sys.modules['utils.exceptions'].BackupError = BackupError
sys.modules['utils.logger'] = type(sys)('utils.logger')
sys.modules['utils.logger'].get_logger = get_logger

try:
    from backup_manager import DatabaseBackupManager
    print("✅ 成功导入数据库诊断增强后的备份管理器")
except ImportError as e:
    print(f"❌ 无法导入备份管理器: {e}")
    sys.exit(1)


class DatabaseDiagnosisTest:
    """数据库诊断功能验证测试"""
    
    def __init__(self):
        self.test_results = []
        self.temp_dir = None
        self.test_db_path = None
        self.backup_manager = None
    
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置数据库诊断测试环境...")
        
        self.temp_dir = Path(tempfile.mkdtemp(prefix="db_diagnosis_test_"))
        self.test_db_path = self.temp_dir / "test_database.db"
        
        # 创建测试数据库
        self._create_test_database()
        
        # 初始化备份管理器
        self.backup_manager = DatabaseBackupManager(str(self.test_db_path))
        
        print(f"✅ 数据库诊断测试环境已设置: {self.temp_dir}")
    
    def _create_test_database(self):
        """创建测试数据库"""
        with sqlite3.connect(self.test_db_path) as conn:
            cursor = conn.cursor()
            
            # 创建测试表
            cursor.execute("""
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    email TEXT UNIQUE,
                    age INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            cursor.execute("""
                CREATE TABLE orders (
                    id INTEGER PRIMARY KEY,
                    user_id INTEGER,
                    product_name TEXT NOT NULL,
                    amount DECIMAL(10,2),
                    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX idx_users_email ON users(email)")
            cursor.execute("CREATE INDEX idx_orders_user_id ON orders(user_id)")
            
            # 插入测试数据
            test_users = [
                ("张三", "<EMAIL>", 25),
                ("李四", "<EMAIL>", 30),
                ("王五", "<EMAIL>", 28),
                ("赵六", "<EMAIL>", 35)
            ]
            cursor.executemany("INSERT INTO users (name, email, age) VALUES (?, ?, ?)", test_users)
            
            test_orders = [
                (1, "笔记本电脑", 5999.99),
                (1, "鼠标", 99.99),
                (2, "键盘", 299.99),
                (3, "显示器", 1999.99),
                (4, "耳机", 399.99)
            ]
            cursor.executemany("INSERT INTO orders (user_id, product_name, amount) VALUES (?, ?, ?)", test_orders)
            
            conn.commit()
    
    def test_basic_diagnosis(self):
        """🔧 测试1：基本诊断功能"""
        print("\n🔍 测试1：基本诊断功能")
        
        try:
            # 测试不同诊断级别
            levels = ["quick", "standard", "full", "deep"]
            
            for level in levels:
                print(f"  测试诊断级别: {level}")
                
                start_time = time.time()
                diagnosis = self.backup_manager.diagnose_database(self.test_db_path, level)
                end_time = time.time()
                
                duration = end_time - start_time
                print(f"    诊断耗时: {duration:.2f} 秒")
                print(f"    总体状态: {diagnosis['overall_status']}")
                print(f"    检查项数: {len(diagnosis.get('checks', {}))}")
                
                # 验证诊断结果结构
                required_keys = ["db_path", "timestamp", "diagnostic_level", "overall_status", "checks"]
                missing_keys = [key for key in required_keys if key not in diagnosis]
                
                if missing_keys:
                    print(f"    ❌ 缺少必要字段: {missing_keys}")
                    self.test_results.append((f"基本诊断-{level}", False, f"缺少字段: {missing_keys}"))
                else:
                    print(f"    ✅ 诊断结构完整")
                    self.test_results.append((f"基本诊断-{level}", True, f"耗时{duration:.2f}秒"))
            
        except Exception as e:
            print(f"❌ 基本诊断功能测试失败: {e}")
            self.test_results.append(("基本诊断功能", False, str(e)))
    
    def test_health_check_and_report(self):
        """🔧 测试2：健康检查和报告生成"""
        print("\n📊 测试2：健康检查和报告生成")
        
        try:
            # 测试健康检查
            print("  执行健康检查...")
            health_result = self.backup_manager.run_database_health_check("full")
            
            if health_result and "overall_status" in health_result:
                print(f"  ✅ 健康检查完成，状态: {health_result['overall_status']}")
                
                # 测试报告生成
                print("  生成健康报告...")
                report = self.backup_manager.generate_health_report("full", save_to_file=True)
                
                if report and len(report) > 100:  # 报告应该有一定长度
                    print(f"  ✅ 健康报告生成成功，长度: {len(report)} 字符")
                    
                    # 检查报告内容
                    if "数据库健康检查报告" in report and "检查摘要" in report:
                        print("  ✅ 报告内容完整")
                        self.test_results.append(("健康检查和报告", True, "功能正常"))
                    else:
                        print("  ❌ 报告内容不完整")
                        self.test_results.append(("健康检查和报告", False, "报告内容不完整"))
                else:
                    print("  ❌ 报告生成失败")
                    self.test_results.append(("健康检查和报告", False, "报告生成失败"))
            else:
                print("  ❌ 健康检查失败")
                self.test_results.append(("健康检查和报告", False, "健康检查失败"))
                
        except Exception as e:
            print(f"❌ 健康检查和报告测试失败: {e}")
            self.test_results.append(("健康检查和报告", False, str(e)))
    
    def test_backup_with_diagnosis(self):
        """🔧 测试3：带诊断的备份功能"""
        print("\n💾 测试3：带诊断的备份功能")
        
        try:
            print("  执行带诊断的备份...")
            
            # 执行备份（应该包含备份前后诊断）
            backup_file = self.backup_manager.create_backup("诊断测试备份")
            
            if backup_file:
                print(f"  ✅ 备份成功: {os.path.basename(backup_file)}")
                
                # 验证备份文件存在
                if os.path.exists(backup_file):
                    print("  ✅ 备份文件存在")
                    
                    # 手动诊断备份文件
                    backup_diagnosis = self.backup_manager.diagnose_database(Path(backup_file), "standard")
                    
                    if backup_diagnosis["overall_status"] in ["healthy", "warning"]:
                        print(f"  ✅ 备份文件诊断通过: {backup_diagnosis['overall_status']}")
                        self.test_results.append(("带诊断的备份", True, "备份和诊断成功"))
                    else:
                        print(f"  ❌ 备份文件诊断失败: {backup_diagnosis['overall_status']}")
                        self.test_results.append(("带诊断的备份", False, "备份文件诊断失败"))
                else:
                    print("  ❌ 备份文件不存在")
                    self.test_results.append(("带诊断的备份", False, "备份文件不存在"))
            else:
                print("  ❌ 备份失败")
                self.test_results.append(("带诊断的备份", False, "备份失败"))
                
        except Exception as e:
            print(f"❌ 带诊断的备份测试失败: {e}")
            self.test_results.append(("带诊断的备份", False, str(e)))
    
    def test_restore_with_diagnosis(self):
        """🔧 测试4：带诊断的恢复功能"""
        print("\n🔄 测试4：带诊断的恢复功能")
        
        try:
            # 先创建一个备份
            backup_file = self.backup_manager.create_backup("恢复测试备份")
            
            if backup_file:
                print(f"  ✅ 创建测试备份: {os.path.basename(backup_file)}")
                
                # 修改原数据库（添加一些数据）
                with sqlite3.connect(self.test_db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("INSERT INTO users (name, email, age) VALUES (?, ?, ?)", 
                                 ("测试用户", "<EMAIL>", 99))
                    conn.commit()
                
                print("  ✅ 修改了原数据库")
                
                # 执行恢复（应该包含恢复前后诊断）
                restore_result = self.backup_manager.restore_from_backup(backup_file, None)
                
                if restore_result:
                    print("  ✅ 恢复成功")
                    
                    # 验证恢复后的数据
                    with sqlite3.connect(self.test_db_path) as conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT COUNT(*) FROM users WHERE name = '测试用户'")
                        test_user_count = cursor.fetchone()[0]
                    
                    if test_user_count == 0:
                        print("  ✅ 数据恢复正确，测试用户已被移除")
                        self.test_results.append(("带诊断的恢复", True, "恢复和诊断成功"))
                    else:
                        print("  ❌ 数据恢复不正确，测试用户仍存在")
                        self.test_results.append(("带诊断的恢复", False, "数据恢复不正确"))
                else:
                    print("  ❌ 恢复失败")
                    self.test_results.append(("带诊断的恢复", False, "恢复失败"))
            else:
                print("  ❌ 创建测试备份失败")
                self.test_results.append(("带诊断的恢复", False, "创建备份失败"))
                
        except Exception as e:
            print(f"❌ 带诊断的恢复测试失败: {e}")
            self.test_results.append(("带诊断的恢复", False, str(e)))
    
    def test_corrupted_database_diagnosis(self):
        """🔧 测试5：损坏数据库诊断"""
        print("\n🚨 测试5：损坏数据库诊断")
        
        try:
            # 创建一个损坏的数据库文件
            corrupted_db_path = self.temp_dir / "corrupted.db"
            
            # 写入无效数据
            with open(corrupted_db_path, 'wb') as f:
                f.write(b"这不是一个有效的SQLite数据库文件")
            
            print("  ✅ 创建损坏的数据库文件")
            
            # 诊断损坏的数据库
            diagnosis = self.backup_manager.diagnose_database(corrupted_db_path, "standard")
            
            if diagnosis["overall_status"] == "error":
                print("  ✅ 正确识别出损坏的数据库")
                print(f"    发现问题: {len(diagnosis.get('issues', []))}")
                self.test_results.append(("损坏数据库诊断", True, "正确识别损坏"))
            else:
                print(f"  ❌ 未能识别损坏的数据库，状态: {diagnosis['overall_status']}")
                self.test_results.append(("损坏数据库诊断", False, "未识别损坏"))
                
        except Exception as e:
            print(f"❌ 损坏数据库诊断测试失败: {e}")
            self.test_results.append(("损坏数据库诊断", False, str(e)))
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        try:
            if self.temp_dir and self.temp_dir.exists():
                import shutil
                shutil.rmtree(self.temp_dir)
                print(f"✅ 已清理测试目录: {self.temp_dir}")
        except Exception as e:
            print(f"⚠️ 清理测试环境失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始数据库诊断功能验证测试")
        print("=" * 70)
        
        try:
            self.setup_test_environment()
            
            # 运行各项测试
            self.test_basic_diagnosis()
            self.test_health_check_and_report()
            self.test_backup_with_diagnosis()
            self.test_restore_with_diagnosis()
            self.test_corrupted_database_diagnosis()
            
            # 显示测试结果
            self.show_test_results()
            
        finally:
            self.cleanup_test_environment()
    
    def show_test_results(self):
        """显示测试结果"""
        print("\n" + "=" * 70)
        print("📊 数据库诊断功能验证结果")
        print("=" * 70)
        
        passed = 0
        failed = 0
        
        for test_name, success, details in self.test_results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{status} {test_name}: {details}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print("=" * 70)
        print(f"总计: {passed + failed} 项测试")
        print(f"✅ 通过: {passed} 项")
        print(f"❌ 失败: {failed} 项")
        
        if failed == 0:
            print("\n🎉 所有测试通过！数据库诊断功能完全正常！")
            print("\n🔧 诊断功能成果：")
            print("   ✅ 多级诊断：支持quick、standard、full、deep四个级别")
            print("   ✅ 全面检查：连接、完整性、外键、索引、性能等")
            print("   ✅ 备份诊断：备份前后自动诊断，确保数据质量")
            print("   ✅ 恢复诊断：恢复前后自动诊断，确保操作安全")
            print("   ✅ 健康报告：详细的诊断报告，支持保存到文件")
            print("   ✅ 异常检测：准确识别损坏和异常的数据库")
            print("\n💯 数据库安全性和可靠性大幅提升！")
        else:
            print(f"\n⚠️ 有 {failed} 项测试失败，部分诊断功能可能存在问题")


def main():
    """主函数"""
    test_suite = DatabaseDiagnosisTest()
    test_suite.run_all_tests()


if __name__ == "__main__":
    main()
