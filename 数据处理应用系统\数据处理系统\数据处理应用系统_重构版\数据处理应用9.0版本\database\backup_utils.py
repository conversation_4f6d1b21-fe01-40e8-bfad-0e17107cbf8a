"""
🔧 备份功能修复：统一的备份工具类
解决时间戳不准确、文件命名不人性化等问题

作者: Claude 4.0 sonnet
创建时间: 2025-01-22
"""

import os
import time
import threading
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import logging


class BackupTimestampManager:
    """统一的备份时间戳管理器"""
    
    def __init__(self):
        self._lock = threading.Lock()
        self._operation_timestamps = {}
    
    def get_operation_timestamp(self, operation_id: str) -> str:
        """
        获取操作的统一时间戳
        
        Args:
            operation_id: 操作唯一标识
            
        Returns:
            格式化的时间戳字符串 YYYYMMDD_HHMMSS
        """
        with self._lock:
            if operation_id not in self._operation_timestamps:
                # 生成新的时间戳，使用秒级精度确保可读性
                self._operation_timestamps[operation_id] = datetime.now().strftime("%Y%m%d_%H%M%S")
            return self._operation_timestamps[operation_id]
    
    def clear_operation_timestamp(self, operation_id: str):
        """清理操作时间戳"""
        with self._lock:
            self._operation_timestamps.pop(operation_id, None)
    
    @staticmethod
    def generate_current_timestamp() -> str:
        """生成当前时间戳"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")


class BackupNamingManager:
    """优化的备份文件命名管理器"""
    
    @staticmethod
    def generate_user_friendly_filename(operation_name: str, timestamp: str = None) -> str:
        """
        生成用户友好的备份文件名
        
        Args:
            operation_name: 操作名称
            timestamp: 时间戳，如果为None则自动生成
            
        Returns:
            用户友好的备份文件名
        """
        if timestamp is None:
            timestamp = BackupTimestampManager.generate_current_timestamp()
        
        # 清理操作名称，保持可读性
        safe_operation_name = BackupNamingManager._sanitize_operation_name(operation_name)
        
        # 生成简洁的文件名格式：backup_操作名称_时间戳.db
        filename = f"backup_{safe_operation_name}_{timestamp}.db"
        
        return filename
    
    @staticmethod
    def _sanitize_operation_name(operation_name: str) -> str:
        """
        清理操作名称，保持可读性
        
        Args:
            operation_name: 原始操作名称
            
        Returns:
            清理后的操作名称
        """
        # 保留中文、英文、数字和基本符号
        safe_chars = []
        for char in operation_name:
            if char.isalnum() or char in ['_', '-', '手', '动', '备', '份', '导', '入', '前', '后', '恢', '复']:
                safe_chars.append(char)
            elif char in [' ', '　']:  # 空格替换为下划线
                safe_chars.append('_')
        
        safe_name = ''.join(safe_chars)
        
        # 清理连续的下划线
        while '__' in safe_name:
            safe_name = safe_name.replace('__', '_')
        
        # 移除首尾下划线
        safe_name = safe_name.strip('_')
        
        # 如果名称为空或过短，使用默认名称
        if len(safe_name) < 2:
            safe_name = "手动备份"
        
        return safe_name
    
    @staticmethod
    def resolve_filename_conflict(backup_dir: Path, filename: str) -> str:
        """
        解决文件名冲突，使用简单的序号后缀
        
        Args:
            backup_dir: 备份目录
            filename: 原始文件名
            
        Returns:
            解决冲突后的文件名
        """
        if not (backup_dir / filename).exists():
            return filename
        
        # 分离文件名和扩展名
        name_part, ext = os.path.splitext(filename)
        
        # 添加序号后缀
        counter = 1
        while True:
            new_filename = f"{name_part}_{counter:02d}{ext}"
            if not (backup_dir / new_filename).exists():
                return new_filename
            counter += 1
            
            # 防止无限循环
            if counter > 999:
                # 使用时间戳作为后缀
                timestamp_suffix = str(int(time.time() * 1000) % 100000)
                return f"{name_part}_{timestamp_suffix}{ext}"


class ImprovedFileLockManager:
    """改进的文件锁管理器，解决Windows平台锁问题"""
    
    def __init__(self, lock_file_path: Path):
        self.lock_file_path = lock_file_path
        self.lock_file = None
        self._thread_lock = threading.Lock()
        self.logger = logging.getLogger('backup_lock')
    
    def acquire_lock(self, operation_name: str, timeout: int = 30) -> bool:
        """
        获取文件锁，改进的Windows兼容实现
        
        Args:
            operation_name: 操作名称
            timeout: 超时时间（秒）
            
        Returns:
            是否成功获取锁
        """
        with self._thread_lock:
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                try:
                    # 确保锁文件目录存在
                    self.lock_file_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 尝试创建锁文件
                    self.lock_file = open(self.lock_file_path, 'w', encoding='utf-8')
                    
                    # Windows平台的文件锁
                    try:
                        import msvcrt
                        msvcrt.locking(self.lock_file.fileno(), msvcrt.LK_NBLCK, 1)
                    except ImportError:
                        # 非Windows平台，使用fcntl
                        import fcntl
                        fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                    
                    # 写入锁信息
                    lock_info = {
                        'operation': operation_name,
                        'timestamp': datetime.now().isoformat(),
                        'pid': os.getpid()
                    }
                    self.lock_file.write(f"{lock_info}\n")
                    self.lock_file.flush()
                    
                    self.logger.info(f"成功获取备份锁: {operation_name}")
                    return True
                    
                except (OSError, IOError) as e:
                    # 锁被占用，等待后重试
                    if self.lock_file:
                        try:
                            self.lock_file.close()
                        except:
                            pass
                        self.lock_file = None
                    
                    self.logger.debug(f"锁被占用，等待重试: {e}")
                    time.sleep(0.5)
                    
                except Exception as e:
                    self.logger.error(f"获取锁时发生未知错误: {e}")
                    if self.lock_file:
                        try:
                            self.lock_file.close()
                        except:
                            pass
                        self.lock_file = None
                    return False
            
            self.logger.warning(f"获取锁超时: {operation_name}")
            return False
    
    def release_lock(self):
        """释放文件锁，改进的异常处理"""
        with self._thread_lock:
            if self.lock_file:
                try:
                    # 先解锁
                    try:
                        import msvcrt
                        msvcrt.locking(self.lock_file.fileno(), msvcrt.LK_UNLCK, 1)
                    except ImportError:
                        import fcntl
                        fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_UN)
                    
                    # 关闭文件
                    self.lock_file.close()
                    self.lock_file = None
                    
                    # 删除锁文件
                    try:
                        if self.lock_file_path.exists():
                            self.lock_file_path.unlink()
                    except OSError:
                        # 删除失败不影响锁的释放
                        pass
                    
                    self.logger.info("成功释放备份锁")
                    
                except Exception as e:
                    self.logger.warning(f"释放锁时出现问题: {e}")
                    # 确保文件句柄被关闭
                    try:
                        if self.lock_file:
                            self.lock_file.close()
                    except:
                        pass
                    self.lock_file = None
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release_lock()


# 全局实例
_timestamp_manager = BackupTimestampManager()

def get_timestamp_manager() -> BackupTimestampManager:
    """获取全局时间戳管理器实例"""
    return _timestamp_manager
