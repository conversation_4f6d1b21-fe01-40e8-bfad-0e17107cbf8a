#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Series布尔值错误修复 - 测试修复后的重复检测逻辑
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_empty_dataframe_handling():
    """测试空DataFrame处理"""
    print("🔧 1. 测试空DataFrame处理")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建空DataFrame
        empty_df = pd.DataFrame(columns=['Transaction_Num', 'Order_time', 'Order_price'])
        normal_df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_time': ['2025-01-01 10:00:00'],
            'Order_price': [100.0]
        })
        
        print("📊 测试场景1: 两个空DataFrame")
        try:
            result = processor._enhanced_duplicate_detection(empty_df, empty_df)
            print("✅ 空DataFrame处理成功")
        except Exception as e:
            print(f"❌ 空DataFrame处理失败: {e}")
            return False
        
        print("📊 测试场景2: 新数据为空，现有数据不为空")
        try:
            result = processor._enhanced_duplicate_detection(empty_df, normal_df)
            print("✅ 新数据为空处理成功")
        except Exception as e:
            print(f"❌ 新数据为空处理失败: {e}")
            return False
        
        print("📊 测试场景3: 新数据不为空，现有数据为空")
        try:
            result = processor._enhanced_duplicate_detection(normal_df, empty_df)
            print("✅ 现有数据为空处理成功")
        except Exception as e:
            print(f"❌ 现有数据为空处理失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 空DataFrame处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transaction_num_detection():
    """测试Transaction_Num检测逻辑"""
    print("\n🔧 2. 测试Transaction_Num检测逻辑")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试场景1: 都有Transaction_Num
        df_with_txn = pd.DataFrame({
            'Transaction_Num': ['TXN001', 'TXN002'],
            'Order_time': ['2025-01-01 10:00:00', '2025-01-02 11:00:00'],
            'Order_price': [100.0, 200.0]
        })
        
        existing_with_txn = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_time': ['2025-01-01 10:00:00'],
            'Order_price': [100.0]
        })
        
        print("📊 测试场景1: 都有Transaction_Num")
        try:
            result = processor._enhanced_duplicate_detection(df_with_txn, existing_with_txn)
            fully_dup, partial_dup, new_data = result
            print(f"✅ Transaction_Num检测成功: 完全重复{len(fully_dup)}, 部分重复{len(partial_dup)}, 新数据{len(new_data)}")
        except Exception as e:
            print(f"❌ Transaction_Num检测失败: {e}")
            return False
        
        # 测试场景2: 都没有Transaction_Num
        df_no_txn = pd.DataFrame({
            'Order_time': ['2025-01-01 10:00:00', '2025-01-02 11:00:00'],
            'Payment_date': ['2025-01-01', '2025-01-02'],
            'Equipment_ID': ['EQ001', 'EQ002'],
            'Order_price': [100.0, 200.0]
        })
        
        existing_no_txn = pd.DataFrame({
            'Order_time': ['2025-01-01 10:00:00'],
            'Payment_date': ['2025-01-01'],
            'Equipment_ID': ['EQ001'],
            'Order_price': [100.0]
        })
        
        print("📊 测试场景2: 都没有Transaction_Num")
        try:
            result = processor._enhanced_duplicate_detection(df_no_txn, existing_no_txn)
            fully_dup, partial_dup, new_data = result
            print(f"✅ 传统方法检测成功: 完全重复{len(fully_dup)}, 部分重复{len(partial_dup)}, 新数据{len(new_data)}")
        except Exception as e:
            print(f"❌ 传统方法检测失败: {e}")
            return False
        
        # 测试场景3: Transaction_Num全为空
        df_empty_txn = pd.DataFrame({
            'Transaction_Num': [None, None],
            'Order_time': ['2025-01-01 10:00:00', '2025-01-02 11:00:00'],
            'Order_price': [100.0, 200.0]
        })
        
        existing_empty_txn = pd.DataFrame({
            'Transaction_Num': [None],
            'Order_time': ['2025-01-01 10:00:00'],
            'Order_price': [100.0]
        })
        
        print("📊 测试场景3: Transaction_Num全为空")
        try:
            result = processor._enhanced_duplicate_detection(df_empty_txn, existing_empty_txn)
            fully_dup, partial_dup, new_data = result
            print(f"✅ 空Transaction_Num处理成功: 完全重复{len(fully_dup)}, 部分重复{len(partial_dup)}, 新数据{len(new_data)}")
        except Exception as e:
            print(f"❌ 空Transaction_Num处理失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Transaction_Num检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smart_incremental_duplicate_check():
    """测试智能增量重复检测"""
    print("\n🔧 3. 测试智能增量重复检测")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        test_df = pd.DataFrame({
            'Transaction_Num': ['TXN001', 'TXN002'],
            'Order_No': ['ORD001', 'ORD002'],
            'Order_time': ['2025-01-01 10:00:00', '2025-01-02 11:00:00'],
            'Order_price': [100.0, 200.0],
            'Order_status': ['Finished', 'Finished'],
            'Equipment_ID': ['EQ001', 'EQ002']
        })
        
        print(f"📊 测试数据: {len(test_df)} 条记录")
        
        # 执行智能增量重复检测
        try:
            fully_dup, partial_dup, new_data = processor.smart_incremental_duplicate_check(test_df, 'IOT')
            print(f"✅ 智能增量检测成功:")
            print(f"  完全重复: {len(fully_dup)} 条")
            print(f"  部分重复: {len(partial_dup)} 条")
            print(f"  新数据: {len(new_data)} 条")
            return True
        except Exception as e:
            print(f"❌ 智能增量检测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 智能增量重复检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_apply_operations():
    """测试apply操作安全性"""
    print("\n🔧 4. 测试apply操作安全性")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_time': ['2025-01-01 10:00:00'],
            'Order_price': [100.0]
        })
        
        existing_df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_time': ['2025-01-01 10:00:00'],
            'Order_price': [100.0]
        })
        
        print("📊 测试正常apply操作")
        try:
            result = processor._detect_by_transaction_num(df, existing_df)
            print("✅ apply操作安全性测试通过")
            return True
        except Exception as e:
            print(f"❌ apply操作安全性测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ apply操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Series布尔值错误修复验证")
    print("=" * 80)
    
    # 设置非交互模式
    os.environ['NON_INTERACTIVE'] = '1'
    os.environ['AUTO_DUPLICATE_HANDLING'] = 'overwrite'
    os.environ['AUTO_MISSING_HANDLING'] = 'ignore'
    
    tests = [
        ("空DataFrame处理", test_empty_dataframe_handling),
        ("Transaction_Num检测逻辑", test_transaction_num_detection),
        ("智能增量重复检测", test_smart_incremental_duplicate_check),
        ("apply操作安全性", test_apply_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 Series布尔值错误修复验证结果")
    print("=" * 80)
    
    print(f"📊 通过测试: {passed}/{total}")
    success_rate = (passed / total) * 100
    print(f"📊 成功率: {success_rate:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过")
        print("✅ Series布尔值错误已修复")
        print("✅ 重复检测逻辑正常工作")
        print("✅ 空DataFrame处理安全")
        print("✅ apply操作不再出错")
        print("\n💡 修复内容:")
        print("  - 添加了DataFrame空值检查")
        print("  - 修复了Series布尔值判断")
        print("  - 优化了apply操作安全性")
        print("  - 增强了错误处理机制")
    elif passed >= total * 0.75:
        print("✅ 大部分测试通过")
        print("⚠️ 少量问题可能仍存在")
    else:
        print("❌ 多个测试失败")
        print("🔧 Series错误可能仍然存在")
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 修复验证{'成功' if success else '需要改进'}")
    input("按回车键退出...")
