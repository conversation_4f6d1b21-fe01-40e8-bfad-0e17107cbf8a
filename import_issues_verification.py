#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入问题修复验证脚本
验证所有模块导入是否正常
"""

import sys
import os
import importlib
from datetime import datetime

def test_module_import(module_name, description=""):
    """测试模块导入"""
    try:
        module = importlib.import_module(module_name)
        print(f"  ✅ {module_name}: 导入成功 {description}")
        return True
    except ImportError as e:
        print(f"  ❌ {module_name}: 导入失败 - {e}")
        return False
    except Exception as e:
        print(f"  ⚠️ {module_name}: 导入异常 - {e}")
        return False

def test_database_modules():
    """测试数据库模块"""
    print("🔍 测试数据库模块...")
    
    modules = [
        ("database.dual_database_manager", "双数据库管理器"),
        ("database.backup_coordinator", "备份协调器"),
        ("database.connection_pool", "连接池"),
        ("database.backup_manager", "备份管理器")
    ]
    
    success_count = 0
    for module_name, description in modules:
        if test_module_import(module_name, description):
            success_count += 1
    
    return success_count, len(modules)

def test_ui_modules():
    """测试UI模块"""
    print("\n🎨 测试UI模块...")
    
    modules = [
        ("ui.database_config_dialog", "数据库配置对话框")
    ]
    
    success_count = 0
    for module_name, description in modules:
        if test_module_import(module_name, description):
            success_count += 1
    
    return success_count, len(modules)

def test_utility_modules():
    """测试工具模块"""
    print("\n🔧 测试工具模块...")
    
    modules = [
        ("backup_naming_utils", "备份命名工具"),
        ("unified_processing_tab", "统一处理标签页")
    ]
    
    success_count = 0
    for module_name, description in modules:
        if test_module_import(module_name, description):
            success_count += 1
    
    return success_count, len(modules)

def test_module_functionality():
    """测试模块基本功能"""
    print("\n⚙️ 测试模块基本功能...")
    
    try:
        # 测试双数据库管理器
        from database.dual_database_manager import get_dual_database_manager
        manager = get_dual_database_manager()
        print(f"  ✅ 双数据库管理器实例化成功")
        
        # 测试备份协调器
        from database.backup_coordinator import get_backup_coordinator
        coordinator = get_backup_coordinator()
        print(f"  ✅ 备份协调器实例化成功")
        
        # 测试连接池
        from database.connection_pool import get_connection_pool
        # 注意：这里不实际初始化连接池，只测试导入
        print(f"  ✅ 连接池模块功能正常")
        
        # 测试备份管理器
        from database.backup_manager import get_backup_manager
        backup_mgr = get_backup_manager()
        print(f"  ✅ 备份管理器实例化成功")
        
        # 测试备份命名工具
        from backup_naming_utils import generate_backup_name
        backup_name = generate_backup_name()
        print(f"  ✅ 备份命名工具功能正常: {backup_name}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模块功能测试失败: {e}")
        return False

def test_main_app_imports():
    """测试main_app.py的关键导入"""
    print("\n📱 测试main_app.py关键导入...")
    
    try:
        # 添加当前目录到Python路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 测试关键导入（不实际运行main_app）
        imports_to_test = [
            "unified_processing_tab",
            "database.dual_database_manager", 
            "ui.database_config_dialog",
            "database.backup_coordinator",
            "database.connection_pool",
            "backup_naming_utils",
            "database.backup_manager"
        ]
        
        success_count = 0
        for module_name in imports_to_test:
            try:
                importlib.import_module(module_name)
                print(f"  ✅ {module_name}: main_app导入测试通过")
                success_count += 1
            except Exception as e:
                print(f"  ❌ {module_name}: main_app导入测试失败 - {e}")
        
        return success_count, len(imports_to_test)
        
    except Exception as e:
        print(f"  ❌ main_app导入测试异常: {e}")
        return 0, 0

def check_file_structure():
    """检查文件结构"""
    print("\n📁 检查文件结构...")
    
    required_files = [
        "database/__init__.py",
        "database/dual_database_manager.py",
        "database/backup_coordinator.py", 
        "database/connection_pool.py",
        "database/backup_manager.py",
        "ui/__init__.py",
        "ui/database_config_dialog.py",
        "backup_naming_utils.py",
        "unified_processing_tab.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}: 文件存在")
        else:
            print(f"  ❌ {file_path}: 文件缺失")
            missing_files.append(file_path)
    
    return len(missing_files) == 0, missing_files

def generate_import_report(db_success, db_total, ui_success, ui_total, 
                          util_success, util_total, main_success, main_total,
                          functionality_ok, structure_ok, missing_files):
    """生成导入问题修复报告"""
    print("\n" + "="*60)
    print("📊 导入问题修复验证报告")
    print("="*60)
    
    total_success = db_success + ui_success + util_success + main_success
    total_modules = db_total + ui_total + util_total + main_total
    
    print(f"\n📋 模块导入测试结果:")
    print(f"  🔧 数据库模块: {db_success}/{db_total}")
    print(f"  🎨 UI模块: {ui_success}/{ui_total}")
    print(f"  🔧 工具模块: {util_success}/{util_total}")
    print(f"  📱 main_app导入: {main_success}/{main_total}")
    print(f"  📊 总计: {total_success}/{total_modules}")
    
    success_rate = (total_success / total_modules * 100) if total_modules > 0 else 0
    
    print(f"\n⚙️ 功能测试:")
    print(f"  {'✅' if functionality_ok else '❌'} 模块基本功能测试")
    
    print(f"\n📁 文件结构检查:")
    print(f"  {'✅' if structure_ok else '❌'} 必需文件完整性")
    if missing_files:
        print(f"  缺失文件: {', '.join(missing_files)}")
    
    print(f"\n🎯 总体评估:")
    print(f"  📊 导入成功率: {success_rate:.1f}%")
    
    if success_rate == 100 and functionality_ok and structure_ok:
        print(f"  🎉 完美！所有导入问题都已修复！")
        print(f"  ✅ main_app.py现在应该能够正常启动！")
        return True
    elif success_rate >= 90:
        print(f"  ✅ 优秀！大部分导入问题已修复")
        print(f"  ⚠️ 少数问题需要关注")
        return True
    else:
        print(f"  ⚠️ 仍有重要的导入问题需要修复")
        return False

def main():
    """主函数"""
    print("🚀 开始导入问题修复验证...")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查文件结构
    structure_ok, missing_files = check_file_structure()
    
    # 2. 测试模块导入
    db_success, db_total = test_database_modules()
    ui_success, ui_total = test_ui_modules()
    util_success, util_total = test_utility_modules()
    main_success, main_total = test_main_app_imports()
    
    # 3. 测试模块功能
    functionality_ok = test_module_functionality()
    
    # 4. 生成报告
    all_ok = generate_import_report(
        db_success, db_total, ui_success, ui_total,
        util_success, util_total, main_success, main_total,
        functionality_ok, structure_ok, missing_files
    )
    
    if all_ok:
        print("\n🎊 恭喜！所有导入问题都已成功修复！")
        print("🚀 main_app.py现在应该能够正常启动和运行！")
    else:
        print("\n🔧 请根据上述报告修复剩余的导入问题")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
