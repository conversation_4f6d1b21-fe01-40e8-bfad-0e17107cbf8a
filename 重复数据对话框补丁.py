
def show_duplicate_data_dialog(fully_duplicate_count=0, partial_different_count=0, new_data_count=0):
    """显示重复数据处理对话框 - 修复版"""
    import tkinter as tk
    from tkinter import ttk
    
    class DuplicateDataDialog:
        def __init__(self):
            self.result = None
            self.root = tk.Tk()
            self.setup_dialog()
        
        def setup_dialog(self):
            # 窗口基本设置
            self.root.title("数据重复处理")
            self.root.geometry("500x400")
            self.root.resizable(False, False)
            
            # 居中显示
            self.center_window()
            
            # 设置为置顶
            self.root.attributes('-topmost', True)
            self.root.focus_force()
            
            # 创建主框架
            main_frame = ttk.Frame(self.root, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 标题
            title_label = ttk.Label(
                main_frame, 
                text="🔍 检测到重复数据", 
                font=("Arial", 14, "bold")
            )
            title_label.pack(pady=(0, 15))
            
            # 信息显示
            info_text = f"""检测到重复数据情况：
            
• 完全重复: {fully_duplicate_count} 条
• 部分重复: {partial_different_count} 条  
• 新数据: {new_data_count} 条

请选择处理方式："""
            
            info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
            info_label.pack(pady=(0, 15))
            
            # 选项
            self.choice_var = tk.StringVar(value="skip")
            
            options = [
                ("skip", "跳过重复数据 - 只导入新数据"),
                ("overwrite", "覆盖更新 - 用新数据覆盖现有数据"),
                ("incremental", "增量更新 - 只更新不同字段"),
                ("refresh_daily", "重新更新 - 删除当天数据后重新导入"),
                ("cancel", "取消导入 - 停止操作")
            ]
            
            for value, text in options:
                rb = ttk.Radiobutton(
                    main_frame,
                    text=text,
                    variable=self.choice_var,
                    value=value
                )
                rb.pack(anchor=tk.W, pady=2)
            
            # 按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(20, 0))
            
            # 确认按钮
            confirm_btn = ttk.Button(
                button_frame,
                text="确认",
                command=self.confirm_choice
            )
            confirm_btn.pack(side=tk.RIGHT, padx=(5, 0))
            
            # 取消按钮
            cancel_btn = ttk.Button(
                button_frame,
                text="取消",
                command=self.cancel_choice
            )
            cancel_btn.pack(side=tk.RIGHT)
        
        def center_window(self):
            self.root.update_idletasks()
            width = 500
            height = 400
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f"{width}x{height}+{x}+{y}")
        
        def confirm_choice(self):
            self.result = self.choice_var.get()
            self.root.destroy()
        
        def cancel_choice(self):
            self.result = "cancel"
            self.root.destroy()
        
        def show(self):
            self.root.mainloop()
            return self.result
    
    dialog = DuplicateDataDialog()
    return dialog.show()
