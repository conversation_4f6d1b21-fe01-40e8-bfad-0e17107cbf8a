# -*- coding: utf-8 -*-
"""
测试双数据库导入功能
"""

import pandas as pd
from database.dual_database_manager import get_dual_database_manager, import_to_databases

def test_dual_import():
    """测试双数据库导入"""
    print("🧪 测试双数据库导入功能")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'Order_No': ['TEST001', 'TEST002'],
        'Equipment_ID': ['EQ001', 'EQ002'],
        'Order_time': ['2024-06-19 10:00:00', '2024-06-19 11:00:00'],
        'Order_price': [10.0, 20.0],
        'Order_status': ['Finished', 'Finished'],
        'Order_types': ['Normal', 'Normal'],
        'Equipment_name': ['设备1', '设备2'],
        'Branch_name': ['分店1', '分店2']
    })
    
    print(f"测试数据: {len(test_data)} 行")
    
    # 获取管理器
    manager = get_dual_database_manager()
    available_dbs = manager.get_available_databases()
    print(f"可用数据库: {available_dbs}")
    
    # 测试单独导入到SQLite
    print("\n📤 测试导入到SQLite...")
    try:
        results = import_to_databases(test_data, 'TEST_Table_SQLite', ['SQLite'])
        print(f"SQLite导入结果: {results}")
    except Exception as e:
        print(f"SQLite导入失败: {e}")
    
    # 测试单独导入到PostgreSQL
    print("\n📤 测试导入到PostgreSQL...")
    try:
        results = import_to_databases(test_data, 'TEST_Table_PostgreSQL', ['PostgreSQL'])
        print(f"PostgreSQL导入结果: {results}")
    except Exception as e:
        print(f"PostgreSQL导入失败: {e}")
    
    # 测试同时导入到两个数据库
    print("\n📤 测试同时导入到两个数据库...")
    try:
        results = import_to_databases(test_data, 'TEST_Table_Both', ['SQLite', 'PostgreSQL'])
        print(f"双数据库导入结果: {results}")
    except Exception as e:
        print(f"双数据库导入失败: {e}")

if __name__ == "__main__":
    test_dual_import()
