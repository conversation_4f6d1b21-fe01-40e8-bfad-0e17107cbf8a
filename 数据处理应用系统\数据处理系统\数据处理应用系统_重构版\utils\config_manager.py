# -*- coding: utf-8 -*-
"""
统一配置管理系统
支持多种配置格式，环境变量覆盖，配置验证等功能
"""

import os
import json
import configparser
from typing import Dict, Any, Optional, Union
from pathlib import Path
from .exceptions import ConfigurationError


class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为当前目录下的config文件夹
        """
        self.config_dir = Path(config_dir) if config_dir else Path(__file__).parent.parent / "config"
        self.config_dir.mkdir(exist_ok=True)
        
        self._config: Dict[str, Any] = {}
        self._load_all_configs()
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        # 加载默认配置
        self._load_default_config()
        
        # 加载配置文件
        config_files = [
            "app_config.json",
            "database_config.json",
            "logging_config.json",
            "app_config.ini"  # 兼容旧配置
        ]
        
        for config_file in config_files:
            config_path = self.config_dir / config_file
            if config_path.exists():
                self._load_config_file(config_path)
        
        # 应用环境变量覆盖
        self._apply_env_overrides()
    
    def _load_default_config(self):
        """加载默认配置"""
        self._config = {
            'database': {
                'path': 'database/sales_reports.db',
                'backup_dir': 'database/backups',
                'max_backups': 10,
                'connection_pool_size': 10,
                'timeout': 30.0
            },
            'processing': {
                'batch_size': 1000,
                'timeout': 300,
                'max_workers': 4,
                'chunk_size': 10000,
                'progress_update_interval': 100
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'max_size': 10485760,  # 10MB
                'backup_count': 5,
                'log_dir': 'logs'
            },
            'ui': {
                'window_width': 900,
                'window_height': 700,
                'theme': 'default',
                'auto_save_config': True
            },
            'scripts': {
                'data_processor': 'core/data_processor.py',
                'data_import': 'scripts/data_import.py',
                'refund_process': 'scripts/refund_process.py'
            },
            'files': {
                'temp_dir': 'temp_data',
                'allowed_extensions': ['.xlsx', '.xls', '.csv'],
                'max_file_size': 104857600  # 100MB
            },
            'backup': {
                'auto_backup': True,
                'backup_before_import': True,
                'backup_before_refund': True,
                'compression': True
            },
            'log_optimization': {
                'batch_update_interval_ms': 100,  # 批量更新间隔（毫秒）
                'stdout_batch_size': 20,          # stdout批量大小
                'stderr_batch_size': 10,          # stderr批量大小
                'completion_delay_seconds': 2,    # 完成检测延迟（秒）
                'enable_transaction_filter': True, # 是否启用Transaction ID过滤
                'enable_batch_update': True       # 是否启用批量更新
            }
        }
    
    def _load_config_file(self, config_path: Path):
        """加载单个配置文件"""
        try:
            if config_path.suffix.lower() == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            elif config_path.suffix.lower() == '.ini':
                config_data = self._load_ini_config(config_path)
            else:
                return

            if config_data:
                self._merge_config(config_data)

        except Exception as e:
            raise ConfigurationError(f"加载配置文件失败: {config_path}", details={'error': str(e)})
    
    def _load_ini_config(self, config_path: Path) -> Dict[str, Any]:
        """加载INI格式配置文件（兼容旧版本）"""
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        result = {}
        for section_name in config.sections():
            section = {}
            for key, value in config[section_name].items():
                # 尝试转换数据类型
                section[key] = self._convert_value(value)
            result[section_name.lower()] = section
        
        return result
    
    def _convert_value(self, value: str) -> Union[str, int, float, bool]:
        """转换配置值的数据类型"""
        # 布尔值
        if value.lower() in ('true', 'yes', 'on', '1'):
            return True
        elif value.lower() in ('false', 'no', 'off', '0'):
            return False
        
        # 数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # 字符串
        return value
    
    def _merge_config(self, new_config: Dict[str, Any]):
        """合并配置"""
        def merge_dict(base: Dict[str, Any], update: Dict[str, Any]):
            for key, value in update.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    merge_dict(base[key], value)
                else:
                    base[key] = value
        
        merge_dict(self._config, new_config)
    
    def _apply_env_overrides(self):
        """应用环境变量覆盖"""
        env_mappings = {
            'DB_PATH': ('database', 'path'),
            'DB_BACKUP_DIR': ('database', 'backup_dir'),
            'LOG_LEVEL': ('logging', 'level'),
            'BATCH_SIZE': ('processing', 'batch_size'),
            'MAX_WORKERS': ('processing', 'max_workers'),
            'WINDOW_WIDTH': ('ui', 'window_width'),
            'WINDOW_HEIGHT': ('ui', 'window_height')
        }
        
        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                if section not in self._config:
                    self._config[section] = {}
                self._config[section][key] = self._convert_value(value)
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            return self._config[section][key]
        except KeyError:
            return default

    def has(self, section: str, key: str = None) -> bool:
        """检查配置项是否存在"""
        if key is None:
            return section in self._config
        return section in self._config and key in self._config[section]
    
    def set(self, section: str, key: str, value: Any):
        """设置配置值"""
        if section not in self._config:
            self._config[section] = {}
        self._config[section][key] = value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取整个配置节"""
        if section not in self._config:
            raise ConfigurationError(f"配置节不存在: {section}")
        return self._config[section].copy()
    
    def save_config(self, config_file: str = "app_config.json"):
        """保存配置到文件"""
        config_path = self.config_dir / config_file
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            raise ConfigurationError(f"保存配置文件失败: {config_path}", details={'error': str(e)})
    
    def validate_config(self) -> bool:
        """验证配置完整性"""
        required_sections = ['database', 'processing', 'logging', 'ui']
        
        for section in required_sections:
            if section not in self._config:
                raise ConfigurationError(f"缺少必需的配置节: {section}")
        
        # 验证数据库配置
        db_config = self._config['database']
        if not db_config.get('path'):
            raise ConfigurationError("数据库路径未配置")
        
        # 验证处理配置
        proc_config = self._config['processing']
        if proc_config.get('batch_size', 0) <= 0:
            raise ConfigurationError("批处理大小必须大于0")
        
        return True
    
    def get_db_path(self) -> str:
        """获取数据库路径（兼容主应用程序接口）"""
        return self.get('database', 'path',
                       os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "database", "sales_reports.db"))

    @property
    def config(self) -> Dict[str, Any]:
        """获取完整配置（只读）"""
        return self._config.copy()


# 全局配置管理器实例
config_manager = ConfigManager()
