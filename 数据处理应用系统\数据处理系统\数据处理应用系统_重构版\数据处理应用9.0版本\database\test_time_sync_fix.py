#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 时间同步修复验证测试

验证备份时间同步修复：
1. 文件名中的时间戳与文件实际时间一致 ✅
2. 备份列表显示正确的时间 ✅
3. 消除时间差异问题 ✅

作者: Claude 4.0 sonnet
创建时间: 2025-01-22
"""

import os
import sys
import sqlite3
import tempfile
import time
from datetime import datetime
from pathlib import Path

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟依赖
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")
    def critical(self, msg): print(f"CRITICAL: {msg}")
    def debug(self, msg): print(f"DEBUG: {msg}")

class DatabaseError(Exception): pass
class BackupError(Exception): pass

def get_logger(name): return MockLogger()

# 模拟导入
sys.modules['utils.exceptions'] = type(sys)('utils.exceptions')
sys.modules['utils.exceptions'].DatabaseError = DatabaseError
sys.modules['utils.exceptions'].BackupError = BackupError
sys.modules['utils.logger'] = type(sys)('utils.logger')
sys.modules['utils.logger'].get_logger = get_logger

try:
    from backup_coordinator import BackupCoordinator
    from backup_manager import DatabaseBackupManager
    print("✅ 成功导入修复后的备份组件")
except ImportError as e:
    print(f"❌ 无法导入备份组件: {e}")
    sys.exit(1)


class TimeSyncFixTest:
    """时间同步修复验证测试"""
    
    def __init__(self):
        self.test_results = []
        self.temp_dir = None
        self.test_db_path = None
        self.backup_coordinator = None
        self.backup_manager = None
    
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置时间同步修复测试环境...")
        
        self.temp_dir = Path(tempfile.mkdtemp(prefix="time_sync_test_"))
        self.test_db_path = self.temp_dir / "test_database.db"
        
        # 创建测试数据库
        self._create_test_database()
        
        # 初始化备份组件
        self.backup_coordinator = BackupCoordinator(str(self.test_db_path))
        self.backup_manager = DatabaseBackupManager(str(self.test_db_path))
        
        print(f"✅ 时间同步修复测试环境已设置: {self.temp_dir}")
    
    def _create_test_database(self):
        """创建测试数据库"""
        with sqlite3.connect(self.test_db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE test_data (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    operation_type TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            test_data = [
                ("时间同步测试数据1", "数据导入"),
                ("时间同步测试数据2", "退款处理"),
                ("时间同步测试数据3", "手动备份")
            ]
            cursor.executemany("INSERT INTO test_data (name, operation_type) VALUES (?, ?)", test_data)
            conn.commit()
    
    def test_coordinator_time_sync(self):
        """🔧 测试1：备份协调器时间同步"""
        print("\n⏰ 测试1：备份协调器时间同步")
        
        try:
            # 记录开始时间
            start_time = datetime.now()
            
            # 生成备份文件名和时间戳
            filename, timestamp = self.backup_coordinator.generate_safe_backup_filename("时间同步测试", "test")
            
            # 创建一个测试文件
            test_file = self.temp_dir / filename
            test_file.write_text("测试内容")
            
            # 同步文件时间戳
            self.backup_coordinator.sync_file_timestamp(test_file, timestamp)
            
            # 检查文件时间
            file_stat = test_file.stat()
            file_time = datetime.fromtimestamp(file_stat.st_mtime)
            
            # 计算时间差
            time_diff = abs((file_time - timestamp).total_seconds())
            
            print(f"  文件名时间戳: {timestamp}")
            print(f"  文件实际时间: {file_time}")
            print(f"  时间差: {time_diff:.2f} 秒")
            
            if time_diff <= 1:  # 允许1秒误差
                print("  ✅ 协调器时间同步成功")
                self.test_results.append(("协调器时间同步", True, f"时间差{time_diff:.2f}秒"))
            else:
                print("  ❌ 协调器时间同步失败")
                self.test_results.append(("协调器时间同步", False, f"时间差{time_diff:.2f}秒"))
                
        except Exception as e:
            print(f"❌ 协调器时间同步测试失败: {e}")
            self.test_results.append(("协调器时间同步", False, str(e)))
    
    def test_manager_time_sync(self):
        """🔧 测试2：备份管理器时间同步"""
        print("\n⏰ 测试2：备份管理器时间同步")
        
        try:
            # 创建备份
            backup_file = self.backup_manager.backup_database("时间同步测试备份")
            
            if backup_file:
                # 解析文件名中的时间戳
                filename = os.path.basename(backup_file)
                print(f"  备份文件名: {filename}")
                
                # 提取时间戳部分
                parts = filename.replace('.db', '').split('_')
                if len(parts) >= 3:
                    date_part = parts[-2]
                    time_part = parts[-1]
                    
                    try:
                        filename_time = datetime.strptime(f"{date_part}_{time_part}", "%Y%m%d_%H%M%S")
                        
                        # 获取文件实际时间
                        file_stat = os.stat(backup_file)
                        file_time = datetime.fromtimestamp(file_stat.st_mtime)
                        
                        # 计算时间差
                        time_diff = abs((file_time - filename_time).total_seconds())
                        
                        print(f"  文件名时间戳: {filename_time}")
                        print(f"  文件实际时间: {file_time}")
                        print(f"  时间差: {time_diff:.2f} 秒")
                        
                        if time_diff <= 1:  # 允许1秒误差
                            print("  ✅ 管理器时间同步成功")
                            self.test_results.append(("管理器时间同步", True, f"时间差{time_diff:.2f}秒"))
                        else:
                            print("  ❌ 管理器时间同步失败")
                            self.test_results.append(("管理器时间同步", False, f"时间差{time_diff:.2f}秒"))
                            
                    except ValueError as parse_error:
                        print(f"  ❌ 时间戳解析失败: {parse_error}")
                        self.test_results.append(("管理器时间同步", False, f"解析失败: {parse_error}"))
                else:
                    print(f"  ❌ 文件名格式不正确: {filename}")
                    self.test_results.append(("管理器时间同步", False, "文件名格式错误"))
            else:
                print("  ❌ 备份创建失败")
                self.test_results.append(("管理器时间同步", False, "备份创建失败"))
                
        except Exception as e:
            print(f"❌ 管理器时间同步测试失败: {e}")
            self.test_results.append(("管理器时间同步", False, str(e)))
    
    def test_backup_list_time_accuracy(self):
        """🔧 测试3：备份列表时间准确性"""
        print("\n📋 测试3：备份列表时间准确性")
        
        try:
            # 创建多个备份
            backup_operations = ["测试备份A", "测试备份B", "测试备份C"]
            created_times = []
            
            for operation in backup_operations:
                # 记录创建时间
                create_time = datetime.now()
                created_times.append(create_time)
                
                # 创建备份
                backup_file = self.backup_manager.backup_database(operation)
                
                if backup_file:
                    print(f"  ✅ 创建备份: {operation}")
                else:
                    print(f"  ❌ 创建备份失败: {operation}")
                
                time.sleep(1)  # 确保时间戳不同
            
            # 获取备份列表
            backup_list = self.backup_manager.get_backup_list()
            
            if backup_list:
                print(f"  📋 备份列表包含 {len(backup_list)} 个备份:")
                
                time_accuracy_count = 0
                
                for i, backup_info in enumerate(backup_list):
                    filename = backup_info.get('filename', '未知')
                    created_str = backup_info.get('created', '未知时间')
                    
                    print(f"    {i+1}. {filename}")
                    print(f"       显示时间: {created_str}")
                    
                    # 检查时间是否在合理范围内
                    if created_str != '未知时间':
                        try:
                            displayed_time = datetime.strptime(created_str, "%Y-%m-%d %H:%M:%S")
                            
                            # 与创建时间比较（允许一定误差）
                            if i < len(created_times):
                                expected_time = created_times[-(i+1)]  # 最新的在前
                                time_diff = abs((displayed_time - expected_time).total_seconds())
                                
                                if time_diff <= 60:  # 允许1分钟误差
                                    time_accuracy_count += 1
                                    print(f"       ✅ 时间准确 (差异{time_diff:.1f}秒)")
                                else:
                                    print(f"       ❌ 时间不准确 (差异{time_diff:.1f}秒)")
                            
                        except ValueError:
                            print(f"       ❌ 时间格式错误")
                    
                    print()
                
                # 计算准确率
                accuracy_rate = time_accuracy_count / len(backup_list) if backup_list else 0
                
                if accuracy_rate >= 0.8:  # 80%以上准确率
                    print(f"  ✅ 备份列表时间准确性良好 ({accuracy_rate:.1%})")
                    self.test_results.append(("备份列表时间准确性", True, f"准确率{accuracy_rate:.1%}"))
                else:
                    print(f"  ❌ 备份列表时间准确性不足 ({accuracy_rate:.1%})")
                    self.test_results.append(("备份列表时间准确性", False, f"准确率{accuracy_rate:.1%}"))
            else:
                print("  ❌ 备份列表为空")
                self.test_results.append(("备份列表时间准确性", False, "备份列表为空"))
                
        except Exception as e:
            print(f"❌ 备份列表时间准确性测试失败: {e}")
            self.test_results.append(("备份列表时间准确性", False, str(e)))
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        try:
            if self.temp_dir and self.temp_dir.exists():
                import shutil
                shutil.rmtree(self.temp_dir)
                print(f"✅ 已清理测试目录: {self.temp_dir}")
        except Exception as e:
            print(f"⚠️ 清理测试环境失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始时间同步修复验证测试")
        print("=" * 60)
        
        try:
            self.setup_test_environment()
            
            # 运行各项测试
            self.test_coordinator_time_sync()
            self.test_manager_time_sync()
            self.test_backup_list_time_accuracy()
            
            # 显示测试结果
            self.show_test_results()
            
        finally:
            self.cleanup_test_environment()
    
    def show_test_results(self):
        """显示测试结果"""
        print("\n" + "=" * 60)
        print("📊 时间同步修复验证结果")
        print("=" * 60)
        
        passed = 0
        failed = 0
        
        for test_name, success, details in self.test_results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{status} {test_name}: {details}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print("=" * 60)
        print(f"总计: {passed + failed} 项测试")
        print(f"✅ 通过: {passed} 项")
        print(f"❌ 失败: {failed} 项")
        
        if failed == 0:
            print("\n🎉 所有测试通过！时间同步修复完全成功！")
            print("\n🔧 修复成果：")
            print("   ✅ 文件名中的时间戳与文件实际时间一致")
            print("   ✅ 备份列表显示正确的时间")
            print("   ✅ 消除了时间差异问题")
            print("   ✅ 用户看到的时间是准确的")
        else:
            print(f"\n⚠️ 有 {failed} 项测试失败，需要进一步检查")


def main():
    """主函数"""
    test_suite = TimeSyncFixTest()
    test_suite.run_all_tests()


if __name__ == "__main__":
    main()
