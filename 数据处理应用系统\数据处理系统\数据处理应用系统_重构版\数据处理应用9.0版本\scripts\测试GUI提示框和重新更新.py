#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI提示框和重新更新功能脚本
"""

import os
import sys
import pandas as pd

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_date_extraction():
    """测试日期提取功能"""
    print("🔧 测试日期提取功能")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试不同的文件名格式
        test_files = [
            "030725 CHINA IOT.xlsx",  # DDMMYY 格式
            "250703 CHINA ZERO.xlsx",  # YYMMDD 格式
            "20250703 CHINA APP.xlsx",  # YYYYMMDD 格式
            "2025-07-03 CHINA IOT.xlsx",  # YYYY-MM-DD 格式
            "CHINA IOT 030725.xlsx",  # 日期在中间
            "CHINA IOT.xlsx",  # 没有日期
        ]
        
        # 创建测试数据框
        test_df = pd.DataFrame([
            {'Order_time': '2025-07-03 10:00:00', 'Order_No': 'TEST001'},
            {'Order_time': '2025-07-03 11:00:00', 'Order_No': 'TEST002'},
        ])
        
        print("日期提取测试结果:")
        for file_path in test_files:
            try:
                extracted_date = processor._extract_file_date(file_path, test_df)
                print(f"  {file_path} → {extracted_date}")
            except Exception as e:
                print(f"  {file_path} → 错误: {e}")
        
        print("✅ 日期提取功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tables_to_clear():
    """测试表清理配置"""
    print("\n🔧 测试表清理配置")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试不同平台的表清理配置
        platforms = ['IOT', 'ZERO', 'APP']
        
        print("表清理配置:")
        for platform in platforms:
            tables = processor._get_tables_to_clear(platform)
            print(f"  {platform}平台:")
            for table in tables:
                print(f"    - {table}")
        
        # 验证IOT平台包含APP_Sales
        iot_tables = processor._get_tables_to_clear('IOT')
        if 'APP_Sales' in iot_tables:
            print("✅ IOT平台正确包含APP_Sales表")
        else:
            print("❌ IOT平台缺少APP_Sales表")
            return False
        
        # 验证ZERO平台不包含APP_Sales
        zero_tables = processor._get_tables_to_clear('ZERO')
        if 'APP_Sales' not in zero_tables:
            print("✅ ZERO平台正确不包含APP_Sales表")
        else:
            print("❌ ZERO平台错误包含APP_Sales表")
            return False
        
        print("✅ 表清理配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_dialog_simulation():
    """测试GUI对话框模拟"""
    print("\n🔧 测试GUI对话框功能")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建模拟重复数据
        fully_duplicate = pd.DataFrame([
            {'Transaction_Num': 'TXN001', 'Order_No': 'ORD001', 'Order_time': '2025-07-03 10:00:00', 'Order_price': '100.00'},
            {'Transaction_Num': 'TXN002', 'Order_No': 'ORD002', 'Order_time': '2025-07-03 11:00:00', 'Order_price': '200.00'},
        ])
        
        partial_different = pd.DataFrame([
            {'Transaction_Num': 'TXN003', 'Order_No': 'ORD003', 'Order_time': '2025-07-03 12:00:00', 'Order_price': '300.00'},
        ])
        
        print("模拟重复数据:")
        print(f"  完全重复: {len(fully_duplicate)} 条")
        print(f"  部分重复: {len(partial_different)} 条")
        
        # 注意：这里不能真正测试GUI对话框，因为需要用户交互
        # 但我们可以验证方法是否存在和基本逻辑
        
        print("GUI对话框方法检查:")
        if hasattr(processor, '_handle_duplicate_gui'):
            print("✅ _handle_duplicate_gui 方法存在")
        else:
            print("❌ _handle_duplicate_gui 方法不存在")
            return False
        
        if hasattr(processor, '_handle_duplicate_console'):
            print("✅ _handle_duplicate_console 方法存在")
        else:
            print("❌ _handle_duplicate_console 方法不存在")
            return False
        
        print("✅ GUI对话框功能检查通过")
        print("💡 实际GUI对话框需要在真实环境中测试")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_handling():
    """测试策略处理"""
    print("\n🔧 测试策略处理功能")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        fully_duplicate = pd.DataFrame([{'Order_No': 'DUP001'}])
        partial_different = pd.DataFrame([{'Order_No': 'PART001'}])
        new_data = pd.DataFrame([{'Order_No': 'NEW001'}])
        
        # 测试不同策略
        strategies = ['overwrite', 'incremental', 'skip', 'refresh_daily', 'cancel']
        
        print("策略处理测试:")
        for strategy in strategies:
            try:
                result_data, stats = processor._apply_duplicate_handling_strategy(
                    strategy, fully_duplicate, partial_different, new_data, 'IOT'
                )
                print(f"  {strategy}: 返回 {len(result_data)} 条数据, 统计: {stats}")
                
                # 验证refresh_daily策略
                if strategy == 'refresh_daily':
                    if stats.get('refresh_daily') == True:
                        print("    ✅ refresh_daily 策略正确标记")
                    else:
                        print("    ❌ refresh_daily 策略标记错误")
                        return False
                        
            except Exception as e:
                print(f"  {strategy}: 错误 - {e}")
                return False
        
        print("✅ 策略处理功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 测试GUI提示框和重新更新功能")
    print("=" * 60)
    
    # 测试1: 日期提取
    test1_result = test_date_extraction()
    
    # 测试2: 表清理配置
    test2_result = test_tables_to_clear()
    
    # 测试3: GUI对话框
    test3_result = test_gui_dialog_simulation()
    
    # 测试4: 策略处理
    test4_result = test_strategy_handling()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   日期提取功能: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   表清理配置: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"   GUI对话框功能: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"   策略处理功能: {'✅ 通过' if test4_result else '❌ 失败'}")
    
    overall_success = all([test1_result, test2_result, test3_result, test4_result])
    print(f"   总体状态: {'✅ 功能完整' if overall_success else '❌ 需要修复'}")
    
    if overall_success:
        print("\n🎉 新功能测试通过！")
        print("新增功能:")
        print("1. 📱 GUI提示框 - 在GUI环境中显示重复数据处理选项")
        print("2. 🔄 重新更新选项 - 删除当天数据后重新导入")
        print("3. 📅 智能日期提取 - 从文件名或内容中提取日期")
        print("4. 🗂️ 平台表配置 - 根据平台确定清理范围")
        print("   - IOT: IOT_Sales + Refunding + Close + APP_Sales")
        print("   - ZERO: ZERO_Sales + Refunding + Close")
        print("   - APP: APP_Sales + Refunding + Close")
        
        print("\n💡 使用说明:")
        print("当检测到重复数据时，用户可以选择:")
        print("1. 覆盖更新 - 用新数据覆盖现有重复数据")
        print("2. 增量更新 - 只更新不同的字段")
        print("3. 跳过重复 - 只导入新数据")
        print("4. 🆕 重新更新 - 删除当天数据后重新导入")
        print("5. 取消导入 - 停止导入操作")

if __name__ == "__main__":
    main()
