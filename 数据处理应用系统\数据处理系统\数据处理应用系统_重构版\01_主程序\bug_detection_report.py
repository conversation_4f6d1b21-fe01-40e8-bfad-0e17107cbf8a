# -*- coding: utf-8 -*-
"""
Bug检测报告 - 架构优化代码全面检查
检查潜在的bug、性能问题和安全隐患

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import sys
import os
import ast
import importlib
import threading
import time
from typing import List, Dict, Any, Set

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


class BugDetector:
    """Bug检测器"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.suggestions = []
        
    def add_issue(self, category: str, severity: str, description: str, file_path: str = "", line_no: int = 0):
        """添加问题"""
        self.issues.append({
            "category": category,
            "severity": severity,
            "description": description,
            "file_path": file_path,
            "line_no": line_no
        })
        
    def add_warning(self, description: str, file_path: str = ""):
        """添加警告"""
        self.warnings.append({
            "description": description,
            "file_path": file_path
        })
        
    def add_suggestion(self, description: str, file_path: str = ""):
        """添加建议"""
        self.suggestions.append({
            "description": description,
            "file_path": file_path
        })


def check_import_issues():
    """检查导入问题"""
    print("\n🔍 检查导入问题...")
    detector = BugDetector()
    
    # 检查关键模块是否可以正常导入
    modules_to_check = [
        "infrastructure",
        "infrastructure.service_container",
        "infrastructure.event_bus",
        "infrastructure.feature_flags",
        "infrastructure.config_service",
        "infrastructure.logging_service",
        "infrastructure.gui_service",
        "infrastructure.gui_compatibility",
        "infrastructure.file_service",
        "infrastructure.backup_service"
    ]
    
    for module_name in modules_to_check:
        try:
            importlib.import_module(module_name)
            print(f"  ✅ {module_name}")
        except ImportError as e:
            detector.add_issue(
                "导入错误", "高", 
                f"模块 {module_name} 导入失败: {e}",
                module_name
            )
            print(f"  ❌ {module_name}: {e}")
        except Exception as e:
            detector.add_issue(
                "导入错误", "中", 
                f"模块 {module_name} 导入异常: {e}",
                module_name
            )
            print(f"  ⚠️ {module_name}: {e}")
            
    return detector


def check_circular_dependencies():
    """检查循环依赖"""
    print("\n🔄 检查循环依赖...")
    detector = BugDetector()
    
    try:
        # 测试基础设施初始化
        from infrastructure import initialize_infrastructure, get_infrastructure, shutdown_infrastructure
        
        config = {"feature_flags_config": "circular_dep_test.json"}
        success = initialize_infrastructure(config)
        
        if success:
            infrastructure = get_infrastructure()
            
            # 启用所有特性
            feature_flags = infrastructure.get_feature_flags()
            features = ["use_service_container", "use_event_bus", "use_cached_config", 
                       "use_async_logging", "use_file_service", "use_backup_service"]
            
            for feature in features:
                feature_flags.enable(feature, "循环依赖测试")
                
            # 重新注册服务
            infrastructure._register_core_services()
            
            # 测试服务获取
            container = infrastructure.get_container()
            
            services_to_test = [
                "config_manager",
                "logging_service", 
                "gui_service",
                "file_manager",
                "backup_manager"
            ]
            
            for service_name in services_to_test:
                try:
                    if container.has(service_name):
                        service = container.get(service_name)
                        print(f"  ✅ {service_name}: 无循环依赖")
                    else:
                        detector.add_warning(f"服务 {service_name} 未注册")
                except Exception as e:
                    detector.add_issue(
                        "循环依赖", "高",
                        f"服务 {service_name} 获取失败，可能存在循环依赖: {e}",
                        service_name
                    )
                    print(f"  ❌ {service_name}: {e}")
                    
            shutdown_infrastructure()
            
            # 清理测试文件
            if os.path.exists("circular_dep_test.json"):
                os.unlink("circular_dep_test.json")
                
        else:
            detector.add_issue("初始化", "高", "基础设施初始化失败")
            
    except Exception as e:
        detector.add_issue("循环依赖", "高", f"循环依赖检查失败: {e}")
        
    return detector


def check_thread_safety():
    """检查线程安全问题"""
    print("\n🧵 检查线程安全问题...")
    detector = BugDetector()
    
    try:
        # 测试日志服务线程安全
        from infrastructure.logging_service import LoggingServiceFactory
        from infrastructure.event_bus import EventBus
        
        event_bus = EventBus(max_queue_size=100, worker_threads=2)
        logging_service = LoggingServiceFactory.create_logging_service(event_bus)
        
        # 多线程并发测试
        def log_worker(worker_id, count):
            for i in range(count):
                logging_service.log_info(f"Worker {worker_id} message {i}")
                
        threads = []
        for i in range(5):
            thread = threading.Thread(target=log_worker, args=(i, 20))
            threads.append(thread)
            thread.start()
            
        for thread in threads:
            thread.join()
            
        # 检查统计信息
        stats = logging_service.get_stats()
        expected_messages = 5 * 20
        
        time.sleep(0.5)  # 等待异步处理
        
        if stats["messages_processed"] < expected_messages * 0.9:  # 允许10%的误差
            detector.add_issue(
                "线程安全", "中",
                f"日志服务可能存在线程安全问题，预期 {expected_messages} 条消息，实际处理 {stats['messages_processed']} 条"
            )
        else:
            print(f"  ✅ 日志服务线程安全: 处理 {stats['messages_processed']} 条消息")
            
        logging_service.shutdown()
        event_bus.shutdown()
        
        # 测试文件服务线程安全
        from infrastructure.file_service import FileServiceFactory
        import tempfile
        
        # 创建测试文件
        temp_dir = tempfile.mkdtemp()
        test_files = []
        for i in range(10):
            file_path = os.path.join(temp_dir, f"test_{i}.txt")
            with open(file_path, 'w') as f:
                f.write(f"测试文件 {i}")
            test_files.append(file_path)
            
        try:
            file_service = FileServiceFactory.create_file_service(max_workers=3)
            
            # 并发文件验证
            def validate_worker(worker_id):
                for _ in range(10):
                    file_service.validate_files_cached(test_files)
                    
            threads = []
            for i in range(3):
                thread = threading.Thread(target=validate_worker, args=(i,))
                threads.append(thread)
                thread.start()
                
            for thread in threads:
                thread.join()
                
            file_stats = file_service.get_stats()
            print(f"  ✅ 文件服务线程安全: 验证 {file_stats['validations_performed']} 次")
            
            file_service.shutdown()
            
        finally:
            import shutil
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        detector.add_issue("线程安全", "高", f"线程安全检查失败: {e}")
        
    return detector


def check_resource_leaks():
    """检查资源泄漏"""
    print("\n💧 检查资源泄漏...")
    detector = BugDetector()
    
    try:
        import psutil
        import gc
        
        process = psutil.Process()
        
        # 获取基线内存
        gc.collect()
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建和销毁多个服务实例
        for i in range(10):
            from infrastructure.logging_service import LoggingServiceFactory
            from infrastructure.event_bus import EventBus
            from infrastructure.file_service import FileServiceFactory
            from infrastructure.backup_service import BackupServiceFactory
            
            # 创建服务
            event_bus = EventBus(max_queue_size=50, worker_threads=1)
            logging_service = LoggingServiceFactory.create_logging_service(event_bus)
            file_service = FileServiceFactory.create_file_service(max_workers=2)
            backup_service = BackupServiceFactory.create_backup_service(max_workers=1)
            
            # 使用服务
            logging_service.log_info(f"测试消息 {i}")
            
            # 关闭服务
            logging_service.shutdown()
            file_service.shutdown()
            backup_service.shutdown()
            event_bus.shutdown()
            
            # 强制垃圾回收
            gc.collect()
            
        # 检查内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - baseline_memory
        
        print(f"  📊 内存使用: 基线 {baseline_memory:.1f}MB, 最终 {final_memory:.1f}MB, 增长 {memory_increase:.1f}MB")
        
        if memory_increase > 50:  # 如果内存增长超过50MB
            detector.add_issue(
                "资源泄漏", "中",
                f"可能存在内存泄漏，内存增长 {memory_increase:.1f}MB"
            )
        else:
            print(f"  ✅ 内存使用正常")
            
    except ImportError:
        detector.add_warning("psutil未安装，无法检查内存使用")
    except Exception as e:
        detector.add_issue("资源泄漏", "中", f"资源泄漏检查失败: {e}")
        
    return detector


def check_error_handling():
    """检查错误处理"""
    print("\n🛡️ 检查错误处理...")
    detector = BugDetector()
    
    try:
        # 测试配置服务错误处理
        from infrastructure.config_service import CachedConfigManager
        
        # 测试不存在的配置文件
        try:
            config_manager = CachedConfigManager("non_existent_config.ini")
            value = config_manager.get("NonExistent", "key", "default")
            if value == "default":
                print("  ✅ 配置服务错误处理正常")
            else:
                detector.add_issue("错误处理", "中", "配置服务未正确处理不存在的文件")
        except Exception as e:
            detector.add_issue("错误处理", "中", f"配置服务错误处理异常: {e}")
            
        # 测试文件服务错误处理
        from infrastructure.file_service import FileServiceFactory
        
        file_service = FileServiceFactory.create_file_service()
        
        # 测试不存在的文件验证
        is_valid, message = file_service.validate_files_cached(["non_existent_file.txt"])
        if not is_valid and "不存在" in message:
            print("  ✅ 文件服务错误处理正常")
        else:
            detector.add_issue("错误处理", "中", "文件服务未正确处理不存在的文件")
            
        file_service.shutdown()
        
        # 测试备份服务错误处理
        from infrastructure.backup_service import BackupServiceFactory
        
        backup_service = BackupServiceFactory.create_backup_service()
        
        # 测试备份不存在的文件
        try:
            task_id = backup_service.create_backup_task("non_existent_source.txt")
            
            # 等待任务完成
            max_wait = 10
            waited = 0
            while waited < max_wait:
                status = backup_service.get_task_status(task_id)
                if status and status["status"] in ["completed", "failed"]:
                    break
                time.sleep(1)
                waited += 1
                
            final_status = backup_service.get_task_status(task_id)
            if final_status and final_status["status"] == "failed":
                print("  ✅ 备份服务错误处理正常")
            else:
                detector.add_issue("错误处理", "中", "备份服务未正确处理不存在的源文件")
                
        except Exception as e:
            detector.add_issue("错误处理", "中", f"备份服务错误处理测试失败: {e}")
            
        backup_service.shutdown()
        
    except Exception as e:
        detector.add_issue("错误处理", "高", f"错误处理检查失败: {e}")
        
    return detector


def check_performance_issues():
    """检查性能问题"""
    print("\n⚡ 检查性能问题...")
    detector = BugDetector()
    
    try:
        # 检查配置缓存性能
        from infrastructure.config_service import CachedConfigManager
        import tempfile
        
        # 创建测试配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False) as f:
            config_file = f.name
            f.write("[Test]\nkey1=value1\nkey2=value2\n")
            
        try:
            config_manager = CachedConfigManager(config_file, cache_ttl=60)
            
            # 测试缓存性能
            start_time = time.perf_counter()
            for _ in range(1000):
                config_manager.get("Test", "key1")
            cache_time = time.perf_counter() - start_time
            
            stats = config_manager.get_cache_stats()
            hit_rate = float(stats["hit_rate"].rstrip('%'))
            
            if hit_rate < 90:
                detector.add_issue(
                    "性能", "中",
                    f"配置缓存命中率过低: {hit_rate}%"
                )
            else:
                print(f"  ✅ 配置缓存性能良好: 命中率 {hit_rate}%")
                
            if cache_time > 1.0:  # 1000次读取超过1秒
                detector.add_issue(
                    "性能", "中",
                    f"配置读取性能较差: 1000次读取耗时 {cache_time:.3f}秒"
                )
                
            config_manager.shutdown()
            
        finally:
            os.unlink(config_file)
            
        # 检查日志处理性能
        from infrastructure.logging_service import LoggingServiceFactory
        from infrastructure.event_bus import EventBus
        
        event_bus = EventBus(max_queue_size=1000, worker_threads=2)
        logging_service = LoggingServiceFactory.create_logging_service(event_bus)
        
        # 测试日志处理性能
        start_time = time.perf_counter()
        for i in range(1000):
            logging_service.log_info(f"性能测试消息 {i}")
            
        time.sleep(1)  # 等待处理完成
        log_time = time.perf_counter() - start_time
        
        stats = logging_service.get_stats()
        avg_time = stats.get("average_processing_time", 0) * 1000  # 毫秒
        
        if avg_time > 10:  # 平均处理时间超过10ms
            detector.add_issue(
                "性能", "中",
                f"日志处理性能较差: 平均处理时间 {avg_time:.3f}ms"
            )
        else:
            print(f"  ✅ 日志处理性能良好: 平均处理时间 {avg_time:.3f}ms")
            
        logging_service.shutdown()
        event_bus.shutdown()
        
    except Exception as e:
        detector.add_issue("性能", "中", f"性能检查失败: {e}")
        
    return detector


def check_compatibility_issues():
    """检查兼容性问题"""
    print("\n🔄 检查兼容性问题...")
    detector = BugDetector()
    
    try:
        from infrastructure import initialize_infrastructure, get_infrastructure, shutdown_infrastructure
        
        # 测试兼容性适配器
        config = {"feature_flags_config": "compatibility_test.json"}
        success = initialize_infrastructure(config)
        
        if success:
            infrastructure = get_infrastructure()
            adapter = infrastructure.get_compatibility_adapter()
            
            # 测试配置管理器兼容性
            try:
                config_manager = adapter.get_config_manager()
                if hasattr(config_manager, 'get'):
                    test_value = config_manager.get("Test", "key", "default")
                    print("  ✅ 配置管理器兼容性正常")
                else:
                    detector.add_issue("兼容性", "高", "配置管理器缺少get方法")
            except Exception as e:
                detector.add_issue("兼容性", "高", f"配置管理器兼容性问题: {e}")
                
            # 测试GUI更新器兼容性
            try:
                import tkinter as tk
                root = tk.Tk()
                root.withdraw()
                
                gui_updater = adapter.get_gui_updater(root)
                if hasattr(gui_updater, 'log_message') and hasattr(gui_updater, 'safe_update'):
                    print("  ✅ GUI更新器兼容性正常")
                else:
                    detector.add_issue("兼容性", "高", "GUI更新器缺少必要方法")
                    
                root.destroy()
            except Exception as e:
                detector.add_issue("兼容性", "中", f"GUI更新器兼容性问题: {e}")
                
            # 测试文件管理器兼容性
            try:
                file_manager = adapter.get_file_manager({})
                if hasattr(file_manager, 'validate_files'):
                    print("  ✅ 文件管理器兼容性正常")
                else:
                    detector.add_issue("兼容性", "高", "文件管理器缺少validate_files方法")
            except Exception as e:
                detector.add_issue("兼容性", "中", f"文件管理器兼容性问题: {e}")
                
            shutdown_infrastructure()
            
            if os.path.exists("compatibility_test.json"):
                os.unlink("compatibility_test.json")
                
    except Exception as e:
        detector.add_issue("兼容性", "高", f"兼容性检查失败: {e}")
        
    return detector


def check_configuration_issues():
    """检查配置问题"""
    print("\n⚙️ 检查配置问题...")
    detector = BugDetector()
    
    try:
        # 检查默认配置文件
        config_files = ["config.ini", "settings.ini"]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"  ✅ 配置文件存在: {config_file}")
                
                # 检查配置文件格式
                try:
                    import configparser
                    config = configparser.ConfigParser()
                    config.read(config_file, encoding='utf-8')
                    
                    # 检查必要的配置节
                    required_sections = ["Database", "UI", "Backup"]
                    for section in required_sections:
                        if not config.has_section(section):
                            detector.add_warning(f"配置文件 {config_file} 缺少 {section} 节")
                            
                except Exception as e:
                    detector.add_issue("配置", "中", f"配置文件 {config_file} 格式错误: {e}")
            else:
                detector.add_warning(f"配置文件不存在: {config_file}")
                
        # 检查备份目录
        backup_dirs = ["backups", "backup"]
        backup_dir_exists = False
        
        for backup_dir in backup_dirs:
            if os.path.exists(backup_dir):
                backup_dir_exists = True
                print(f"  ✅ 备份目录存在: {backup_dir}")
                break
                
        if not backup_dir_exists:
            detector.add_suggestion("建议创建备份目录: backups")
            
    except Exception as e:
        detector.add_issue("配置", "中", f"配置检查失败: {e}")
        
    return detector


def run_comprehensive_bug_check():
    """运行综合bug检查"""
    print("🔍 开始全面Bug检查...")
    print("=" * 80)
    
    all_detectors = []
    
    # 运行所有检查
    checks = [
        ("导入问题", check_import_issues),
        ("循环依赖", check_circular_dependencies),
        ("线程安全", check_thread_safety),
        ("资源泄漏", check_resource_leaks),
        ("错误处理", check_error_handling),
        ("性能问题", check_performance_issues),
        ("兼容性问题", check_compatibility_issues),
        ("配置问题", check_configuration_issues)
    ]
    
    for check_name, check_func in checks:
        try:
            detector = check_func()
            all_detectors.append((check_name, detector))
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 Bug检查汇总报告")
    print("=" * 80)
    
    total_issues = 0
    total_warnings = 0
    total_suggestions = 0
    
    for check_name, detector in all_detectors:
        issues = len(detector.issues)
        warnings = len(detector.warnings)
        suggestions = len(detector.suggestions)
        
        total_issues += issues
        total_warnings += warnings
        total_suggestions += suggestions
        
        if issues > 0 or warnings > 0:
            print(f"\n🔍 {check_name}:")
            
            for issue in detector.issues:
                severity_icon = {"高": "🚨", "中": "⚠️", "低": "ℹ️"}.get(issue["severity"], "❓")
                print(f"  {severity_icon} [{issue['severity']}] {issue['description']}")
                if issue["file_path"]:
                    print(f"      文件: {issue['file_path']}")
                    
            for warning in detector.warnings:
                print(f"  ⚠️ [警告] {warning['description']}")
                if warning["file_path"]:
                    print(f"      文件: {warning['file_path']}")
                    
            for suggestion in detector.suggestions:
                print(f"  💡 [建议] {suggestion['description']}")
                if suggestion["file_path"]:
                    print(f"      文件: {suggestion['file_path']}")
        else:
            print(f"✅ {check_name}: 无问题发现")
            
    print(f"\n📈 总计:")
    print(f"  🚨 严重问题: {total_issues}")
    print(f"  ⚠️ 警告: {total_warnings}")
    print(f"  💡 建议: {total_suggestions}")
    
    if total_issues == 0:
        print("\n🎉 恭喜！未发现严重bug，代码质量良好！")
        return True
    else:
        print(f"\n⚠️ 发现 {total_issues} 个问题需要修复")
        return False


if __name__ == "__main__":
    success = run_comprehensive_bug_check()
    sys.exit(0 if success else 1)
