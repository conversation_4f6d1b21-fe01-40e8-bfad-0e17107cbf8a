# -*- coding: utf-8 -*-
"""
日志过滤规则测试脚本
测试新的日志过滤功能，确保统计信息正确显示
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入SafeGUIUpdater
try:
    from 数据处理与导入应用_完整版 import SafeGUIUpdater
    print("✅ 成功导入SafeGUIUpdater")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_log_filtering():
    """测试日志过滤功能"""
    print("\n🔍 测试日志过滤规则...")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("日志过滤测试")
    root.geometry("800x600")
    
    # 创建GUI更新器
    gui_updater = SafeGUIUpdater(root)
    
    # 创建界面
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="日志过滤测试", font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 10))
    
    # 日志显示区域
    log_frame = ttk.LabelFrame(main_frame, text="主界面日志显示（已过滤）", padding="10")
    log_frame.pack(fill=tk.BOTH, expand=True)
    
    log_text = tk.Text(log_frame, height=20, wrap=tk.WORD, font=("Consolas", 9))
    scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=log_text.yview)
    log_text.configure(yscrollcommand=scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 注册日志控件
    gui_updater.register_log_widget("test", log_text)
    
    # 按钮区域
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(10, 0))
    
    def test_import_statistics():
        """测试导入统计信息"""
        gui_updater.safe_log("开始导入数据，平台类型: IOT, 订单类型: Normal", "test")
        gui_updater.safe_log("📤 导入脚本: Table statistics:", "test")
        gui_updater.safe_log("📤 导入脚本:   - IOT_Sales: 3048 records", "test")
        gui_updater.safe_log("📤 导入脚本:   - IOT_Sales_Close: 359 records", "test")
        gui_updater.safe_log("📤 导入脚本:   - IOT_Sales_Refunding: 206 records", "test")
        gui_updater.safe_log("📤 导入脚本: Insert details:", "test")
        gui_updater.safe_log("📤 导入脚本:   - IOT_Sales: inserted 2970 records", "test")
        gui_updater.safe_log("📤 导入脚本:   - IOT_Sales_Close: inserted 359 records", "test")
        gui_updater.safe_log("📤 导入脚本:   - IOT_Sales_Refunding: inserted 206 records", "test")
        gui_updater.safe_log("📤 导入脚本:   - APP_Sales: inserted 78 records", "test")
        gui_updater.safe_log("✅ 导入成功完成", "test")
    
    def test_processing_statistics():
        """测试处理统计信息"""
        gui_updater.safe_log("开始处理文件...", "test")
        gui_updater.safe_log("🔍 调试 1: Transaction ID: '12345' -> '12345'", "test")
        gui_updater.safe_log("📤 脚本路径: /path/to/script.py", "test")
        gui_updater.safe_log("🎯 RM5.00记录调试: Transaction ID=12345", "test")
        gui_updater.safe_log("处理完成，总金额对比：差异 RM5.00", "test")
        gui_updater.safe_log("成功处理 1500 条记录", "test")
        gui_updater.safe_log("✅ 文件处理成功完成", "test")
    
    def test_error_warnings():
        """测试错误和警告信息"""
        gui_updater.safe_log("⚠️ 警告: 发现重复记录", "test")
        gui_updater.safe_log("❌ 错误: 文件格式不正确", "test")
        gui_updater.safe_log("🔴 严重错误: 数据库连接失败", "test")
        gui_updater.safe_log("退款处理失败，返回码: 1", "test")
    
    def test_file_operations():
        """测试文件操作信息"""
        gui_updater.safe_log("已选择第一文件: SETTLEMENT_REPORT.xlsx", "test")
        gui_updater.safe_log("已选择第二文件: CHINA_IOT.xlsx", "test")
        gui_updater.safe_log("自动识别为IOT平台", "test")
        gui_updater.safe_log("已清空文件选择", "test")
    
    def test_debug_info():
        """测试调试信息（应该被过滤）"""
        gui_updater.safe_log("🔍 调试信息: 这条应该被过滤", "test")
        gui_updater.safe_log("📤 执行命令: python script.py", "test")
        gui_updater.safe_log("使用脚本: report.py", "test")
        gui_updater.safe_log("检测到平台类型: IOT", "test")
        gui_updater.safe_log("目标数据库: SQLite", "test")
        gui_updater.safe_log("使用指定的Sheet名称: TRANSACTION_LIST", "test")
    
    def clear_log():
        """清空日志"""
        log_text.config(state=tk.NORMAL)
        log_text.delete(1.0, tk.END)
        log_text.config(state=tk.DISABLED)
    
    def show_detailed_log():
        """显示详细日志文件"""
        log_path = gui_updater.get_detailed_log_path()
        if os.path.exists(log_path):
            os.startfile(log_path)
    
    # 添加测试按钮
    ttk.Button(button_frame, text="测试导入统计", command=test_import_statistics).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="测试处理统计", command=test_processing_statistics).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="测试错误警告", command=test_error_warnings).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="测试文件操作", command=test_file_operations).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="测试调试信息", command=test_debug_info).pack(side=tk.LEFT, padx=5)
    
    # 第二行按钮
    button_frame2 = ttk.Frame(main_frame)
    button_frame2.pack(fill=tk.X, pady=(5, 0))
    
    ttk.Button(button_frame2, text="清空日志", command=clear_log).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame2, text="查看详细日志", command=show_detailed_log).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame2, text="退出", command=root.quit).pack(side=tk.RIGHT, padx=5)
    
    # 说明文本
    info_frame = ttk.LabelFrame(main_frame, text="测试说明", padding="10")
    info_frame.pack(fill=tk.X, pady=(10, 0))
    
    info_text = """测试说明：
• 导入统计：应该显示所有记录数和插入数统计
• 处理统计：应该显示金额对比、记录数等重要信息
• 错误警告：应该显示所有错误和警告信息
• 文件操作：应该显示文件选择和基本操作
• 调试信息：应该被过滤，不在主界面显示（但会记录到详细日志）

所有信息都会完整记录到详细日志文件中。"""
    
    ttk.Label(info_frame, text=info_text, justify=tk.LEFT, font=("Arial", 9)).pack(anchor=tk.W)
    
    print(f"📁 详细日志文件: {gui_updater.get_detailed_log_path()}")
    print("🖥️ 测试窗口已创建，请点击按钮测试各种日志类型")
    
    # 运行GUI
    root.mainloop()

def main():
    """主函数"""
    print("🚀 日志过滤规则测试")
    print("=" * 50)
    
    test_log_filtering()
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main()
