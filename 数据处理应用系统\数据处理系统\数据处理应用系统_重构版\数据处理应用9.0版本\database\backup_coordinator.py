# -*- coding: utf-8 -*-
"""
🔧 备份功能修复：全局备份协调器
解决应用内备份代码与备份脚本的冲突问题

修复内容：
1. 改进文件锁管理，解决Windows平台锁问题
2. 优化备份文件命名，移除技术细节
3. 统一时间戳管理
4. 提升恢复操作性能

作者: Claude 4.0 sonnet
修复时间: 2025-01-22
"""

import os
import time
import threading
import tempfile
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any
import sqlite3

# 🔧 Windows兼容性修复：fcntl模块在Windows上不可用
try:
    import fcntl
    HAS_FCNTL = True
except ImportError:
    HAS_FCNTL = False
    # Windows上的文件锁替代方案
    import msvcrt

from utils.logger import get_logger

# 🔧 备份功能修复：导入新的工具类
try:
    from .backup_utils import (
        BackupTimestampManager,
        BackupNamingManager,
        ImprovedFileLockManager,
        get_timestamp_manager
    )
except ImportError:
    # 如果导入失败，使用内联实现
    BackupTimestampManager = None
    BackupNamingManager = None
    ImprovedFileLockManager = None
    get_timestamp_manager = None


class BackupCoordinator:
    """🔧 备份功能修复：全局备份协调器，防止并发备份冲突"""

    def __init__(self, db_path: str):
        """
        初始化备份协调器

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.backup_dir = self.db_path.parent / "backups"
        self.logger = get_logger('backup_coordinator')

        # 🔧 紧急修复：确保所有必要属性都被初始化
        self.lock_file_path = self.backup_dir / ".backup_lock"

        # 初始化所有可能需要的属性
        self.lock_file = None
        self._thread_lock = threading.Lock()
        self.lock_manager = None
        self.timestamp_manager = None

        # 🔧 备份功能修复：尝试使用改进的文件锁管理器
        if ImprovedFileLockManager:
            try:
                self.lock_manager = ImprovedFileLockManager(self.lock_file_path)
                self.logger.info("使用改进的文件锁管理器")
            except Exception as e:
                self.logger.warning(f"改进文件锁管理器初始化失败，使用原始实现: {e}")
                self.lock_manager = None

        # 🔧 备份功能修复：初始化时间戳管理器
        if get_timestamp_manager:
            try:
                self.timestamp_manager = get_timestamp_manager()
            except Exception as e:
                self.logger.warning(f"时间戳管理器初始化失败: {e}")

        # 确保备份目录和锁文件目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)

        self.logger.info(f"备份协调器已初始化 (修复版): {self.db_path}")
    
    def acquire_backup_lock(self, operation_name: str, timeout: int = 30) -> bool:
        """
        🔧 紧急修复：获取备份锁，支持两种锁机制

        Args:
            operation_name: 操作名称
            timeout: 超时时间（秒）

        Returns:
            是否成功获取锁
        """
        # 🔧 紧急修复：优先使用改进的锁管理器
        if self.lock_manager:
            try:
                return self.lock_manager.acquire_lock(operation_name, timeout)
            except Exception as e:
                self.logger.error(f"改进锁管理器获取锁失败: {e}")
                # 继续使用原始方法

        # 原始锁机制
        thread_lock_acquired = False
        try:
            # 线程级锁
            if not self._thread_lock.acquire(blocking=False):
                self.logger.warning(f"线程级备份锁被占用: {operation_name}")
                return False
            thread_lock_acquired = True
            
            # 🔧 Windows兼容性修复：跨平台文件锁实现
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    # 🔧 编码问题修复：指定UTF-8编码处理中文字符
                    self.lock_file = open(self.lock_file_path, 'w', encoding='utf-8')

                    if HAS_FCNTL:
                        # Unix/Linux系统使用fcntl
                        fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                    else:
                        # Windows系统使用msvcrt
                        msvcrt.locking(self.lock_file.fileno(), msvcrt.LK_NBLCK, 1)

                    # 写入锁信息
                    lock_info = {
                        'operation': operation_name,
                        'timestamp': datetime.now().isoformat(),
                        'pid': os.getpid()
                    }
                    self.lock_file.write(f"{lock_info}\n")
                    self.lock_file.flush()

                    self.logger.info(f"成功获取备份锁: {operation_name}")
                    return True

                except (IOError, OSError):
                    # 锁被占用，等待一段时间后重试
                    if self.lock_file:
                        self.lock_file.close()
                        self.lock_file = None
                    time.sleep(0.5)
                    continue
            
            # 超时未获取到锁
            self.logger.warning(f"获取备份锁超时: {operation_name}")
            self._thread_lock.release()
            return False
            
        except Exception as e:
            self.logger.error(f"获取备份锁失败: {e}")
            if self.lock_file:
                self.lock_file.close()
                self.lock_file = None
            # 🔧 线程锁释放修复：只有成功获取的锁才需要释放
            if thread_lock_acquired:
                try:
                    self._thread_lock.release()
                except:
                    pass
            return False
    
    def release_backup_lock(self):
        """🔧 紧急修复：释放备份锁，支持两种锁机制"""

        # 🔧 紧急修复：优先使用改进的锁管理器
        if self.lock_manager:
            try:
                self.lock_manager.release_lock()
                return
            except Exception as e:
                self.logger.error(f"改进锁管理器释放锁失败: {e}")
                # 继续使用原始方法

        # 原始锁机制
        file_lock_released = False
        thread_lock_released = False

        try:
            # 🔧 锁释放修复：分别处理文件锁和线程锁，确保线程锁总是被释放

            # 1. 释放文件锁
            if self.lock_file:
                try:
                    if HAS_FCNTL:
                        # Unix/Linux系统使用fcntl
                        fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_UN)
                    else:
                        # Windows系统使用msvcrt（自动释放）
                        msvcrt.locking(self.lock_file.fileno(), msvcrt.LK_UNLCK, 1)

                    self.lock_file.close()
                    self.lock_file = None
                    file_lock_released = True

                except Exception as file_error:
                    self.logger.warning(f"文件锁释放失败: {file_error}")

                # 2. 删除锁文件（即使文件锁释放失败也尝试）
                if self.lock_file_path.exists():
                    try:
                        self.lock_file_path.unlink()
                    except (OSError, PermissionError) as e:
                        # 如果无法删除锁文件，记录警告但不阻止程序继续
                        self.logger.warning(f"无法删除锁文件 {self.lock_file_path}: {e}")
                        # 尝试重命名文件以标记为已释放
                        try:
                            import time
                            old_name = str(self.lock_file_path)
                            new_name = f"{old_name}.released_{int(time.time())}"
                            self.lock_file_path.rename(new_name)
                            self.logger.info(f"锁文件已重命名为: {new_name}")
                        except Exception as rename_error:
                            self.logger.warning(f"锁文件重命名也失败: {rename_error}")

        except Exception as e:
            self.logger.warning(f"文件锁处理过程出错: {e}")

        finally:
            # 3. 🔧 关键修复：无论文件锁处理是否成功，都要释放线程锁
            try:
                if self._thread_lock.locked():
                    self._thread_lock.release()
                    thread_lock_released = True
                    self.logger.debug("线程锁已释放")
            except Exception as thread_error:
                self.logger.error(f"线程锁释放失败: {thread_error}")

            # 记录释放状态
            if file_lock_released and thread_lock_released:
                self.logger.info("备份锁完全释放")
            elif thread_lock_released:
                self.logger.info("线程锁已释放（文件锁可能有问题）")
            else:
                self.logger.error("备份锁释放失败")
    
    def is_database_locked(self) -> bool:
        """
        🔧 冲突解决：检查数据库是否被锁定
        
        Returns:
            数据库是否被锁定
        """
        try:
            with sqlite3.connect(self.db_path, timeout=1) as conn:
                conn.execute("BEGIN IMMEDIATE;")
                conn.rollback()
            return False
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                return True
            return False
        except Exception:
            return True
    
    def wait_for_database_unlock(self, timeout: int = 30) -> bool:
        """
        🔧 冲突解决：等待数据库解锁
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            数据库是否已解锁
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            if not self.is_database_locked():
                self.logger.info("数据库已解锁")
                return True
            time.sleep(0.5)
        
        self.logger.warning("等待数据库解锁超时")
        return False
    
    def generate_safe_backup_filename(self, operation_name: str, source: str = "unknown",
                                     custom_timestamp: datetime = None) -> tuple[str, datetime]:
        """
        🔧 时间同步修复：生成人性化的备份文件名，并返回时间戳

        Args:
            operation_name: 操作名称
            source: 备份来源（app/script）
            custom_timestamp: 自定义时间戳（可选）

        Returns:
            tuple: (人性化的备份文件名, 使用的时间戳)
        """
        # 🔧 时间同步修复：使用统一的时间戳，确保文件名和文件时间一致
        if custom_timestamp:
            timestamp_obj = custom_timestamp
        else:
            timestamp_obj = datetime.now()

        timestamp_str = timestamp_obj.strftime("%Y%m%d_%H%M%S")

        # 🔧 备份命名优化：创建人性化的操作名称
        human_readable_name = self._create_human_readable_operation_name(operation_name)

        # 🔧 备份命名优化：简洁的文件名格式，移除技术细节
        filename = f"backup_{human_readable_name}_{timestamp_str}.db"

        # 🔧 备份命名优化：简单的冲突解决，使用序号而非随机数
        final_filename = self._resolve_filename_conflict(filename)

        return final_filename, timestamp_obj

    def sync_file_timestamp(self, file_path: Path, target_timestamp: datetime):
        """
        🔧 时间同步修复：同步文件时间戳，使其与文件名中的时间戳一致

        Args:
            file_path: 文件路径
            target_timestamp: 目标时间戳
        """
        try:
            # 将datetime转换为时间戳
            timestamp = target_timestamp.timestamp()

            # 设置文件的访问时间和修改时间
            os.utime(file_path, (timestamp, timestamp))

            self.logger.info(f"文件时间戳已同步: {file_path} -> {target_timestamp}")

        except Exception as e:
            self.logger.warning(f"同步文件时间戳失败: {file_path}, {e}")

    def _create_human_readable_operation_name(self, operation_name: str) -> str:
        """
        🔧 备份命名优化：创建人性化的操作名称

        Args:
            operation_name: 原始操作名称

        Returns:
            人性化的操作名称
        """
        # 🔧 备份命名优化：操作名称映射表
        name_mapping = {
            # 导入相关
            "数据导入": "数据导入",
            "导入前": "导入前备份",
            "导入操作": "数据导入",
            "数据导入操作": "数据导入",

            # 退款相关
            "退款处理": "退款处理",
            "退款前": "退款前备份",
            "退款操作": "退款处理",

            # 手动备份
            "创建备份": "手动备份",
            "手动备份": "手动备份",
            "数据库操作": "手动备份",

            # 恢复相关
            "恢复前备份": "恢复前备份",
            "恢复操作": "恢复前备份",

            # 其他
            "数据修复": "数据修复",
            "系统维护": "系统维护"
        }

        # 🔧 备份命名优化：智能匹配操作类型
        for key, value in name_mapping.items():
            if key in operation_name:
                return value

        # 🔧 备份命名优化：处理文件名相关的备份
        if "导入前" in operation_name and (".xlsx" in operation_name or ".csv" in operation_name):
            # 提取文件名部分
            import re
            file_match = re.search(r'([^\\\/]+\.(xlsx|csv))', operation_name)
            if file_match:
                filename = file_match.group(1).replace('.xlsx', '').replace('.csv', '')
                return f"{filename}导入前"

        # 🔧 备份命名优化：保留中文字符，创建可读的名称
        safe_chars = []
        for char in operation_name:
            if char.isalnum() or char in ['_', '-']:
                safe_chars.append(char)
            elif '\u4e00' <= char <= '\u9fff':  # 中文字符范围
                safe_chars.append(char)
            elif char in [' ', '　']:  # 空格替换为下划线
                safe_chars.append('_')

        safe_name = ''.join(safe_chars)

        # 清理连续的下划线
        while '__' in safe_name:
            safe_name = safe_name.replace('__', '_')

        # 移除首尾下划线
        safe_name = safe_name.strip('_')

        # 如果名称为空或过短，使用默认名称
        if len(safe_name) < 2:
            safe_name = "手动备份"

        return safe_name

    def _resolve_filename_conflict(self, filename: str) -> str:
        """
        🔧 备份命名优化：解决文件名冲突，使用简单的序号后缀

        Args:
            filename: 原始文件名

        Returns:
            解决冲突后的文件名
        """
        if not (self.backup_dir / filename).exists():
            return filename

        # 分离文件名和扩展名
        name_part, ext = os.path.splitext(filename)

        # 添加序号后缀
        counter = 1
        while True:
            new_filename = f"{name_part}_{counter:02d}{ext}"
            if not (self.backup_dir / new_filename).exists():
                return new_filename
            counter += 1

            # 防止无限循环
            if counter > 999:
                import time
                timestamp_suffix = str(int(time.time() * 1000) % 100000)
                return f"{name_part}_{timestamp_suffix}{ext}"
    
    def safe_backup_operation(self, operation_name: str, source: str = "unknown"):
        """
        🔧 冲突解决：安全备份操作上下文管理器

        Args:
            operation_name: 操作名称
            source: 备份来源
        """
        return SafeBackupContext(self, operation_name, source)

    def safe_restore_operation(self, backup_path: str, operation_name: str = "恢复操作"):
        """
        🔧 恢复安全增强：安全恢复操作上下文管理器

        Args:
            backup_path: 备份文件路径
            operation_name: 操作名称
        """
        return SafeRestoreContext(self, backup_path, operation_name)

    def validate_restore_safety(self, backup_path: str) -> dict:
        """
        🔧 恢复安全增强：验证恢复操作的安全性

        Args:
            backup_path: 备份文件路径

        Returns:
            验证结果
        """
        try:
            from .restore_safety_validator import validate_restore_operation
            return validate_restore_operation(str(self.db_path), backup_path)
        except Exception as e:
            self.logger.error(f"恢复安全验证失败: {e}")
            return {
                'safe_to_restore': False,
                'errors': [f"验证失败: {e}"]
            }


class SafeBackupContext:
    """🔧 冲突解决：安全备份上下文管理器"""

    def __init__(self, coordinator: BackupCoordinator, operation_name: str, source: str):
        self.coordinator = coordinator
        self.operation_name = operation_name
        self.source = source
        self.lock_acquired = False

    def __enter__(self):
        # 获取备份锁
        self.lock_acquired = self.coordinator.acquire_backup_lock(self.operation_name)
        if not self.lock_acquired:
            raise Exception(f"无法获取备份锁: {self.operation_name}")

        # 等待数据库解锁
        if not self.coordinator.wait_for_database_unlock():
            raise Exception("数据库被锁定，无法进行备份")

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.lock_acquired:
            self.coordinator.release_backup_lock()

    def generate_backup_filename(self) -> str:
        """生成安全的备份文件名"""
        filename, _ = self.coordinator.generate_safe_backup_filename(self.operation_name, self.source)
        return filename


class SafeRestoreContext:
    """🔧 恢复安全增强：安全恢复上下文管理器"""

    def __init__(self, coordinator: BackupCoordinator, backup_path: str, operation_name: str):
        self.coordinator = coordinator
        self.backup_path = backup_path
        self.operation_name = operation_name
        self.lock_acquired = False
        self.validation_result = None

    def __enter__(self):
        # 获取恢复锁
        self.lock_acquired = self.coordinator.acquire_backup_lock(f"恢复_{self.operation_name}")
        if not self.lock_acquired:
            raise Exception(f"无法获取恢复锁: {self.operation_name}")

        # 验证恢复安全性
        self.validation_result = self.coordinator.validate_restore_safety(self.backup_path)
        if not self.validation_result.get('safe_to_restore', False):
            errors = self.validation_result.get('errors', [])
            issues = []
            for check_name, check_result in self.validation_result.get('checks', {}).items():
                if not check_result.get('passed', False):
                    issues.extend(check_result.get('issues', []))

            error_msg = f"恢复安全验证失败: {'; '.join(errors + issues)}"
            raise Exception(error_msg)

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.lock_acquired:
            self.coordinator.release_backup_lock()

    def get_validation_result(self) -> dict:
        """获取验证结果"""
        return self.validation_result or {}


# 🔧 冲突解决：全局协调器实例
_global_coordinator: Optional[BackupCoordinator] = None


def get_backup_coordinator(db_path: str = None) -> BackupCoordinator:
    """
    获取全局备份协调器实例
    
    Args:
        db_path: 数据库路径
        
    Returns:
        备份协调器实例
    """
    global _global_coordinator
    
    if _global_coordinator is None:
        if db_path is None:
            # 动态获取项目根目录
            import os
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(script_dir))))
            db_path = os.path.join(project_root, "database", "sales_reports.db")
        
        _global_coordinator = BackupCoordinator(db_path)
    
    return _global_coordinator


def reset_backup_coordinator():
    """重置全局备份协调器实例"""
    global _global_coordinator
    if _global_coordinator:
        _global_coordinator.release_backup_lock()
    _global_coordinator = None
