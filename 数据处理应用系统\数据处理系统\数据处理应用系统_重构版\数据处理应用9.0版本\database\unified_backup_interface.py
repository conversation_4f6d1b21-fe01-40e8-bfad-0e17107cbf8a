# -*- coding: utf-8 -*-
"""
统一备份接口适配器
为主应用和独立脚本提供统一的备份管理接口
"""

import os
from pathlib import Path
from typing import Optional, Union, Dict, Any
from datetime import datetime

from utils.logger import get_logger


class UnifiedBackupInterface:
    """统一备份接口适配器"""
    
    def __init__(self, db_path: str, backup_dir: Optional[str] = None, 
                 config_manager=None, gui_updater=None):
        """
        初始化统一备份接口
        
        Args:
            db_path: 数据库文件路径
            backup_dir: 备份目录
            config_manager: 配置管理器（主应用使用）
            gui_updater: GUI更新器（主应用使用）
        """
        self.db_path = Path(db_path)
        self.backup_dir = Path(backup_dir) if backup_dir else self.db_path.parent / "backups"
        self.config_manager = config_manager
        self.gui_updater = gui_updater
        self.logger = get_logger('unified_backup')
        
        # 根据环境选择合适的备份管理器
        self._backup_manager = self._create_backup_manager()
        
    def _create_backup_manager(self):
        """根据环境创建合适的备份管理器"""
        try:
            if self.config_manager and self.gui_updater:
                # 主应用环境：使用内置备份管理器
                from ..01_主程序.数据处理与导入应用_完整版 import DatabaseBackupManager
                return DatabaseBackupManager(self.config_manager, self.gui_updater)
            else:
                # 独立脚本环境：使用独立备份管理器
                from .backup_manager import DatabaseBackupManager
                return DatabaseBackupManager(str(self.db_path), str(self.backup_dir))
        except Exception as e:
            self.logger.warning(f"创建备份管理器失败，使用默认实现: {e}")
            # 回退到当前的独立备份管理器
            from .backup_manager import DatabaseBackupManager
            return DatabaseBackupManager(str(self.db_path), str(self.backup_dir))
    
    def create_backup(self, operation_name: str = "手动备份") -> Optional[str]:
        """
        🔧 统一备份接口：创建备份
        
        Args:
            operation_name: 操作名称
            
        Returns:
            备份文件路径
        """
        try:
            # 统一调用接口
            if hasattr(self._backup_manager, 'backup_database'):
                # 主应用的方法
                return self._backup_manager.backup_database(operation_name)
            elif hasattr(self._backup_manager, 'create_backup'):
                # 独立脚本的方法
                return self._backup_manager.create_backup(operation_name)
            else:
                raise Exception("备份管理器没有可用的备份方法")
                
        except Exception as e:
            self.logger.error(f"统一备份接口创建备份失败: {e}")
            return None
    
    def restore_backup(self, backup_path: str, confirm_callback=None) -> bool:
        """
        🔧 统一恢复接口：恢复备份
        
        Args:
            backup_path: 备份文件路径
            confirm_callback: 确认回调函数
            
        Returns:
            恢复是否成功
        """
        try:
            if hasattr(self._backup_manager, 'restore_backup'):
                return self._backup_manager.restore_backup(backup_path, confirm_callback)
            elif hasattr(self._backup_manager, 'restore_from_backup'):
                return self._backup_manager.restore_from_backup(backup_path, confirm_callback)
            else:
                raise Exception("备份管理器没有可用的恢复方法")
                
        except Exception as e:
            self.logger.error(f"统一备份接口恢复失败: {e}")
            return False
    
    def list_backups(self) -> list:
        """
        🔧 统一列表接口：获取备份列表
        
        Returns:
            备份文件列表
        """
        try:
            if hasattr(self._backup_manager, 'list_backups'):
                return self._backup_manager.list_backups()
            elif hasattr(self._backup_manager, 'get_backup_list'):
                return self._backup_manager.get_backup_list()
            else:
                # 回退到基本实现
                return self._get_basic_backup_list()
                
        except Exception as e:
            self.logger.error(f"获取备份列表失败: {e}")
            return []
    
    def _get_basic_backup_list(self) -> list:
        """基本的备份列表获取"""
        try:
            backup_files = list(self.backup_dir.glob("backup_*.db"))
            backup_info = []
            
            for backup_file in backup_files:
                stat = backup_file.stat()
                info = {
                    'filename': backup_file.name,
                    'path': str(backup_file),
                    'size': stat.st_size,
                    'created_time': datetime.fromtimestamp(stat.st_mtime)
                }
                backup_info.append(info)
            
            # 按创建时间排序
            backup_info.sort(key=lambda x: x['created_time'], reverse=True)
            return backup_info
            
        except Exception as e:
            self.logger.error(f"基本备份列表获取失败: {e}")
            return []
    
    def auto_backup_before_operation(self, operation_name: str) -> Optional[str]:
        """
        🔧 统一自动备份接口：操作前自动备份
        
        Args:
            operation_name: 操作名称
            
        Returns:
            备份文件路径
        """
        try:
            # 检查是否启用自动备份
            if self.config_manager:
                auto_backup = self.config_manager.get('Backup', 'auto_backup', 'true').lower() == 'true'
                if not auto_backup:
                    self.logger.info("自动备份已禁用")
                    return None
            
            # 创建操作前备份
            backup_name = f"操作前_{operation_name}_{datetime.now().strftime('%H%M%S')}"
            return self.create_backup(backup_name)
            
        except Exception as e:
            self.logger.error(f"操作前自动备份失败: {e}")
            return None
    
    def handle_operation_failure(self, operation_name: str, error: Exception, 
                                backup_path: Optional[str] = None) -> bool:
        """
        🔧 统一错误处理接口：处理操作失败
        
        Args:
            operation_name: 操作名称
            error: 错误信息
            backup_path: 备份文件路径
            
        Returns:
            是否进行了恢复
        """
        try:
            if hasattr(self._backup_manager, 'handle_operation_error'):
                return self._backup_manager.handle_operation_error(operation_name, error, backup_path)
            else:
                # 基本的错误处理
                self.logger.error(f"操作失败: {operation_name}, 错误: {error}")
                
                if backup_path and Path(backup_path).exists():
                    self.logger.info(f"可用备份: {backup_path}")
                    # 这里可以添加基本的恢复逻辑
                    return False
                else:
                    self.logger.warning("没有可用的备份文件")
                    return False
                    
        except Exception as e:
            self.logger.error(f"错误处理失败: {e}")
            return False


# 全局统一备份接口实例
_unified_backup_interface: Optional[UnifiedBackupInterface] = None


def get_unified_backup_interface(db_path: str = None, backup_dir: str = None,
                                config_manager=None, gui_updater=None) -> UnifiedBackupInterface:
    """
    获取统一备份接口实例
    
    Args:
        db_path: 数据库路径
        backup_dir: 备份目录
        config_manager: 配置管理器
        gui_updater: GUI更新器
        
    Returns:
        统一备份接口实例
    """
    global _unified_backup_interface
    
    if _unified_backup_interface is None:
        if db_path is None:
            db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
        
        _unified_backup_interface = UnifiedBackupInterface(
            db_path, backup_dir, config_manager, gui_updater
        )
    
    return _unified_backup_interface


def reset_unified_backup_interface():
    """重置统一备份接口实例"""
    global _unified_backup_interface
    _unified_backup_interface = None
