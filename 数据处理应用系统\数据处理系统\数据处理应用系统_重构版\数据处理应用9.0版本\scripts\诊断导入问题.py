#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断导入问题 - 找出Refunded/Close数据没有导入的真正原因
"""

import sqlite3
import os
import sys
import pandas as pd
from pathlib import Path

def check_table_existence():
    """检查表是否存在"""
    print("🔍 检查表存在性")
    print("=" * 50)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查关键表
        tables_to_check = [
            'IOT_Sales_Refunding', 'IOT_Sales_Close',
            'ZERO_Sales_Refunding', 'ZERO_Sales_Close'
        ]
        
        existing_tables = []
        missing_tables = []
        
        for table in tables_to_check:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                existing_tables.append(table)
                print(f"   ✅ {table}: 存在")
            else:
                missing_tables.append(table)
                print(f"   ❌ {table}: 不存在")
        
        conn.close()
        
        if missing_tables:
            print(f"\n🚨 发现缺失的表: {missing_tables}")
            print("这可能是导入失败的原因！")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_table_structure():
    """检查表结构"""
    print("\n🔍 检查表结构")
    print("=" * 50)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        tables_to_check = ['IOT_Sales', 'IOT_Sales_Refunding', 'IOT_Sales_Close']
        
        for table in tables_to_check:
            try:
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                if columns:
                    column_names = [col[1] for col in columns]
                    print(f"   📊 {table}: {len(column_names)} 列")
                    print(f"       {', '.join(column_names[:8])}{'...' if len(column_names) > 8 else ''}")
                else:
                    print(f"   ❌ {table}: 无法获取表结构")
                    
            except Exception as e:
                print(f"   ❌ {table}: 查询失败 - {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 表结构检查失败: {e}")

def check_recent_data():
    """检查最近的数据"""
    print("\n🔍 检查最近的数据")
    print("=" * 50)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查主表中的状态分布
        print("📊 IOT_Sales 状态分布:")
        cursor.execute("""
            SELECT Order_status, COUNT(*) as count 
            FROM IOT_Sales 
            WHERE Order_status IS NOT NULL AND Order_status != ''
            GROUP BY Order_status 
            ORDER BY count DESC
            LIMIT 10
        """)
        
        status_distribution = cursor.fetchall()
        refunded_in_main = 0
        close_in_main = 0
        
        for status, count in status_distribution:
            status_lower = status.lower()
            if any(keyword in status_lower for keyword in ['refund', 'cancel']):
                refunded_in_main += count
                print(f"   🚨 {status}: {count:,} 条 (应该在Refunding表)")
            elif any(keyword in status_lower for keyword in ['close', 'end', 'stop']):
                close_in_main += count
                print(f"   🚨 {status}: {count:,} 条 (应该在Close表)")
            else:
                print(f"   ✅ {status}: {count:,} 条")
        
        # 检查Refunding和Close表的数据
        tables_to_check = ['IOT_Sales_Refunding', 'IOT_Sales_Close']
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"\n📊 {table}: {count:,} 条记录")
                
                if count > 0:
                    # 显示最近的记录
                    cursor.execute(f"""
                        SELECT Order_status, COUNT(*) 
                        FROM {table} 
                        GROUP BY Order_status 
                        ORDER BY COUNT(*) DESC 
                        LIMIT 5
                    """)
                    recent_status = cursor.fetchall()
                    for status, status_count in recent_status:
                        print(f"       - {status}: {status_count} 条")
                        
            except Exception as e:
                print(f"   ❌ {table}: 查询失败 - {e}")
        
        # 总结问题
        print(f"\n🎯 问题诊断:")
        if refunded_in_main > 0:
            print(f"   🚨 主表中有 {refunded_in_main} 条Refunded状态记录未迁移")
        if close_in_main > 0:
            print(f"   🚨 主表中有 {close_in_main} 条Close状态记录未迁移")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据检查失败: {e}")

def test_insert_capability():
    """测试插入能力"""
    print("\n🔍 测试插入能力")
    print("=" * 50)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 测试是否可以插入到Refunding表
        test_table = 'IOT_Sales_Refunding'
        
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({test_table})")
        columns = cursor.fetchall()
        
        if not columns:
            print(f"   ❌ {test_table} 表不存在或无法访问")
            return False
        
        column_names = [col[1] for col in columns]
        print(f"   ✅ {test_table} 表结构正常，有 {len(column_names)} 列")
        
        # 尝试插入测试数据
        test_data = {
            'Copartner_name': 'TEST',
            'Order_No': 'TEST_REFUND_001',
            'Order_status': 'Refunded',
            'Order_price': 10.00,
            'Payment': 10.00,
            'Order_time': '2025-01-09 10:00:00',
            'Equipment_ID': 'TEST001',
            'Equipment_name': 'Test Equipment',
            'Branch_name': 'Test Branch',
            'Payment_date': '2025-01-09',
            'User_name': 'Test User',
            'Time': '2025-01-09 10:00:00'
        }
        
        # 只使用表中实际存在的列
        valid_data = {k: v for k, v in test_data.items() if k in column_names}
        
        # 构建插入SQL
        columns_str = ', '.join(valid_data.keys())
        placeholders = ', '.join(['?' for _ in valid_data])
        values = list(valid_data.values())
        
        insert_sql = f"INSERT INTO {test_table} ({columns_str}) VALUES ({placeholders})"
        
        # 执行插入
        cursor.execute(insert_sql, values)
        conn.commit()
        
        # 验证插入
        cursor.execute(f"SELECT COUNT(*) FROM {test_table} WHERE Order_No = 'TEST_REFUND_001'")
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"   ✅ 测试插入成功，{test_table} 表可以正常写入")
            
            # 清理测试数据
            cursor.execute(f"DELETE FROM {test_table} WHERE Order_No = 'TEST_REFUND_001'")
            conn.commit()
            print(f"   🧹 测试数据已清理")
            
            return True
        else:
            print(f"   ❌ 测试插入失败，数据未写入")
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ 插入测试失败: {e}")
        return False

def create_missing_tables():
    """创建缺失的表"""
    print("\n🔧 创建缺失的表")
    print("=" * 50)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取IOT_Sales的表结构
        cursor.execute("PRAGMA table_info(IOT_Sales)")
        columns = cursor.fetchall()
        
        if not columns:
            print("❌ IOT_Sales表不存在，无法创建其他表")
            return False
        
        # 构建CREATE TABLE语句
        column_definitions = []
        for col in columns:
            col_name = col[1]
            col_type = col[2]
            not_null = "NOT NULL" if col[3] else ""
            default_val = f"DEFAULT {col[4]}" if col[4] is not None else ""
            primary_key = "PRIMARY KEY" if col[5] else ""
            
            col_def = f"{col_name} {col_type} {not_null} {default_val} {primary_key}".strip()
            column_definitions.append(col_def)
        
        columns_sql = ", ".join(column_definitions)
        
        # 要创建的表
        tables_to_create = [
            'IOT_Sales_Refunding', 'IOT_Sales_Close',
            'ZERO_Sales_Refunding', 'ZERO_Sales_Close',
            'APP_Sales_Refunding', 'APP_Sales_Close'
        ]
        
        created_count = 0
        
        for table_name in tables_to_create:
            try:
                # 检查表是否已存在
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                if cursor.fetchone():
                    print(f"   ✅ {table_name}: 已存在")
                    continue
                
                # 创建表
                create_sql = f"CREATE TABLE {table_name} ({columns_sql})"
                cursor.execute(create_sql)
                created_count += 1
                print(f"   ✅ {table_name}: 创建成功")
                
            except Exception as e:
                print(f"   ❌ {table_name}: 创建失败 - {e}")
        
        conn.commit()
        conn.close()
        
        if created_count > 0:
            print(f"\n🎉 成功创建 {created_count} 个表")
            return True
        else:
            print(f"\n✅ 所有表都已存在")
            return True
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 诊断Refunded/Close导入问题")
    print("=" * 60)
    
    try:
        # 1. 检查表存在性
        tables_exist = check_table_existence()
        
        # 2. 如果表不存在，创建它们
        if not tables_exist:
            print("\n🚨 发现表缺失，尝试创建...")
            if create_missing_tables():
                print("✅ 表创建完成，请重新尝试导入")
            else:
                print("❌ 表创建失败")
                return 1
        
        # 3. 检查表结构
        check_table_structure()
        
        # 4. 检查现有数据
        check_recent_data()
        
        # 5. 测试插入能力
        insert_ok = test_insert_capability()
        
        # 6. 总结
        print("\n" + "=" * 60)
        print("🎯 诊断结果")
        print("=" * 60)
        
        if insert_ok:
            print("✅ 数据库表结构正常，可以正常插入")
            print("🔍 如果导入仍有问题，可能的原因:")
            print("1. 数据导入时选择了错误的模式")
            print("2. Excel文件中Order_status列格式不正确")
            print("3. 重复数据检测过于严格")
            print("4. 事务回滚导致数据未提交")
            print("\n💡 建议:")
            print("- 确保选择'智能识别导入'模式")
            print("- 检查Excel文件中Order_status列的值")
            print("- 查看导入日志中的详细错误信息")
        else:
            print("❌ 数据库表有问题，无法正常插入")
            print("🔧 需要修复数据库表结构")
        
        return 0
        
    except Exception as e:
        print(f"❌ 诊断过程中出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
