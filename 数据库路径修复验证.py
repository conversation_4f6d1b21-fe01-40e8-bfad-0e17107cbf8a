#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库路径修复验证脚本
验证数据库路径和脚本路径是否已正确更新
"""

import os
import configparser
from datetime import datetime

def verify_database_path():
    """验证数据库路径"""
    print("🔍 验证数据库路径...")
    
    config_path = "config.ini"
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        if 'Database' not in config:
            print("❌ 配置文件中没有Database section")
            return False
        
        db_path = config.get('Database', 'db_path', fallback='')
        print(f"📁 配置中的数据库路径: {db_path}")
        
        # 检查路径是否正确
        expected_new_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
        old_path_pattern = "Day report 3"
        
        if old_path_pattern in db_path:
            print(f"❌ 数据库路径仍然包含旧路径: {old_path_pattern}")
            return False
        
        if db_path == expected_new_path:
            print(f"✅ 数据库路径已正确更新为新路径")
        else:
            print(f"⚠️ 数据库路径与预期不完全匹配")
            print(f"   预期: {expected_new_path}")
            print(f"   实际: {db_path}")
        
        # 检查数据库文件是否存在
        if os.path.exists(db_path):
            file_size = os.path.getsize(db_path) / (1024*1024)
            print(f"✅ 数据库文件存在，大小: {file_size:.2f} MB")
            return True
        else:
            print(f"❌ 数据库文件不存在: {db_path}")
            return False
            
    except Exception as e:
        print(f"❌ 验证数据库路径时出错: {e}")
        return False

def verify_script_paths():
    """验证脚本路径"""
    print("\n🔍 验证脚本路径...")
    
    config_path = "config.ini"
    
    try:
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        if 'Scripts' not in config:
            print("❌ 配置文件中没有Scripts section")
            return False
        
        scripts = dict(config['Scripts'])
        old_path_pattern = "Day report 3"
        new_path_pattern = "Day Report"
        
        all_updated = True
        existing_scripts = 0
        
        for script_name, script_path in scripts.items():
            print(f"\n📋 检查脚本: {script_name}")
            print(f"   路径: {script_path}")
            
            # 检查是否还有旧路径
            if old_path_pattern in script_path:
                print(f"   ❌ 仍包含旧路径: {old_path_pattern}")
                all_updated = False
            elif new_path_pattern in script_path:
                print(f"   ✅ 已更新为新路径")
            else:
                print(f"   ⚠️ 路径格式异常")
            
            # 检查脚本文件是否存在
            if os.path.exists(script_path):
                print(f"   ✅ 脚本文件存在")
                existing_scripts += 1
            else:
                print(f"   ❌ 脚本文件不存在")
        
        print(f"\n📊 脚本路径验证总结:")
        print(f"   总脚本数: {len(scripts)}")
        print(f"   存在的脚本: {existing_scripts}")
        print(f"   路径更新状态: {'✅ 全部更新' if all_updated else '❌ 部分未更新'}")
        
        return all_updated and existing_scripts > 0
        
    except Exception as e:
        print(f"❌ 验证脚本路径时出错: {e}")
        return False

def verify_refund_script_functionality():
    """验证退款脚本功能"""
    print("\n🔍 验证退款脚本功能...")
    
    try:
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')
        
        # 获取退款脚本路径
        refund_script = config.get('Scripts', 'refund_script', fallback='')
        refund_script_optimized = config.get('Scripts', 'refund_script_optimized', fallback='')
        
        print(f"📋 主退款脚本: {refund_script}")
        print(f"📋 优化退款脚本: {refund_script_optimized}")
        
        # 检查脚本是否存在
        main_exists = os.path.exists(refund_script)
        optimized_exists = os.path.exists(refund_script_optimized)
        
        print(f"   主脚本存在: {'✅' if main_exists else '❌'}")
        print(f"   优化脚本存在: {'✅' if optimized_exists else '❌'}")
        
        if main_exists:
            # 检查脚本语法
            try:
                with open(refund_script, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 简单的语法检查
                if 'def ' in content and 'import ' in content:
                    print(f"   ✅ 主退款脚本语法基本正常")
                else:
                    print(f"   ⚠️ 主退款脚本可能有语法问题")
                    
            except Exception as e:
                print(f"   ❌ 读取主退款脚本时出错: {e}")
        
        return main_exists or optimized_exists
        
    except Exception as e:
        print(f"❌ 验证退款脚本功能时出错: {e}")
        return False

def verify_application_config():
    """验证应用程序配置"""
    print("\n🔍 验证应用程序配置...")
    
    try:
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')
        
        # 检查所有必要的section
        required_sections = ['Database', 'Scripts', 'UI', 'Files', 'Backup']
        missing_sections = []
        
        for section in required_sections:
            if section in config:
                print(f"   ✅ {section} section 存在")
            else:
                print(f"   ❌ {section} section 缺失")
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ 缺失的配置section: {missing_sections}")
            return False
        
        # 检查关键配置项
        db_path = config.get('Database', 'db_path', fallback='')
        if db_path:
            print(f"   ✅ 数据库路径配置存在")
        else:
            print(f"   ❌ 数据库路径配置缺失")
            return False
        
        print(f"✅ 应用程序配置验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证应用程序配置时出错: {e}")
        return False

def generate_fix_summary():
    """生成修复总结"""
    print("\n" + "="*60)
    print("📊 数据库路径修复验证报告")
    print("="*60)
    
    print(f"\n📋 修复内容:")
    print(f"1. ✅ 更新应用程序默认数据库路径")
    print(f"   从: C:/Users/<USER>/Desktop/Day report 3/database/sales_reports.db")
    print(f"   到: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db")
    
    print(f"\n2. ✅ 更新config.ini中的所有脚本路径")
    print(f"   从: Day report 3")
    print(f"   到: Day Report")
    
    print(f"\n3. ✅ 修复硬编码路径问题")
    print(f"   - 第1988行: 默认数据库路径")
    print(f"   - 第9090行: 日志显示路径")
    print(f"   - 第11762行: 文件选择初始路径")
    
    print(f"\n🎯 解决的问题:")
    print(f"• 应用程序显示路径与实际路径不一致")
    print(f"• 退款脚本可能无法找到正确的数据库")
    print(f"• 配置文件中的脚本路径过时")
    
    print(f"\n💡 建议:")
    print(f"• 重启应用程序以确保所有更改生效")
    print(f"• 测试退款功能以确认脚本调用正常")
    print(f"• 如有问题，检查文件夹名称是否完全匹配")
    
    return True

def main():
    """主函数"""
    print("🚀 开始数据库路径修复验证...")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项验证
    tests = [
        ("数据库路径", verify_database_path),
        ("脚本路径", verify_script_paths),
        ("退款脚本功能", verify_refund_script_functionality),
        ("应用程序配置", verify_application_config)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行失败: {e}")
    
    # 生成总结
    generate_fix_summary()
    
    # 总结
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n📊 验证结果:")
    print(f"通过测试: {passed_tests}/{total_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print("\n🎊 数据库路径修复验证成功！")
        print("💡 建议重启应用程序以确保所有更改生效")
        return True
    else:
        print("\n⚠️ 部分验证未通过，可能需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
