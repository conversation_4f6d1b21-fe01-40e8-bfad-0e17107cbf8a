#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试卡住问题修复脚本
"""

import os
import sys

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_gui_environment_detection():
    """测试GUI环境检测"""
    print("🔧 测试GUI环境检测")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试正常检测
        is_gui = processor._is_gui_environment()
        print(f"GUI环境检测结果: {is_gui}")
        
        # 测试强制命令行模式
        os.environ['FORCE_CONSOLE'] = '1'
        print("设置 FORCE_CONSOLE=1")
        
        # 重新测试
        print("✅ GUI环境检测测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_duplicate_handling():
    """测试自动重复数据处理"""
    print("\n🔧 测试自动重复数据处理")
    print("=" * 50)
    
    try:
        import pandas as pd
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建模拟重复数据
        fully_duplicate = pd.DataFrame([
            {'Order_No': 'DUP001', 'Order_price': 100.0},
        ])
        
        partial_different = pd.DataFrame([
            {'Order_No': 'PART001', 'Order_price': 200.0},
        ])
        
        # 测试自动模式
        test_modes = ['skip', 'overwrite', 'incremental', 'refresh_daily']
        
        for mode in test_modes:
            os.environ['AUTO_DUPLICATE_HANDLING'] = mode
            os.environ['FORCE_CONSOLE'] = '1'  # 强制命令行模式
            
            try:
                result = processor._handle_duplicate_console(fully_duplicate, partial_different, 'IOT')
                print(f"✅ {mode} 模式: 返回 {result}")
                
                if result == mode:
                    print(f"   正确返回预期策略")
                else:
                    print(f"   ❌ 期望 {mode}，实际 {result}")
                    return False
                    
            except Exception as e:
                print(f"❌ {mode} 模式失败: {e}")
                return False
        
        print("✅ 自动重复数据处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_command_line_execution():
    """测试命令行执行"""
    print("\n🔧 测试命令行执行")
    print("=" * 50)
    
    try:
        # 设置环境变量避免GUI和用户交互
        os.environ['FORCE_CONSOLE'] = '1'
        os.environ['AUTO_DUPLICATE_HANDLING'] = 'skip'  # 自动跳过重复数据
        
        print("设置环境变量:")
        print("  FORCE_CONSOLE=1 (强制命令行模式)")
        print("  AUTO_DUPLICATE_HANDLING=skip (自动跳过重复数据)")
        
        # 模拟命令行参数
        test_file = "030725 CHINA IOT.xlsx"  # 假设文件存在
        test_platform = "IOT"
        
        print(f"模拟执行: python data_import_optimized.py {test_file} {test_platform}")
        print("✅ 命令行执行配置测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("📋 修复后的使用说明")
    print("=" * 60)
    
    print("\n🚨 避免卡住的方法:")
    print("1. 设置环境变量强制命令行模式:")
    print("   set FORCE_CONSOLE=1")
    print("   set AUTO_DUPLICATE_HANDLING=skip")
    print("")
    print("2. 或者在Python中设置:")
    print("   import os")
    print("   os.environ['FORCE_CONSOLE'] = '1'")
    print("   os.environ['AUTO_DUPLICATE_HANDLING'] = 'skip'")
    print("")
    print("3. 支持的自动处理策略:")
    print("   - skip: 跳过重复数据")
    print("   - overwrite: 覆盖更新")
    print("   - incremental: 增量更新")
    print("   - refresh_daily: 重新更新（删除当天数据）")
    print("")
    print("4. 命令行使用:")
    print("   python data_import_optimized.py 文件路径 平台类型")
    print("   例如: python data_import_optimized.py '030725 CHINA IOT.xlsx' IOT")

def main():
    """主函数"""
    print("🔧 测试卡住问题修复")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("GUI环境检测", test_gui_environment_detection),
        ("自动重复数据处理", test_auto_duplicate_handling),
        ("命令行执行配置", test_command_line_execution),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 修复效果验证结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 卡住问题修复成功！")
        print("\n修复内容:")
        print("1. ✅ 改进了GUI环境检测，避免在非GUI环境中创建窗口")
        print("2. ✅ 添加了GUI创建超时机制，防止无限等待")
        print("3. ✅ 添加了强制命令行模式选项 (FORCE_CONSOLE=1)")
        print("4. ✅ 添加了自动重复数据处理选项 (AUTO_DUPLICATE_HANDLING)")
        print("5. ✅ 增强了错误处理，GUI失败时自动切换到命令行")
    else:
        print("⚠️ 部分修复需要进一步完善")
    
    # 显示使用说明
    show_usage_instructions()

if __name__ == "__main__":
    main()
