#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 测试修复后的导入功能
"""

import os
import sys

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_import():
    """测试导入功能"""
    print("🔧 快速测试：导入功能")
    print("=" * 40)
    
    try:
        # 测试导入
        print("📋 测试模块导入...")
        from data_import_optimized import DataImportProcessor
        print("✅ DataImportProcessor 导入成功")
        
        # 测试实例化
        print("📋 测试实例化...")
        processor = DataImportProcessor()
        print("✅ 实例化成功")
        
        # 测试基本方法
        print("📋 测试基本方法...")
        
        # 测试文件验证（使用一个不存在的文件）
        try:
            processor.validate_file("不存在的文件.xlsx")
        except Exception as e:
            print(f"✅ 文件验证方法正常工作（预期的错误）: {type(e).__name__}")
        
        print("🎉 基本测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_import()
    
    if success:
        print("\n✅ 快速测试结果: 基本功能正常")
        print("   可以尝试实际的文件导入测试")
    else:
        print("\n❌ 快速测试结果: 存在问题")
        print("   需要进一步修复")

if __name__ == "__main__":
    main()
