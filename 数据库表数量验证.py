#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库表数量验证脚本
分析为什么数据库管理工具和应用程序显示的表数量不一致
"""

import sqlite3
import os
from datetime import datetime

def analyze_database_tables(db_path):
    """分析数据库中的所有对象"""
    print("🔍 数据库表数量分析报告")
    print("="*60)
    print(f"📁 数据库路径: {db_path}")
    print(f"📊 文件大小: {os.path.getsize(db_path) / (1024*1024):.2f} MB")
    print(f"🕒 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 1. 获取所有对象类型的统计
            print("📋 数据库对象统计:")
            cursor.execute("""
                SELECT type, COUNT(*) as count 
                FROM sqlite_master 
                GROUP BY type 
                ORDER BY count DESC
            """)
            
            object_stats = cursor.fetchall()
            total_objects = 0
            
            for obj_type, count in object_stats:
                print(f"  {obj_type:12}: {count:3d} 个")
                total_objects += count
            
            print(f"  {'总计':12}: {total_objects:3d} 个")
            print()
            
            # 2. 详细列出所有表
            print("📊 所有表详细列表:")
            cursor.execute("""
                SELECT name, sql 
                FROM sqlite_master 
                WHERE type='table' 
                ORDER BY name
            """)
            
            tables = cursor.fetchall()
            print(f"表数量: {len(tables)} 个")
            print("-" * 50)
            
            for i, (table_name, create_sql) in enumerate(tables, 1):
                # 获取表的记录数
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
                    record_count = cursor.fetchone()[0]
                except:
                    record_count = "N/A"
                
                print(f"{i:2d}. {table_name:25} ({record_count:>8} 条记录)")
            
            print()
            
            # 3. 详细列出所有视图
            print("👁️ 所有视图详细列表:")
            cursor.execute("""
                SELECT name, sql 
                FROM sqlite_master 
                WHERE type='view' 
                ORDER BY name
            """)
            
            views = cursor.fetchall()
            print(f"视图数量: {len(views)} 个")
            print("-" * 50)
            
            for i, (view_name, create_sql) in enumerate(views, 1):
                print(f"{i:2d}. {view_name}")
            
            print()
            
            # 4. 检查索引
            print("🔍 索引统计:")
            cursor.execute("""
                SELECT COUNT(*) 
                FROM sqlite_master 
                WHERE type='index' AND name NOT LIKE 'sqlite_autoindex%'
            """)
            
            user_indexes = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT COUNT(*) 
                FROM sqlite_master 
                WHERE type='index' AND name LIKE 'sqlite_autoindex%'
            """)
            
            auto_indexes = cursor.fetchone()[0]
            
            print(f"  用户索引: {user_indexes} 个")
            print(f"  自动索引: {auto_indexes} 个")
            print(f"  总索引数: {user_indexes + auto_indexes} 个")
            print()
            
            # 5. 检查触发器
            print("⚡ 触发器统计:")
            cursor.execute("""
                SELECT COUNT(*) 
                FROM sqlite_master 
                WHERE type='trigger'
            """)
            
            triggers = cursor.fetchone()[0]
            print(f"  触发器数量: {triggers} 个")
            print()
            
            # 6. 应用程序计算方式验证
            print("🔧 应用程序计算方式验证:")
            
            # 模拟应用程序的计算方式
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            app_table_count = cursor.fetchone()[0]
            
            print(f"  应用程序计算的表数量: {app_table_count} 个")
            print(f"  实际查询到的表数量: {len(tables)} 个")
            
            if app_table_count != len(tables):
                print("  ⚠️ 发现差异！")
            else:
                print("  ✅ 计算一致")
            
            print()
            
            # 7. 可能的差异原因分析
            print("🤔 差异原因分析:")
            
            # 检查是否有系统表被计算在内
            cursor.execute("""
                SELECT name 
                FROM sqlite_master 
                WHERE type='table' AND name LIKE 'sqlite_%'
            """)
            
            system_tables = cursor.fetchall()
            if system_tables:
                print(f"  发现系统表: {len(system_tables)} 个")
                for table in system_tables:
                    print(f"    - {table[0]}")
            else:
                print("  未发现系统表")
            
            # 检查是否有临时表
            cursor.execute("""
                SELECT name 
                FROM sqlite_temp_master 
                WHERE type='table'
            """)
            
            temp_tables = cursor.fetchall()
            if temp_tables:
                print(f"  发现临时表: {len(temp_tables)} 个")
                for table in temp_tables:
                    print(f"    - {table[0]}")
            else:
                print("  未发现临时表")
            
            print()
            
            # 8. 数据库管理工具可能的计算方式
            print("🛠️ 数据库管理工具可能的差异:")
            print("  可能原因:")
            print("  1. 只显示用户表，排除系统表")
            print("  2. 可能有缓存或连接问题")
            print("  3. 不同的查询条件或过滤规则")
            print("  4. 工具版本或配置差异")
            
            # 9. 建议的解决方案
            print()
            print("💡 建议解决方案:")
            print("  1. 刷新数据库管理工具的连接")
            print("  2. 检查工具的过滤设置")
            print("  3. 使用SQL直接查询验证")
            print("  4. 检查数据库文件是否完全加载")
            
            return {
                'total_tables': len(tables),
                'total_views': len(views),
                'total_indexes': user_indexes + auto_indexes,
                'total_triggers': triggers,
                'app_calculated': app_table_count,
                'system_tables': len(system_tables),
                'temp_tables': len(temp_tables)
            }
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def generate_sql_verification():
    """生成SQL验证脚本"""
    print("\n" + "="*60)
    print("📝 SQL验证脚本")
    print("="*60)
    print("您可以在数据库管理工具中执行以下SQL来验证:")
    print()
    
    sql_commands = [
        ("所有表数量", "SELECT COUNT(*) FROM sqlite_master WHERE type='table';"),
        ("所有视图数量", "SELECT COUNT(*) FROM sqlite_master WHERE type='view';"),
        ("所有对象统计", "SELECT type, COUNT(*) FROM sqlite_master GROUP BY type;"),
        ("表详细列表", "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;"),
        ("视图详细列表", "SELECT name FROM sqlite_master WHERE type='view' ORDER BY name;"),
        ("系统表检查", "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'sqlite_%';")
    ]
    
    for desc, sql in sql_commands:
        print(f"-- {desc}")
        print(f"{sql}")
        print()

def main():
    """主函数"""
    db_path = "database/sales_reports.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    # 分析数据库
    result = analyze_database_tables(db_path)
    
    if result:
        # 生成总结
        print("\n" + "="*60)
        print("📊 分析总结")
        print("="*60)
        print(f"✅ 实际表数量: {result['total_tables']} 个")
        print(f"✅ 实际视图数量: {result['total_views']} 个")
        print(f"✅ 应用程序显示: {result['app_calculated']} 个表")
        print(f"✅ 索引数量: {result['total_indexes']} 个")
        print(f"✅ 触发器数量: {result['total_triggers']} 个")
        
        if result['total_tables'] != 14:
            print(f"\n🔍 差异分析:")
            print(f"  数据库管理工具显示: 14 个表")
            print(f"  实际数据库包含: {result['total_tables']} 个表")
            print(f"  差异: {result['total_tables'] - 14} 个表")
            
            if result['system_tables'] > 0:
                print(f"  可能原因: 包含了 {result['system_tables']} 个系统表")
        
        print(f"\n💡 结论:")
        if result['total_tables'] == result['app_calculated']:
            print("  应用程序的计算是正确的")
            print("  数据库管理工具可能有显示过滤或缓存问题")
        else:
            print("  需要进一步检查应用程序的计算逻辑")
    
    # 生成验证脚本
    generate_sql_verification()

if __name__ == "__main__":
    main()
