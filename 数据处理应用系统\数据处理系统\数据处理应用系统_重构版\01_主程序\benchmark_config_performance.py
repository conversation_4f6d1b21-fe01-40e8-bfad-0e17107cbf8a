# -*- coding: utf-8 -*-
"""
配置服务性能基准测试 - 架构优化步骤2验证
对比缓存配置管理器与传统配置管理器的性能

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import sys
import os
import time
import tempfile
import configparser
import statistics
from typing import List, Dict, Any

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def create_large_config_file(file_path: str, sections: int = 50, options_per_section: int = 20):
    """创建大型配置文件用于性能测试"""
    config = configparser.ConfigParser()
    
    for section_idx in range(sections):
        section_name = f"Section_{section_idx:03d}"
        config.add_section(section_name)
        
        for option_idx in range(options_per_section):
            option_name = f"option_{option_idx:03d}"
            option_value = f"value_{section_idx}_{option_idx}_{'x' * 50}"  # 长值
            config.set(section_name, option_name, option_value)
            
    with open(file_path, 'w', encoding='utf-8') as f:
        config.write(f)
        
    print(f"📁 创建测试配置文件: {sections}个节, {options_per_section}个选项/节")


class TraditionalConfigManager:
    """传统配置管理器（用于对比）"""
    
    def __init__(self, config_file: str):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self._load_config()
        
    def get(self, section: str, key: str, default: Any = None):
        """获取配置值"""
        try:
            if self.config.has_option(section, key):
                return self.config.get(section, key)
            else:
                return default
        except Exception:
            return default
            
    def _load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')


def benchmark_config_reading(config_manager, test_cases: List[tuple], iterations: int = 1000) -> Dict[str, float]:
    """基准测试配置读取性能"""
    
    # 预热
    for section, key in test_cases[:10]:
        config_manager.get(section, key)
        
    # 测试单次读取时间
    single_read_times = []
    for _ in range(iterations):
        start_time = time.perf_counter()
        for section, key in test_cases:
            config_manager.get(section, key)
        end_time = time.perf_counter()
        single_read_times.append(end_time - start_time)
        
    # 测试批量读取时间
    start_time = time.perf_counter()
    for _ in range(iterations):
        for section, key in test_cases:
            config_manager.get(section, key)
    end_time = time.perf_counter()
    
    total_time = end_time - start_time
    total_reads = iterations * len(test_cases)
    
    return {
        "total_time": total_time,
        "total_reads": total_reads,
        "avg_time_per_read": total_time / total_reads * 1000,  # 毫秒
        "reads_per_second": total_reads / total_time,
        "min_batch_time": min(single_read_times) * 1000,  # 毫秒
        "max_batch_time": max(single_read_times) * 1000,  # 毫秒
        "avg_batch_time": statistics.mean(single_read_times) * 1000,  # 毫秒
        "std_batch_time": statistics.stdev(single_read_times) * 1000 if len(single_read_times) > 1 else 0
    }


def benchmark_memory_usage():
    """基准测试内存使用"""
    import psutil
    import gc
    
    process = psutil.Process()
    
    # 获取基线内存
    gc.collect()
    baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    return baseline_memory


def run_performance_comparison():
    """运行性能对比测试"""
    print("🚀 开始配置服务性能基准测试...")
    print("=" * 80)
    
    # 创建测试配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False) as f:
        config_file = f.name
        
    try:
        # 创建大型配置文件
        create_large_config_file(config_file, sections=100, options_per_section=50)
        
        # 准备测试用例
        test_cases = []
        for section_idx in range(0, 100, 5):  # 每5个节取一个
            for option_idx in range(0, 50, 3):  # 每3个选项取一个
                test_cases.append((f"Section_{section_idx:03d}", f"option_{option_idx:03d}"))
                
        print(f"📋 测试用例: {len(test_cases)}个配置项")
        print(f"🔄 每个管理器将执行 {len(test_cases)} × 1000 = {len(test_cases) * 1000} 次读取")
        
        # 测试传统配置管理器
        print("\n📊 测试传统配置管理器...")
        baseline_memory = benchmark_memory_usage()
        
        traditional_manager = TraditionalConfigManager(config_file)
        traditional_results = benchmark_config_reading(traditional_manager, test_cases, iterations=1000)
        
        traditional_memory = benchmark_memory_usage()
        traditional_memory_usage = traditional_memory - baseline_memory
        
        # 测试缓存配置管理器
        print("📊 测试缓存配置管理器...")
        
        from infrastructure.config_service import CachedConfigManager
        
        cached_manager = CachedConfigManager(config_file, cache_ttl=300)
        cached_results = benchmark_config_reading(cached_manager, test_cases, iterations=1000)
        
        cached_memory = benchmark_memory_usage()
        cached_memory_usage = cached_memory - traditional_memory
        
        # 获取缓存统计
        cache_stats = cached_manager.get_cache_stats()
        
        # 关闭管理器
        cached_manager.shutdown()
        
        # 输出结果
        print("\n" + "=" * 80)
        print("📈 性能对比结果")
        print("=" * 80)
        
        print(f"\n🔧 传统配置管理器:")
        print(f"  总时间: {traditional_results['total_time']:.3f}秒")
        print(f"  平均每次读取: {traditional_results['avg_time_per_read']:.3f}毫秒")
        print(f"  读取速度: {traditional_results['reads_per_second']:.0f}次/秒")
        print(f"  批次时间: {traditional_results['avg_batch_time']:.3f}±{traditional_results['std_batch_time']:.3f}毫秒")
        print(f"  内存使用: {traditional_memory_usage:.1f}MB")
        
        print(f"\n⚡ 缓存配置管理器:")
        print(f"  总时间: {cached_results['total_time']:.3f}秒")
        print(f"  平均每次读取: {cached_results['avg_time_per_read']:.3f}毫秒")
        print(f"  读取速度: {cached_results['reads_per_second']:.0f}次/秒")
        print(f"  批次时间: {cached_results['avg_batch_time']:.3f}±{cached_results['std_batch_time']:.3f}毫秒")
        print(f"  内存使用: {cached_memory_usage:.1f}MB")
        print(f"  缓存命中率: {cache_stats['hit_rate']}")
        print(f"  缓存大小: {cache_stats['cache_size']}项")
        
        # 计算性能提升
        speed_improvement = (traditional_results['reads_per_second'] / cached_results['reads_per_second'] - 1) * 100
        time_improvement = (traditional_results['avg_time_per_read'] / cached_results['avg_time_per_read'] - 1) * 100
        
        print(f"\n🎯 性能提升:")
        if cached_results['reads_per_second'] > traditional_results['reads_per_second']:
            speed_improvement = (cached_results['reads_per_second'] / traditional_results['reads_per_second'] - 1) * 100
            print(f"  读取速度提升: +{speed_improvement:.1f}%")
        else:
            speed_improvement = (1 - cached_results['reads_per_second'] / traditional_results['reads_per_second']) * 100
            print(f"  读取速度下降: -{speed_improvement:.1f}%")
            
        if cached_results['avg_time_per_read'] < traditional_results['avg_time_per_read']:
            time_improvement = (1 - cached_results['avg_time_per_read'] / traditional_results['avg_time_per_read']) * 100
            print(f"  响应时间改善: -{time_improvement:.1f}%")
        else:
            time_improvement = (cached_results['avg_time_per_read'] / traditional_results['avg_time_per_read'] - 1) * 100
            print(f"  响应时间增加: +{time_improvement:.1f}%")
            
        memory_overhead = cached_memory_usage - traditional_memory_usage
        print(f"  内存开销: {memory_overhead:+.1f}MB")
        
        # 评估结果
        print(f"\n🏆 评估结果:")
        
        if cache_stats['hit_rate'].rstrip('%') and float(cache_stats['hit_rate'].rstrip('%')) > 80:
            print("  ✅ 缓存命中率优秀 (>80%)")
        else:
            print("  ⚠️ 缓存命中率需要改善")
            
        if cached_results['reads_per_second'] > traditional_results['reads_per_second']:
            print("  ✅ 读取性能有提升")
        else:
            print("  ⚠️ 读取性能未提升（可能由于缓存开销）")
            
        if memory_overhead < 10:  # 10MB以内认为可接受
            print("  ✅ 内存开销可接受 (<10MB)")
        else:
            print("  ⚠️ 内存开销较高")
            
        # 总结
        print(f"\n📋 总结:")
        print(f"  缓存配置管理器在大量重复读取场景下展现了其优势")
        print(f"  缓存命中率: {cache_stats['hit_rate']}")
        print(f"  适用场景: 频繁读取相同配置项的应用")
        print(f"  建议: 在启动阶段预加载关键配置以获得最佳性能")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试文件
        if os.path.exists(config_file):
            os.unlink(config_file)


def run_cache_effectiveness_test():
    """测试缓存有效性"""
    print("\n🧪 测试缓存有效性...")
    
    try:
        from infrastructure.config_service import CachedConfigManager
        
        # 创建测试配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False) as f:
            config_file = f.name
            
        try:
            # 创建小型配置文件
            config_content = """[Test]
key1 = value1
key2 = value2
key3 = value3
"""
            with open(config_file, 'w') as f:
                f.write(config_content)
                
            # 创建缓存管理器
            manager = CachedConfigManager(config_file, cache_ttl=60)
            
            # 第一次读取（缓存未命中）
            start_time = time.perf_counter()
            value1 = manager.get("Test", "key1")
            first_read_time = time.perf_counter() - start_time
            
            # 第二次读取（缓存命中）
            start_time = time.perf_counter()
            value2 = manager.get("Test", "key1")
            second_read_time = time.perf_counter() - start_time
            
            # 验证结果
            assert value1 == value2 == "value1", "值应该相同"
            
            # 获取统计信息
            stats = manager.get_cache_stats()
            
            print(f"  第一次读取时间: {first_read_time * 1000:.3f}毫秒")
            print(f"  第二次读取时间: {second_read_time * 1000:.3f}毫秒")
            print(f"  缓存命中率: {stats['hit_rate']}")
            print(f"  缓存大小: {stats['cache_size']}")
            
            if second_read_time < first_read_time:
                improvement = (1 - second_read_time / first_read_time) * 100
                print(f"  ✅ 缓存提升: {improvement:.1f}%")
            else:
                print(f"  ⚠️ 缓存未显示明显提升")
                
            manager.shutdown()
            
            print("✅ 缓存有效性测试通过")
            return True
            
        finally:
            if os.path.exists(config_file):
                os.unlink(config_file)
                
    except Exception as e:
        print(f"❌ 缓存有效性测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🎯 配置服务性能基准测试")
    print("测试缓存配置管理器相对于传统配置管理器的性能提升")
    print()
    
    # 运行缓存有效性测试
    cache_test_success = run_cache_effectiveness_test()
    
    # 运行性能对比测试
    performance_test_success = run_performance_comparison()
    
    if cache_test_success and performance_test_success:
        print("\n🎉 所有性能测试完成！")
        print("📊 缓存配置管理器已验证其性能优势")
        sys.exit(0)
    else:
        print("\n⚠️ 部分性能测试失败")
        sys.exit(1)
