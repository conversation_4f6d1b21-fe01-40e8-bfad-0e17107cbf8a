# coding: utf-8
import os
import configparser
import logging
from datetime import datetime

# 配置日志
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"config_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger()

# 配置文件路径
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")

def fix_config_file():
    """修复配置文件中的文件模式匹配规则"""
    try:
        if not os.path.exists(config_path):
            logger.error(f"配置文件不存在: {config_path}")
            return False
        
        # 读取当前配置
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        # 备份原始配置
        backup_path = config_path + ".bak"
        with open(backup_path, 'w', encoding='utf-8') as f:
            config.write(f)
        logger.info(f"已备份原始配置到: {backup_path}")
        
        # 检查并修复文件模式匹配规则
        if config.has_section('FilePatterns'):
            # 获取当前值
            current_iot_pattern = config.get('FilePatterns', 'settlement_iot_pattern')
            current_zero_pattern = config.get('FilePatterns', 'settlement_zero_pattern')
            
            logger.info(f"当前IOT模式: {current_iot_pattern}")
            logger.info(f"当前ZERO模式: {current_zero_pattern}")
            
            # 检查IOT文件夹中的文件
            iot_folder = config.get('Paths', 'iot_folder')
            iot_files = [f for f in os.listdir(iot_folder) if f.startswith('SETTLEMENT')]
            
            if iot_files:
                logger.info(f"IOT文件夹中的SETTLEMENT文件: {iot_files}")
                # 分析文件名模式
                for file in iot_files:
                    if 'zeroiot' in file.lower():
                        # 更新IOT模式
                        new_iot_pattern = r"SETTLEMENT_REPORT.*_zeroiot\.xlsx$"
                        config.set('FilePatterns', 'settlement_iot_pattern', new_iot_pattern)
                        logger.info(f"已更新IOT模式为: {new_iot_pattern}")
                        break
            
            # 检查ZERO文件夹中的文件
            zero_folder = config.get('Paths', 'zero_folder')
            zero_files = [f for f in os.listdir(zero_folder) if f.startswith('SETTLEMENT')]
            
            if zero_files:
                logger.info(f"ZERO文件夹中的SETTLEMENT文件: {zero_files}")
                # 分析文件名模式
                for file in zero_files:
                    if 'zeropowerstatio' in file.lower():
                        # 更新ZERO模式
                        new_zero_pattern = r"SETTLEMENT_REPORT.*_zeropowerstatio\.xlsx$"
                        config.set('FilePatterns', 'settlement_zero_pattern', new_zero_pattern)
                        logger.info(f"已更新ZERO模式为: {new_zero_pattern}")
                        break
        
        # 保存修改后的配置
        with open(config_path, 'w', encoding='utf-8') as f:
            config.write(f)
        logger.info("已保存修复后的配置文件")
        
        return True
    
    except Exception as e:
        logger.error(f"修复配置文件时出错: {str(e)}")
        return False

def main():
    logger.info("=== 开始修复配置文件 ===")
    
    if fix_config_file():
        logger.info("=== 配置文件修复成功 ===")
    else:
        logger.error("=== 配置文件修复失败 ===")

if __name__ == "__main__":
    main()