# -*- coding: utf-8 -*-
"""
简化Bug检测 - 快速验证修复效果
只检查关键的导入和基本功能

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import sys
import os
import importlib

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def test_imports():
    """测试关键模块导入"""
    print("🔍 测试模块导入...")
    
    modules_to_test = [
        "infrastructure",
        "infrastructure.service_container",
        "infrastructure.event_bus", 
        "infrastructure.feature_flags",
        "infrastructure.config_service",
        "infrastructure.logging_service",
        "infrastructure.gui_service",
        "infrastructure.gui_compatibility",
        "infrastructure.file_service",
        "infrastructure.backup_service"
    ]
    
    failed_imports = []
    
    for module_name in modules_to_test:
        try:
            importlib.import_module(module_name)
            print(f"  ✅ {module_name}")
        except Exception as e:
            print(f"  ❌ {module_name}: {e}")
            failed_imports.append((module_name, str(e)))
            
    return len(failed_imports) == 0, failed_imports


def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        # 测试基础设施初始化
        from infrastructure import initialize_infrastructure, get_infrastructure, shutdown_infrastructure
        
        config = {"feature_flags_config": "simple_test.json"}
        success = initialize_infrastructure(config)
        
        if not success:
            print("  ❌ 基础设施初始化失败")
            return False
            
        print("  ✅ 基础设施初始化成功")
        
        # 获取基础设施实例
        infrastructure = get_infrastructure()
        if infrastructure is None:
            print("  ❌ 无法获取基础设施实例")
            return False
            
        print("  ✅ 基础设施实例获取成功")
        
        # 测试特性开关
        feature_flags = infrastructure.get_feature_flags()
        feature_flags.enable("test_feature", "测试特性")
        
        if not feature_flags.is_enabled("test_feature"):
            print("  ❌ 特性开关功能异常")
            return False
            
        print("  ✅ 特性开关功能正常")
        
        # 测试服务容器
        container = infrastructure.get_container()
        if container is None:
            print("  ❌ 无法获取服务容器")
            return False
            
        print("  ✅ 服务容器获取成功")
        
        # 测试事件总线
        event_bus = infrastructure.get_event_bus()
        if event_bus is None:
            print("  ❌ 无法获取事件总线")
            return False
            
        print("  ✅ 事件总线获取成功")
        
        # 关闭基础设施
        shutdown_infrastructure()
        print("  ✅ 基础设施关闭成功")
        
        # 清理测试文件
        if os.path.exists("simple_test.json"):
            os.unlink("simple_test.json")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 基本功能测试失败: {e}")
        return False


def test_service_creation():
    """测试服务创建"""
    print("\n🏗️ 测试服务创建...")
    
    try:
        # 测试日志服务创建
        from infrastructure.logging_service import LoggingServiceFactory
        from infrastructure.event_bus import EventBus
        
        event_bus = EventBus(max_queue_size=50, worker_threads=1)
        logging_service = LoggingServiceFactory.create_logging_service(event_bus)
        
        if logging_service is None:
            print("  ❌ 日志服务创建失败")
            return False
            
        print("  ✅ 日志服务创建成功")
        
        # 测试文件服务创建
        from infrastructure.file_service import FileServiceFactory
        
        file_service = FileServiceFactory.create_file_service(max_workers=2)
        
        if file_service is None:
            print("  ❌ 文件服务创建失败")
            return False
            
        print("  ✅ 文件服务创建成功")
        
        # 测试备份服务创建
        from infrastructure.backup_service import BackupServiceFactory
        
        backup_service = BackupServiceFactory.create_backup_service(max_workers=1)
        
        if backup_service is None:
            print("  ❌ 备份服务创建失败")
            return False
            
        print("  ✅ 备份服务创建成功")
        
        # 关闭所有服务
        backup_service.shutdown()
        file_service.shutdown()
        logging_service.shutdown()
        event_bus.shutdown()
        
        print("  ✅ 所有服务关闭成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 服务创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_compatibility():
    """测试兼容性"""
    print("\n🔄 测试兼容性...")
    
    try:
        from infrastructure import initialize_infrastructure, get_infrastructure, shutdown_infrastructure
        
        config = {"feature_flags_config": "compatibility_test.json"}
        success = initialize_infrastructure(config)
        
        if not success:
            print("  ❌ 基础设施初始化失败")
            return False
            
        infrastructure = get_infrastructure()
        adapter = infrastructure.get_compatibility_adapter()
        
        if adapter is None:
            print("  ❌ 无法获取兼容性适配器")
            return False
            
        print("  ✅ 兼容性适配器获取成功")
        
        # 测试配置管理器兼容性
        config_manager = adapter.get_config_manager()
        if config_manager is None:
            print("  ❌ 配置管理器兼容性失败")
            return False
            
        print("  ✅ 配置管理器兼容性正常")
        
        # 测试文件管理器兼容性
        file_manager = adapter.get_file_manager({})
        if file_manager is None:
            print("  ❌ 文件管理器兼容性失败")
            return False
            
        print("  ✅ 文件管理器兼容性正常")
        
        shutdown_infrastructure()
        
        # 清理测试文件
        if os.path.exists("compatibility_test.json"):
            os.unlink("compatibility_test.json")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 兼容性测试失败: {e}")
        return False


def run_simple_bug_check():
    """运行简化Bug检查"""
    print("🚀 开始简化Bug检查...")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("基本功能", test_basic_functionality),
        ("服务创建", test_service_creation),
        ("兼容性", test_compatibility)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            success, details = test_func() if test_name == "模块导入" else (test_func(), None)
            
            if success:
                passed += 1
            else:
                failed += 1
                if details:
                    print(f"\n  详细错误信息:")
                    for module, error in details:
                        print(f"    {module}: {error}")
                        
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            failed += 1
            
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}个通过, {failed}个失败")
    
    if failed == 0:
        print("🎉 所有基本功能测试通过！Bug修复验证成功")
        print("✅ 架构优化代码质量良好，可以安全使用")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
        return False


if __name__ == "__main__":
    success = run_simple_bug_check()
    sys.exit(0 if success else 1)
