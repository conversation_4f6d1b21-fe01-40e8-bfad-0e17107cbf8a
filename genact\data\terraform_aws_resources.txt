aws_accessanalyzer_analyzer
aws_accessanalyzer_archive_rule
aws_account_alternate_contact
aws_account_primary_contact
aws_acm_certificate
aws_acm_certificate_validation
aws_acmpca_certificate
aws_acmpca_certificate_authority
aws_acmpca_certificate_authority_certificate
aws_acmpca_permission
aws_acmpca_policy
aws_ami
aws_ami_copy
aws_ami_from_instance
aws_ami_launch_permission
aws_amplify_app
aws_amplify_backend_environment
aws_amplify_branch
aws_amplify_domain_association
aws_amplify_webhook
aws_api_gateway_account
aws_api_gateway_api_key
aws_api_gateway_authorizer
aws_api_gateway_base_path_mapping
aws_api_gateway_client_certificate
aws_api_gateway_deployment
aws_api_gateway_documentation_part
aws_api_gateway_documentation_version
aws_api_gateway_domain_name
aws_api_gateway_gateway_response
aws_api_gateway_integration
aws_api_gateway_integration_response
aws_api_gateway_method
aws_api_gateway_method_response
aws_api_gateway_method_settings
aws_api_gateway_model
aws_api_gateway_request_validator
aws_api_gateway_resource
aws_api_gateway_rest_api
aws_api_gateway_rest_api_policy
aws_api_gateway_stage
aws_api_gateway_usage_plan
aws_api_gateway_usage_plan_key
aws_api_gateway_vpc_link
aws_apigatewayv2_api
aws_apigatewayv2_api_mapping
aws_apigatewayv2_authorizer
aws_apigatewayv2_deployment
aws_apigatewayv2_domain_name
aws_apigatewayv2_integration
aws_apigatewayv2_integration_response
aws_apigatewayv2_model
aws_apigatewayv2_route
aws_apigatewayv2_route_response
aws_apigatewayv2_stage
aws_apigatewayv2_vpc_link
aws_app_cookie_stickiness_policy
aws_appautoscaling_policy
aws_appautoscaling_scheduled_action
aws_appautoscaling_target
aws_appconfig_application
aws_appconfig_configuration_profile
aws_appconfig_deployment
aws_appconfig_deployment_strategy
aws_appconfig_environment
aws_appconfig_extension
aws_appconfig_extension_association
aws_appconfig_hosted_configuration_version
aws_appflow_connector_profile
aws_appflow_flow
aws_appintegrations_data_integration
aws_appintegrations_event_integration
aws_applicationinsights_application
aws_appmesh_gateway_route
aws_appmesh_mesh
aws_appmesh_route
aws_appmesh_virtual_gateway
aws_appmesh_virtual_node
aws_appmesh_virtual_router
aws_appmesh_virtual_service
aws_apprunner_auto_scaling_configuration_version
aws_apprunner_connection
aws_apprunner_custom_domain_association
aws_apprunner_default_auto_scaling_configuration_version
aws_apprunner_observability_configuration
aws_apprunner_service
aws_apprunner_vpc_connector
aws_apprunner_vpc_ingress_connection
aws_appstream_directory_config
aws_appstream_fleet
aws_appstream_fleet_stack_association
aws_appstream_image_builder
aws_appstream_stack
aws_appstream_user
aws_appstream_user_stack_association
aws_appsync_api_cache
aws_appsync_api_key
aws_appsync_datasource
aws_appsync_domain_name
aws_appsync_domain_name_api_association
aws_appsync_function
aws_appsync_graphql_api
aws_appsync_resolver
aws_appsync_type
aws_athena_data_catalog
aws_athena_database
aws_athena_named_query
aws_athena_prepared_statement
aws_athena_workgroup
aws_auditmanager_account_registration
aws_auditmanager_assessment
aws_auditmanager_assessment_delegation
aws_auditmanager_assessment_report
aws_auditmanager_control
aws_auditmanager_framework
aws_auditmanager_framework_share
aws_auditmanager_organization_admin_account_registration
aws_autoscaling_attachment
aws_autoscaling_group
aws_autoscaling_group_tag
aws_autoscaling_lifecycle_hook
aws_autoscaling_notification
aws_autoscaling_policy
aws_autoscaling_schedule
aws_autoscaling_traffic_source_attachment
aws_autoscalingplans_scaling_plan
aws_backup_framework
aws_backup_global_settings
aws_backup_plan
aws_backup_region_settings
aws_backup_report_plan
aws_backup_selection
aws_backup_vault
aws_backup_vault_lock_configuration
aws_backup_vault_notifications
aws_backup_vault_policy
aws_batch_compute_environment
aws_batch_job_definition
aws_batch_job_queue
aws_batch_scheduling_policy
aws_bedrock_model_invocation_logging_configuration
aws_budgets_budget
aws_budgets_budget_action
aws_ce_anomaly_monitor
aws_ce_anomaly_subscription
aws_ce_cost_allocation_tag
aws_ce_cost_category
aws_chime_voice_connector
aws_chime_voice_connector_group
aws_chime_voice_connector_logging
aws_chime_voice_connector_origination
aws_chime_voice_connector_streaming
aws_chime_voice_connector_termination
aws_chime_voice_connector_termination_credentials
aws_chimesdkmediapipelines_media_insights_pipeline_configuration
aws_chimesdkvoice_global_settings
aws_chimesdkvoice_sip_media_application
aws_chimesdkvoice_sip_rule
aws_chimesdkvoice_voice_profile_domain
aws_cleanrooms_collaboration
aws_cleanrooms_configured_table
aws_cloud9_environment_ec2
aws_cloud9_environment_membership
aws_cloudcontrolapi_resource
aws_cloudformation_stack
aws_cloudformation_stack_set
aws_cloudformation_stack_set_instance
aws_cloudformation_type
aws_cloudfront_cache_policy
aws_cloudfront_continuous_deployment_policy
aws_cloudfront_distribution
aws_cloudfront_field_level_encryption_config
aws_cloudfront_field_level_encryption_profile
aws_cloudfront_function
aws_cloudfront_key_group
aws_cloudfront_monitoring_subscription
aws_cloudfront_origin_access_control
aws_cloudfront_origin_access_identity
aws_cloudfront_origin_request_policy
aws_cloudfront_public_key
aws_cloudfront_realtime_log_config
aws_cloudfront_response_headers_policy
aws_cloudhsm_v2_cluster
aws_cloudhsm_v2_hsm
aws_cloudsearch_domain
aws_cloudsearch_domain_service_access_policy
aws_cloudtrail
aws_cloudtrail_event_data_store
aws_cloudwatch_composite_alarm
aws_cloudwatch_dashboard
aws_cloudwatch_event_api_destination
aws_cloudwatch_event_archive
aws_cloudwatch_event_bus
aws_cloudwatch_event_bus_policy
aws_cloudwatch_event_connection
aws_cloudwatch_event_endpoint
aws_cloudwatch_event_permission
aws_cloudwatch_event_rule
aws_cloudwatch_event_target
aws_cloudwatch_log_data_protection_policy
aws_cloudwatch_log_destination
aws_cloudwatch_log_destination_policy
aws_cloudwatch_log_group
aws_cloudwatch_log_metric_filter
aws_cloudwatch_log_resource_policy
aws_cloudwatch_log_stream
aws_cloudwatch_log_subscription_filter
aws_cloudwatch_metric_alarm
aws_cloudwatch_metric_stream
aws_cloudwatch_query_definition
aws_codeartifact_domain
aws_codeartifact_domain_permissions_policy
aws_codeartifact_repository
aws_codeartifact_repository_permissions_policy
aws_codebuild_project
aws_codebuild_report_group
aws_codebuild_resource_policy
aws_codebuild_source_credential
aws_codebuild_webhook
aws_codecatalyst_dev_environment
aws_codecatalyst_project
aws_codecatalyst_source_repository
aws_codecommit_approval_rule_template
aws_codecommit_approval_rule_template_association
aws_codecommit_repository
aws_codecommit_trigger
aws_codedeploy_app
aws_codedeploy_deployment_config
aws_codedeploy_deployment_group
aws_codeguruprofiler_profiling_group
aws_codegurureviewer_repository_association
aws_codepipeline
aws_codepipeline_custom_action_type
aws_codepipeline_webhook
aws_codestarconnections_connection
aws_codestarconnections_host
aws_codestarnotifications_notification_rule
aws_cognito_identity_pool
aws_cognito_identity_pool_provider_principal_tag
aws_cognito_identity_pool_roles_attachment
aws_cognito_identity_provider
aws_cognito_managed_user_pool_client
aws_cognito_resource_server
aws_cognito_risk_configuration
aws_cognito_user
aws_cognito_user_group
aws_cognito_user_in_group
aws_cognito_user_pool
aws_cognito_user_pool_client
aws_cognito_user_pool_domain
aws_cognito_user_pool_ui_customization
aws_comprehend_document_classifier
aws_comprehend_entity_recognizer
aws_config_aggregate_authorization
aws_config_config_rule
aws_config_configuration_aggregator
aws_config_configuration_recorder
aws_config_configuration_recorder_status
aws_config_conformance_pack
aws_config_delivery_channel
aws_config_organization_conformance_pack
aws_config_organization_custom_policy_rule
aws_config_organization_custom_rule
aws_config_organization_managed_rule
aws_config_remediation_configuration
aws_connect_bot_association
aws_connect_contact_flow
aws_connect_contact_flow_module
aws_connect_hours_of_operation
aws_connect_instance
aws_connect_instance_storage_config
aws_connect_lambda_function_association
aws_connect_phone_number
aws_connect_queue
aws_connect_quick_connect
aws_connect_routing_profile
aws_connect_security_profile
aws_connect_user
aws_connect_user_hierarchy_group
aws_connect_user_hierarchy_structure
aws_connect_vocabulary
aws_controltower_control
aws_cur_report_definition
aws_customer_gateway
aws_customerprofiles_domain
aws_customerprofiles_profile
aws_dataexchange_data_set
aws_dataexchange_revision
aws_datapipeline_pipeline
aws_datapipeline_pipeline_definition
aws_datasync_agent
aws_datasync_location_azure_blob
aws_datasync_location_efs
aws_datasync_location_fsx_lustre_file_system
aws_datasync_location_fsx_ontap_file_system
aws_datasync_location_fsx_openzfs_file_system
aws_datasync_location_fsx_windows_file_system
aws_datasync_location_hdfs
aws_datasync_location_nfs
aws_datasync_location_object_storage
aws_datasync_location_s3
aws_datasync_location_smb
aws_datasync_task
aws_dax_cluster
aws_dax_parameter_group
aws_dax_subnet_group
aws_db_cluster_snapshot
aws_db_event_subscription
aws_db_instance
aws_db_instance_automated_backups_replication
aws_db_instance_role_association
aws_db_option_group
aws_db_parameter_group
aws_db_proxy
aws_db_proxy_default_target_group
aws_db_proxy_endpoint
aws_db_proxy_target
aws_db_snapshot
aws_db_snapshot_copy
aws_db_subnet_group
aws_default_network_acl
aws_default_route_table
aws_default_security_group
aws_default_subnet
aws_default_vpc
aws_default_vpc_dhcp_options
aws_detective_graph
aws_detective_invitation_accepter
aws_detective_member
aws_detective_organization_admin_account
aws_detective_organization_configuration
aws_devicefarm_device_pool
aws_devicefarm_instance_profile
aws_devicefarm_network_profile
aws_devicefarm_project
aws_devicefarm_test_grid_project
aws_devicefarm_upload
aws_directory_service_conditional_forwarder
aws_directory_service_directory
aws_directory_service_log_subscription
aws_directory_service_radius_settings
aws_directory_service_region
aws_directory_service_shared_directory
aws_directory_service_shared_directory_accepter
aws_directory_service_trust
aws_dlm_lifecycle_policy
aws_dms_certificate
aws_dms_endpoint
aws_dms_event_subscription
aws_dms_replication_config
aws_dms_replication_instance
aws_dms_replication_subnet_group
aws_dms_replication_task
aws_dms_s3_endpoint
aws_docdb_cluster
aws_docdb_cluster_instance
aws_docdb_cluster_parameter_group
aws_docdb_cluster_snapshot
aws_docdb_event_subscription
aws_docdb_global_cluster
aws_docdb_subnet_group
aws_docdbelastic_cluster
aws_dx_bgp_peer
aws_dx_connection
aws_dx_connection_association
aws_dx_connection_confirmation
aws_dx_gateway
aws_dx_gateway_association
aws_dx_gateway_association_proposal
aws_dx_hosted_connection
aws_dx_hosted_private_virtual_interface
aws_dx_hosted_private_virtual_interface_accepter
aws_dx_hosted_public_virtual_interface
aws_dx_hosted_public_virtual_interface_accepter
aws_dx_hosted_transit_virtual_interface
aws_dx_hosted_transit_virtual_interface_accepter
aws_dx_lag
aws_dx_macsec_key_association
aws_dx_private_virtual_interface
aws_dx_public_virtual_interface
aws_dx_transit_virtual_interface
aws_dynamodb_contributor_insights
aws_dynamodb_global_table
aws_dynamodb_kinesis_streaming_destination
aws_dynamodb_table
aws_dynamodb_table_item
aws_dynamodb_table_replica
aws_dynamodb_tag
aws_ebs_default_kms_key
aws_ebs_encryption_by_default
aws_ebs_snapshot
aws_ebs_snapshot_copy
aws_ebs_snapshot_import
aws_ebs_volume
aws_ec2_availability_zone_group
aws_ec2_capacity_reservation
aws_ec2_carrier_gateway
aws_ec2_client_vpn_authorization_rule
aws_ec2_client_vpn_endpoint
aws_ec2_client_vpn_network_association
aws_ec2_client_vpn_route
aws_ec2_fleet
aws_ec2_host
aws_ec2_image_block_public_access
aws_ec2_instance_connect_endpoint
aws_ec2_instance_state
aws_ec2_local_gateway_route
aws_ec2_local_gateway_route_table_vpc_association
aws_ec2_managed_prefix_list
aws_ec2_managed_prefix_list_entry
aws_ec2_network_insights_analysis
aws_ec2_network_insights_path
aws_ec2_serial_console_access
aws_ec2_subnet_cidr_reservation
aws_ec2_tag
aws_ec2_traffic_mirror_filter
aws_ec2_traffic_mirror_filter_rule
aws_ec2_traffic_mirror_session
aws_ec2_traffic_mirror_target
aws_ec2_transit_gateway
aws_ec2_transit_gateway_connect
aws_ec2_transit_gateway_connect_peer
aws_ec2_transit_gateway_multicast_domain
aws_ec2_transit_gateway_multicast_domain_association
aws_ec2_transit_gateway_multicast_group_member
aws_ec2_transit_gateway_multicast_group_source
aws_ec2_transit_gateway_peering_attachment
aws_ec2_transit_gateway_peering_attachment_accepter
aws_ec2_transit_gateway_policy_table
aws_ec2_transit_gateway_policy_table_association
aws_ec2_transit_gateway_prefix_list_reference
aws_ec2_transit_gateway_route
aws_ec2_transit_gateway_route_table
aws_ec2_transit_gateway_route_table_association
aws_ec2_transit_gateway_route_table_propagation
aws_ec2_transit_gateway_vpc_attachment
aws_ec2_transit_gateway_vpc_attachment_accepter
aws_ecr_lifecycle_policy
aws_ecr_pull_through_cache_rule
aws_ecr_registry_policy
aws_ecr_registry_scanning_configuration
aws_ecr_replication_configuration
aws_ecr_repository
aws_ecr_repository_policy
aws_ecrpublic_repository
aws_ecrpublic_repository_policy
aws_ecs_account_setting_default
aws_ecs_capacity_provider
aws_ecs_cluster
aws_ecs_cluster_capacity_providers
aws_ecs_service
aws_ecs_tag
aws_ecs_task_definition
aws_ecs_task_set
aws_efs_access_point
aws_efs_backup_policy
aws_efs_file_system
aws_efs_file_system_policy
aws_efs_mount_target
aws_efs_replication_configuration
aws_egress_only_internet_gateway
aws_eip
aws_eip_association
aws_eks_addon
aws_eks_cluster
aws_eks_fargate_profile
aws_eks_identity_provider_config
aws_eks_node_group
aws_eks_pod_identity_association
aws_elastic_beanstalk_application
aws_elastic_beanstalk_application_version
aws_elastic_beanstalk_configuration_template
aws_elastic_beanstalk_environment
aws_elasticache_cluster
aws_elasticache_global_replication_group
aws_elasticache_parameter_group
aws_elasticache_replication_group
aws_elasticache_subnet_group
aws_elasticache_user
aws_elasticache_user_group
aws_elasticache_user_group_association
aws_elasticsearch_domain
aws_elasticsearch_domain_policy
aws_elasticsearch_domain_saml_options
aws_elasticsearch_vpc_endpoint
aws_elastictranscoder_pipeline
aws_elastictranscoder_preset
aws_elb
aws_elb_attachment
aws_emr_block_public_access_configuration
aws_emr_cluster
aws_emr_instance_fleet
aws_emr_instance_group
aws_emr_managed_scaling_policy
aws_emr_security_configuration
aws_emr_studio
aws_emr_studio_session_mapping
aws_emrcontainers_job_template
aws_emrcontainers_virtual_cluster
aws_emrserverless_application
aws_evidently_feature
aws_evidently_launch
aws_evidently_project
aws_evidently_segment
aws_finspace_kx_cluster
aws_finspace_kx_database
aws_finspace_kx_environment
aws_finspace_kx_user
aws_fis_experiment_template
aws_flow_log
aws_fms_admin_account
aws_fms_policy
aws_fsx_backup
aws_fsx_data_repository_association
aws_fsx_file_cache
aws_fsx_lustre_file_system
aws_fsx_ontap_file_system
aws_fsx_ontap_storage_virtual_machine
aws_fsx_ontap_volume
aws_fsx_openzfs_file_system
aws_fsx_openzfs_snapshot
aws_fsx_openzfs_volume
aws_fsx_windows_file_system
aws_gamelift_alias
aws_gamelift_build
aws_gamelift_fleet
aws_gamelift_game_server_group
aws_gamelift_game_session_queue
aws_gamelift_script
aws_glacier_vault
aws_glacier_vault_lock
aws_globalaccelerator_accelerator
aws_globalaccelerator_custom_routing_accelerator
aws_globalaccelerator_custom_routing_endpoint_group
aws_globalaccelerator_custom_routing_listener
aws_globalaccelerator_endpoint_group
aws_globalaccelerator_listener
aws_glue_catalog_database
aws_glue_catalog_table
aws_glue_classifier
aws_glue_connection
aws_glue_crawler
aws_glue_data_catalog_encryption_settings
aws_glue_data_quality_ruleset
aws_glue_dev_endpoint
aws_glue_job
aws_glue_ml_transform
aws_glue_partition
aws_glue_partition_index
aws_glue_registry
aws_glue_resource_policy
aws_glue_schema
aws_glue_security_configuration
aws_glue_trigger
aws_glue_user_defined_function
aws_glue_workflow
aws_grafana_license_association
aws_grafana_role_association
aws_grafana_workspace
aws_grafana_workspace_api_key
aws_grafana_workspace_saml_configuration
aws_guardduty_detector
aws_guardduty_detector_feature
aws_guardduty_filter
aws_guardduty_invite_accepter
aws_guardduty_ipset
aws_guardduty_member
aws_guardduty_organization_admin_account
aws_guardduty_organization_configuration
aws_guardduty_organization_configuration_feature
aws_guardduty_publishing_destination
aws_guardduty_threatintelset
aws_iam_access_key
aws_iam_account_alias
aws_iam_account_password_policy
aws_iam_group
aws_iam_group_membership
aws_iam_group_policy
aws_iam_group_policy_attachment
aws_iam_instance_profile
aws_iam_openid_connect_provider
aws_iam_policy
aws_iam_policy_attachment
aws_iam_role
aws_iam_role_policy
aws_iam_role_policy_attachment
aws_iam_saml_provider
aws_iam_security_token_service_preferences
aws_iam_server_certificate
aws_iam_service_linked_role
aws_iam_service_specific_credential
aws_iam_signing_certificate
aws_iam_user
aws_iam_user_group_membership
aws_iam_user_login_profile
aws_iam_user_policy
aws_iam_user_policy_attachment
aws_iam_user_ssh_key
aws_iam_virtual_mfa_device
aws_identitystore_group
aws_identitystore_group_membership
aws_identitystore_user
aws_imagebuilder_component
aws_imagebuilder_container_recipe
aws_imagebuilder_distribution_configuration
aws_imagebuilder_image
aws_imagebuilder_image_pipeline
aws_imagebuilder_image_recipe
aws_imagebuilder_infrastructure_configuration
aws_infinidash
aws_inspector_assessment_target
aws_inspector_assessment_template
aws_inspector_resource_group
aws_inspector2_delegated_admin_account
aws_inspector2_enabler
aws_inspector2_member_association
aws_inspector2_organization_configuration
aws_instance
aws_internet_gateway
aws_internet_gateway_attachment
aws_internetmonitor_monitor
aws_iot_authorizer
aws_iot_billing_group
aws_iot_ca_certificate
aws_iot_certificate
aws_iot_domain_configuration
aws_iot_event_configurations
aws_iot_indexing_configuration
aws_iot_logging_options
aws_iot_policy
aws_iot_policy_attachment
aws_iot_provisioning_template
aws_iot_role_alias
aws_iot_thing
aws_iot_thing_group
aws_iot_thing_group_membership
aws_iot_thing_principal_attachment
aws_iot_thing_type
aws_iot_topic_rule
aws_iot_topic_rule_destination
aws_ivs_channel
aws_ivs_playback_key_pair
aws_ivs_recording_configuration
aws_ivschat_logging_configuration
aws_ivschat_room
aws_kendra_data_source
aws_kendra_experience
aws_kendra_faq
aws_kendra_index
aws_kendra_query_suggestions_block_list
aws_kendra_thesaurus
aws_key_pair
aws_keyspaces_keyspace
aws_keyspaces_table
aws_kinesis_analytics_application
aws_kinesis_firehose_delivery_stream
aws_kinesis_stream
aws_kinesis_stream_consumer
aws_kinesis_video_stream
aws_kinesisanalyticsv2_application
aws_kinesisanalyticsv2_application_snapshot
aws_kms_alias
aws_kms_ciphertext
aws_kms_custom_key_store
aws_kms_external_key
aws_kms_grant
aws_kms_key
aws_kms_key_policy
aws_kms_replica_external_key
aws_kms_replica_key
aws_lakeformation_data_lake_settings
aws_lakeformation_lf_tag
aws_lakeformation_permissions
aws_lakeformation_resource
aws_lakeformation_resource_lf_tags
aws_lambda_alias
aws_lambda_code_signing_config
aws_lambda_event_source_mapping
aws_lambda_function
aws_lambda_function_event_invoke_config
aws_lambda_function_url
aws_lambda_invocation
aws_lambda_layer_version
aws_lambda_layer_version_permission
aws_lambda_permission
aws_lambda_provisioned_concurrency_config
aws_launch_configuration
aws_launch_template
aws_lb
aws_lb_cookie_stickiness_policy
aws_lb_listener
aws_lb_listener_certificate
aws_lb_listener_rule
aws_lb_ssl_negotiation_policy
aws_lb_target_group
aws_lb_target_group_attachment
aws_lb_trust_store
aws_lb_trust_store_revocation
aws_lex_bot
aws_lex_bot_alias
aws_lex_intent
aws_lex_slot_type
aws_lexv2models_bot
aws_lexv2models_bot_locale
aws_lexv2models_bot_version
aws_licensemanager_association
aws_licensemanager_grant
aws_licensemanager_grant_accepter
aws_licensemanager_license_configuration
aws_lightsail_bucket
aws_lightsail_bucket_access_key
aws_lightsail_bucket_resource_access
aws_lightsail_certificate
aws_lightsail_container_service
aws_lightsail_container_service_deployment_version
aws_lightsail_database
aws_lightsail_disk
aws_lightsail_disk_attachment
aws_lightsail_distribution
aws_lightsail_domain
aws_lightsail_domain_entry
aws_lightsail_instance
aws_lightsail_instance_public_ports
aws_lightsail_key_pair
aws_lightsail_lb
aws_lightsail_lb_attachment
aws_lightsail_lb_certificate
aws_lightsail_lb_certificate_attachment
aws_lightsail_lb_https_redirection_policy
aws_lightsail_lb_stickiness_policy
aws_lightsail_static_ip
aws_lightsail_static_ip_attachment
aws_load_balancer_backend_server_policy
aws_load_balancer_listener_policy
aws_load_balancer_policy
aws_location_geofence_collection
aws_location_map
aws_location_place_index
aws_location_route_calculator
aws_location_tracker
aws_location_tracker_association
aws_macie2_account
aws_macie2_classification_export_configuration
aws_macie2_classification_job
aws_macie2_custom_data_identifier
aws_macie2_findings_filter
aws_macie2_invitation_accepter
aws_macie2_member
aws_macie2_organization_admin_account
aws_main_route_table_association
aws_media_convert_queue
aws_media_package_channel
aws_media_store_container
aws_media_store_container_policy
aws_medialive_channel
aws_medialive_input
aws_medialive_input_security_group
aws_medialive_multiplex
aws_medialive_multiplex_program
aws_memorydb_acl
aws_memorydb_cluster
aws_memorydb_parameter_group
aws_memorydb_snapshot
aws_memorydb_subnet_group
aws_memorydb_user
aws_mq_broker
aws_mq_configuration
aws_msk_cluster
aws_msk_cluster_policy
aws_msk_configuration
aws_msk_replicator
aws_msk_scram_secret_association
aws_msk_serverless_cluster
aws_msk_vpc_connection
aws_mskconnect_connector
aws_mskconnect_custom_plugin
aws_mskconnect_worker_configuration
aws_mwaa_environment
aws_nat_gateway
aws_neptune_cluster
aws_neptune_cluster_endpoint
aws_neptune_cluster_instance
aws_neptune_cluster_parameter_group
aws_neptune_cluster_snapshot
aws_neptune_event_subscription
aws_neptune_global_cluster
aws_neptune_parameter_group
aws_neptune_subnet_group
aws_network_acl
aws_network_acl_association
aws_network_acl_rule
aws_network_interface
aws_network_interface_attachment
aws_network_interface_sg_attachment
aws_networkfirewall_firewall
aws_networkfirewall_firewall_policy
aws_networkfirewall_logging_configuration
aws_networkfirewall_resource_policy
aws_networkfirewall_rule_group
aws_networkmanager_attachment_accepter
aws_networkmanager_connect_attachment
aws_networkmanager_connect_peer
aws_networkmanager_connection
aws_networkmanager_core_network
aws_networkmanager_core_network_policy_attachment
aws_networkmanager_customer_gateway_association
aws_networkmanager_device
aws_networkmanager_global_network
aws_networkmanager_link
aws_networkmanager_link_association
aws_networkmanager_site
aws_networkmanager_site_to_site_vpn_attachment
aws_networkmanager_transit_gateway_connect_peer_association
aws_networkmanager_transit_gateway_peering
aws_networkmanager_transit_gateway_registration
aws_networkmanager_transit_gateway_route_table_attachment
aws_networkmanager_vpc_attachment
aws_oam_link
aws_oam_sink
aws_oam_sink_policy
aws_opensearch_domain
aws_opensearch_domain_policy
aws_opensearch_domain_saml_options
aws_opensearch_inbound_connection_accepter
aws_opensearch_outbound_connection
aws_opensearch_package
aws_opensearch_package_association
aws_opensearch_vpc_endpoint
aws_opensearchserverless_access_policy
aws_opensearchserverless_collection
aws_opensearchserverless_lifecycle_policy
aws_opensearchserverless_security_config
aws_opensearchserverless_security_policy
aws_opensearchserverless_vpc_endpoint
aws_opsworks_application
aws_opsworks_custom_layer
aws_opsworks_ecs_cluster_layer
aws_opsworks_ganglia_layer
aws_opsworks_haproxy_layer
aws_opsworks_instance
aws_opsworks_java_app_layer
aws_opsworks_memcached_layer
aws_opsworks_mysql_layer
aws_opsworks_nodejs_app_layer
aws_opsworks_permission
aws_opsworks_php_app_layer
aws_opsworks_rails_app_layer
aws_opsworks_rds_db_instance
aws_opsworks_stack
aws_opsworks_static_web_layer
aws_opsworks_user_profile
aws_organizations_account
aws_organizations_delegated_administrator
aws_organizations_organization
aws_organizations_organizational_unit
aws_organizations_policy
aws_organizations_policy_attachment
aws_organizations_resource_policy
aws_pinpoint_adm_channel
aws_pinpoint_apns_channel
aws_pinpoint_apns_sandbox_channel
aws_pinpoint_apns_voip_channel
aws_pinpoint_apns_voip_sandbox_channel
aws_pinpoint_app
aws_pinpoint_baidu_channel
aws_pinpoint_email_channel
aws_pinpoint_event_stream
aws_pinpoint_gcm_channel
aws_pinpoint_sms_channel
aws_pipes_pipe
aws_placement_group
aws_prometheus_alert_manager_definition
aws_prometheus_rule_group_namespace
aws_prometheus_workspace
aws_proxy_protocol_policy
aws_qldb_ledger
aws_qldb_stream
aws_quicksight_account_subscription
aws_quicksight_analysis
aws_quicksight_dashboard
aws_quicksight_data_set
aws_quicksight_data_source
aws_quicksight_folder
aws_quicksight_folder_membership
aws_quicksight_group
aws_quicksight_group_membership
aws_quicksight_iam_policy_assignment
aws_quicksight_ingestion
aws_quicksight_namespace
aws_quicksight_refresh_schedule
aws_quicksight_template
aws_quicksight_template_alias
aws_quicksight_theme
aws_quicksight_user
aws_quicksight_vpc_connection
aws_ram_principal_association
aws_ram_resource_association
aws_ram_resource_share
aws_ram_resource_share_accepter
aws_ram_sharing_with_organization
aws_rbin_rule
aws_rds_cluster
aws_rds_cluster_activity_stream
aws_rds_cluster_endpoint
aws_rds_cluster_instance
aws_rds_cluster_parameter_group
aws_rds_cluster_role_association
aws_rds_custom_db_engine_version
aws_rds_export_task
aws_rds_global_cluster
aws_rds_reserved_instance
aws_redshift_authentication_profile
aws_redshift_cluster
aws_redshift_cluster_iam_roles
aws_redshift_cluster_snapshot
aws_redshift_endpoint_access
aws_redshift_endpoint_authorization
aws_redshift_event_subscription
aws_redshift_hsm_client_certificate
aws_redshift_hsm_configuration
aws_redshift_parameter_group
aws_redshift_partner
aws_redshift_resource_policy
aws_redshift_scheduled_action
aws_redshift_snapshot_copy_grant
aws_redshift_snapshot_schedule
aws_redshift_snapshot_schedule_association
aws_redshift_subnet_group
aws_redshift_usage_limit
aws_redshiftdata_statement
aws_redshiftserverless_endpoint_access
aws_redshiftserverless_namespace
aws_redshiftserverless_resource_policy
aws_redshiftserverless_snapshot
aws_redshiftserverless_usage_limit
aws_redshiftserverless_workgroup
aws_resourceexplorer2_index
aws_resourceexplorer2_view
aws_resourcegroups_group
aws_resourcegroups_resource
aws_rolesanywhere_profile
aws_rolesanywhere_trust_anchor
aws_route
aws_route_table
aws_route_table_association
aws_route53_cidr_collection
aws_route53_cidr_location
aws_route53_delegation_set
aws_route53_health_check
aws_route53_hosted_zone_dnssec
aws_route53_key_signing_key
aws_route53_query_log
aws_route53_record
aws_route53_resolver_config
aws_route53_resolver_dnssec_config
aws_route53_resolver_endpoint
aws_route53_resolver_firewall_config
aws_route53_resolver_firewall_domain_list
aws_route53_resolver_firewall_rule
aws_route53_resolver_firewall_rule_group
aws_route53_resolver_firewall_rule_group_association
aws_route53_resolver_query_log_config
aws_route53_resolver_query_log_config_association
aws_route53_resolver_rule
aws_route53_resolver_rule_association
aws_route53_traffic_policy
aws_route53_traffic_policy_instance
aws_route53_vpc_association_authorization
aws_route53_zone
aws_route53_zone_association
aws_route53domains_registered_domain
aws_route53recoverycontrolconfig_cluster
aws_route53recoverycontrolconfig_control_panel
aws_route53recoverycontrolconfig_routing_control
aws_route53recoverycontrolconfig_safety_rule
aws_route53recoveryreadiness_cell
aws_route53recoveryreadiness_readiness_check
aws_route53recoveryreadiness_recovery_group
aws_route53recoveryreadiness_resource_set
aws_rum_app_monitor
aws_rum_metrics_destination
aws_s3_access_point
aws_s3_account_public_access_block
aws_s3_bucket
aws_s3_bucket_accelerate_configuration
aws_s3_bucket_acl
aws_s3_bucket_analytics_configuration
aws_s3_bucket_cors_configuration
aws_s3_bucket_intelligent_tiering_configuration
aws_s3_bucket_inventory
aws_s3_bucket_lifecycle_configuration
aws_s3_bucket_logging
aws_s3_bucket_metric
aws_s3_bucket_notification
aws_s3_bucket_object
aws_s3_bucket_object_lock_configuration
aws_s3_bucket_ownership_controls
aws_s3_bucket_policy
aws_s3_bucket_public_access_block
aws_s3_bucket_replication_configuration
aws_s3_bucket_request_payment_configuration
aws_s3_bucket_server_side_encryption_configuration
aws_s3_bucket_versioning
aws_s3_bucket_website_configuration
aws_s3_directory_bucket
aws_s3_object
aws_s3_object_copy
aws_s3control_access_grant
aws_s3control_access_grants_instance
aws_s3control_access_grants_instance_resource_policy
aws_s3control_access_grants_location
aws_s3control_access_point_policy
aws_s3control_bucket
aws_s3control_bucket_lifecycle_configuration
aws_s3control_bucket_policy
aws_s3control_multi_region_access_point
aws_s3control_multi_region_access_point_policy
aws_s3control_object_lambda_access_point
aws_s3control_object_lambda_access_point_policy
aws_s3control_storage_lens_configuration
aws_s3outposts_endpoint
aws_sagemaker_app
aws_sagemaker_app_image_config
aws_sagemaker_code_repository
aws_sagemaker_data_quality_job_definition
aws_sagemaker_device
aws_sagemaker_device_fleet
aws_sagemaker_domain
aws_sagemaker_endpoint
aws_sagemaker_endpoint_configuration
aws_sagemaker_feature_group
aws_sagemaker_flow_definition
aws_sagemaker_human_task_ui
aws_sagemaker_image
aws_sagemaker_image_version
aws_sagemaker_model
aws_sagemaker_model_package_group
aws_sagemaker_model_package_group_policy
aws_sagemaker_monitoring_schedule
aws_sagemaker_notebook_instance
aws_sagemaker_notebook_instance_lifecycle_configuration
aws_sagemaker_pipeline
aws_sagemaker_project
aws_sagemaker_servicecatalog_portfolio_status
aws_sagemaker_space
aws_sagemaker_studio_lifecycle_config
aws_sagemaker_user_profile
aws_sagemaker_workforce
aws_sagemaker_workteam
aws_scheduler_schedule
aws_scheduler_schedule_group
aws_schemas_discoverer
aws_schemas_registry
aws_schemas_registry_policy
aws_schemas_schema
aws_secretsmanager_secret
aws_secretsmanager_secret_policy
aws_secretsmanager_secret_rotation
aws_secretsmanager_secret_version
aws_security_group
aws_security_group_rule
aws_securityhub_account
aws_securityhub_action_target
aws_securityhub_finding_aggregator
aws_securityhub_insight
aws_securityhub_invite_accepter
aws_securityhub_member
aws_securityhub_organization_admin_account
aws_securityhub_organization_configuration
aws_securityhub_product_subscription
aws_securityhub_standards_control
aws_securityhub_standards_subscription
aws_securitylake_data_lake
aws_serverlessapplicationrepository_cloudformation_stack
aws_service_discovery_http_namespace
aws_service_discovery_instance
aws_service_discovery_private_dns_namespace
aws_service_discovery_public_dns_namespace
aws_service_discovery_service
aws_servicecatalog_budget_resource_association
aws_servicecatalog_constraint
aws_servicecatalog_organizations_access
aws_servicecatalog_portfolio
aws_servicecatalog_portfolio_share
aws_servicecatalog_principal_portfolio_association
aws_servicecatalog_product
aws_servicecatalog_product_portfolio_association
aws_servicecatalog_provisioned_product
aws_servicecatalog_provisioning_artifact
aws_servicecatalog_service_action
aws_servicecatalog_tag_option
aws_servicecatalog_tag_option_resource_association
aws_servicequotas_service_quota
aws_servicequotas_template
aws_servicequotas_template_association
aws_ses_active_receipt_rule_set
aws_ses_configuration_set
aws_ses_domain_dkim
aws_ses_domain_identity
aws_ses_domain_identity_verification
aws_ses_domain_mail_from
aws_ses_email_identity
aws_ses_event_destination
aws_ses_identity_notification_topic
aws_ses_identity_policy
aws_ses_receipt_filter
aws_ses_receipt_rule
aws_ses_receipt_rule_set
aws_ses_template
aws_sesv2_account_vdm_attributes
aws_sesv2_configuration_set
aws_sesv2_configuration_set_event_destination
aws_sesv2_contact_list
aws_sesv2_dedicated_ip_assignment
aws_sesv2_dedicated_ip_pool
aws_sesv2_email_identity
aws_sesv2_email_identity_feedback_attributes
aws_sesv2_email_identity_mail_from_attributes
aws_sfn_activity
aws_sfn_alias
aws_sfn_state_machine
aws_shield_application_layer_automatic_response
aws_shield_drt_access_log_bucket_association
aws_shield_drt_access_role_arn_association
aws_shield_protection
aws_shield_protection_group
aws_shield_protection_health_check_association
aws_signer_signing_job
aws_signer_signing_profile
aws_signer_signing_profile_permission
aws_simpledb_domain
aws_snapshot_create_volume_permission
aws_sns_platform_application
aws_sns_sms_preferences
aws_sns_topic
aws_sns_topic_data_protection_policy
aws_sns_topic_policy
aws_sns_topic_subscription
aws_spot_datafeed_subscription
aws_spot_fleet_request
aws_spot_instance_request
aws_sqs_queue
aws_sqs_queue_policy
aws_sqs_queue_redrive_allow_policy
aws_sqs_queue_redrive_policy
aws_ssm_activation
aws_ssm_association
aws_ssm_default_patch_baseline
aws_ssm_document
aws_ssm_maintenance_window
aws_ssm_maintenance_window_target
aws_ssm_maintenance_window_task
aws_ssm_parameter
aws_ssm_patch_baseline
aws_ssm_patch_group
aws_ssm_resource_data_sync
aws_ssm_service_setting
aws_ssmcontacts_contact
aws_ssmcontacts_contact_channel
aws_ssmcontacts_plan
aws_ssmincidents_replication_set
aws_ssmincidents_response_plan
aws_ssoadmin_account_assignment
aws_ssoadmin_application
aws_ssoadmin_application_assignment
aws_ssoadmin_application_assignment_configuration
aws_ssoadmin_customer_managed_policy_attachment
aws_ssoadmin_instance_access_control_attributes
aws_ssoadmin_managed_policy_attachment
aws_ssoadmin_permission_set
aws_ssoadmin_permission_set_inline_policy
aws_ssoadmin_permissions_boundary_attachment
aws_storagegateway_cache
aws_storagegateway_cached_iscsi_volume
aws_storagegateway_file_system_association
aws_storagegateway_gateway
aws_storagegateway_nfs_file_share
aws_storagegateway_smb_file_share
aws_storagegateway_stored_iscsi_volume
aws_storagegateway_tape_pool
aws_storagegateway_upload_buffer
aws_storagegateway_working_storage
aws_subnet
aws_swf_domain
aws_synthetics_canary
aws_synthetics_group
aws_synthetics_group_association
aws_timestreamwrite_database
aws_timestreamwrite_table
aws_transcribe_language_model
aws_transcribe_medical_vocabulary
aws_transcribe_vocabulary
aws_transcribe_vocabulary_filter
aws_transfer_access
aws_transfer_agreement
aws_transfer_certificate
aws_transfer_connector
aws_transfer_profile
aws_transfer_server
aws_transfer_ssh_key
aws_transfer_tag
aws_transfer_user
aws_transfer_workflow
aws_verifiedaccess_endpoint
aws_verifiedaccess_group
aws_verifiedaccess_instance
aws_verifiedaccess_instance_logging_configuration
aws_verifiedaccess_instance_trust_provider_attachment
aws_verifiedaccess_trust_provider
aws_volume_attachment
aws_vpc
aws_vpc_dhcp_options
aws_vpc_dhcp_options_association
aws_vpc_endpoint
aws_vpc_endpoint_connection_accepter
aws_vpc_endpoint_connection_notification
aws_vpc_endpoint_policy
aws_vpc_endpoint_route_table_association
aws_vpc_endpoint_security_group_association
aws_vpc_endpoint_service
aws_vpc_endpoint_service_allowed_principal
aws_vpc_endpoint_subnet_association
aws_vpc_ipam
aws_vpc_ipam_organization_admin_account
aws_vpc_ipam_pool
aws_vpc_ipam_pool_cidr
aws_vpc_ipam_pool_cidr_allocation
aws_vpc_ipam_preview_next_cidr
aws_vpc_ipam_resource_discovery
aws_vpc_ipam_resource_discovery_association
aws_vpc_ipam_scope
aws_vpc_ipv4_cidr_block_association
aws_vpc_ipv6_cidr_block_association
aws_vpc_network_performance_metric_subscription
aws_vpc_peering_connection
aws_vpc_peering_connection_accepter
aws_vpc_peering_connection_options
aws_vpc_security_group_egress_rule
aws_vpc_security_group_ingress_rule
aws_vpclattice_access_log_subscription
aws_vpclattice_auth_policy
aws_vpclattice_listener
aws_vpclattice_listener_rule
aws_vpclattice_resource_policy
aws_vpclattice_service
aws_vpclattice_service_network
aws_vpclattice_service_network_service_association
aws_vpclattice_service_network_vpc_association
aws_vpclattice_target_group
aws_vpclattice_target_group_attachment
aws_vpn_connection
aws_vpn_connection_route
aws_vpn_gateway
aws_vpn_gateway_attachment
aws_vpn_gateway_route_propagation
aws_waf_byte_match_set
aws_waf_geo_match_set
aws_waf_ipset
aws_waf_rate_based_rule
aws_waf_regex_match_set
aws_waf_regex_pattern_set
aws_waf_rule
aws_waf_rule_group
aws_waf_size_constraint_set
aws_waf_sql_injection_match_set
aws_waf_web_acl
aws_waf_xss_match_set
aws_wafregional_byte_match_set
aws_wafregional_geo_match_set
aws_wafregional_ipset
aws_wafregional_rate_based_rule
aws_wafregional_regex_match_set
aws_wafregional_regex_pattern_set
aws_wafregional_rule
aws_wafregional_rule_group
aws_wafregional_size_constraint_set
aws_wafregional_sql_injection_match_set
aws_wafregional_web_acl
aws_wafregional_web_acl_association
aws_wafregional_xss_match_set
aws_wafv2_ip_set
aws_wafv2_regex_pattern_set
aws_wafv2_rule_group
aws_wafv2_web_acl
aws_wafv2_web_acl_association
aws_wafv2_web_acl_logging_configuration
aws_worklink_fleet
aws_worklink_website_certificate_authority_association
aws_workspaces_connection_alias
aws_workspaces_directory
aws_workspaces_ip_group
aws_workspaces_workspace
aws_xray_encryption_config
aws_xray_group
aws_xray_sampling_rule