# -*- coding: utf-8 -*-
"""
特性开关系统 - 架构优化步骤1
实现安全的功能切换和回滚机制

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import json
import os
import threading
import time
from typing import Any, Dict, Optional, Callable, List
from dataclasses import dataclass, field
from enum import Enum
import traceback


class FeatureState(Enum):
    """特性状态枚举"""
    DISABLED = "disabled"
    ENABLED = "enabled"
    TESTING = "testing"      # 测试模式
    ROLLOUT = "rollout"      # 逐步推出


@dataclass
class FeatureFlag:
    """特性开关配置"""
    name: str
    state: FeatureState
    description: str
    rollout_percentage: float = 0.0  # 推出百分比 (0-100)
    dependencies: List[str] = field(default_factory=list)
    created_time: float = field(default_factory=time.time)
    last_modified: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


class FeatureFlags:
    """
    特性开关管理器
    
    功能：
    - 特性开关的启用/禁用
    - 配置持久化
    - 依赖关系管理
    - 回滚安全保证
    - 性能监控
    """
    
    def __init__(self, config_file: str = "feature_flags.json"):
        self.config_file = config_file
        self._flags: Dict[str, FeatureFlag] = {}
        self._lock = threading.RLock()
        self._observers: List[Callable[[str, FeatureState], None]] = []
        
        # 性能统计
        self._stats = {
            "flag_checks": 0,
            "cache_hits": 0,
            "config_reloads": 0,
            "last_reload_time": 0.0
        }
        
        # 缓存机制
        self._cache: Dict[str, bool] = {}
        self._cache_ttl = 60  # 缓存60秒
        self._cache_timestamps: Dict[str, float] = {}
        
        # 加载配置
        self._load_config()
        
        # 注册默认特性开关
        self._register_default_flags()
        
    def is_enabled(self, feature_name: str, user_id: Optional[str] = None) -> bool:
        """
        检查特性是否启用
        
        Args:
            feature_name: 特性名称
            user_id: 用户ID（用于渐进式推出）
            
        Returns:
            bool: 特性是否启用
        """
        with self._lock:
            self._stats["flag_checks"] += 1
            
            # 检查缓存
            cache_key = f"{feature_name}_{user_id or 'default'}"
            if self._is_cache_valid(cache_key):
                self._stats["cache_hits"] += 1
                return self._cache[cache_key]
                
            # 获取特性配置
            if feature_name not in self._flags:
                # 默认禁用未知特性
                result = False
            else:
                flag = self._flags[feature_name]
                result = self._evaluate_flag(flag, user_id)
                
            # 更新缓存
            self._cache[cache_key] = result
            self._cache_timestamps[cache_key] = time.time()
            
            return result
            
    def enable(self, feature_name: str, description: str = "") -> bool:
        """
        启用特性
        
        Args:
            feature_name: 特性名称
            description: 特性描述
            
        Returns:
            bool: 是否成功启用
        """
        return self._set_feature_state(feature_name, FeatureState.ENABLED, description)
        
    def disable(self, feature_name: str) -> bool:
        """
        禁用特性
        
        Args:
            feature_name: 特性名称
            
        Returns:
            bool: 是否成功禁用
        """
        return self._set_feature_state(feature_name, FeatureState.DISABLED)
        
    def set_testing(self, feature_name: str, description: str = "") -> bool:
        """
        设置特性为测试模式
        
        Args:
            feature_name: 特性名称
            description: 特性描述
            
        Returns:
            bool: 是否成功设置
        """
        return self._set_feature_state(feature_name, FeatureState.TESTING, description)
        
    def set_rollout(self, feature_name: str, percentage: float, description: str = "") -> bool:
        """
        设置特性为渐进推出模式
        
        Args:
            feature_name: 特性名称
            percentage: 推出百分比 (0-100)
            description: 特性描述
            
        Returns:
            bool: 是否成功设置
        """
        if not 0 <= percentage <= 100:
            raise ValueError("Rollout percentage must be between 0 and 100")
            
        with self._lock:
            if feature_name not in self._flags:
                self._flags[feature_name] = FeatureFlag(
                    name=feature_name,
                    state=FeatureState.ROLLOUT,
                    description=description,
                    rollout_percentage=percentage
                )
            else:
                flag = self._flags[feature_name]
                flag.state = FeatureState.ROLLOUT
                flag.rollout_percentage = percentage
                flag.last_modified = time.time()
                if description:
                    flag.description = description
                    
            self._clear_cache()
            self._save_config()
            self._notify_observers(feature_name, FeatureState.ROLLOUT)
            
        return True
        
    def get_flag_info(self, feature_name: str) -> Optional[Dict[str, Any]]:
        """获取特性开关信息"""
        with self._lock:
            if feature_name not in self._flags:
                return None
                
            flag = self._flags[feature_name]
            return {
                "name": flag.name,
                "state": flag.state.value,
                "description": flag.description,
                "rollout_percentage": flag.rollout_percentage,
                "dependencies": flag.dependencies,
                "created_time": flag.created_time,
                "last_modified": flag.last_modified,
                "metadata": flag.metadata
            }
            
    def get_all_flags(self) -> Dict[str, Dict[str, Any]]:
        """获取所有特性开关信息"""
        with self._lock:
            return {
                name: self.get_flag_info(name)
                for name in self._flags.keys()
            }
            
    def add_dependency(self, feature_name: str, dependency: str) -> bool:
        """
        添加特性依赖
        
        Args:
            feature_name: 特性名称
            dependency: 依赖的特性名称
            
        Returns:
            bool: 是否成功添加
        """
        with self._lock:
            if feature_name not in self._flags:
                return False
                
            # 检查循环依赖
            if self._has_circular_dependency(feature_name, dependency):
                raise ValueError(f"Circular dependency detected: {feature_name} -> {dependency}")
                
            flag = self._flags[feature_name]
            if dependency not in flag.dependencies:
                flag.dependencies.append(dependency)
                flag.last_modified = time.time()
                self._save_config()
                
        return True
        
    def remove_dependency(self, feature_name: str, dependency: str) -> bool:
        """移除特性依赖"""
        with self._lock:
            if feature_name not in self._flags:
                return False
                
            flag = self._flags[feature_name]
            if dependency in flag.dependencies:
                flag.dependencies.remove(dependency)
                flag.last_modified = time.time()
                self._save_config()
                return True
                
        return False
        
    def add_observer(self, observer: Callable[[str, FeatureState], None]):
        """添加状态变更观察者"""
        with self._lock:
            self._observers.append(observer)
            
    def remove_observer(self, observer: Callable[[str, FeatureState], None]):
        """移除状态变更观察者"""
        with self._lock:
            if observer in self._observers:
                self._observers.remove(observer)
                
    def reload_config(self) -> bool:
        """重新加载配置"""
        try:
            with self._lock:
                self._load_config()
                self._clear_cache()
                self._stats["config_reloads"] += 1
                self._stats["last_reload_time"] = time.time()
            return True
        except Exception as e:
            print(f"❌ Failed to reload feature flags config: {e}")
            return False
            
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            stats = self._stats.copy()
            stats["total_flags"] = len(self._flags)
            stats["enabled_flags"] = sum(
                1 for flag in self._flags.values() 
                if flag.state == FeatureState.ENABLED
            )
            stats["cache_size"] = len(self._cache)
            return stats
            
    def _set_feature_state(self, feature_name: str, state: FeatureState, description: str = "") -> bool:
        """设置特性状态"""
        with self._lock:
            if feature_name not in self._flags:
                self._flags[feature_name] = FeatureFlag(
                    name=feature_name,
                    state=state,
                    description=description
                )
            else:
                flag = self._flags[feature_name]
                flag.state = state
                flag.last_modified = time.time()
                if description:
                    flag.description = description
                    
            self._clear_cache()
            self._save_config()
            self._notify_observers(feature_name, state)
            
        return True
        
    def _evaluate_flag(self, flag: FeatureFlag, user_id: Optional[str]) -> bool:
        """评估特性开关状态"""
        # 检查依赖
        for dependency in flag.dependencies:
            if not self.is_enabled(dependency, user_id):
                return False
                
        # 根据状态评估
        if flag.state == FeatureState.DISABLED:
            return False
        elif flag.state == FeatureState.ENABLED:
            return True
        elif flag.state == FeatureState.TESTING:
            # 测试模式：只对特定用户启用
            return user_id is not None and user_id.startswith("test_")
        elif flag.state == FeatureState.ROLLOUT:
            # 渐进推出：基于用户ID哈希
            if user_id is None:
                return flag.rollout_percentage >= 100
            else:
                hash_value = hash(user_id) % 100
                return hash_value < flag.rollout_percentage
                
        return False
        
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache:
            return False
            
        cache_time = self._cache_timestamps.get(cache_key, 0)
        return time.time() - cache_time < self._cache_ttl
        
    def _clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        self._cache_timestamps.clear()
        
    def _has_circular_dependency(self, feature_name: str, new_dependency: str) -> bool:
        """检查是否存在循环依赖"""
        visited = set()
        
        def dfs(current: str) -> bool:
            if current == feature_name:
                return True
            if current in visited:
                return False
                
            visited.add(current)
            
            if current in self._flags:
                for dep in self._flags[current].dependencies:
                    if dfs(dep):
                        return True
                        
            return False
            
        return dfs(new_dependency)
        
    def _notify_observers(self, feature_name: str, state: FeatureState):
        """通知观察者状态变更"""
        for observer in self._observers:
            try:
                observer(feature_name, state)
            except Exception as e:
                print(f"❌ Feature flag observer error: {e}")
                
    def _load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            return
            
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            for flag_data in data.get("flags", []):
                flag = FeatureFlag(
                    name=flag_data["name"],
                    state=FeatureState(flag_data["state"]),
                    description=flag_data.get("description", ""),
                    rollout_percentage=flag_data.get("rollout_percentage", 0.0),
                    dependencies=flag_data.get("dependencies", []),
                    created_time=flag_data.get("created_time", time.time()),
                    last_modified=flag_data.get("last_modified", time.time()),
                    metadata=flag_data.get("metadata", {})
                )
                self._flags[flag.name] = flag
                
        except Exception as e:
            print(f"❌ Failed to load feature flags config: {e}")
            
    def _save_config(self):
        """保存配置文件"""
        try:
            data = {
                "version": "1.0",
                "last_updated": time.time(),
                "flags": [
                    {
                        "name": flag.name,
                        "state": flag.state.value,
                        "description": flag.description,
                        "rollout_percentage": flag.rollout_percentage,
                        "dependencies": flag.dependencies,
                        "created_time": flag.created_time,
                        "last_modified": flag.last_modified,
                        "metadata": flag.metadata
                    }
                    for flag in self._flags.values()
                ]
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ Failed to save feature flags config: {e}")
            
    def _register_default_flags(self):
        """注册默认特性开关"""
        default_flags = [
            ("use_service_container", "使用服务容器架构"),
            ("use_event_bus", "使用事件总线系统"),
            ("use_cached_config", "使用配置缓存"),
            ("use_async_logging", "使用异步日志"),
            ("use_plugin_system", "使用插件系统"),
            ("use_startup_cache", "使用启动状态缓存"),
            ("use_lazy_loading", "使用延迟加载"),
            ("use_performance_monitoring", "使用性能监控")
        ]
        
        for flag_name, description in default_flags:
            if flag_name not in self._flags:
                self._flags[flag_name] = FeatureFlag(
                    name=flag_name,
                    state=FeatureState.DISABLED,  # 默认禁用
                    description=description
                )


# 全局特性开关实例
_global_feature_flags: Optional[FeatureFlags] = None


def get_feature_flags() -> FeatureFlags:
    """获取全局特性开关实例"""
    global _global_feature_flags
    if _global_feature_flags is None:
        _global_feature_flags = FeatureFlags()
    return _global_feature_flags


def is_feature_enabled(feature_name: str, user_id: Optional[str] = None) -> bool:
    """检查特性是否启用（便捷函数）"""
    return get_feature_flags().is_enabled(feature_name, user_id)
