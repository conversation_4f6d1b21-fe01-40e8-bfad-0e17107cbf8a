#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Refunded和Close表状态 - 确认表是否存在以及数据导入情况
"""

import sqlite3
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.connection_pool import get_connection

def check_database_tables():
    """检查数据库中的表结构"""
    print("🔍 检查数据库表结构")
    print("=" * 60)
    
    try:
        with get_connection() as conn:
            cursor = conn.connection.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
            tables = cursor.fetchall()
            
            print("📋 数据库中的所有表:")
            main_tables = []
            refunding_tables = []
            close_tables = []
            other_tables = []
            
            for table in tables:
                table_name = table[0]
                print(f"   📊 {table_name}")
                
                if 'Refunding' in table_name:
                    refunding_tables.append(table_name)
                elif 'Close' in table_name:
                    close_tables.append(table_name)
                elif any(platform in table_name for platform in ['IOT_Sales', 'ZERO_Sales', 'APP_Sales']):
                    if table_name in ['IOT_Sales', 'ZERO_Sales', 'APP_Sales']:
                        main_tables.append(table_name)
                    else:
                        other_tables.append(table_name)
                else:
                    other_tables.append(table_name)
            
            print(f"\n📊 表分类统计:")
            print(f"   主表: {len(main_tables)} 个 - {main_tables}")
            print(f"   退款表: {len(refunding_tables)} 个 - {refunding_tables}")
            print(f"   关闭表: {len(close_tables)} 个 - {close_tables}")
            print(f"   其他表: {len(other_tables)} 个")
            
            return {
                'main_tables': main_tables,
                'refunding_tables': refunding_tables,
                'close_tables': close_tables,
                'other_tables': other_tables
            }
            
    except Exception as e:
        print(f"❌ 检查数据库表失败: {e}")
        return None

def check_table_data_counts():
    """检查各表的数据数量"""
    print("\n🔍 检查各表数据数量")
    print("=" * 60)
    
    try:
        with get_connection() as conn:
            cursor = conn.connection.cursor()
            
            # 需要检查的表
            tables_to_check = [
                'IOT_Sales', 'IOT_Sales_Refunding', 'IOT_Sales_Close',
                'ZERO_Sales', 'ZERO_Sales_Refunding', 'ZERO_Sales_Close',
                'APP_Sales', 'APP_Sales_Refunding', 'APP_Sales_Close'
            ]
            
            table_counts = {}
            
            for table_name in tables_to_check:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    table_counts[table_name] = count
                    
                    if count > 0:
                        print(f"   ✅ {table_name}: {count:,} 条记录")
                    else:
                        print(f"   ⚠️ {table_name}: 0 条记录 (表为空)")
                        
                except sqlite3.OperationalError as e:
                    if "no such table" in str(e).lower():
                        print(f"   ❌ {table_name}: 表不存在")
                        table_counts[table_name] = -1  # 表示表不存在
                    else:
                        print(f"   ❌ {table_name}: 查询失败 - {e}")
                        table_counts[table_name] = -2  # 表示查询错误
            
            return table_counts
            
    except Exception as e:
        print(f"❌ 检查表数据失败: {e}")
        return None

def check_order_status_distribution():
    """检查主表中的订单状态分布"""
    print("\n🔍 检查主表中的订单状态分布")
    print("=" * 60)
    
    try:
        with get_connection() as conn:
            cursor = conn.connection.cursor()
            
            main_tables = ['IOT_Sales', 'ZERO_Sales', 'APP_Sales']
            
            for table_name in main_tables:
                try:
                    print(f"\n📊 {table_name} 订单状态分布:")
                    
                    # 检查是否有Order_status列
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    if 'Order_status' not in columns:
                        print(f"   ⚠️ 表中没有 Order_status 列")
                        continue
                    
                    # 统计订单状态分布
                    cursor.execute(f"""
                        SELECT Order_status, COUNT(*) as count 
                        FROM {table_name} 
                        GROUP BY Order_status 
                        ORDER BY count DESC
                    """)
                    
                    status_distribution = cursor.fetchall()
                    
                    if not status_distribution:
                        print(f"   ⚠️ 表为空")
                        continue
                    
                    total_records = sum(row[1] for row in status_distribution)
                    
                    for status, count in status_distribution:
                        percentage = (count / total_records) * 100
                        status_display = status if status else "(空值)"
                        
                        # 标记可能需要迁移的状态
                        if status and any(keyword in status.lower() for keyword in ['refund', 'close', 'cancel']):
                            print(f"   🚨 {status_display}: {count:,} 条 ({percentage:.1f}%) - 可能需要迁移")
                        else:
                            print(f"   ✅ {status_display}: {count:,} 条 ({percentage:.1f}%)")
                    
                    print(f"   📊 总计: {total_records:,} 条记录")
                    
                except sqlite3.OperationalError as e:
                    if "no such table" in str(e).lower():
                        print(f"   ❌ 表 {table_name} 不存在")
                    else:
                        print(f"   ❌ 查询失败: {e}")
                        
    except Exception as e:
        print(f"❌ 检查订单状态分布失败: {e}")

def check_recent_imports():
    """检查最近的导入记录"""
    print("\n🔍 检查最近的导入记录")
    print("=" * 60)
    
    try:
        with get_connection() as conn:
            cursor = conn.connection.cursor()
            
            tables_to_check = [
                'IOT_Sales_Refunding', 'IOT_Sales_Close',
                'ZERO_Sales_Refunding', 'ZERO_Sales_Close'
            ]
            
            for table_name in tables_to_check:
                try:
                    print(f"\n📊 {table_name} 最近导入:")
                    
                    # 检查是否有时间相关的列
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    time_columns = [col for col in columns if any(keyword in col.lower() for keyword in ['time', 'date', 'created', 'updated'])]
                    
                    if not time_columns:
                        print(f"   ⚠️ 表中没有时间列，无法检查最近导入")
                        continue
                    
                    # 使用第一个时间列检查最近记录
                    time_col = time_columns[0]
                    cursor.execute(f"""
                        SELECT COUNT(*), MAX({time_col}) as latest_time
                        FROM {table_name}
                    """)
                    
                    result = cursor.fetchone()
                    count, latest_time = result
                    
                    if count > 0:
                        print(f"   ✅ 总记录数: {count:,}")
                        print(f"   📅 最新记录时间: {latest_time}")
                        
                        # 检查最近24小时的导入
                        cursor.execute(f"""
                            SELECT COUNT(*) 
                            FROM {table_name} 
                            WHERE datetime({time_col}) > datetime('now', '-1 day')
                        """)
                        recent_count = cursor.fetchone()[0]
                        print(f"   🕐 最近24小时导入: {recent_count} 条")
                    else:
                        print(f"   ⚠️ 表为空")
                        
                except sqlite3.OperationalError as e:
                    if "no such table" in str(e).lower():
                        print(f"   ❌ 表 {table_name} 不存在")
                    else:
                        print(f"   ❌ 查询失败: {e}")
                        
    except Exception as e:
        print(f"❌ 检查最近导入失败: {e}")

def generate_diagnosis_report():
    """生成诊断报告"""
    print("\n" + "=" * 60)
    print("📋 Refunded/Close表诊断报告")
    print("=" * 60)
    
    # 检查表结构
    table_info = check_database_tables()
    
    # 检查数据数量
    table_counts = check_table_data_counts()
    
    # 检查订单状态分布
    check_order_status_distribution()
    
    # 检查最近导入
    check_recent_imports()
    
    # 生成诊断结论
    print("\n" + "=" * 60)
    print("🎯 诊断结论")
    print("=" * 60)
    
    if not table_info or not table_counts:
        print("❌ 无法完成诊断，数据库连接失败")
        return
    
    # 检查Refunding表
    refunding_tables = table_info['refunding_tables']
    close_tables = table_info['close_tables']
    
    print(f"\n📊 Refunding表状态:")
    if refunding_tables:
        print(f"   ✅ 发现 {len(refunding_tables)} 个Refunding表: {refunding_tables}")
        for table in refunding_tables:
            count = table_counts.get(table, 0)
            if count > 0:
                print(f"   ✅ {table}: {count:,} 条数据")
            elif count == 0:
                print(f"   ⚠️ {table}: 表存在但为空")
            else:
                print(f"   ❌ {table}: 表不存在或查询失败")
    else:
        print(f"   ❌ 未发现Refunding表")
    
    print(f"\n📊 Close表状态:")
    if close_tables:
        print(f"   ✅ 发现 {len(close_tables)} 个Close表: {close_tables}")
        for table in close_tables:
            count = table_counts.get(table, 0)
            if count > 0:
                print(f"   ✅ {table}: {count:,} 条数据")
            elif count == 0:
                print(f"   ⚠️ {table}: 表存在但为空")
            else:
                print(f"   ❌ {table}: 表不存在或查询失败")
    else:
        print(f"   ❌ 未发现Close表")
    
    # 提供解决建议
    print(f"\n🔧 解决建议:")
    
    if not refunding_tables and not close_tables:
        print("1. 🚨 需要创建Refunding和Close表")
        print("   运行: python scripts/create_refunding_close_tables.py")
    
    empty_tables = [table for table, count in table_counts.items() if count == 0 and '_Refunding' in table or '_Close' in table]
    if empty_tables:
        print("2. 📥 需要导入Refunded/Close数据")
        print("   使用智能识别导入功能，确保选择正确的订单类型")
    
    print("3. 🔍 检查数据导入脚本的状态检测逻辑")
    print("4. 📋 验证Excel文件中的Order_status列内容")

def main():
    """主函数"""
    print("🔧 Refunded/Close表状态检查工具")
    print("=" * 60)
    
    try:
        generate_diagnosis_report()
        
        print("\n" + "=" * 60)
        print("✅ Refunded/Close表状态检查完成！")
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
