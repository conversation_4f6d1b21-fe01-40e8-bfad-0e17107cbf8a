#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库Order_time格式 - 检查数据库中实际存储的Order_time格式
"""

import sqlite3
import pandas as pd
from pathlib import Path

def check_database_order_time_format():
    """检查数据库中Order_time字段的实际格式"""
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    print("🔍 检查数据库中Order_time字段的实际格式")
    print("=" * 80)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 数据库中的表: {len(tables)} 个")
        for table in tables:
            print(f"  - {table}")
        
        # 检查主要表的Order_time格式
        main_tables = ['IOT_Sales', 'IOT_Sales_Close', 'IOT_Sales_Refunding', 'APP_Sales']
        
        for table_name in main_tables:
            if table_name in tables:
                print(f"\n{'='*20} {table_name} {'='*20}")
                
                # 检查表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                order_time_column = None
                for col in columns:
                    if col[1] == 'Order_time':
                        order_time_column = col
                        break
                
                if order_time_column:
                    print(f"📋 Order_time列信息:")
                    print(f"  - 列名: {order_time_column[1]}")
                    print(f"  - 数据类型: {order_time_column[2]}")
                    print(f"  - 是否允许NULL: {'是' if order_time_column[3] == 0 else '否'}")
                    print(f"  - 默认值: {order_time_column[4]}")
                else:
                    print("❌ 没有找到Order_time列")
                    continue
                
                # 检查记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                total_count = cursor.fetchone()[0]
                print(f"📊 总记录数: {total_count:,}")
                
                if total_count > 0:
                    # 检查Order_time的不同格式
                    print(f"\n📅 Order_time格式分析:")
                    
                    # 获取样本数据
                    cursor.execute(f"""
                        SELECT DISTINCT Order_time 
                        FROM {table_name} 
                        WHERE Order_time IS NOT NULL 
                        ORDER BY Order_time 
                        LIMIT 10
                    """)
                    sample_times = cursor.fetchall()
                    
                    print(f"  📋 样本Order_time值 (前10个不同值):")
                    for i, (time_val,) in enumerate(sample_times, 1):
                        print(f"    {i:2d}. '{time_val}' (长度: {len(str(time_val))})")
                    
                    # 分析格式模式
                    print(f"\n  🔍 格式模式分析:")
                    
                    # 检查只有日期的记录
                    cursor.execute(f"""
                        SELECT COUNT(*) 
                        FROM {table_name} 
                        WHERE Order_time IS NOT NULL 
                        AND LENGTH(Order_time) = 10 
                        AND Order_time LIKE '____-__-__'
                    """)
                    date_only_count = cursor.fetchone()[0]
                    
                    # 检查包含时间的记录
                    cursor.execute(f"""
                        SELECT COUNT(*) 
                        FROM {table_name} 
                        WHERE Order_time IS NOT NULL 
                        AND LENGTH(Order_time) > 10 
                        AND Order_time LIKE '____-__-__ __:__:__'
                    """)
                    datetime_count = cursor.fetchone()[0]
                    
                    # 检查其他格式
                    other_count = total_count - date_only_count - datetime_count
                    
                    print(f"    📊 只有日期 (YYYY-MM-DD): {date_only_count:,} 条 ({date_only_count/total_count*100:.1f}%)")
                    print(f"    📊 包含时间 (YYYY-MM-DD HH:MM:SS): {datetime_count:,} 条 ({datetime_count/total_count*100:.1f}%)")
                    print(f"    📊 其他格式: {other_count:,} 条 ({other_count/total_count*100:.1f}%)")
                    
                    # 检查最新插入的记录
                    print(f"\n  📋 最新插入的5条记录:")
                    cursor.execute(f"""
                        SELECT Order_time, Order_No, rowid 
                        FROM {table_name} 
                        ORDER BY rowid DESC 
                        LIMIT 5
                    """)
                    latest_records = cursor.fetchall()
                    
                    for i, (order_time, order_no, rowid) in enumerate(latest_records, 1):
                        print(f"    {i}. '{order_time}' | {order_no} (rowid: {rowid})")
                    
                    # 检查今天的记录
                    print(f"\n  📋 今天的记录格式:")
                    cursor.execute(f"""
                        SELECT Order_time, COUNT(*) as count
                        FROM {table_name} 
                        WHERE Order_time LIKE '2025-07-%'
                        GROUP BY Order_time
                        ORDER BY Order_time DESC
                        LIMIT 5
                    """)
                    today_records = cursor.fetchall()
                    
                    for order_time, count in today_records:
                        print(f"    '{order_time}': {count} 条记录")
                
                else:
                    print("⚠️ 表中没有数据")
        
        conn.close()
        
        print(f"\n{'='*80}")
        print("🎯 检查完成")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

def check_recent_imports():
    """检查最近导入的数据格式"""
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    print(f"\n🔍 检查最近导入的数据格式")
    print("-" * 60)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查最近导入的数据（按rowid排序）
        tables_to_check = ['IOT_Sales', 'IOT_Sales_Close', 'IOT_Sales_Refunding']
        
        for table_name in tables_to_check:
            try:
                print(f"\n📋 {table_name} 最近导入的数据:")
                
                cursor.execute(f"""
                    SELECT Order_time, Order_No, Equipment_ID, rowid
                    FROM {table_name} 
                    ORDER BY rowid DESC 
                    LIMIT 10
                """)
                recent_data = cursor.fetchall()
                
                if recent_data:
                    for i, (order_time, order_no, equipment_id, rowid) in enumerate(recent_data, 1):
                        print(f"  {i:2d}. '{order_time}' | {order_no} | {equipment_id} (rowid: {rowid})")
                else:
                    print("  ⚠️ 没有数据")
                    
            except Exception as e:
                print(f"  ❌ 检查 {table_name} 失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查最近导入数据失败: {e}")

def main():
    """主函数"""
    check_database_order_time_format()
    check_recent_imports()
    
    print(f"\n💡 分析结论:")
    print("1. 如果看到混合格式（有些是日期，有些是日期时间），说明存在格式不一致问题")
    print("2. 如果最新导入的数据格式与历史数据不同，说明需要统一格式")
    print("3. 重复检测失败可能是因为新旧数据格式不匹配")

if __name__ == "__main__":
    main()
    input("按回车键退出...")
