#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理临时文件和备份文件
"""

import os
import shutil
import glob
from datetime import datetime, timedelta

def cleanup_temp_files():
    """清理临时文件"""
    
    print("🧹 清理临时文件...")
    
    temp_dirs = [
        "temp_data",
        "temp_refund_data", 
        "__pycache__",
        "详细日志"
    ]
    
    cleaned_count = 0
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            print(f"\n📁 处理目录: {temp_dir}")
            
            if temp_dir == "__pycache__":
                # 完全删除 __pycache__ 目录
                try:
                    shutil.rmtree(temp_dir)
                    print(f"  🗑️ 删除整个目录: {temp_dir}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"  ❌ 删除失败: {e}")
            
            elif temp_dir == "详细日志":
                # 保留最近7天的日志，删除旧的
                try:
                    cutoff_date = datetime.now() - timedelta(days=7)
                    for file in os.listdir(temp_dir):
                        file_path = os.path.join(temp_dir, file)
                        if os.path.isfile(file_path):
                            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                            if file_time < cutoff_date:
                                os.remove(file_path)
                                print(f"  🗑️ 删除旧日志: {file}")
                                cleaned_count += 1
                            else:
                                print(f"  ✅ 保留日志: {file}")
                except Exception as e:
                    print(f"  ❌ 处理失败: {e}")
            
            else:
                # 对于其他临时目录，清空内容但保留目录
                try:
                    for item in os.listdir(temp_dir):
                        item_path = os.path.join(temp_dir, item)
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                            print(f"  🗑️ 删除文件: {item}")
                            cleaned_count += 1
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                            print(f"  🗑️ 删除目录: {item}")
                            cleaned_count += 1
                except Exception as e:
                    print(f"  ❌ 清理失败: {e}")
        else:
            print(f"⚠️ 目录不存在: {temp_dir}")
    
    return cleaned_count

def cleanup_backup_files():
    """清理散落的备份文件"""
    
    print("\n🧹 清理散落的备份文件...")
    
    # 备份文件模式
    backup_patterns = [
        "*.db-shm",
        "*.db-wal", 
        "sqlite_backup_*.db",
        "postgresql_backup_*.sql",
        "*.backup_*",
        "*_backup.py",
        "*.bak"
    ]
    
    # 创建备份整理目录
    backup_archive = "backup_versions/old_backups"
    os.makedirs(backup_archive, exist_ok=True)
    
    moved_count = 0
    
    for pattern in backup_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                dest_path = os.path.join(backup_archive, os.path.basename(file))
                
                # 如果目标文件已存在，添加时间戳
                if os.path.exists(dest_path):
                    name, ext = os.path.splitext(os.path.basename(file))
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    dest_path = os.path.join(backup_archive, f"{name}_{timestamp}{ext}")
                
                shutil.move(file, dest_path)
                print(f"  📦 移动备份文件: {file} -> {dest_path}")
                moved_count += 1
                
            except Exception as e:
                print(f"  ❌ 移动失败: {file} - {e}")
    
    return moved_count

def cleanup_log_files():
    """清理过期日志文件"""
    
    print("\n🧹 清理过期日志文件...")
    
    if not os.path.exists("logs"):
        print("⚠️ logs目录不存在")
        return 0
    
    # 保留最近30天的日志
    cutoff_date = datetime.now() - timedelta(days=30)
    cleaned_count = 0
    
    for file in os.listdir("logs"):
        file_path = os.path.join("logs", file)
        if os.path.isfile(file_path):
            try:
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                if file_time < cutoff_date:
                    os.remove(file_path)
                    print(f"  🗑️ 删除过期日志: {file}")
                    cleaned_count += 1
                else:
                    print(f"  ✅ 保留日志: {file}")
            except Exception as e:
                print(f"  ❌ 处理失败: {file} - {e}")
    
    return cleaned_count

def main():
    """主函数"""
    
    print("🚀 开始文件清理和整理...")
    
    temp_cleaned = cleanup_temp_files()
    backup_moved = cleanup_backup_files() 
    logs_cleaned = cleanup_log_files()
    
    print(f"\n📊 清理完成！")
    print(f"  🗑️ 清理临时文件: {temp_cleaned} 个")
    print(f"  📦 整理备份文件: {backup_moved} 个")
    print(f"  🗑️ 清理过期日志: {logs_cleaned} 个")
    print(f"  📁 总计处理: {temp_cleaned + backup_moved + logs_cleaned} 个文件")

if __name__ == "__main__":
    main()
