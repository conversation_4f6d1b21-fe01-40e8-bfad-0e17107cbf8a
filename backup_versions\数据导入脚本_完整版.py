# 数据导入脚本_完整版.py
# 基于原始数据导入脚本，确保完整支持Transaction_Num字段

import os
import re
import sqlite3
import shutil
import datetime
import pandas as pd
from dateutil import parser as dateparser
from datetime import timedelta
import tkinter as tk
from tkinter import messagebox
import traceback
from tqdm import tqdm

# —— 配置 ——
# 默认配置，可通过函数参数覆盖
# 动态获取项目根目录
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = script_dir  # 脚本在项目根目录
DEFAULT_DB_PATH = os.path.join(project_root, "database", "sales_reports.db")
PLATFORMS = {
    "IOT": os.path.join(project_root, "IOT"),
    "ZERO": os.path.join(project_root, "ZERO"),
    "APP": os.path.join(project_root, "APP")
}
LOG_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs", "import_error.log")
os.makedirs(os.path.dirname(LOG_PATH), exist_ok=True)

def log_error(msg):
    with open(LOG_PATH, "a", encoding="utf-8") as f:
        f.write(f"{datetime.datetime.now()} {msg}\n")

def standardize_date(date_str):
    if pd.isna(date_str) or not str(date_str).strip():
        return None
    s = str(date_str).strip()
    m = re.fullmatch(r'(\d{4})[-/.]?(\d{1,2})[-/.]?(\d{1,2})[ T](\d{1,2}):(\d{1,2}):(\d{1,2})', s)
    if m:
        y, mo, d, h, mi, sec = m.groups()
        return f"{int(y):04d}-{int(mo):02d}-{int(d):02d}"
    if s.isdigit() and len(s) >= 5:
        try:
            serial = int(s)
            if serial >= 60:
                serial -= 1
            base = datetime.datetime(1899, 12, 30)
            dt = base + timedelta(days=serial)
            if 1950 <= dt.year <= 2100:
                return dt.strftime("%Y-%m-%d")
        except Exception:
            pass
    try:
        dt = dateparser.parse(s, dayfirst=False, yearfirst=True)
        return dt.strftime("%Y-%m-%d")
    except (ValueError, OverflowError):
        return s

def check_date_exists(platform, import_date, db_path=None):
    db_path = db_path or DEFAULT_DB_PATH
    table = f"{platform}_Sales"
    try:
        with sqlite3.connect(db_path) as conn:
            cur = conn.cursor()
            cur.execute(f"SELECT COUNT(*) FROM {table} WHERE Import_Date=?", (import_date,))
            cnt = cur.fetchone()[0]
        return cnt > 0
    except Exception as e:
        log_error(f"检查日期存在性时出错: {e}")
        return False

def delete_existing_data(platform, import_date, db_path=None):
    db_path = db_path or DEFAULT_DB_PATH
    table = f"{platform}_Sales"
    try:
        with sqlite3.connect(db_path) as conn:
            cur = conn.cursor()
            cur.execute(f"DELETE FROM {table} WHERE Import_Date=?", (import_date,))
            return cur.rowcount
    except Exception as e:
        log_error(f"删除数据时出错: {e}")
        return 0

def create_tables(db_path=None):
    db_path = db_path or DEFAULT_DB_PATH
    try:
        with sqlite3.connect(db_path) as conn:
            cur = conn.cursor()
            
            # 创建IOT_Sales表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS IOT_Sales (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Copartner_name TEXT,
                Order_No TEXT,
                Order_types TEXT,
                Order_status TEXT,
                Order_price TEXT,
                Payment TEXT,
                Order_time TEXT,
                Equipment_ID TEXT,
                Equipment_name TEXT,
                Branch_name TEXT,
                Payment_date TEXT,
                User_name TEXT,
                Time TEXT,
                Matched_Order_ID TEXT,
                OrderTime_dt TEXT,
                Transaction_Num TEXT,
                Import_Date TEXT,
                Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 创建ZERO_Sales表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS ZERO_Sales (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Copartner_name TEXT,
                Order_No TEXT,
                Order_types TEXT,
                Order_status TEXT,
                Order_price TEXT,
                Payment TEXT,
                Order_time TEXT,
                Equipment_ID TEXT,
                Equipment_name TEXT,
                Branch_name TEXT,
                Payment_date TEXT,
                User_name TEXT,
                Time TEXT,
                Matched_Order_ID TEXT,
                OrderTime_dt TEXT,
                Transaction_Num TEXT,
                Import_Date TEXT,
                Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 创建APP_Sales表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS APP_Sales (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Copartner_name TEXT,
                Order_No TEXT,
                Order_types TEXT,
                Order_status TEXT,
                Order_price TEXT,
                Payment TEXT,
                Order_time TEXT,
                Equipment_ID TEXT,
                Equipment_name TEXT,
                Branch_name TEXT,
                Payment_date TEXT,
                User_name TEXT,
                Time TEXT,
                Matched_Order_ID TEXT,
                OrderTime_dt TEXT,
                Transaction_Num TEXT,
                Import_Date TEXT,
                Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 创建导入日志表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS Import_Logs (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Platform TEXT,
                Filename TEXT,
                Sheet_Name TEXT,
                Import_Date TEXT,
                Status TEXT,
                Message TEXT,
                Log_Time TEXT
            )
            """)
            
            # 创建设备ID异常表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS Equipment_ID_Anomalies (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Equipment_ID TEXT,
                Platform TEXT,
                File_Source TEXT,
                Detection_Date TEXT,
                Notes TEXT
            )
            """)
            
            # 创建椅子序列号表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS Chair_Serial_No (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Serial_No TEXT UNIQUE,
                Equipment_ID TEXT,
                Platform TEXT,
                Registration_Date TEXT,
                Last_Update TEXT
            )
            """)
            
            conn.commit()
            log_error("数据库表结构已创建或已存在")
    except Exception as e:
        log_error(f"初始化数据库表结构时出错: {e}")


def import_excel(file_path, platform, db_path=None):
    db_path = db_path or DEFAULT_DB_PATH
    filename = os.path.basename(file_path)
    try:
        sheets = pd.ExcelFile(file_path).sheet_names
    except Exception as e:
        log_error(f"{filename} 读取sheet失败: {e}")
        return False, f"{filename} 读取sheet失败: {e}"

    valid = [s for s in sheets if s.startswith(platform)]
    if not valid:
        return False, f"未找到 {platform} 前缀的 sheet"

    column_map = {
        'Copartner name': 'Copartner_name',
        'Order No.': 'Order_No',
        'Order types': 'Order_types',
        'Order status': 'Order_status',
        'Order price': 'Order_price',
        'Payment': 'Payment',
        'Order time': 'Order_time',
        'Equipment ID': 'Equipment_ID',
        'Equipment name': 'Equipment_name',
        'Branch name': 'Branch_name',
        'Payment date': 'Payment_date',
        'User name': 'User_name',
        'Time': 'Time',
        'Matched Order ID': 'Matched_Order_ID',
        'OrderTime_dt': 'OrderTime_dt',
        'Transaction_Num': 'Transaction_Num'  # 添加Transaction_Num字段映射
    }
    required_cols = ['Order_price', 'Order_time', 'Equipment_ID', 'Equipment_name', 'Branch_name']


    with sqlite3.connect(db_path) as conn:
        for sheet in valid:
            try:
                df = pd.read_excel(file_path, sheet_name=sheet, engine='openpyxl')
                df = df.dropna(how='all')
                df = df.rename(columns=column_map)
                for col in required_cols:
                    if col not in df.columns:
                        df[col] = None
                df['Order_time'] = df['Order_time'].apply(standardize_date)
                if 'Payment_date' in df.columns:
                    df['Payment_date'] = df['Payment_date'].apply(standardize_date)
                else:
                    df['Payment_date'] = df['Order_time']
                for col in ['Order_price', 'Equipment_ID']:
                    if col in df.columns:
                        df[col] = df[col].astype(str)
                
                # 检查是否有Api order类型的订单，如果有，将其分离出来导入到APP_Sales表
                app_df = None
                if 'Order_types' in df.columns:
                    # 提取Api order类型的订单到APP表
                    app_df = df[df['Order_types'] == 'Api order'].copy()
                    if not app_df.empty:
                        safe_print(f"文件 {filename} 中发现 {len(app_df)} 条Api order类型订单，将导入到APP_Sales表")
                        log_error(f"文件 {filename} 中发现 {len(app_df)} 条Api order类型订单，将导入到APP_Sales表")
                    
                    # 从原始数据中移除Api order类型的订单
                    df = df[df['Order_types'] != 'Api order']
                
                # 处理平台数据
                total_rows = len(df)
                # 1. 先查出数据库已有的关键字段
                db_df = pd.read_sql(f"SELECT Order_time, Payment_date, Equipment_ID, Order_price FROM {platform}_Sales", conn)
                # 2. 合并查找重复
                merged = df.merge(db_df, on=["Order_time", "Payment_date", "Equipment_ID"], how="left", suffixes=("", "_db"), indicator=True)
                # 3. 分类
                fully_dup = merged[(merged["_merge"] == "both") & (merged["Order_price"] == merged["Order_price_db"])]
                partial_diff = merged[(merged["_merge"] == "both") & (merged["Order_price"] != merged["Order_price_db"])]
                new_rows = merged[merged["_merge"] == "left_only"]
                repeat_count = len(fully_dup)
                safe_print(f"文件 {filename} 总数据: {total_rows}，完全重复的: {repeat_count}，部分字段不同的: {len(partial_diff)}，新增: {len(new_rows)}")
                log_error(f"文件 {filename} 总数据: {total_rows}，完全重复的: {repeat_count}，部分字段不同的: {len(partial_diff)}，新增: {len(new_rows)}")
                unique_dates = df['Order_time'].dropna().unique()
                safe_print(f"文件 {filename} 涉及日期: {unique_dates}")
                log_error(f"文件 {filename} 涉及日期: {unique_dates}")
                
                # 4. 只导入新增数据，部分不同和完全重复的可后续弹窗或人工处理
                if not new_rows.empty:
                    rows_before = conn.execute(f"SELECT COUNT(*) FROM {platform}_Sales").fetchone()[0]
                    try:
                        # 只插入新增数据
                        insert_cols = df.columns.tolist()
                        new_rows[insert_cols].to_sql(f"{platform}_Sales", conn, if_exists='append', index=False)
                    except Exception as e:
                        log_error(f"{filename} 的 sheet {sheet} 写入数据库失败: {e}")
                        return False, f"{filename} 的 sheet {sheet} 写入数据库失败: {e}"
                    rows_after = conn.execute(f"SELECT COUNT(*) FROM {platform}_Sales").fetchone()[0]
                    safe_print(f"实际写入数据库 {rows_after - rows_before} 条")
                    log_error(f"实际写入数据库 {rows_after - rows_before} 条")
                else:
                    safe_print(f"无新增数据写入数据库。")
                    log_error(f"无新增数据写入数据库。")
                
                # 处理APP数据
                if app_df is not None and not app_df.empty:
                    # 查询APP_Sales表中已有的数据
                    app_db_df = pd.read_sql("SELECT Order_time, Payment_date, Equipment_ID, Order_price FROM APP_Sales", conn)
                    
                    # 合并查找重复
                    app_merged = app_df.merge(app_db_df, on=["Order_time", "Payment_date", "Equipment_ID"], how="left", suffixes=("", "_db"), indicator=True)
                    
                    # 分类
                    app_fully_dup = app_merged[(app_merged["_merge"] == "both") & (app_merged["Order_price"] == app_merged["Order_price_db"])]
                    app_partial_diff = app_merged[(app_merged["_merge"] == "both") & (app_merged["Order_price"] != app_merged["Order_price_db"])]
                    app_new_rows = app_merged[app_merged["_merge"] == "left_only"]
                    
                    safe_print(f"APP数据: 总数据: {len(app_df)}，完全重复的: {len(app_fully_dup)}，部分字段不同的: {len(app_partial_diff)}，新增: {len(app_new_rows)}")
                    log_error(f"APP数据: 总数据: {len(app_df)}，完全重复的: {len(app_fully_dup)}，部分字段不同的: {len(app_partial_diff)}，新增: {len(app_new_rows)}")
                    
                    # 只导入新增数据
                    if not app_new_rows.empty:
                        app_rows_before = conn.execute("SELECT COUNT(*) FROM APP_Sales").fetchone()[0]
                        try:
                            # 只插入新增数据
                            app_insert_cols = app_df.columns.tolist()
                            app_new_rows[app_insert_cols].to_sql("APP_Sales", conn, if_exists='append', index=False)
                            
                            app_rows_after = conn.execute("SELECT COUNT(*) FROM APP_Sales").fetchone()[0]
                            safe_print(f"实际写入APP_Sales表 {app_rows_after - app_rows_before} 条")
                            log_error(f"实际写入APP_Sales表 {app_rows_after - app_rows_before} 条")
                        except Exception as e:
                            log_error(f"APP数据写入数据库失败: {e}")
                            # 不返回错误，继续处理主平台数据
                    else:
                        safe_print(f"无新增APP数据写入数据库。")
                        log_error(f"无新增APP数据写入数据库。")
                # 可选：将 partial_diff 导出为 Excel 或弹窗提示人工处理
            except Exception as e:
                tb = traceback.format_exc()
                log_error(f"{filename} 的 sheet {sheet} 导入失败: {e}\n{tb}")
                return False, f"{filename} 的 sheet {sheet} 导入失败，错误信息：{e}"
    return True, "完成"



def process_folder(path, platform, db_path=None):
    db_path = db_path or DEFAULT_DB_PATH
    os.makedirs(os.path.join(path, "已处理"), exist_ok=True)
    os.makedirs(os.path.join(path, "需人工检查"), exist_ok=True)
    files = [fname for fname in os.listdir(path) if fname.lower().endswith(('.xls','.xlsx'))]
    total = len(files)
    if total == 0:
        safe_print(f"{platform} 文件夹无待处理文件。")
        return
    safe_print(f"开始导入 {platform} 平台数据，共 {total} 个文件。")
    for fname in tqdm(files, desc=f"{platform} 进度", ncols=80):
        fp = os.path.join(path, fname)
        try:
            ok, msg = import_excel(fp, platform, db_path)
        except Exception as e:
            tb = traceback.format_exc()
            log_error(f"文件 {fname} 导入主流程异常: {e}\n{tb}")
            ok = False
            msg = f"主流程异常: {e}"
        target = "已处理" if ok else "需人工检查"
        try:
            shutil.move(fp, os.path.join(path, target, fname))
        except Exception as e:
            log_error(f"移动文件 {fname} 到 {target} 失败: {e}")
        if ok:
            log_error(f"文件 {fname} 导入成功，已移动到 {target}。")
        else:
            log_error(f"文件 {fname} 导入失败，已移动到 {target}。错误信息：{msg}")

def main(db_path=None, file=None, platform=None):
    db_path = db_path or DEFAULT_DB_PATH
    try:
        create_tables(db_path)
        
        # 如果指定了文件和平台，则只处理该文件
        if file and platform:
            safe_print(f"开始处理单个文件: {file}, 平台: {platform}")
            success, message = import_excel(file, platform, db_path)
            if success:
                safe_print(f"文件 {os.path.basename(file)} 导入成功")
                return 0
            else:
                safe_print(f"文件 {os.path.basename(file)} 导入失败: {message}")
                return 1
        else:
            # 批量处理文件夹
            platforms_to_process = {k: v for k, v in PLATFORMS.items() if k != "APP"}
            for plat, folder in platforms_to_process.items():
                process_folder(folder, plat, db_path)
            safe_print("全部平台数据导入处理完成。")
            safe_print("注意：APP数据已从IOT和ZERO文件中的'Api order'类型订单中提取并导入。")
            return 0
    except Exception as e:
        tb = traceback.format_exc()
        error_msg = f"主流程异常: {e}\n{tb}"
        safe_print(error_msg)
        log_error(error_msg)
        return 1

# 设置标准输出编码为UTF-8，解决Windows终端中文显示问题
def safe_print(text):
    """安全打印函数，处理编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 如果遇到编码错误，尝试使用ASCII编码，忽略无法编码的字符
        print(text.encode('ascii', 'ignore').decode('ascii'))

if __name__ == "__main__":
    import argparse
    import sys
    import io
    
    # 尝试设置标准输出编码为UTF-8
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        # Python 3.7及以下版本没有reconfigure方法
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='导入销售数据到数据库')
    parser.add_argument('--file', help='要导入的Excel文件路径')
    parser.add_argument('--platform', help='平台类型 (IOT 或 ZERO)')
    parser.add_argument('--db_path', help='数据库路径')
    
    args = parser.parse_args()
    
    # 调用主函数
    exit_code = main(db_path=args.db_path, file=args.file, platform=args.platform)
    sys.exit(exit_code)