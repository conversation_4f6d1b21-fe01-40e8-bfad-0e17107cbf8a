# -*- coding: utf-8 -*-
"""
智能数据库备份和恢复管理器
提供自动备份、错误恢复、数据安全保护功能
"""

import os
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any
import tkinter as tk
from tkinter import messagebox

from utils.exceptions import DatabaseError
from utils.logger import get_logger


class SmartBackupManager:
    """智能数据库备份和恢复管理器"""
    
    def __init__(self, db_path: str, backup_dir: Optional[str] = None):
        """
        初始化备份管理器
        
        Args:
            db_path: 数据库文件路径
            backup_dir: 备份目录，如果为None则使用默认目录
        """
        self.db_path = Path(db_path)
        self.backup_dir = Path(backup_dir) if backup_dir else self.db_path.parent / "backups"
        self.logger = get_logger('smart_backup_manager')
        
        # 确保备份目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 最大备份文件数量
        self.max_backups = 20
        
        self.logger.info(f"Smart backup manager initialized - DB: {self.db_path}, Backup dir: {self.backup_dir}")
    
    def create_backup(self, operation_name: str = "手动备份") -> Optional[str]:
        """
        创建数据库备份
        
        Args:
            operation_name: 操作名称，用于备份文件命名
            
        Returns:
            备份文件路径，失败时返回None
        """
        try:
            if not self.db_path.exists():
                self.logger.warning(f"数据库文件不存在，无法备份: {self.db_path}")
                return None
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_operation_name = "".join(c for c in operation_name if c.isalnum() or c in (' ', '-', '_')).strip()
            safe_operation_name = safe_operation_name.replace(' ', '_')
            backup_filename = f"backup_{safe_operation_name}_{timestamp}.db"
            backup_path = self.backup_dir / backup_filename
            
            # 创建备份
            shutil.copy2(self.db_path, backup_path)
            
            # 验证备份文件
            if self._verify_backup(backup_path):
                self.logger.info(f"数据库备份成功: {backup_path}")
                
                # 清理旧备份
                self._cleanup_old_backups()
                
                return str(backup_path)
            else:
                # 备份验证失败，删除无效备份
                backup_path.unlink(missing_ok=True)
                raise DatabaseError(f"备份文件验证失败: {backup_path}")
                
        except Exception as e:
            self.logger.error(f"创建数据库备份失败: {e}")
            raise DatabaseError(f"创建数据库备份失败: {e}")
    
    def _verify_backup(self, backup_path: Path) -> bool:
        """验证备份文件的完整性"""
        try:
            with sqlite3.connect(str(backup_path)) as conn:
                cursor = conn.cursor()
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                return result and result[0] == 'ok'
        except Exception:
            return False
    
    def _cleanup_old_backups(self):
        """清理旧的备份文件"""
        try:
            backup_files = list(self.backup_dir.glob("backup_*.db"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            if len(backup_files) > self.max_backups:
                files_to_delete = backup_files[self.max_backups:]
                for file_path in files_to_delete:
                    try:
                        file_path.unlink()
                        self.logger.debug(f"已删除旧备份: {file_path}")
                    except Exception as e:
                        self.logger.warning(f"删除旧备份失败: {file_path}, 错误: {e}")
        except Exception as e:
            self.logger.warning(f"清理旧备份时出错: {e}")
    
    def get_latest_backup(self) -> Optional[str]:
        """获取最新的备份文件"""
        try:
            backup_files = list(self.backup_dir.glob("backup_*.db"))
            if not backup_files:
                return None
            
            # 按修改时间排序，获取最新的
            latest_backup = max(backup_files, key=lambda x: x.stat().st_mtime)
            
            if self._verify_backup(latest_backup):
                return str(latest_backup)
            else:
                self.logger.warning(f"最新备份文件无效: {latest_backup}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取最新备份失败: {e}")
            return None
    
    def restore_from_backup(self, backup_path: str) -> bool:
        """从备份恢复数据库"""
        try:
            backup_file = Path(backup_path)
            
            if not backup_file.exists():
                raise DatabaseError(f"备份文件不存在: {backup_path}")
            
            if not self._verify_backup(backup_file):
                raise DatabaseError(f"备份文件无效或损坏: {backup_path}")
            
            # 创建当前数据库的备份
            current_backup = None
            if self.db_path.exists():
                current_backup = self.create_backup("恢复前备份")
            
            # 恢复数据库
            shutil.copy2(backup_file, self.db_path)
            
            if self._verify_backup(self.db_path):
                self.logger.info(f"数据库恢复成功: {backup_path}")
                return True
            else:
                # 恢复失败，尝试回滚
                if current_backup and Path(current_backup).exists():
                    shutil.copy2(current_backup, self.db_path)
                    self.logger.error(f"数据库恢复失败，已回滚到原始状态")
                raise DatabaseError(f"恢复的数据库验证失败")
                
        except Exception as e:
            self.logger.error(f"数据库恢复失败: {e}")
            raise DatabaseError(f"数据库恢复失败: {e}")

    def restore_backup(self, backup_filename: str) -> bool:
        """从备份恢复数据库 - 兼容性方法"""
        try:
            # 如果传入的是文件名，构建完整路径
            if not os.path.isabs(backup_filename):
                backup_path = os.path.join(self.backup_dir, backup_filename)
            else:
                backup_path = backup_filename

            return self.restore_from_backup(backup_path)

        except Exception as e:
            self.logger.error(f"数据库恢复失败: {e}")
            return False

    def safe_operation_wrapper(self, operation_name: str, operation_func, *args, **kwargs):
        """
        安全操作包装器 - 自动备份和错误恢复
        
        Args:
            operation_name: 操作名称
            operation_func: 要执行的操作函数
            *args, **kwargs: 操作函数的参数
            
        Returns:
            操作函数的返回值
        """
        backup_path = None
        try:
            # 操作前自动备份
            backup_path = self.create_backup(f"操作前_{operation_name}")
            self.logger.info(f"🔄 开始执行操作: {operation_name}")
            
            # 执行操作
            result = operation_func(*args, **kwargs)
            
            self.logger.info(f"✅ 操作成功完成: {operation_name}")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 操作失败: {operation_name}, 错误: {e}")
            
            # 显示错误和恢复选项
            self._handle_operation_error(operation_name, e, backup_path)
            
            # 重新抛出异常
            raise
    
    def _handle_operation_error(self, operation_name: str, error: Exception, 
                              backup_path: Optional[str] = None):
        """处理操作错误，提供恢复选项"""
        try:
            if backup_path and Path(backup_path).exists():
                # 显示错误和恢复选项
                result = messagebox.askyesno(
                    "❌ 数据库操作失败",
                    f"操作失败: {operation_name}\n"
                    f"错误: {str(error)}\n\n"
                    f"🔄 是否立即恢复到操作前的备份？\n"
                    f"📁 备份文件: {Path(backup_path).name}\n\n"
                    f"⚠️ 选择'是'将恢复数据库到操作前状态\n"
                    f"选择'否'将保持当前状态",
                    icon='warning'
                )
                
                if result:
                    try:
                        self.restore_from_backup(backup_path)
                        messagebox.showinfo("✅ 恢复成功", "数据库已恢复到操作前状态")
                    except Exception as restore_error:
                        messagebox.showerror("❌ 恢复失败", f"数据库恢复失败: {restore_error}")
            else:
                messagebox.showerror(
                    "❌ 操作失败",
                    f"操作失败: {operation_name}\n"
                    f"错误: {str(error)}\n\n"
                    f"⚠️ 没有可用的备份文件进行恢复"
                )
                
        except Exception as e:
            self.logger.error(f"处理操作错误时出错: {e}")


# 全局智能备份管理器实例
_smart_backup_manager: Optional[SmartBackupManager] = None


def get_smart_backup_manager(db_path: str = None) -> SmartBackupManager:
    """获取智能备份管理器实例"""
    global _smart_backup_manager
    
    if _smart_backup_manager is None:
        if db_path is None:
            db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
        _smart_backup_manager = SmartBackupManager(db_path)
    
    return _smart_backup_manager
