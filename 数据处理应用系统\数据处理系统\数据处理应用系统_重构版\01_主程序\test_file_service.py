# -*- coding: utf-8 -*-
"""
文件服务测试脚本 - 架构优化步骤4验证
测试异步文件服务、验证缓存和文件监控功能

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import sys
import os
import time
import tempfile
import threading
from typing import Any, Dict, List

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def create_test_files():
    """创建测试文件"""
    test_files = []
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    # 创建不同类型的测试文件
    files_to_create = [
        ("test_data.xlsx", "Excel文件内容"),
        ("test_data.csv", "name,age,city\nJohn,25,NYC\nJane,30,LA"),
        ("test_config.ini", "[section]\nkey=value"),
        ("test_script.py", "print('Hello World')"),
        ("test_data.txt", "这是一个测试文本文件"),
        ("test_backup.bak", "备份文件内容")
    ]
    
    for filename, content in files_to_create:
        file_path = os.path.join(temp_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        test_files.append(file_path)
        
    return temp_dir, test_files


def test_file_service_basic():
    """测试文件服务基本功能"""
    print("\n🧪 测试文件服务基本功能...")
    
    try:
        from infrastructure.file_service import FileServiceFactory, FileStatus, FileType
        
        # 创建测试文件
        temp_dir, test_files = create_test_files()
        
        try:
            # 创建文件服务
            file_service = FileServiceFactory.create_file_service()
            
            # 测试1：文件验证
            is_valid, message = file_service.validate_files_cached(test_files)
            assert is_valid, f"文件验证应该成功: {message}"
            
            # 测试2：获取文件信息
            for file_path in test_files:
                file_info = file_service.get_file_info(file_path)
                assert file_info.exists, f"文件应该存在: {file_path}"
                assert file_info.status == FileStatus.VALID, f"文件应该有效: {file_path}"
                assert file_info.size > 0, f"文件大小应该大于0: {file_path}"
                
            # 测试3：文件类型识别
            excel_file = [f for f in test_files if f.endswith('.xlsx')][0]
            excel_info = file_service.get_file_info(excel_file)
            assert excel_info.file_type == FileType.EXCEL, "应该识别为Excel文件"
            
            csv_file = [f for f in test_files if f.endswith('.csv')][0]
            csv_info = file_service.get_file_info(csv_file)
            assert csv_info.file_type == FileType.CSV, "应该识别为CSV文件"
            
            # 测试4：不存在的文件
            non_existent = os.path.join(temp_dir, "non_existent.txt")
            non_info = file_service.get_file_info(non_existent)
            assert not non_info.exists, "不存在的文件应该标记为不存在"
            assert non_info.status == FileStatus.NOT_EXISTS, "状态应该为NOT_EXISTS"
            
            # 关闭服务
            file_service.shutdown()
            
            print("✅ 文件服务基本功能测试通过")
            return True
            
        finally:
            # 清理测试文件
            import shutil
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"❌ 文件服务基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_file_validation_cache():
    """测试文件验证缓存"""
    print("\n🧪 测试文件验证缓存...")
    
    try:
        from infrastructure.file_service import FileServiceFactory
        
        # 创建测试文件
        temp_dir, test_files = create_test_files()
        
        try:
            # 创建文件服务
            file_service = FileServiceFactory.create_file_service(cache_ttl=60)
            
            # 第一次验证（缓存未命中）
            start_time = time.perf_counter()
            is_valid1, message1 = file_service.validate_files_cached(test_files)
            first_time = time.perf_counter() - start_time
            
            # 第二次验证（缓存命中）
            start_time = time.perf_counter()
            is_valid2, message2 = file_service.validate_files_cached(test_files)
            second_time = time.perf_counter() - start_time
            
            # 验证结果
            assert is_valid1 == is_valid2, "验证结果应该相同"
            assert message1 == message2, "验证消息应该相同"
            
            # 检查缓存统计
            stats = file_service.get_stats()
            assert stats["cache_hits"] > 0, "应该有缓存命中"
            assert stats["cache_misses"] > 0, "应该有缓存未命中"
            assert stats["validations_performed"] > 0, "应该有验证执行"
            
            print(f"  第一次验证时间: {first_time:.3f}秒")
            print(f"  第二次验证时间: {second_time:.3f}秒")
            print(f"  缓存命中率: {stats['cache_hit_rate']}")
            print(f"  验证缓存大小: {stats['validation_cache_size']}")
            
            # 验证缓存效果
            if second_time < first_time:
                improvement = (1 - second_time / first_time) * 100
                print(f"  ✅ 缓存提升: {improvement:.1f}%")
            else:
                print(f"  ⚠️ 缓存未显示明显提升")
                
            # 关闭服务
            file_service.shutdown()
            
            print("✅ 文件验证缓存测试通过")
            return True
            
        finally:
            # 清理测试文件
            import shutil
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"❌ 文件验证缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_async_file_operations():
    """测试异步文件操作"""
    print("\n🧪 测试异步文件操作...")
    
    try:
        from infrastructure.file_service import FileServiceFactory
        from concurrent.futures import as_completed
        
        # 创建测试文件
        temp_dir, test_files = create_test_files()
        
        try:
            # 创建文件服务
            file_service = FileServiceFactory.create_file_service(max_workers=4)
            
            # 测试1：异步获取文件信息
            futures = []
            for file_path in test_files:
                future = file_service.get_file_info_async(file_path)
                futures.append((file_path, future))
                
            # 等待所有异步操作完成
            results = []
            for file_path, future in futures:
                file_info = future.result(timeout=5)
                results.append((file_path, file_info))
                assert file_info.exists, f"异步获取的文件信息应该正确: {file_path}"
                
            # 测试2：批量异步验证
            batch_future = file_service.batch_validate_async(test_files)
            batch_results = batch_future.result(timeout=10)
            
            assert len(batch_results) == len(test_files), "批量验证结果数量应该匹配"
            
            for file_path, is_valid, message in batch_results:
                assert is_valid, f"批量验证应该成功: {file_path} - {message}"
                
            # 检查统计信息
            stats = file_service.get_stats()
            assert stats["async_operations"] >= len(test_files) + 1, "应该记录异步操作"
            
            print(f"  异步操作数量: {stats['async_operations']}")
            print(f"  活跃线程数: {stats['executor_active_threads']}")
            
            # 关闭服务
            file_service.shutdown()
            
            print("✅ 异步文件操作测试通过")
            return True
            
        finally:
            # 清理测试文件
            import shutil
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"❌ 异步文件操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_file_monitoring():
    """测试文件监控"""
    print("\n🧪 测试文件监控...")
    
    try:
        from infrastructure.file_service import FileServiceFactory
        
        # 创建测试文件
        temp_dir, test_files = create_test_files()
        
        try:
            # 创建文件服务
            file_service = FileServiceFactory.create_file_service()
            
            # 监控变更记录
            changes_detected = []
            
            def on_file_changed(file_path, file_info):
                changes_detected.append((file_path, file_info.modified_time))
                print(f"  📁 检测到文件变更: {os.path.basename(file_path)}")
                
            # 开始监控第一个文件
            monitor_file = test_files[0]
            file_service.monitor_files([monitor_file], on_file_changed)
            
            # 等待监控启动
            time.sleep(1.5)
            
            # 修改文件
            with open(monitor_file, 'a', encoding='utf-8') as f:
                f.write("\n修改内容")
                
            # 等待变更检测
            time.sleep(2)
            
            # 验证变更检测
            assert len(changes_detected) > 0, "应该检测到文件变更"
            
            # 检查统计信息
            stats = file_service.get_stats()
            assert stats["files_monitored"] >= 1, "应该有监控的文件"
            
            print(f"  检测到 {len(changes_detected)} 次文件变更")
            print(f"  监控文件数量: {stats['files_monitored']}")
            
            # 关闭服务
            file_service.shutdown()
            
            print("✅ 文件监控测试通过")
            return True
            
        finally:
            # 清理测试文件
            import shutil
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"❌ 文件监控测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_file_service_integration():
    """测试文件服务集成"""
    print("\n🧪 测试文件服务集成...")
    
    try:
        from infrastructure import initialize_infrastructure, get_infrastructure, shutdown_infrastructure
        
        # 初始化基础设施
        config = {
            "feature_flags_config": "test_file_service_flags.json"
        }
        
        success = initialize_infrastructure(config)
        assert success, "基础设施初始化应该成功"
        
        infrastructure = get_infrastructure()
        
        # 启用文件服务特性
        feature_flags = infrastructure.get_feature_flags()
        feature_flags.enable("use_file_service", "测试文件服务")
        
        # 重新注册服务以应用特性开关
        infrastructure._register_core_services()
        
        # 测试服务获取
        container = infrastructure.get_container()
        
        # 检查文件服务是否已注册
        if container.has("file_manager"):
            file_manager = container.get("file_manager")
            assert file_manager is not None, "应该能获取文件管理器"
            
            # 创建测试文件
            temp_dir, test_files = create_test_files()
            
            try:
                # 测试兼容性接口
                is_valid, message = file_manager.validate_files(test_files)
                assert is_valid, f"兼容性验证应该成功: {message}"
                
                # 测试文件信息获取
                file_info_dict = file_manager.get_file_info_compat(test_files[0])
                assert file_info_dict["exists"], "文件应该存在"
                assert file_info_dict["status"] == "valid", "文件应该有效"
                
                print("✅ 文件服务集成测试通过")
                
            finally:
                # 清理测试文件
                import shutil
                shutil.rmtree(temp_dir)
                
        else:
            print("⚠️ 文件服务未注册，可能特性开关未生效")
            
        # 关闭基础设施
        shutdown_infrastructure()
        
        # 清理测试文件
        if os.path.exists("test_file_service_flags.json"):
            os.unlink("test_file_service_flags.json")
            
        return True
        
    except Exception as e:
        print(f"❌ 文件服务集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_file_service_performance():
    """测试文件服务性能"""
    print("\n🧪 测试文件服务性能...")
    
    try:
        from infrastructure.file_service import FileServiceFactory
        
        # 创建大量测试文件
        temp_dir = tempfile.mkdtemp()
        test_files = []
        
        try:
            # 创建100个测试文件
            for i in range(100):
                file_path = os.path.join(temp_dir, f"test_file_{i:03d}.txt")
                with open(file_path, 'w') as f:
                    f.write(f"测试文件内容 {i}")
                test_files.append(file_path)
                
            # 创建文件服务
            file_service = FileServiceFactory.create_file_service(max_workers=4)
            
            # 性能测试：验证大量文件
            start_time = time.perf_counter()
            is_valid, message = file_service.validate_files_cached(test_files)
            first_validation_time = time.perf_counter() - start_time
            
            # 第二次验证（缓存命中）
            start_time = time.perf_counter()
            is_valid2, message2 = file_service.validate_files_cached(test_files)
            second_validation_time = time.perf_counter() - start_time
            
            # 检查结果
            assert is_valid and is_valid2, "大量文件验证应该成功"
            
            # 性能统计
            stats = file_service.get_stats()
            
            print(f"  📊 性能测试结果:")
            print(f"    文件数量: {len(test_files)}")
            print(f"    首次验证时间: {first_validation_time:.3f}秒")
            print(f"    缓存验证时间: {second_validation_time:.3f}秒")
            print(f"    验证速度: {len(test_files)/first_validation_time:.0f}文件/秒")
            print(f"    缓存命中率: {stats['cache_hit_rate']}")
            print(f"    平均验证时间: {stats['average_validation_time']*1000:.3f}毫秒")
            
            # 验证性能要求
            files_per_second = len(test_files) / first_validation_time
            assert files_per_second > 50, f"验证速度应该 > 50文件/秒，实际: {files_per_second:.0f}"
            
            if second_validation_time < first_validation_time:
                improvement = (1 - second_validation_time / first_validation_time) * 100
                assert improvement > 30, f"缓存提升应该 > 30%，实际: {improvement:.1f}%"
                print(f"    ✅ 缓存性能提升: {improvement:.1f}%")
            else:
                print(f"    ⚠️ 缓存未显示明显提升")
                
            # 关闭服务
            file_service.shutdown()
            
            print("✅ 文件服务性能测试通过")
            return True
            
        finally:
            # 清理测试文件
            import shutil
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"❌ 文件服务性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有文件服务测试"""
    print("🚀 开始文件服务测试...")
    print("=" * 60)
    
    tests = [
        ("文件服务基本功能", test_file_service_basic),
        ("文件验证缓存", test_file_validation_cache),
        ("异步文件操作", test_async_file_operations),
        ("文件监控", test_file_monitoring),
        ("文件服务集成", test_file_service_integration),
        ("文件服务性能", test_file_service_performance)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            failed += 1
            
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}个通过, {failed}个失败")
    
    if failed == 0:
        print("🎉 所有文件服务测试通过！架构优化步骤4完成")
        return True
    else:
        print("⚠️ 部分测试失败，需要修复问题")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
