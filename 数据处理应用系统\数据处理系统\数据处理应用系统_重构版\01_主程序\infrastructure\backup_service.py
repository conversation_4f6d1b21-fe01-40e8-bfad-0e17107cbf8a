# -*- coding: utf-8 -*-
"""
备份服务 - 架构优化步骤5
实现异步备份处理、任务队列管理和增量备份

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import os
import time
import threading
import shutil
import sqlite3
import hashlib
from typing import Any, Dict, List, Optional, Tuple, Callable
try:
    from typing import Future
except ImportError:
    from concurrent.futures import Future
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback
from datetime import datetime, timedelta
import queue


class BackupType(Enum):
    """备份类型枚举"""
    FULL = "full"
    INCREMENTAL = "incremental"
    DIFFERENTIAL = "differential"
    MANUAL = "manual"
    SCHEDULED = "scheduled"


class BackupStatus(Enum):
    """备份状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class BackupPriority(Enum):
    """备份优先级枚举"""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3


@dataclass
class BackupTask:
    """备份任务数据结构"""
    task_id: str
    source_path: str
    backup_path: str
    backup_type: BackupType = BackupType.FULL
    priority: BackupPriority = BackupPriority.NORMAL
    status: BackupStatus = BackupStatus.PENDING
    created_time: float = field(default_factory=time.time)
    started_time: Optional[float] = None
    completed_time: Optional[float] = None
    progress: float = 0.0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __lt__(self, other):
        """用于优先级队列排序"""
        return self.priority.value > other.priority.value
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "source_path": self.source_path,
            "backup_path": self.backup_path,
            "backup_type": self.backup_type.value,
            "priority": self.priority.value,
            "status": self.status.value,
            "created_time": self.created_time,
            "started_time": self.started_time,
            "completed_time": self.completed_time,
            "progress": self.progress,
            "error_message": self.error_message,
            "metadata": self.metadata,
            "duration": self.get_duration()
        }
        
    def get_duration(self) -> Optional[float]:
        """获取任务持续时间"""
        if self.started_time:
            end_time = self.completed_time or time.time()
            return end_time - self.started_time
        return None


@dataclass
class BackupPolicy:
    """备份策略配置"""
    name: str
    backup_type: BackupType = BackupType.FULL
    schedule_interval: int = 3600  # 秒
    max_backups: int = 10
    compression: bool = True
    verify_backup: bool = True
    cleanup_old: bool = True
    incremental_threshold: float = 0.1  # 10%变化触发增量备份
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "backup_type": self.backup_type.value,
            "schedule_interval": self.schedule_interval,
            "max_backups": self.max_backups,
            "compression": self.compression,
            "verify_backup": self.verify_backup,
            "cleanup_old": self.cleanup_old,
            "incremental_threshold": self.incremental_threshold
        }


class BackupService:
    """
    备份服务
    
    功能：
    - 异步备份处理
    - 任务队列管理
    - 增量备份支持
    - 备份状态监控
    """
    
    def __init__(self, config_service=None, logging_service=None, gui_service=None,
                 max_workers: int = 2, max_queue_size: int = 100):
        self.config_service = config_service
        self.logging_service = logging_service
        self.gui_service = gui_service
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 任务队列
        self.task_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self.active_tasks: Dict[str, BackupTask] = {}
        self.completed_tasks: Dict[str, BackupTask] = {}
        self.task_lock = threading.RLock()
        
        # 备份策略
        self.policies: Dict[str, BackupPolicy] = {}
        self.default_policy = BackupPolicy("default")
        
        # 工作线程
        self.worker_threads: List[threading.Thread] = []
        self.running = False
        
        # 性能统计
        self.stats = {
            "tasks_created": 0,
            "tasks_completed": 0,
            "tasks_failed": 0,
            "total_backup_time": 0.0,
            "average_backup_time": 0.0,
            "total_backup_size": 0,
            "queue_high_water_mark": 0
        }
        
        # 备份历史记录
        self.backup_history: List[Dict[str, Any]] = []
        self.max_history_size = 100
        
        # 启动工作线程
        self._start_workers()
        
        self._log_info("备份服务已初始化")
        
    def create_backup_task(self, source_path: str, backup_path: Optional[str] = None,
                          backup_type: BackupType = BackupType.FULL,
                          priority: BackupPriority = BackupPriority.NORMAL,
                          policy_name: Optional[str] = None) -> str:
        """
        创建备份任务
        
        Args:
            source_path: 源文件路径
            backup_path: 备份路径（可选，自动生成）
            backup_type: 备份类型
            priority: 优先级
            policy_name: 备份策略名称
            
        Returns:
            str: 任务ID
        """
        try:
            # 生成任务ID
            task_id = self._generate_task_id()
            
            # 生成备份路径
            if not backup_path:
                backup_path = self._generate_backup_path(source_path, backup_type)
                
            # 获取备份策略
            policy = self.policies.get(policy_name, self.default_policy)
            
            # 创建备份任务
            task = BackupTask(
                task_id=task_id,
                source_path=source_path,
                backup_path=backup_path,
                backup_type=backup_type,
                priority=priority,
                metadata={
                    "policy": policy.to_dict(),
                    "source_size": self._get_file_size(source_path)
                }
            )
            
            # 加入任务队列
            try:
                self.task_queue.put(task, timeout=1)
                
                with self.task_lock:
                    self.active_tasks[task_id] = task
                    
                # 更新统计
                self.stats["tasks_created"] += 1
                current_size = self.task_queue.qsize()
                if current_size > self.stats["queue_high_water_mark"]:
                    self.stats["queue_high_water_mark"] = current_size
                    
                self._log_info(f"备份任务已创建: {task_id} ({backup_type.value})")
                return task_id
                
            except queue.Full:
                self._log_error("备份任务队列已满，无法创建新任务")
                raise Exception("备份任务队列已满")
                
        except Exception as e:
            self._log_error(f"创建备份任务失败: {e}")
            raise
            
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self.task_lock:
            if task_id in self.active_tasks:
                return self.active_tasks[task_id].to_dict()
            elif task_id in self.completed_tasks:
                return self.completed_tasks[task_id].to_dict()
        return None
        
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            with self.task_lock:
                if task_id in self.active_tasks:
                    task = self.active_tasks[task_id]
                    if task.status == BackupStatus.PENDING:
                        task.status = BackupStatus.CANCELLED
                        self._log_info(f"备份任务已取消: {task_id}")
                        return True
                    elif task.status == BackupStatus.RUNNING:
                        # 标记为取消，工作线程会检查此状态
                        task.status = BackupStatus.CANCELLED
                        self._log_info(f"正在取消运行中的备份任务: {task_id}")
                        return True
            return False
        except Exception as e:
            self._log_error(f"取消任务失败: {e}")
            return False
            
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """获取活跃任务列表"""
        with self.task_lock:
            return [task.to_dict() for task in self.active_tasks.values()]
            
    def get_completed_tasks(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取已完成任务列表"""
        with self.task_lock:
            tasks = list(self.completed_tasks.values())
            # 按完成时间排序
            tasks.sort(key=lambda t: t.completed_time or 0, reverse=True)
            return [task.to_dict() for task in tasks[:limit]]
            
    def add_backup_policy(self, policy: BackupPolicy):
        """添加备份策略"""
        self.policies[policy.name] = policy
        self._log_info(f"备份策略已添加: {policy.name}")
        
    def get_backup_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取备份历史"""
        return self.backup_history[-limit:]
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.task_lock:
            active_count = len(self.active_tasks)
            completed_count = len(self.completed_tasks)
            
        return {
            **self.stats,
            "active_tasks": active_count,
            "completed_tasks": completed_count,
            "queue_size": self.task_queue.qsize(),
            "worker_threads": len(self.worker_threads),
            "running": self.running
        }
        
    def shutdown(self):
        """关闭备份服务"""
        self._log_info("关闭备份服务...")

        try:
            self.running = False

            # 等待工作线程结束
            for thread in self.worker_threads:
                if thread.is_alive():
                    thread.join(timeout=5)
                    if thread.is_alive():
                        self._log_error(f"工作线程 {thread.name} 未能在5秒内停止")

            # 关闭线程池
            if self.executor:
                self.executor.shutdown(wait=True)
                self.executor = None

            # 清空队列和任务
            try:
                while not self.task_queue.empty():
                    self.task_queue.get_nowait()
            except:
                pass

            with self.task_lock:
                self.active_tasks.clear()

            self.worker_threads.clear()
            self.backup_history.clear()

            self._log_info("备份服务已关闭")

        except Exception as e:
            self._log_error(f"关闭备份服务时出错: {e}")
        
    def _start_workers(self):
        """启动工作线程"""
        self.running = True
        
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"BackupService-Worker-{i}",
                daemon=True
            )
            worker.start()
            self.worker_threads.append(worker)
            
        self._log_info(f"启动 {self.max_workers} 个备份工作线程")
        
    def _worker_loop(self):
        """工作线程主循环"""
        while self.running:
            try:
                # 获取备份任务（带超时）
                task = self.task_queue.get(timeout=1)
                
                # 检查任务是否已取消
                if task.status == BackupStatus.CANCELLED:
                    self._complete_task(task)
                    continue
                    
                # 执行备份任务
                self._execute_backup_task(task)
                
                # 标记任务完成
                self.task_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self._log_error(f"备份工作线程错误: {e}")
                traceback.print_exc()
                
    def _execute_backup_task(self, task: BackupTask):
        """执行备份任务"""
        start_time = time.perf_counter()
        
        try:
            # 更新任务状态
            task.status = BackupStatus.RUNNING
            task.started_time = time.time()
            task.progress = 0.0
            
            self._log_info(f"开始执行备份任务: {task.task_id}")
            self._update_gui_progress(task, "开始备份...")
            
            # 检查源文件
            if not os.path.exists(task.source_path):
                raise Exception(f"源文件不存在: {task.source_path}")
                
            # 创建备份目录
            backup_dir = os.path.dirname(task.backup_path)
            os.makedirs(backup_dir, exist_ok=True)
            
            # 执行备份
            if task.backup_type == BackupType.FULL:
                self._perform_full_backup(task)
            elif task.backup_type == BackupType.INCREMENTAL:
                self._perform_incremental_backup(task)
            else:
                self._perform_full_backup(task)  # 默认全量备份
                
            # 验证备份
            if task.metadata.get("policy", {}).get("verify_backup", True):
                self._verify_backup(task)
                
            # 完成任务
            task.status = BackupStatus.COMPLETED
            task.completed_time = time.time()
            task.progress = 1.0
            
            # 更新统计
            backup_time = time.perf_counter() - start_time
            self.stats["tasks_completed"] += 1
            self.stats["total_backup_time"] += backup_time
            self.stats["average_backup_time"] = (
                self.stats["total_backup_time"] / self.stats["tasks_completed"]
            )
            
            # 添加到历史记录
            self._add_to_history(task)
            
            self._log_info(f"备份任务完成: {task.task_id}, 耗时: {backup_time:.3f}秒")
            self._update_gui_progress(task, "备份完成")
            
        except Exception as e:
            # 任务失败
            task.status = BackupStatus.FAILED
            task.error_message = str(e)
            task.completed_time = time.time()
            
            self.stats["tasks_failed"] += 1
            
            self._log_error(f"备份任务失败: {task.task_id} - {e}")
            self._update_gui_progress(task, f"备份失败: {e}")
            
        finally:
            # 移动到已完成任务
            self._complete_task(task)
            
    def _perform_full_backup(self, task: BackupTask):
        """执行全量备份"""
        try:
            # 检查是否为数据库文件
            if task.source_path.endswith(('.db', '.sqlite', '.sqlite3')):
                self._backup_database(task)
            else:
                self._backup_file(task)
                
        except Exception as e:
            raise Exception(f"全量备份失败: {e}")
            
    def _perform_incremental_backup(self, task: BackupTask):
        """执行增量备份"""
        try:
            # 检查是否需要增量备份
            if self._should_perform_incremental(task):
                self._perform_full_backup(task)  # 简化实现，实际可以做差异备份
            else:
                task.status = BackupStatus.COMPLETED
                task.progress = 1.0
                self._log_info(f"文件未变化，跳过增量备份: {task.task_id}")
                
        except Exception as e:
            raise Exception(f"增量备份失败: {e}")
            
    def _backup_database(self, task: BackupTask):
        """备份数据库文件"""
        try:
            task.progress = 0.1
            self._update_gui_progress(task, "连接数据库...")
            
            # 使用SQLite的备份API
            source_conn = sqlite3.connect(task.source_path)
            backup_conn = sqlite3.connect(task.backup_path)
            
            task.progress = 0.3
            self._update_gui_progress(task, "备份数据库...")
            
            # 执行备份
            source_conn.backup(backup_conn)
            
            task.progress = 0.8
            self._update_gui_progress(task, "关闭连接...")
            
            source_conn.close()
            backup_conn.close()
            
            task.progress = 1.0
            
        except Exception as e:
            raise Exception(f"数据库备份失败: {e}")
            
    def _backup_file(self, task: BackupTask):
        """备份普通文件"""
        try:
            task.progress = 0.1
            self._update_gui_progress(task, "复制文件...")
            
            # 复制文件
            shutil.copy2(task.source_path, task.backup_path)
            
            task.progress = 1.0
            
        except Exception as e:
            raise Exception(f"文件备份失败: {e}")
            
    def _verify_backup(self, task: BackupTask):
        """验证备份文件"""
        try:
            if not os.path.exists(task.backup_path):
                raise Exception("备份文件不存在")
                
            # 比较文件大小
            source_size = os.path.getsize(task.source_path)
            backup_size = os.path.getsize(task.backup_path)
            
            if source_size != backup_size:
                raise Exception(f"备份文件大小不匹配: {source_size} != {backup_size}")
                
            self._log_info(f"备份验证通过: {task.task_id}")
            
        except Exception as e:
            raise Exception(f"备份验证失败: {e}")
            
    def _should_perform_incremental(self, task: BackupTask) -> bool:
        """检查是否应该执行增量备份"""
        try:
            if not os.path.exists(task.backup_path):
                return True  # 备份文件不存在，需要备份
                
            # 比较修改时间
            source_mtime = os.path.getmtime(task.source_path)
            backup_mtime = os.path.getmtime(task.backup_path)
            
            return source_mtime > backup_mtime
            
        except Exception:
            return True  # 出错时默认需要备份
            
    def _complete_task(self, task: BackupTask):
        """完成任务处理"""
        with self.task_lock:
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
                self.completed_tasks[task.task_id] = task
                
                # 限制已完成任务数量
                if len(self.completed_tasks) > 100:
                    # 删除最旧的任务
                    oldest_task_id = min(
                        self.completed_tasks.keys(),
                        key=lambda tid: self.completed_tasks[tid].completed_time or 0
                    )
                    del self.completed_tasks[oldest_task_id]
                    
    def _add_to_history(self, task: BackupTask):
        """添加到历史记录"""
        history_entry = {
            "task_id": task.task_id,
            "source_path": task.source_path,
            "backup_path": task.backup_path,
            "backup_type": task.backup_type.value,
            "status": task.status.value,
            "duration": task.get_duration(),
            "timestamp": task.completed_time,
            "size": self._get_file_size(task.backup_path)
        }
        
        self.backup_history.append(history_entry)
        
        # 保持历史记录大小
        if len(self.backup_history) > self.max_history_size:
            self.backup_history.pop(0)
            
    def _generate_task_id(self) -> str:
        """生成任务ID"""
        timestamp = int(time.time() * 1000000)
        return f"backup_{timestamp}"
        
    def _generate_backup_path(self, source_path: str, backup_type: BackupType) -> str:
        """生成备份路径"""
        try:
            # 获取备份目录
            backup_dir = self._get_backup_directory()
            
            # 生成备份文件名
            source_name = os.path.basename(source_path)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{source_name}.{backup_type.value}.{timestamp}.bak"
            
            return os.path.join(backup_dir, backup_name)
            
        except Exception as e:
            # 回退到临时目录
            import tempfile
            return os.path.join(tempfile.gettempdir(), f"backup_{int(time.time())}.bak")
            
    def _get_backup_directory(self) -> str:
        """获取备份目录"""
        if self.config_service:
            try:
                backup_dir = self.config_service.get("Backup", "backup_dir", "backups")
                os.makedirs(backup_dir, exist_ok=True)
                return backup_dir
            except Exception:
                pass
                
        # 默认备份目录
        default_dir = os.path.join(os.getcwd(), "backups")
        os.makedirs(default_dir, exist_ok=True)
        return default_dir
        
    def _get_file_size(self, file_path: str) -> int:
        """获取文件大小"""
        try:
            if os.path.exists(file_path):
                return os.path.getsize(file_path)
        except Exception:
            pass
        return 0
        
    def _update_gui_progress(self, task: BackupTask, message: str):
        """更新GUI进度"""
        if self.gui_service:
            try:
                self.gui_service.update_progress(task.progress, message)
            except Exception as e:
                self._log_error(f"GUI进度更新失败: {e}")
                
    def _log_info(self, message: str):
        """记录信息日志"""
        if self.logging_service:
            self.logging_service.log_info(message, source="BackupService")
        else:
            print(f"[BACKUP] {message}")
            
    def _log_error(self, message: str):
        """记录错误日志"""
        if self.logging_service:
            self.logging_service.log_error(message, source="BackupService")
        else:
            print(f"[BACKUP ERROR] {message}")


class BackupServiceFactory:
    """备份服务工厂"""
    
    @staticmethod
    def create_backup_service(config_service=None, logging_service=None, gui_service=None,
                            config: Optional[Dict[str, Any]] = None, max_workers: int = 2) -> BackupService:
        """创建备份服务实例"""
        print("🏗️ [BACKUP] 创建备份服务...")
        
        # 默认配置
        default_config = {
            "max_workers": max_workers,
            "max_queue_size": 100
        }

        if config:
            default_config.update(config)

        # 创建备份服务
        backup_service = BackupService(
            config_service=config_service,
            logging_service=logging_service,
            gui_service=gui_service,
            max_workers=default_config["max_workers"],
            max_queue_size=default_config["max_queue_size"]
        )
        
        # 添加默认备份策略
        default_policy = BackupPolicy(
            name="default",
            backup_type=BackupType.FULL,
            schedule_interval=3600,
            max_backups=10,
            compression=False,
            verify_backup=True,
            cleanup_old=True
        )
        backup_service.add_backup_policy(default_policy)
        
        print("✅ [BACKUP] 备份服务创建完成")
        return backup_service
        
    @staticmethod
    def create_legacy_compatible_service(config_service=None, logging_service=None, gui_service=None):
        """创建兼容旧系统的备份服务"""
        backup_service = BackupServiceFactory.create_backup_service(
            config_service, logging_service, gui_service
        )
        
        # 添加兼容性方法
        def backup_database(backup_name=None):
            """兼容性方法：备份数据库"""
            try:
                # 获取数据库路径
                if config_service:
                    db_path = config_service.get("Database", "db_path", "data.db")
                else:
                    db_path = "data.db"
                    
                # 创建备份任务
                task_id = backup_service.create_backup_task(
                    source_path=db_path,
                    backup_type=BackupType.FULL,
                    priority=BackupPriority.HIGH
                )
                
                # 等待任务完成（简化实现）
                import time
                max_wait = 60  # 最多等待60秒
                waited = 0
                
                while waited < max_wait:
                    status = backup_service.get_task_status(task_id)
                    if status and status["status"] in ["completed", "failed", "cancelled"]:
                        return status["status"] == "completed"
                    time.sleep(1)
                    waited += 1
                    
                return False
                
            except Exception as e:
                if logging_service:
                    logging_service.log_error(f"数据库备份失败: {e}")
                return False
                
        def restore_database(backup_file):
            """兼容性方法：恢复数据库"""
            try:
                # 简化实现：直接复制文件
                if config_service:
                    db_path = config_service.get("Database", "db_path", "data.db")
                else:
                    db_path = "data.db"
                    
                if os.path.exists(backup_file):
                    shutil.copy2(backup_file, db_path)
                    return True
                    
                return False
                
            except Exception as e:
                if logging_service:
                    logging_service.log_error(f"数据库恢复失败: {e}")
                return False
                
        # 动态添加兼容性方法
        backup_service.backup_database = backup_database
        backup_service.restore_database = restore_database
        
        return backup_service


# 全局备份服务实例
_global_backup_service: Optional[BackupService] = None


def get_backup_service() -> Optional[BackupService]:
    """获取全局备份服务实例"""
    return _global_backup_service


def set_backup_service(backup_service: BackupService):
    """设置全局备份服务实例"""
    global _global_backup_service
    _global_backup_service = backup_service
