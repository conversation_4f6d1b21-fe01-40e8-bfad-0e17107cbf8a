#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理重复测试文件和优化项目结构
"""

import os
import shutil
import re
from datetime import datetime
from collections import defaultdict

def analyze_duplicate_files():
    """分析重复的测试文件"""
    
    print("🔍 分析重复测试文件...")
    
    # 测试文件模式
    test_patterns = [
        r"测试.*\.py$",
        r"test.*\.py$", 
        r".*测试.*\.py$",
        r".*验证.*\.py$",
        r".*检查.*\.py$",
        r".*修复.*\.py$",
        r".*诊断.*\.py$"
    ]
    
    # 收集所有匹配的文件
    test_files = []
    for root, dirs, files in os.walk("."):
        # 跳过备份目录和特定目录
        if any(skip in root for skip in ["backup_versions", "__pycache__", "node_modules", ".git"]):
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                for pattern in test_patterns:
                    if re.search(pattern, file, re.IGNORECASE):
                        test_files.append(file_path)
                        break
    
    print(f"📊 发现 {len(test_files)} 个测试相关文件")
    
    # 按功能分组
    groups = defaultdict(list)
    for file_path in test_files:
        filename = os.path.basename(file_path)
        # 提取核心功能名
        core_name = re.sub(r'(测试|test|验证|检查|修复|诊断)', '', filename, flags=re.IGNORECASE)
        core_name = re.sub(r'[_\-\s]+', '_', core_name)
        core_name = core_name.strip('_')
        groups[core_name].append(file_path)
    
    return groups

def cleanup_duplicate_files():
    """清理重复的测试文件"""
    
    groups = analyze_duplicate_files()
    
    # 创建清理目录
    cleanup_dir = "backup_versions/old_test_files"
    os.makedirs(cleanup_dir, exist_ok=True)
    
    moved_count = 0
    kept_count = 0
    
    print("\n🧹 开始清理重复文件...")
    
    for core_name, files in groups.items():
        if len(files) > 1:
            print(f"\n📁 处理组: {core_name} ({len(files)} 个文件)")
            
            # 按修改时间排序，保留最新的
            files_with_time = []
            for file_path in files:
                try:
                    mtime = os.path.getmtime(file_path)
                    size = os.path.getsize(file_path)
                    files_with_time.append((file_path, mtime, size))
                except:
                    continue
            
            files_with_time.sort(key=lambda x: x[1], reverse=True)  # 按时间降序
            
            # 保留最新的文件
            if files_with_time:
                keep_file = files_with_time[0][0]
                print(f"  ✅ 保留: {keep_file}")
                kept_count += 1
                
                # 移动其他文件到备份目录
                for file_path, mtime, size in files_with_time[1:]:
                    try:
                        filename = os.path.basename(file_path)
                        dest_path = os.path.join(cleanup_dir, filename)
                        
                        # 如果目标文件已存在，添加时间戳
                        if os.path.exists(dest_path):
                            name, ext = os.path.splitext(filename)
                            timestamp = datetime.fromtimestamp(mtime).strftime('%Y%m%d_%H%M%S')
                            dest_path = os.path.join(cleanup_dir, f"{name}_{timestamp}{ext}")
                        
                        shutil.move(file_path, dest_path)
                        print(f"  📦 移动: {file_path} -> {dest_path}")
                        moved_count += 1
                        
                    except Exception as e:
                        print(f"  ❌ 移动失败: {file_path} - {e}")
        else:
            print(f"📄 单文件组: {core_name} - {files[0]}")
            kept_count += 1
    
    print(f"\n📊 清理完成！")
    print(f"  ✅ 保留文件: {kept_count} 个")
    print(f"  📦 移动文件: {moved_count} 个")
    
    return moved_count, kept_count

if __name__ == "__main__":
    cleanup_duplicate_files()
