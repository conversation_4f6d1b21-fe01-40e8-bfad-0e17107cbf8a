#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细诊断脚本 - 逐步测试导入流程的每个环节
"""

import os
import sys
import pandas as pd
import traceback
from pathlib import Path

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_step_by_step():
    """逐步测试导入流程"""
    print("🔧 详细诊断：逐步测试导入流程")
    print("=" * 60)
    
    # 测试文件路径
    test_file = "030725 CHINA ZERO.xlsx"
    test_platform = "ZERO"
    
    try:
        # 步骤1: 导入必要的模块
        print("📋 步骤1: 导入模块...")
        try:
            from data_import_optimized import DataImportProcessor
            print("✅ DataImportProcessor 导入成功")
        except Exception as e:
            print(f"❌ DataImportProcessor 导入失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤2: 创建处理器实例
        print("\n📋 步骤2: 创建处理器实例...")
        try:
            processor = DataImportProcessor()
            print("✅ 处理器实例创建成功")
        except Exception as e:
            print(f"❌ 处理器实例创建失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤3: 验证文件
        print("\n📋 步骤3: 验证文件...")
        try:
            processor.validate_file(test_file)
            print("✅ 文件验证成功")
        except Exception as e:
            print(f"❌ 文件验证失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤4: 检测工作表
        print("\n📋 步骤4: 检测工作表...")
        try:
            sheet_name = processor._detect_correct_sheet(test_file, test_platform)
            print(f"✅ 工作表检测成功: {sheet_name}")
        except Exception as e:
            print(f"❌ 工作表检测失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤5: 读取Excel文件
        print("\n📋 步骤5: 读取Excel文件...")
        try:
            df_raw = pd.read_excel(test_file, sheet_name=sheet_name, engine='openpyxl')
            print(f"✅ Excel文件读取成功")
            print(f"   原始数据形状: {df_raw.shape}")
            print(f"   原始列名: {list(df_raw.columns)}")
            
            if df_raw is None:
                print("❌ 读取的DataFrame为None")
                return False
                
        except Exception as e:
            print(f"❌ Excel文件读取失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤6: Unicode清理
        print("\n📋 步骤6: Unicode清理...")
        try:
            df_cleaned = processor._clean_unicode_characters(df_raw)
            print(f"✅ Unicode清理成功")
            print(f"   清理后数据形状: {df_cleaned.shape}")
            
            if df_cleaned is None:
                print("❌ 清理后的DataFrame为None")
                return False
                
        except Exception as e:
            print(f"❌ Unicode清理失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤7: 数据清洗
        print("\n📋 步骤7: 数据清洗...")
        try:
            df_clean = processor._clean_data(df_cleaned)
            print(f"✅ 数据清洗成功")
            print(f"   清洗后数据形状: {df_clean.shape}")
            
            if df_clean is None:
                print("❌ 清洗后的DataFrame为None")
                return False
                
        except Exception as e:
            print(f"❌ 数据清洗失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤8: 列名标准化
        print("\n📋 步骤8: 列名标准化...")
        try:
            df_standardized = processor._standardize_column_names(df_clean)
            print(f"✅ 列名标准化成功")
            print(f"   标准化后数据形状: {df_standardized.shape}")
            print(f"   标准化后列名: {list(df_standardized.columns)}")
            
            if df_standardized is None:
                print("❌ 标准化后的DataFrame为None")
                return False
                
        except Exception as e:
            print(f"❌ 列名标准化失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤9: 处理缺失列
        print("\n📋 步骤9: 处理缺失列...")
        try:
            df_complete = processor._handle_missing_columns(df_standardized, test_platform)
            print(f"✅ 缺失列处理成功")
            print(f"   完整数据形状: {df_complete.shape}")
            print(f"   完整列名: {list(df_complete.columns)}")
            
            if df_complete is None:
                print("❌ 处理缺失列后的DataFrame为None")
                return False
                
        except Exception as e:
            print(f"❌ 缺失列处理失败: {e}")
            traceback.print_exc()
            return False
        
        # 步骤10: 完整的load_and_validate_data测试
        print("\n📋 步骤10: 完整的load_and_validate_data测试...")
        try:
            df_final = processor.load_and_validate_data(test_file, test_platform)
            print(f"✅ 完整数据加载验证成功")
            print(f"   最终数据形状: {df_final.shape}")
            print(f"   最终列名: {list(df_final.columns)}")
            
            if df_final is None:
                print("❌ 最终的DataFrame为None")
                return False
                
        except Exception as e:
            print(f"❌ 完整数据加载验证失败: {e}")
            traceback.print_exc()
            return False
        
        print(f"\n🎉 所有步骤都成功完成！")
        print(f"   最终数据包含 {len(df_final)} 行，{len(df_final.columns)} 列")
        return True
        
    except Exception as e:
        print(f"❌ 诊断过程中发生未预期的错误: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 详细诊断工具 - 逐步测试导入流程")
    print("=" * 60)
    
    success = test_step_by_step()
    
    if success:
        print(f"\n✅ 诊断结果: 导入流程正常")
        print(f"   问题可能出现在其他地方")
    else:
        print(f"\n❌ 诊断结果: 导入流程存在问题")
        print(f"   请根据上述错误信息进行修复")

if __name__ == "__main__":
    main()
