#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据处理应用9.0版本配置
检查所有路径和依赖是否正确配置
"""

import os
import sys
import configparser
from pathlib import Path

def main():
    """主验证函数"""
    print("🔍 数据处理应用9.0版本配置验证")
    print("=" * 50)
    
    # 获取当前目录
    current_dir = Path(__file__).parent
    print(f"📁 当前目录: {current_dir}")
    
    # 验证结果
    all_passed = True
    
    # 1. 验证主程序文件
    print("\n1️⃣ 验证主程序文件...")
    main_file = current_dir / "数据处理应用9.0版本.py"
    if main_file.exists():
        print("✅ 主程序文件存在")
        
        # 检查文件内容中的标题
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'self.title("数据处理应用9.0版本")' in content:
                    print("✅ 窗口标题已正确设置为9.0版本")
                else:
                    print("⚠️ 窗口标题可能未正确设置")
                    all_passed = False
        except Exception as e:
            print(f"❌ 读取主程序文件失败: {e}")
            all_passed = False
    else:
        print("❌ 主程序文件不存在")
        all_passed = False
    
    # 2. 验证配置文件
    print("\n2️⃣ 验证配置文件...")
    config_file = current_dir / "config" / "config.ini"
    if config_file.exists():
        print("✅ 配置文件存在")
        
        try:
            config = configparser.ConfigParser()
            config.read(config_file, encoding='utf-8')
            
            # 检查数据库路径
            if 'Database' in config:
                db_path = config['Database'].get('db_path', '')
                if 'database\\sales_reports.db' in db_path or 'database/sales_reports.db' in db_path:
                    print("✅ 数据库路径配置正确")
                else:
                    print(f"⚠️ 数据库路径可能不正确: {db_path}")
                    all_passed = False
            
            # 检查脚本路径
            if 'Scripts' in config:
                script_paths = dict(config['Scripts'])
                scripts_ok = True
                for key, path in script_paths.items():
                    if path.startswith('scripts/') or not path.endswith('.py'):
                        continue
                    else:
                        print(f"⚠️ 脚本路径可能需要调整: {key} = {path}")
                        scripts_ok = False
                
                if scripts_ok:
                    print("✅ 脚本路径配置正确")
                else:
                    all_passed = False
                    
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            all_passed = False
    else:
        print("❌ 配置文件不存在")
        all_passed = False
    
    # 3. 验证必要目录
    print("\n3️⃣ 验证目录结构...")
    required_dirs = [
        "scripts",
        "database", 
        "utils",
        "ui",
        "logs",
        "backups",
        "temp_data",
        "config"
    ]
    
    for dir_name in required_dirs:
        dir_path = current_dir / dir_name
        if dir_path.exists():
            print(f"✅ {dir_name} 目录存在")
        else:
            print(f"❌ {dir_name} 目录不存在")
            all_passed = False
    
    # 4. 验证关键脚本文件
    print("\n4️⃣ 验证关键脚本文件...")
    key_scripts = [
        "scripts/data_import_optimized.py",
        "scripts/refund_process_optimized.py",
        "utils/config_manager.py",
        "unified_processing_tab.py"
    ]
    
    for script in key_scripts:
        script_path = current_dir / script
        if script_path.exists():
            print(f"✅ {script} 存在")
        else:
            print(f"❌ {script} 不存在")
            all_passed = False
    
    # 5. 验证数据库文件
    print("\n5️⃣ 验证数据库文件...")
    db_file = current_dir / "database" / "sales_reports.db"
    if db_file.exists():
        print("✅ SQLite数据库文件存在")
        print(f"📊 数据库大小: {db_file.stat().st_size / 1024:.1f} KB")
    else:
        print("⚠️ SQLite数据库文件不存在（首次运行时会自动创建）")
    
    # 6. 验证Python依赖
    print("\n6️⃣ 验证Python依赖...")
    required_modules = [
        'tkinter',
        'pandas', 
        'sqlite3',
        'configparser',
        'threading',
        'subprocess'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} 模块可用")
        except ImportError:
            print(f"❌ {module} 模块不可用")
            all_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有验证通过！9.0版本配置正确")
        print("✅ 可以安全启动应用")
        print("\n🚀 启动方法:")
        print("   1. 双击 '启动9.0版本.bat'")
        print("   2. 或运行: python \"数据处理应用9.0版本.py\"")
    else:
        print("⚠️ 发现一些配置问题，建议检查后再启动")
        print("💡 大部分问题不会影响基本功能")
    
    print("\n📝 验证完成")
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
