#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
使用正确的sheet名称测试修复后的脚本
"""

import subprocess
import sys
import os

def test_with_correct_sheet():
    """使用正确的sheet名称测试脚本"""
    print("🔧 使用正确的sheet名称测试脚本...")
    
    # 正确的sheet名称
    correct_sheet = "SETTLEMENT_REPORT_FROM_DATE_140"
    
    script_args = [
        sys.executable,
        "report 模块化设计 7.0.py",
        "--file1", "C:/Users/<USER>/Desktop/July/IOT/SETTLEMENT_REPORT_10072025_zeroiot_TRANSACTION_LIST.xlsx",
        "--file2", "C:/Users/<USER>/Desktop/July/IOT/100725 CHINA IOT.xlsx",
        "--sheet_name", correct_sheet
    ]
    
    print(f"执行命令: {' '.join(script_args)}")
    print(f"使用正确的sheet名称: {correct_sheet}")
    
    try:
        result = subprocess.run(
            script_args,
            capture_output=True,
            text=True,
            timeout=120,
            encoding='utf-8',
            errors='replace'
        )
        
        print(f"\n📊 执行结果:")
        print(f"  返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("  ✅ 脚本执行成功！")
        else:
            print("  ❌ 脚本执行失败")
        
        print(f"\n📝 标准输出 (前1000字符):")
        stdout = result.stdout[:1000] if result.stdout else "(无输出)"
        print(stdout)
        
        if result.stderr:
            print(f"\n❌ 错误输出 (前1000字符):")
            stderr = result.stderr[:1000]
            print(stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ 脚本执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行脚本时发生错误: {e}")
        return False

def test_help_command():
    """测试帮助命令"""
    print("\n🔧 测试帮助命令...")
    
    try:
        result = subprocess.run([
            sys.executable, "report 模块化设计 7.0.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        print(f"返回码: {result.returncode}")
        if result.returncode == 0:
            print("✅ 帮助命令成功")
        else:
            print("❌ 帮助命令失败")
            if result.stderr:
                print(f"错误: {result.stderr[:500]}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 帮助命令测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 快速测试工具")
    print("=" * 50)
    
    print("📋 发现的问题:")
    print("  1. ❌ Sheet名称错误: 'TRANSACTION_LIST'")
    print("  2. ✅ 正确的sheet名称: 'SETTLEMENT_REPORT_FROM_DATE_140'")
    print("  3. ✅ 已修复变量初始化问题")
    
    tests = [
        ("帮助命令测试", test_help_command),
        ("正确sheet名称测试", test_with_correct_sheet),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 50)
    print("📊 快速测试结果总结")
    print("=" * 50)
    print(f"通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！脚本修复成功！")
        print("\n📝 用户需要做的:")
        print("  1. 在应用程序中使用正确的sheet名称:")
        print("     ❌ 错误: TRANSACTION_LIST")
        print("     ✅ 正确: SETTLEMENT_REPORT_FROM_DATE_140")
        print("  2. 或者重命名Excel文件中的sheet为 TRANSACTION_LIST")
    else:
        print("\n⚠️ 仍有问题需要解决")

if __name__ == "__main__":
    main()
