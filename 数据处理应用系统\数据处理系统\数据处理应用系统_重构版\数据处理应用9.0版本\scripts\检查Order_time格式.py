#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Order_time格式 - 验证导入后Order_time是否只包含日期
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_standardize_date_method():
    """测试standardize_date方法"""
    print("🔧 1. 测试standardize_date方法")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试各种时间格式
        test_cases = [
            ("完整日期时间", "2025-07-07 00:00:00"),
            ("完整日期时间2", "2025-07-07 12:30:45"),
            ("只有日期", "2025-07-07"),
            ("斜杠分隔", "2025/07/07"),
            ("Excel格式", "2025-07-07 10:15:30"),
        ]
        
        print("📋 测试结果:")
        all_correct = True
        
        for case_name, input_date in test_cases:
            try:
                result = processor.standardize_date(input_date)
                
                # 检查结果格式
                if result and len(result) == 10 and result.count('-') == 2:
                    print(f"✅ {case_name}: '{input_date}' → '{result}' (只有日期)")
                else:
                    print(f"❌ {case_name}: '{input_date}' → '{result}' (包含时间)")
                    all_correct = False
                    
            except Exception as e:
                print(f"❌ {case_name}: '{input_date}' → 错误: {e}")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ standardize_date方法测试失败: {e}")
        return False

def test_data_processing():
    """测试数据处理流程"""
    print("\n🔧 2. 测试数据处理流程")
    print("-" * 60)
    
    try:
        # 设置环境变量
        os.environ['NON_INTERACTIVE'] = '1'
        os.environ['AUTO_DUPLICATE_HANDLING'] = 'overwrite'
        os.environ['AUTO_MISSING_HANDLING'] = 'ignore'
        
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建包含时间的测试数据
        test_data = pd.DataFrame({
            'Order time': ['2025-07-07 00:00:00', '2025-07-08 12:30:45'],  # 包含时间
            'Order No.': ['ORD001', 'ORD002'],
            'Transaction Num': ['TXN001', 'TXN002'],
        })
        
        print(f"📊 原始数据Order time: {list(test_data['Order time'])}")
        
        # 数据清洗
        cleaned_data = processor._clean_data(test_data.copy())
        print(f"📊 清洗后Order_time: {list(cleaned_data.get('Order_time', []))}")
        
        # 数据标准化
        standardized_data = processor._standardize_data_types(cleaned_data.copy())
        
        if 'Order_time' in standardized_data.columns:
            order_times = list(standardized_data['Order_time'])
            print(f"📊 标准化后Order_time: {order_times}")
            
            # 检查是否只包含日期
            all_date_only = True
            for order_time in order_times:
                if pd.notna(order_time):
                    time_str = str(order_time)
                    if len(time_str) != 10 or time_str.count('-') != 2:
                        all_date_only = False
                        print(f"❌ 发现包含时间: '{time_str}'")
                        break
            
            if all_date_only:
                print("✅ 所有Order_time都只包含日期")
                return True
            else:
                print("❌ 发现包含时间的Order_time")
                return False
        else:
            print("❌ Order_time列不存在")
            return False
            
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_current_database():
    """检查当前数据库中的Order_time格式"""
    print("\n🔧 3. 检查当前数据库中的Order_time格式")
    print("-" * 60)
    
    try:
        import sqlite3
        
        db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
        
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查主要表的Order_time格式
        tables_to_check = ['IOT_Sales', 'IOT_Sales_Close', 'IOT_Sales_Refunding']
        
        for table_name in tables_to_check:
            try:
                # 检查表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                if not cursor.fetchone():
                    print(f"⚠️ 表 {table_name} 不存在")
                    continue
                
                # 获取最新的5条记录的Order_time
                cursor.execute(f"""
                    SELECT Order_time, rowid 
                    FROM {table_name} 
                    WHERE Order_time IS NOT NULL 
                    ORDER BY rowid DESC 
                    LIMIT 5
                """)
                
                records = cursor.fetchall()
                
                if records:
                    print(f"\n📋 表 {table_name} 最新的Order_time格式:")
                    has_time = False
                    
                    for i, (order_time, rowid) in enumerate(records, 1):
                        time_str = str(order_time)
                        print(f"  {i}. '{time_str}' (长度: {len(time_str)}, rowid: {rowid})")
                        
                        # 检查是否包含时间
                        if len(time_str) > 10 and ':' in time_str:
                            has_time = True
                    
                    if has_time:
                        print(f"❌ 表 {table_name} 中发现包含时间的记录")
                    else:
                        print(f"✅ 表 {table_name} 中所有记录都只包含日期")
                else:
                    print(f"⚠️ 表 {table_name} 中没有Order_time数据")
                    
            except Exception as e:
                print(f"❌ 检查表 {table_name} 失败: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 检查Order_time导入格式")
    print("=" * 80)
    print("🎯 目标：确认导入后Order_time是否只包含日期，不包含时间")
    
    tests = [
        ("standardize_date方法", test_standardize_date_method),
        ("数据处理流程", test_data_processing),
        ("当前数据库格式", check_current_database)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 检查通过")
            else:
                print(f"❌ {test_name} 检查失败")
        except Exception as e:
            print(f"❌ {test_name} 检查异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 Order_time格式检查结果")
    print("=" * 80)
    
    print(f"📊 通过检查: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 检查结果：Order_time导入后只包含日期")
        print("✅ 修复成功：不会出现时间部分")
        print("✅ 格式统一：所有记录都是 YYYY-MM-DD 格式")
        print("✅ 数据一致：新旧数据格式匹配")
        
        print("\n💡 具体表现：")
        print("  - 输入：'2025-07-07 12:30:45' → 输出：'2025-07-07'")
        print("  - 输入：'2025-07-07 00:00:00' → 输出：'2025-07-07'")
        print("  - 输入：'2025-07-07' → 输出：'2025-07-07'")
        
    elif passed >= 2:
        print("\n✅ 大部分检查通过")
        print("⚠️ Order_time基本只包含日期，可能有少量例外")
        
    else:
        print("\n❌ 多项检查失败")
        print("🔧 Order_time可能仍包含时间信息")
        print("💡 建议：")
        print("  - 检查standardize_date方法实现")
        print("  - 验证数据处理流程")
        print("  - 确认数据库中的实际格式")
    
    # 最终答案
    print(f"\n🎯 最终答案：")
    if passed == total:
        print("❌ 不会出现时间 - Order_time导入后只包含日期部分")
    else:
        print("⚠️ 可能会出现时间 - 需要进一步检查和修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 检查{'完成' if success else '发现问题'}")
    input("按回车键退出...")
