# -*- coding: utf-8 -*-
"""
数据库备份和恢复管理器
提供自动备份、错误恢复、数据安全保护功能
"""

import os
import shutil
import sqlite3
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any

# 🔧 GUI依赖修复：条件导入GUI组件，避免非GUI环境崩溃
try:
    from tkinter import messagebox
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False
    # 创建模拟的messagebox用于非GUI环境
    class MockMessageBox:
        @staticmethod
        def askyesno(title, message):
            print(f"[GUI模拟] {title}: {message}")
            return False  # 默认不确认

        @staticmethod
        def showinfo(title, message):
            print(f"[信息] {title}: {message}")

        @staticmethod
        def showerror(title, message):
            print(f"[错误] {title}: {message}")

    messagebox = MockMessageBox()

from utils.exceptions import DatabaseError, BackupError
from utils.logger import get_logger


class DatabaseBackupManager:
    """🔧 应用集成优化：增强的数据库备份和恢复管理器"""

    def __init__(self, db_path: str, backup_dir: Optional[str] = None,
                 config_manager=None, gui_updater=None):
        """
        🔧 应用集成优化：支持多种初始化模式的备份管理器

        Args:
            db_path: 数据库文件路径
            backup_dir: 备份目录，如果为None则使用默认目录
            config_manager: 配置管理器（主应用模式）
            gui_updater: GUI更新器（主应用模式）
        """
        # 🔧 参数验证修复：验证数据库路径
        if not db_path or not isinstance(db_path, (str, Path)):
            raise ValueError("数据库路径不能为空且必须是字符串或Path对象")

        self.db_path = Path(db_path)
        self._backup_dir_path = Path(backup_dir) if backup_dir else self.db_path.parent / "backups"
        self.logger = get_logger('backup_manager')

        # 🔧 应用调用兼容：提供字符串格式的backup_dir属性
        self.backup_dir = str(self._backup_dir_path)

        # 🔧 应用集成优化：支持主应用模式
        self.config_manager = config_manager
        self.gui_updater = gui_updater
        self.is_app_mode = config_manager is not None and gui_updater is not None

        # 🔧 冲突解决：集成备份协调器
        try:
            from .backup_coordinator import get_backup_coordinator
            self.coordinator = get_backup_coordinator(str(self.db_path))
            self.logger.info("✅ 备份协调器集成成功")
        except Exception as e:
            self.logger.warning(f"⚠️ 备份协调器集成失败，使用传统锁机制: {e}")
            self.coordinator = None

        # 🔧 备份并发控制修复：添加线程锁防止并发备份冲突
        self._backup_lock = threading.Lock()
        self._restore_lock = threading.Lock()

        # 🔧 目录创建修复：安全地创建备份目录
        try:
            self._backup_dir_path.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"备份目录已确保存在: {self._backup_dir_path}")
        except (OSError, PermissionError) as e:
            self.logger.error(f"无法创建备份目录 {self._backup_dir_path}: {e}")
            # 尝试使用临时目录作为备份
            import tempfile
            temp_backup_dir = Path(tempfile.gettempdir()) / "db_backups"
            try:
                temp_backup_dir.mkdir(parents=True, exist_ok=True)
                self._backup_dir_path = temp_backup_dir
                self.backup_dir = str(self._backup_dir_path)
                self.logger.warning(f"使用临时备份目录: {self._backup_dir_path}")
            except Exception as temp_error:
                self.logger.critical(f"无法创建任何备份目录: {temp_error}")
                raise BackupError(f"无法创建备份目录: {e}")
        except Exception as e:
            self.logger.critical(f"创建备份目录时发生未知错误: {e}")
            raise BackupError(f"创建备份目录失败: {e}")

        # 🔧 配置获取修复：安全地从配置获取最大备份数量
        if self.config_manager:
            try:
                backup_config = self.config_manager.get('Backup', 'max_backup_files', '20')
                self.max_backups = int(backup_config)
                # 验证配置值的合理性
                if self.max_backups < 1:
                    self.logger.warning(f"最大备份数量配置无效 ({self.max_backups})，使用默认值20")
                    self.max_backups = 20
                elif self.max_backups > 100:
                    self.logger.warning(f"最大备份数量过大 ({self.max_backups})，限制为100")
                    self.max_backups = 100
            except (ValueError, TypeError, AttributeError) as e:
                self.logger.warning(f"获取备份配置失败: {e}，使用默认值20")
                self.max_backups = 20
            except Exception as e:
                self.logger.error(f"配置获取时发生未知错误: {e}，使用默认值20")
                self.max_backups = 20
        else:
            self.max_backups = 20

        mode_info = "主应用模式" if self.is_app_mode else "独立脚本模式"
        self.logger.info(f"备份管理器已初始化 ({mode_info}) - 数据库: {self.db_path}, 备份目录: {self.backup_dir}")
    
    def create_backup(self, operation_name: str = "手动备份") -> Optional[str]:
        """
        🔧 应用集成优化：统一的数据库备份创建接口

        Args:
            operation_name: 操作名称，用于备份文件命名

        Returns:
            备份文件路径，失败时返回None
        """
        # 🔧 冲突解决：使用协调器或传统锁机制
        if self.coordinator:
            return self._coordinated_backup(operation_name)
        else:
            return self._traditional_backup(operation_name)

    def _coordinated_backup(self, operation_name: str) -> Optional[str]:
        """🔧 备份功能修复：协调式备份实现，失败时返回None而不抛出异常"""
        try:
            with self.coordinator.safe_backup_operation(operation_name, "script"):
                if not self.db_path.exists():
                    self.logger.warning(f"数据库文件不存在，无法备份: {self.db_path}")
                    return None

                # 🔧 数据库诊断增强：协调式备份前诊断源数据库
                self.logger.info("开始协调式备份前数据库诊断...")
                pre_backup_diagnosis = self.diagnose_database(self.db_path, "standard")

                if pre_backup_diagnosis["overall_status"] == "error":
                    self.logger.error("协调式备份前诊断发现严重问题，建议修复后再备份")
                    for issue in pre_backup_diagnosis["issues"]:
                        self.logger.error(f"  - {issue}")

                    # 询问用户是否继续
                    if GUI_AVAILABLE:
                        continue_backup = messagebox.askyesno(
                            "数据库诊断警告",
                            f"备份前诊断发现以下问题：\n" +
                            "\n".join(pre_backup_diagnosis["issues"]) +
                            "\n\n是否仍要继续备份？"
                        )
                        if not continue_backup:
                            self.logger.info("用户选择取消协调式备份")
                            return None
                    else:
                        self.logger.warning("发现数据库问题但继续协调式备份（非GUI模式）")

                elif pre_backup_diagnosis["overall_status"] == "warning":
                    self.logger.warning("协调式备份前诊断发现警告")
                    for warning in pre_backup_diagnosis["warnings"]:
                        self.logger.warning(f"  - {warning}")
                else:
                    self.logger.info("协调式备份前诊断通过，数据库状态健康")

                # 🔧 时间同步修复：生成安全的备份文件名并获取时间戳
                backup_filename, backup_timestamp = self.coordinator.generate_safe_backup_filename(operation_name, "script")
                backup_path = self._backup_dir_path / backup_filename

                # 🔧 文件锁定修复：安全的文件复制
                try:
                    self._safe_copy_file(self.db_path, backup_path)

                    # 🔧 时间同步修复：同步文件时间戳，确保文件时间与文件名时间一致
                    self.coordinator.sync_file_timestamp(backup_path, backup_timestamp)

                except Exception as copy_error:
                    self.logger.error(f"文件复制失败: {copy_error}")
                    return None

                # 🔧 备份验证修复：多级验证机制
                verification_passed = False

                # 尝试完整验证
                try:
                    if self._verify_backup(backup_path):
                        verification_passed = True
                        self.logger.info(f"完整验证通过: {backup_path}")
                except Exception as verify_error:
                    self.logger.warning(f"完整验证失败，尝试简化验证: {verify_error}")

                    # 尝试简化验证
                    try:
                        if self._simple_verify_backup(backup_path):
                            verification_passed = True
                            self.logger.info(f"简化验证通过: {backup_path}")
                    except Exception as simple_verify_error:
                        self.logger.error(f"简化验证也失败: {simple_verify_error}")

                if verification_passed:
                    # 🔧 数据库诊断增强：协调式备份后诊断备份文件
                    self.logger.info("开始协调式备份后数据库诊断...")
                    post_backup_diagnosis = self.diagnose_database(backup_path, "standard")

                    if post_backup_diagnosis["overall_status"] == "error":
                        self.logger.error("协调式备份文件诊断发现严重问题")
                        for issue in post_backup_diagnosis["issues"]:
                            self.logger.error(f"  - {issue}")

                        # 删除有问题的备份文件
                        try:
                            backup_path.unlink()
                            self.logger.warning("已删除有问题的协调式备份文件")
                        except Exception as delete_error:
                            self.logger.error(f"删除有问题的协调式备份文件失败: {delete_error}")

                        return None

                    elif post_backup_diagnosis["overall_status"] == "warning":
                        self.logger.warning("协调式备份文件诊断发现警告")
                        for warning in post_backup_diagnosis["warnings"]:
                            self.logger.warning(f"  - {warning}")
                    else:
                        self.logger.info("协调式备份文件诊断通过，备份质量良好")

                    self.logger.info(f"协调式备份成功: {backup_path}")

                    # 清理旧备份
                    try:
                        self._cleanup_old_backups()
                    except Exception as cleanup_error:
                        self.logger.warning(f"清理旧备份失败: {cleanup_error}")

                    return str(backup_path)
                else:
                    # 🔧 备份功能修复：验证失败时返回None而不抛出异常
                    self.logger.error(f"备份文件验证失败: {backup_path}")
                    # 删除无效备份
                    try:
                        backup_path.unlink(missing_ok=True)
                    except Exception as delete_error:
                        self.logger.warning(f"删除无效备份失败: {delete_error}")
                    return None

        except Exception as e:
            # 🔧 备份功能修复：异常时返回None而不抛出异常
            self.logger.error(f"协调式备份失败: {e}")
            return None

        finally:
            # 🔧 属性名修复：确保无论成功还是失败都释放锁
            try:
                if hasattr(self, 'coordinator') and self.coordinator:
                    # 检查协调器是否有释放锁的方法
                    if hasattr(self.coordinator, 'release_backup_lock'):
                        self.coordinator.release_backup_lock()
            except Exception as release_error:
                self.logger.warning(f"释放备份锁时出错: {release_error}")

    def _traditional_backup(self, operation_name: str) -> Optional[str]:
        """🔧 备份功能修复：传统备份实现，失败时返回None而不抛出异常"""
        # 🔧 备份并发控制修复：使用锁防止并发备份冲突
        with self._backup_lock:
            try:
                if not self.db_path.exists():
                    self.logger.warning(f"数据库文件不存在，无法备份: {self.db_path}")
                    return None

                # 🔧 数据库诊断增强：备份前诊断源数据库
                self.logger.info("开始备份前数据库诊断...")
                pre_backup_diagnosis = self.diagnose_database(self.db_path, "standard")

                if pre_backup_diagnosis["overall_status"] == "error":
                    self.logger.error("备份前诊断发现严重问题，建议修复后再备份")
                    for issue in pre_backup_diagnosis["issues"]:
                        self.logger.error(f"  - {issue}")

                    # 询问用户是否继续
                    if GUI_AVAILABLE:
                        continue_backup = messagebox.askyesno(
                            "数据库诊断警告",
                            f"备份前诊断发现以下问题：\n" +
                            "\n".join(pre_backup_diagnosis["issues"]) +
                            "\n\n是否仍要继续备份？"
                        )
                        if not continue_backup:
                            self.logger.info("用户选择取消备份")
                            return None
                    else:
                        self.logger.warning("发现数据库问题但继续备份（非GUI模式）")

                elif pre_backup_diagnosis["overall_status"] == "warning":
                    self.logger.warning("备份前诊断发现警告")
                    for warning in pre_backup_diagnosis["warnings"]:
                        self.logger.warning(f"  - {warning}")
                else:
                    self.logger.info("备份前诊断通过，数据库状态健康")

                # 🔧 时间同步修复：智能备份命名策略，获取时间戳
                try:
                    backup_filename, backup_timestamp = self._generate_backup_filename(operation_name)
                except Exception as name_error:
                    self.logger.error(f"生成备份文件名失败: {name_error}")
                    return None

                backup_path = self._backup_dir_path / backup_filename

                # 🔧 文件锁定修复：安全的文件复制
                try:
                    self._safe_copy_file(self.db_path, backup_path)

                    # 🔧 时间同步修复：同步文件时间戳，确保文件时间与文件名时间一致
                    self._sync_file_timestamp(backup_path, backup_timestamp)

                except Exception as copy_error:
                    self.logger.error(f"文件复制失败: {copy_error}")
                    return None

                # 🔧 备份验证修复：多级验证机制
                verification_passed = False

                # 尝试完整验证
                try:
                    if self._verify_backup(backup_path):
                        verification_passed = True
                        self.logger.info(f"完整验证通过: {backup_path}")
                except Exception as verify_error:
                    self.logger.warning(f"完整验证失败，尝试简化验证: {verify_error}")

                    # 尝试简化验证
                    try:
                        if self._simple_verify_backup(backup_path):
                            verification_passed = True
                            self.logger.info(f"简化验证通过: {backup_path}")
                    except Exception as simple_verify_error:
                        self.logger.error(f"简化验证也失败: {simple_verify_error}")

                if verification_passed:
                    # 🔧 数据库诊断增强：备份后诊断备份文件
                    self.logger.info("开始备份后数据库诊断...")
                    post_backup_diagnosis = self.diagnose_database(backup_path, "standard")

                    if post_backup_diagnosis["overall_status"] == "error":
                        self.logger.error("备份文件诊断发现严重问题")
                        for issue in post_backup_diagnosis["issues"]:
                            self.logger.error(f"  - {issue}")

                        # 删除有问题的备份文件
                        try:
                            backup_path.unlink()
                            self.logger.warning("已删除有问题的备份文件")
                        except Exception as delete_error:
                            self.logger.error(f"删除有问题的备份文件失败: {delete_error}")

                        return None

                    elif post_backup_diagnosis["overall_status"] == "warning":
                        self.logger.warning("备份文件诊断发现警告")
                        for warning in post_backup_diagnosis["warnings"]:
                            self.logger.warning(f"  - {warning}")
                    else:
                        self.logger.info("备份文件诊断通过，备份质量良好")

                    self.logger.info(f"传统备份成功: {backup_path}")

                    # 清理旧备份
                    try:
                        self._cleanup_old_backups()
                    except Exception as cleanup_error:
                        self.logger.warning(f"清理旧备份失败: {cleanup_error}")

                    return str(backup_path)
                else:
                    # 🔧 备份功能修复：验证失败时返回None而不抛出异常
                    self.logger.error(f"备份文件验证失败: {backup_path}")
                    # 删除无效备份
                    try:
                        backup_path.unlink(missing_ok=True)
                    except Exception as delete_error:
                        self.logger.warning(f"删除无效备份失败: {delete_error}")
                    return None

            except Exception as e:
                # 🔧 备份功能修复：异常时返回None而不抛出异常
                self.logger.error(f"创建数据库备份失败: {e}")
                return None

    def _generate_backup_filename(self, operation_name: str) -> tuple[str, datetime]:
        """
        🔧 时间同步修复：人性化的备份文件命名，返回文件名和时间戳

        Args:
            operation_name: 操作名称

        Returns:
            tuple: (人性化的备份文件名, 时间戳对象)
        """
        try:
            # 🔧 时间同步修复：生成统一的时间戳对象
            timestamp_obj = datetime.now()
            timestamp_str = timestamp_obj.strftime("%Y%m%d_%H%M%S")

            # 🔧 备份功能优化：人性化的操作名称处理
            human_readable_name = self._create_human_readable_name(operation_name)

            # 🔧 备份功能优化：简洁的文件名格式
            filename = f"backup_{human_readable_name}_{timestamp_str}.db"

            # 确保文件名不会冲突
            final_filename = self._ensure_unique_filename(filename)

            return final_filename, timestamp_obj

        except Exception as e:
            # 最后的回退方案
            self.logger.warning(f"备份命名失败，使用默认格式: {e}")
            timestamp_obj = datetime.now()
            timestamp_str = timestamp_obj.strftime("%Y%m%d_%H%M%S")
            return f"backup_手动备份_{timestamp_str}.db", timestamp_obj

    def _sync_file_timestamp(self, file_path: Path, target_timestamp: datetime):
        """
        🔧 时间同步修复：同步文件时间戳，使其与文件名中的时间戳一致

        Args:
            file_path: 文件路径
            target_timestamp: 目标时间戳
        """
        try:
            import os
            # 将datetime转换为时间戳
            timestamp = target_timestamp.timestamp()

            # 设置文件的访问时间和修改时间
            os.utime(file_path, (timestamp, timestamp))

            self.logger.info(f"文件时间戳已同步: {file_path} -> {target_timestamp}")

        except Exception as e:
            self.logger.warning(f"同步文件时间戳失败: {file_path}, {e}")

    def _create_human_readable_name(self, operation_name: str) -> str:
        """
        🔧 备份功能优化：创建人性化的操作名称

        Args:
            operation_name: 原始操作名称

        Returns:
            人性化的操作名称
        """
        # 🔧 备份功能优化：操作名称映射表
        name_mapping = {
            # 导入相关
            "数据导入": "数据导入",
            "导入前": "导入前备份",
            "导入操作": "数据导入",
            "数据导入操作": "数据导入",

            # 退款相关
            "退款处理": "退款处理",
            "退款前": "退款前备份",
            "退款操作": "退款处理",

            # 手动备份
            "创建备份": "手动备份",
            "手动备份": "手动备份",
            "数据库操作": "手动备份",

            # 恢复相关
            "恢复前备份": "恢复前备份",
            "恢复操作": "恢复前备份",

            # 其他
            "数据修复": "数据修复",
            "系统维护": "系统维护"
        }

        # 🔧 备份功能优化：智能匹配操作类型
        for key, value in name_mapping.items():
            if key in operation_name:
                return value

        # 🔧 备份功能优化：处理文件名相关的备份
        if "导入前" in operation_name and ".xlsx" in operation_name:
            # 提取文件名部分
            import re
            file_match = re.search(r'([^\\\/]+\.xlsx)', operation_name)
            if file_match:
                filename = file_match.group(1).replace('.xlsx', '')
                return f"{filename}导入前"

        # 🔧 备份功能优化：保留中文字符，创建可读的名称
        safe_chars = []
        for char in operation_name:
            if char.isalnum() or char in ['_', '-', '导', '入', '退', '款', '备', '份', '手', '动', '前', '后', '处', '理', '数', '据', '恢', '复', '修', '系', '统', '维', '护']:
                safe_chars.append(char)
            elif char in [' ', '　']:  # 空格替换为下划线
                safe_chars.append('_')

        safe_name = ''.join(safe_chars)

        # 清理连续的下划线
        while '__' in safe_name:
            safe_name = safe_name.replace('__', '_')

        # 移除首尾下划线
        safe_name = safe_name.strip('_')

        # 如果名称为空或过短，使用默认名称
        if len(safe_name) < 2:
            safe_name = "手动备份"

        return safe_name

    def _ensure_unique_filename(self, filename: str) -> str:
        """
        🔧 备份功能优化：确保文件名唯一性

        Args:
            filename: 原始文件名

        Returns:
            唯一的文件名
        """
        if not (self._backup_dir_path / filename).exists():
            return filename

        # 分离文件名和扩展名
        name_part, ext = os.path.splitext(filename)

        # 添加序号后缀
        counter = 1
        while True:
            new_filename = f"{name_part}_{counter:02d}{ext}"
            if not (self._backup_dir_path / new_filename).exists():
                return new_filename
            counter += 1

            # 防止无限循环
            if counter > 999:
                timestamp_suffix = str(int(time.time() * 1000) % 100000)
                return f"{name_part}_{timestamp_suffix}{ext}"

    def _safe_copy_file(self, source_path, dest_path, max_retries=3):
        """
        🔧 数据安全修复：原子性文件复制，确保数据完整性

        Args:
            source_path: 源文件路径
            dest_path: 目标文件路径
            max_retries: 最大重试次数
        """
        import time
        import tempfile

        for attempt in range(max_retries):
            temp_path = None
            try:
                # 确保目标目录存在
                dest_path.parent.mkdir(parents=True, exist_ok=True)

                # 🔧 资源管理修复：使用临时文件进行原子性复制
                with tempfile.NamedTemporaryFile(
                    dir=dest_path.parent,
                    prefix=f".{dest_path.name}.tmp",
                    delete=False
                ) as temp_file:
                    temp_path = temp_file.name

                    # 使用二进制模式复制文件，避免编码问题
                    with open(source_path, 'rb') as src:
                        # 分块复制，避免大文件内存问题
                        chunk_size = 64 * 1024  # 64KB
                        while True:
                            chunk = src.read(chunk_size)
                            if not chunk:
                                break
                            temp_file.write(chunk)

                    # 确保数据写入磁盘
                    temp_file.flush()
                    os.fsync(temp_file.fileno())

                # 复制文件属性到临时文件
                import shutil
                shutil.copystat(source_path, temp_path)

                # 🔧 数据安全修复：验证复制的完整性
                if not self._verify_file_integrity(source_path, temp_path):
                    raise IOError("文件复制完整性验证失败")

                # 🔧 数据安全修复：原子性移动到最终位置
                if dest_path.exists():
                    # 如果目标文件已存在，先备份
                    backup_path = str(dest_path) + ".backup"
                    shutil.move(str(dest_path), backup_path)

                    try:
                        shutil.move(temp_path, str(dest_path))
                        # 成功后删除备份
                        os.unlink(backup_path)
                    except Exception as move_error:
                        # 移动失败，恢复备份
                        if os.path.exists(backup_path):
                            shutil.move(backup_path, str(dest_path))
                        raise move_error
                else:
                    shutil.move(temp_path, str(dest_path))

                self.logger.debug(f"文件复制成功: {source_path} -> {dest_path}")
                return

            except (OSError, IOError) as e:
                # 🔧 资源管理修复：清理临时文件
                if temp_path and os.path.exists(temp_path):
                    try:
                        os.unlink(temp_path)
                    except Exception as cleanup_error:
                        self.logger.warning(f"清理临时文件失败: {cleanup_error}")

                if attempt < max_retries - 1:
                    self.logger.warning(f"文件复制失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(0.5 * (attempt + 1))  # 递增延迟
                else:
                    self.logger.error(f"文件复制最终失败: {e}")
                    raise

    def _verify_file_integrity(self, source_path, dest_path) -> bool:
        """
        🔧 数据安全修复：验证文件复制的完整性

        Args:
            source_path: 源文件路径
            dest_path: 目标文件路径

        Returns:
            文件是否完整
        """
        try:
            # 比较文件大小
            source_size = os.path.getsize(source_path)
            dest_size = os.path.getsize(dest_path)

            if source_size != dest_size:
                self.logger.error(f"文件大小不匹配: 源文件{source_size}字节, 目标文件{dest_size}字节")
                return False

            # 对于小文件，比较内容
            if source_size < 10 * 1024 * 1024:  # 小于10MB
                with open(source_path, 'rb') as src, open(dest_path, 'rb') as dst:
                    chunk_size = 64 * 1024
                    while True:
                        src_chunk = src.read(chunk_size)
                        dst_chunk = dst.read(chunk_size)

                        if src_chunk != dst_chunk:
                            self.logger.error("文件内容不匹配")
                            return False

                        if not src_chunk:  # 到达文件末尾
                            break

            return True

        except Exception as e:
            self.logger.error(f"文件完整性验证失败: {e}")
            return False

    def _verify_backup(self, backup_path: Path) -> bool:
        """
        🔧 数据库诊断增强：使用诊断功能验证备份文件的完整性

        Args:
            backup_path: 备份文件路径

        Returns:
            备份是否有效
        """
        try:
            # 🔧 数据库诊断增强：使用诊断功能进行全面验证
            self.logger.debug(f"开始验证备份文件: {backup_path}")

            # 执行标准级别的诊断
            diagnosis = self.diagnose_database(backup_path, "standard")

            # 根据诊断结果判断备份是否有效
            if diagnosis["overall_status"] == "healthy":
                self.logger.debug(f"备份验证成功: {backup_path}")
                return True
            elif diagnosis["overall_status"] == "warning":
                # 有警告但仍可用
                self.logger.warning(f"备份文件有警告但仍可用: {backup_path}")
                warning_count = len(diagnosis.get("warnings", []))

                # 如果警告数量不多，认为备份仍然有效
                if warning_count <= 2:
                    self.logger.info(f"备份验证通过（宽松模式）: {backup_path}, 警告数: {warning_count}")
                    return True
                else:
                    self.logger.warning(f"备份文件警告过多，可能不安全: {backup_path}, 警告数: {warning_count}")
                    return False
            else:
                # 有严重错误
                self.logger.error(f"备份文件验证失败: {backup_path}")
                for issue in diagnosis.get("issues", []):
                    self.logger.error(f"  - {issue}")
                return False

        except Exception as e:
            self.logger.error(f"备份文件验证过程出错: {backup_path}, 错误: {e}")

            # 🔧 降级处理：如果诊断失败，使用传统验证方法
            self.logger.warning("诊断验证失败，尝试传统验证方法...")
            return self._legacy_verify_backup(backup_path)

    def _legacy_verify_backup(self, backup_path: Path) -> bool:
        """
        🔧 数据库诊断增强：传统的备份验证方法（作为降级选项）

        Args:
            backup_path: 备份文件路径

        Returns:
            备份是否有效
        """
        try:
            # 🔧 文件锁定修复：确保数据库连接完全关闭
            conn = None
            try:
                # 尝试连接备份数据库
                conn = sqlite3.connect(str(backup_path), timeout=5)
                cursor = conn.cursor()

                # 检查数据库完整性
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()

                # 🔧 完整性检查修复：对于有轻微完整性问题但仍可用的数据库，使用宽松验证
                if result and result[0] == 'ok':
                    # 完整性检查完全通过
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()

                    if tables:
                        self.logger.debug(f"传统备份验证成功: {backup_path}, 包含 {len(tables)} 个表")
                        return True
                    else:
                        self.logger.warning(f"备份文件为空: {backup_path}")
                        return False
                elif result and 'out of order' in result[0]:
                    # 有轻微的行ID顺序问题，但数据库仍然可用
                    self.logger.warning(f"备份文件有轻微完整性问题但仍可用: {backup_path}")

                    # 检查是否有表和基本功能
                    try:
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                        tables = cursor.fetchall()

                        if tables:
                            self.logger.info(f"传统备份验证通过（宽松模式）: {backup_path}, 包含 {len(tables)} 个表")
                            return True
                        else:
                            self.logger.warning(f"备份文件为空: {backup_path}")
                            return False
                    except Exception as table_error:
                        self.logger.error(f"无法读取备份文件表结构: {table_error}")
                        return False
                else:
                    self.logger.error(f"备份文件完整性检查失败: {backup_path}, 结果: {result}")
                    return False
            finally:
                # 确保连接被关闭
                if conn:
                    conn.close()
                # 强制垃圾回收
                import gc
                gc.collect()

        except Exception as e:
            self.logger.error(f"传统备份文件验证失败: {backup_path}, 错误: {e}")
            # 🔧 调试信息增强：提供更详细的错误信息
            import traceback
            self.logger.debug(f"传统备份验证异常详情: {traceback.format_exc()}")

            # 检查文件是否存在和基本信息
            try:
                if backup_path.exists():
                    file_size = backup_path.stat().st_size
                    self.logger.error(f"备份文件存在但验证失败，大小: {file_size} bytes")
                else:
                    self.logger.error(f"备份文件不存在: {backup_path}")
            except Exception as check_error:
                self.logger.error(f"无法检查备份文件状态: {check_error}")

            return False

    def _simple_verify_backup(self, backup_path: Path) -> bool:
        """
        🔧 备份验证修复：简化的备份验证方法

        Args:
            backup_path: 备份文件路径

        Returns:
            备份是否有效
        """
        try:
            # 基本文件检查
            if not backup_path.exists():
                self.logger.error(f"备份文件不存在: {backup_path}")
                return False

            # 文件大小检查
            file_size = backup_path.stat().st_size
            if file_size < 1024:  # 小于1KB
                self.logger.error(f"备份文件太小: {file_size} bytes")
                return False

            # 尝试简单的SQLite文件头检查
            try:
                with open(backup_path, 'rb') as f:
                    header = f.read(16)
                    if header.startswith(b'SQLite format 3'):
                        self.logger.info(f"简化验证通过: {backup_path}, 大小: {file_size:,} bytes")
                        return True
                    else:
                        self.logger.error(f"不是有效的SQLite文件: {backup_path}")
                        return False
            except Exception as header_error:
                self.logger.error(f"文件头检查失败: {header_error}")
                return False

        except Exception as e:
            self.logger.error(f"简化备份验证失败: {backup_path}, 错误: {e}")
            return False

    def diagnose_database(self, db_path: Path, diagnostic_level: str = "full") -> Dict[str, Any]:
        """
        🔧 数据库诊断增强：全面的数据库健康检查

        Args:
            db_path: 数据库文件路径
            diagnostic_level: 诊断级别 ("quick", "standard", "full", "deep")

        Returns:
            诊断结果字典
        """
        diagnosis = {
            "db_path": str(db_path),
            "timestamp": datetime.now().isoformat(),
            "diagnostic_level": diagnostic_level,
            "overall_status": "unknown",
            "issues": [],
            "warnings": [],
            "info": [],
            "checks": {}
        }

        try:
            if not db_path.exists():
                diagnosis["overall_status"] = "error"
                diagnosis["issues"].append("数据库文件不存在")
                return diagnosis

            # 检查文件大小
            file_size = db_path.stat().st_size
            diagnosis["info"].append(f"数据库文件大小: {file_size:,} 字节")

            if file_size == 0:
                diagnosis["overall_status"] = "error"
                diagnosis["issues"].append("数据库文件为空")
                return diagnosis

            # 连接数据库进行诊断
            conn = None
            try:
                conn = sqlite3.connect(str(db_path), timeout=10)
                cursor = conn.cursor()

                # 1. 基本连接测试
                diagnosis["checks"]["connection"] = self._check_database_connection(cursor)

                # 2. SQLite版本和编译选项
                diagnosis["checks"]["version"] = self._check_sqlite_version(cursor)

                # 3. 数据库模式检查
                diagnosis["checks"]["schema"] = self._check_database_schema(cursor)

                if diagnostic_level in ["standard", "full", "deep"]:
                    # 4. 完整性检查
                    diagnosis["checks"]["integrity"] = self._check_database_integrity(cursor)

                    # 5. 外键检查
                    diagnosis["checks"]["foreign_keys"] = self._check_foreign_keys(cursor)

                if diagnostic_level in ["full", "deep"]:
                    # 6. 表统计信息
                    diagnosis["checks"]["table_stats"] = self._check_table_statistics(cursor)

                    # 7. 索引检查
                    diagnosis["checks"]["indexes"] = self._check_indexes(cursor)

                if diagnostic_level == "deep":
                    # 8. 深度数据一致性检查
                    diagnosis["checks"]["data_consistency"] = self._check_data_consistency(cursor)

                    # 9. 性能相关检查
                    diagnosis["checks"]["performance"] = self._check_performance_metrics(cursor)

                # 汇总诊断结果
                diagnosis = self._summarize_diagnosis(diagnosis)

            finally:
                # 🔧 异常处理修复：安全地关闭连接，避免掩盖原始异常
                if conn:
                    try:
                        conn.close()
                    except Exception as close_error:
                        self.logger.warning(f"关闭诊断数据库连接时出错: {close_error}")
                        # 不重新抛出异常，避免掩盖原始异常

        except Exception as e:
            diagnosis["overall_status"] = "error"
            diagnosis["issues"].append(f"诊断过程出错: {e}")
            self.logger.error(f"数据库诊断失败: {e}")

        return diagnosis

    def _check_database_connection(self, cursor) -> Dict[str, Any]:
        """检查数据库连接"""
        try:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            return {
                "status": "ok" if result and result[0] == 1 else "error",
                "message": "数据库连接正常" if result and result[0] == 1 else "数据库连接异常"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"连接测试失败: {e}"
            }

    def _check_sqlite_version(self, cursor) -> Dict[str, Any]:
        """检查SQLite版本信息"""
        try:
            cursor.execute("SELECT sqlite_version()")
            version = cursor.fetchone()[0]

            cursor.execute("PRAGMA compile_options")
            compile_options = [row[0] for row in cursor.fetchall()]

            return {
                "status": "ok",
                "version": version,
                "compile_options": compile_options,
                "message": f"SQLite版本: {version}"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"版本检查失败: {e}"
            }

    def _check_database_schema(self, cursor) -> Dict[str, Any]:
        """检查数据库模式"""
        try:
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            # 获取所有视图
            cursor.execute("SELECT name FROM sqlite_master WHERE type='view'")
            views = [row[0] for row in cursor.fetchall()]

            # 获取所有索引
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
            indexes = [row[0] for row in cursor.fetchall()]

            # 获取所有触发器
            cursor.execute("SELECT name FROM sqlite_master WHERE type='trigger'")
            triggers = [row[0] for row in cursor.fetchall()]

            schema_info = {
                "tables": tables,
                "views": views,
                "indexes": indexes,
                "triggers": triggers,
                "table_count": len(tables),
                "view_count": len(views),
                "index_count": len(indexes),
                "trigger_count": len(triggers)
            }

            return {
                "status": "ok",
                "schema": schema_info,
                "message": f"发现 {len(tables)} 个表, {len(views)} 个视图, {len(indexes)} 个索引, {len(triggers)} 个触发器"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"模式检查失败: {e}"
            }

    def _check_database_integrity(self, cursor) -> Dict[str, Any]:
        """检查数据库完整性"""
        try:
            # 完整性检查
            cursor.execute("PRAGMA integrity_check")
            integrity_results = cursor.fetchall()

            # 快速检查
            cursor.execute("PRAGMA quick_check")
            quick_results = cursor.fetchall()

            integrity_ok = len(integrity_results) == 1 and integrity_results[0][0] == 'ok'
            quick_ok = len(quick_results) == 1 and quick_results[0][0] == 'ok'

            status = "ok" if integrity_ok and quick_ok else "warning" if quick_ok else "error"

            return {
                "status": status,
                "integrity_check": integrity_results,
                "quick_check": quick_results,
                "integrity_ok": integrity_ok,
                "quick_ok": quick_ok,
                "message": "数据库完整性正常" if integrity_ok else f"发现完整性问题: {integrity_results}"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"完整性检查失败: {e}"
            }

    def _check_foreign_keys(self, cursor) -> Dict[str, Any]:
        """检查外键约束"""
        try:
            # 检查外键约束
            cursor.execute("PRAGMA foreign_key_check")
            fk_violations = cursor.fetchall()

            # 获取外键设置
            cursor.execute("PRAGMA foreign_keys")
            fk_enabled = cursor.fetchone()[0] == 1

            return {
                "status": "ok" if len(fk_violations) == 0 else "warning",
                "foreign_keys_enabled": fk_enabled,
                "violations": fk_violations,
                "violation_count": len(fk_violations),
                "message": "外键约束正常" if len(fk_violations) == 0 else f"发现 {len(fk_violations)} 个外键违规"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"外键检查失败: {e}"
            }

    def _check_table_statistics(self, cursor) -> Dict[str, Any]:
        """检查表统计信息"""
        try:
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            table_stats = {}
            total_rows = 0

            for table in tables:
                try:
                    # 🔧 SQL安全修复：验证表名安全性
                    if not self._is_safe_table_name(table):
                        table_stats[table] = {
                            "error": "表名包含不安全字符"
                        }
                        continue

                    # 🔧 性能优化：获取行数 - 使用引号包围表名以防止SQL注入
                    safe_table_name = table.replace('`', '``')  # 转义反引号

                    # 对于大表，使用ANALYZE表的统计信息（如果可用）
                    try:
                        cursor.execute(f"SELECT stat FROM sqlite_stat1 WHERE tbl = '{safe_table_name}' AND idx IS NULL")
                        stat_result = cursor.fetchone()
                        if stat_result:
                            row_count = int(stat_result[0])
                        else:
                            # 如果没有统计信息，使用COUNT(*)，但设置超时
                            cursor.execute(f"SELECT COUNT(*) FROM `{safe_table_name}`")
                            row_count = cursor.fetchone()[0]
                    except:
                        # 如果统计信息查询失败，回退到COUNT(*)
                        cursor.execute(f"SELECT COUNT(*) FROM `{safe_table_name}`")
                        row_count = cursor.fetchone()[0]

                    total_rows += row_count

                    # 获取表信息 - PRAGMA语句相对安全，但仍然转义
                    cursor.execute(f"PRAGMA table_info(`{safe_table_name}`)")
                    columns = cursor.fetchall()

                    table_stats[table] = {
                        "row_count": row_count,
                        "column_count": len(columns),
                        "columns": [col[1] for col in columns]  # 列名
                    }
                except Exception as table_error:
                    table_stats[table] = {
                        "error": str(table_error)
                    }

            return {
                "status": "ok",
                "table_count": len(tables),
                "total_rows": total_rows,
                "table_stats": table_stats,
                "message": f"统计了 {len(tables)} 个表，总计 {total_rows:,} 行数据"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"表统计失败: {e}"
            }

    def _check_indexes(self, cursor) -> Dict[str, Any]:
        """检查索引"""
        try:
            # 获取所有索引
            cursor.execute("SELECT name, tbl_name, sql FROM sqlite_master WHERE type='index'")
            indexes = cursor.fetchall()

            index_info = []
            for index_name, table_name, sql in indexes:
                if index_name.startswith('sqlite_autoindex_'):
                    continue  # 跳过自动索引

                try:
                    # 🔧 SQL安全修复：验证索引名安全性
                    if not self._is_safe_identifier_name(index_name):
                        index_info.append({
                            "name": index_name,
                            "table": table_name,
                            "error": "索引名包含不安全字符"
                        })
                        continue

                    # 检查索引完整性 - 转义索引名
                    safe_index_name = index_name.replace('`', '``')
                    cursor.execute(f"PRAGMA index_info(`{safe_index_name}`)")
                    index_columns = cursor.fetchall()

                    index_info.append({
                        "name": index_name,
                        "table": table_name,
                        "sql": sql,
                        "columns": [col[2] for col in index_columns]
                    })
                except Exception as index_error:
                    index_info.append({
                        "name": index_name,
                        "table": table_name,
                        "error": str(index_error)
                    })

            return {
                "status": "ok",
                "index_count": len(index_info),
                "indexes": index_info,
                "message": f"检查了 {len(index_info)} 个索引"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"索引检查失败: {e}"
            }

    def _check_data_consistency(self, cursor) -> Dict[str, Any]:
        """深度数据一致性检查"""
        try:
            consistency_issues = []

            # 检查重复的主键（理论上不应该存在）
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            for table in tables:
                try:
                    # 🔧 SQL安全修复：验证表名安全性
                    if not self._is_safe_table_name(table):
                        consistency_issues.append({
                            "table": table,
                            "issue": "unsafe_table_name",
                            "error": "表名包含不安全字符"
                        })
                        continue

                    # 获取主键信息 - 转义表名
                    safe_table_name = table.replace('`', '``')
                    cursor.execute(f"PRAGMA table_info(`{safe_table_name}`)")
                    columns = cursor.fetchall()
                    pk_columns = [col[1] for col in columns if col[5] > 0]  # pk > 0

                    if pk_columns:
                        # 🔧 SQL安全修复：验证列名安全性
                        safe_pk_columns = []
                        for col in pk_columns:
                            if self._is_safe_identifier_name(col):
                                safe_col = col.replace('`', '``')
                                safe_pk_columns.append(f'`{safe_col}`')
                            else:
                                consistency_issues.append({
                                    "table": table,
                                    "issue": "unsafe_column_name",
                                    "error": f"列名不安全: {col}"
                                })
                                continue

                        if safe_pk_columns:
                            pk_list = ', '.join(safe_pk_columns)
                            cursor.execute(f"""
                                SELECT {pk_list}, COUNT(*) as cnt
                                FROM `{safe_table_name}`
                                GROUP BY {pk_list}
                                HAVING COUNT(*) > 1
                            """)
                            duplicates = cursor.fetchall()

                            if duplicates:
                                consistency_issues.append({
                                    "table": table,
                                    "issue": "duplicate_primary_keys",
                                    "count": len(duplicates),
                                    "details": duplicates[:5]  # 只显示前5个
                                })

                except Exception as table_error:
                    consistency_issues.append({
                        "table": table,
                        "issue": "check_failed",
                        "error": str(table_error)
                    })

            return {
                "status": "ok" if len(consistency_issues) == 0 else "warning",
                "issues": consistency_issues,
                "issue_count": len(consistency_issues),
                "message": "数据一致性正常" if len(consistency_issues) == 0 else f"发现 {len(consistency_issues)} 个一致性问题"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"数据一致性检查失败: {e}"
            }

    def _check_performance_metrics(self, cursor) -> Dict[str, Any]:
        """检查性能相关指标"""
        try:
            metrics = {}

            # 数据库页面大小
            cursor.execute("PRAGMA page_size")
            metrics["page_size"] = cursor.fetchone()[0]

            # 数据库页面数量
            cursor.execute("PRAGMA page_count")
            metrics["page_count"] = cursor.fetchone()[0]

            # 空闲页面数量
            cursor.execute("PRAGMA freelist_count")
            metrics["freelist_count"] = cursor.fetchone()[0]

            # 计算数据库大小
            metrics["db_size_bytes"] = metrics["page_size"] * metrics["page_count"]
            metrics["free_space_bytes"] = metrics["page_size"] * metrics["freelist_count"]
            metrics["utilization_percent"] = round(
                (1 - metrics["freelist_count"] / max(metrics["page_count"], 1)) * 100, 2
            )

            # WAL模式检查
            cursor.execute("PRAGMA journal_mode")
            metrics["journal_mode"] = cursor.fetchone()[0]

            # 同步模式
            cursor.execute("PRAGMA synchronous")
            metrics["synchronous"] = cursor.fetchone()[0]

            # 缓存大小
            cursor.execute("PRAGMA cache_size")
            metrics["cache_size"] = cursor.fetchone()[0]

            return {
                "status": "ok",
                "metrics": metrics,
                "message": f"数据库大小: {metrics['db_size_bytes']:,} 字节, 利用率: {metrics['utilization_percent']}%"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"性能指标检查失败: {e}"
            }

    def _summarize_diagnosis(self, diagnosis: Dict[str, Any]) -> Dict[str, Any]:
        """汇总诊断结果"""
        try:
            error_count = 0
            warning_count = 0

            # 统计各检查的状态
            for check_name, check_result in diagnosis["checks"].items():
                if isinstance(check_result, dict) and "status" in check_result:
                    if check_result["status"] == "error":
                        error_count += 1
                        diagnosis["issues"].append(f"{check_name}: {check_result.get('message', '未知错误')}")
                    elif check_result["status"] == "warning":
                        warning_count += 1
                        diagnosis["warnings"].append(f"{check_name}: {check_result.get('message', '未知警告')}")
                    else:
                        diagnosis["info"].append(f"{check_name}: {check_result.get('message', '检查通过')}")

            # 确定总体状态
            if error_count > 0:
                diagnosis["overall_status"] = "error"
            elif warning_count > 0:
                diagnosis["overall_status"] = "warning"
            else:
                diagnosis["overall_status"] = "healthy"

            # 添加汇总信息
            diagnosis["summary"] = {
                "total_checks": len(diagnosis["checks"]),
                "error_count": error_count,
                "warning_count": warning_count,
                "healthy_checks": len(diagnosis["checks"]) - error_count - warning_count
            }

            return diagnosis

        except Exception as e:
            diagnosis["overall_status"] = "error"
            diagnosis["issues"].append(f"诊断汇总失败: {e}")
            return diagnosis

    def run_database_health_check(self, diagnostic_level: str = "full") -> Dict[str, Any]:
        """
        🔧 数据库诊断增强：运行数据库健康检查

        Args:
            diagnostic_level: 诊断级别 ("quick", "standard", "full", "deep")

        Returns:
            诊断结果字典
        """
        self.logger.info(f"开始数据库健康检查（级别：{diagnostic_level}）...")

        diagnosis = self.diagnose_database(self.db_path, diagnostic_level)

        # 记录诊断结果
        if diagnosis["overall_status"] == "healthy":
            self.logger.info("✅ 数据库健康检查通过")
        elif diagnosis["overall_status"] == "warning":
            self.logger.warning("⚠️ 数据库健康检查发现警告")
            for warning in diagnosis["warnings"]:
                self.logger.warning(f"  - {warning}")
        elif diagnosis["overall_status"] == "error":
            self.logger.error("❌ 数据库健康检查发现严重问题")
            for issue in diagnosis["issues"]:
                self.logger.error(f"  - {issue}")
        else:
            self.logger.error("❓ 数据库健康检查状态未知")

        # 显示诊断摘要
        summary = diagnosis.get("summary", {})
        self.logger.info(f"诊断摘要：总检查项 {summary.get('total_checks', 0)}，"
                        f"健康 {summary.get('healthy_checks', 0)}，"
                        f"警告 {summary.get('warning_count', 0)}，"
                        f"错误 {summary.get('error_count', 0)}")

        return diagnosis

    def generate_health_report(self, diagnostic_level: str = "full", save_to_file: bool = False) -> str:
        """
        🔧 数据库诊断增强：生成数据库健康报告

        Args:
            diagnostic_level: 诊断级别
            save_to_file: 是否保存到文件

        Returns:
            健康报告文本
        """
        diagnosis = self.run_database_health_check(diagnostic_level)

        # 生成报告
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("数据库健康检查报告")
        report_lines.append("=" * 80)
        report_lines.append(f"数据库路径: {diagnosis['db_path']}")
        report_lines.append(f"检查时间: {diagnosis['timestamp']}")
        report_lines.append(f"诊断级别: {diagnosis['diagnostic_level']}")
        report_lines.append(f"总体状态: {diagnosis['overall_status']}")
        report_lines.append("")

        # 摘要信息
        if "summary" in diagnosis:
            summary = diagnosis["summary"]
            report_lines.append("检查摘要:")
            report_lines.append(f"  总检查项: {summary.get('total_checks', 0)}")
            report_lines.append(f"  健康检查: {summary.get('healthy_checks', 0)}")
            report_lines.append(f"  警告数量: {summary.get('warning_count', 0)}")
            report_lines.append(f"  错误数量: {summary.get('error_count', 0)}")
            report_lines.append("")

        # 详细检查结果
        if "checks" in diagnosis:
            report_lines.append("详细检查结果:")
            report_lines.append("-" * 40)

            for check_name, check_result in diagnosis["checks"].items():
                if isinstance(check_result, dict):
                    status = check_result.get("status", "unknown")
                    message = check_result.get("message", "无消息")

                    status_symbol = {
                        "ok": "✅",
                        "warning": "⚠️",
                        "error": "❌"
                    }.get(status, "❓")

                    report_lines.append(f"{status_symbol} {check_name}: {message}")

            report_lines.append("")

        # 问题和警告
        if diagnosis.get("issues"):
            report_lines.append("发现的问题:")
            report_lines.append("-" * 40)
            for issue in diagnosis["issues"]:
                report_lines.append(f"❌ {issue}")
            report_lines.append("")

        if diagnosis.get("warnings"):
            report_lines.append("发现的警告:")
            report_lines.append("-" * 40)
            for warning in diagnosis["warnings"]:
                report_lines.append(f"⚠️ {warning}")
            report_lines.append("")

        # 信息
        if diagnosis.get("info"):
            report_lines.append("其他信息:")
            report_lines.append("-" * 40)
            for info in diagnosis["info"]:
                report_lines.append(f"ℹ️ {info}")
            report_lines.append("")

        report_lines.append("=" * 80)
        report_lines.append("报告结束")
        report_lines.append("=" * 80)

        report_text = "\n".join(report_lines)

        # 保存到文件
        if save_to_file:
            try:
                report_filename = f"db_health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                report_path = self._backup_dir_path / report_filename

                with open(report_path, 'w', encoding='utf-8') as f:
                    f.write(report_text)

                self.logger.info(f"健康报告已保存到: {report_path}")
            except Exception as save_error:
                self.logger.error(f"保存健康报告失败: {save_error}")

        return report_text

    def _is_safe_table_name(self, table_name: str) -> bool:
        """
        🔧 SQL安全修复：验证表名是否安全

        Args:
            table_name: 表名

        Returns:
            表名是否安全
        """
        import re

        # 检查表名是否为空或None
        if not table_name:
            return False

        # 检查表名长度（SQLite表名最大长度通常是64字符）
        if len(table_name) > 64:
            return False

        # 检查表名是否包含危险字符
        # 允许字母、数字、下划线、连字符，以及一些常见的特殊字符
        safe_pattern = re.compile(r'^[a-zA-Z0-9_\-\.\s]+$')
        if not safe_pattern.match(table_name):
            return False

        # 检查是否以数字开头（虽然SQLite允许，但不是好的实践）
        if table_name[0].isdigit():
            return False

        # 检查是否是SQLite保留字（部分常见的）
        reserved_words = {
            'select', 'insert', 'update', 'delete', 'drop', 'create', 'alter',
            'table', 'index', 'view', 'trigger', 'database', 'schema',
            'pragma', 'union', 'join', 'where', 'order', 'group', 'having'
        }

        if table_name.lower() in reserved_words:
            return False

        return True

    def _is_safe_identifier_name(self, identifier: str) -> bool:
        """
        🔧 SQL安全修复：验证标识符（表名、索引名等）是否安全

        Args:
            identifier: 标识符名称

        Returns:
            标识符是否安全
        """
        import re

        # 检查标识符是否为空或None
        if not identifier:
            return False

        # 检查标识符长度
        if len(identifier) > 128:  # 更宽松的长度限制
            return False

        # 检查标识符是否包含危险字符
        # 允许字母、数字、下划线、连字符，以及一些常见的特殊字符
        safe_pattern = re.compile(r'^[a-zA-Z0-9_\-\.\s]+$')
        if not safe_pattern.match(identifier):
            return False

        # 检查是否包含连续的特殊字符（可能是攻击尝试）
        if '--' in identifier or '/*' in identifier or '*/' in identifier:
            return False

        return True

    def _cleanup_old_backups(self):
        """清理旧的备份文件"""
        try:
            # 获取所有备份文件
            backup_files = list(self._backup_dir_path.glob("backup_*.db"))
            
            # 按修改时间排序（最新的在前）
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 删除超出数量限制的备份
            if len(backup_files) > self.max_backups:
                files_to_delete = backup_files[self.max_backups:]
                for file_path in files_to_delete:
                    try:
                        file_path.unlink()
                        self.logger.debug(f"已删除旧备份: {file_path}")
                    except Exception as e:
                        self.logger.warning(f"删除旧备份失败: {file_path}, 错误: {e}")
                
                self.logger.info(f"清理了 {len(files_to_delete)} 个旧备份文件")
                
        except Exception as e:
            self.logger.warning(f"清理旧备份时出错: {e}")
    
    def get_backup_list(self) -> List[Dict[str, Any]]:
        """
        🔧 备份功能优化：获取备份文件列表，支持智能筛选和人性化显示

        Returns:
            备份文件信息列表
        """
        try:
            backup_files = list(self._backup_dir_path.glob("backup_*.db"))
            backup_info = []

            for backup_file in backup_files:
                try:
                    stat = backup_file.stat()

                    # 🔧 备份功能优化：解析备份文件信息
                    parsed_info = self._parse_backup_filename(backup_file.name)

                    info = {
                        'filename': backup_file.name,
                        'path': str(backup_file),
                        'size': stat.st_size,
                        'created_time': datetime.fromtimestamp(stat.st_mtime),
                        'created': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                        'mtime': stat.st_mtime,
                        'is_valid': self._verify_backup(backup_file),
                        'operation_type': parsed_info.get('operation_type', '未知操作'),
                        'human_readable_name': parsed_info.get('human_readable_name', backup_file.name),
                        'is_recent': (time.time() - stat.st_mtime) < 86400  # 24小时内
                    }
                    backup_info.append(info)
                except Exception as e:
                    self.logger.warning(f"获取备份文件信息失败: {backup_file}, 错误: {e}")

            # 🔧 备份功能优化：按创建时间排序（最新的在前）
            backup_info.sort(key=lambda x: x['created_time'], reverse=True)

            return backup_info

        except Exception as e:
            self.logger.error(f"获取备份列表失败: {e}")
            return []

    def _parse_backup_filename(self, filename: str) -> dict:
        """
        🔧 备份功能优化：解析备份文件名，提取操作信息

        Args:
            filename: 备份文件名

        Returns:
            包含操作信息的字典
        """
        try:
            # 🔧 备份功能优化：解析新格式文件名
            if filename.startswith('backup_'):
                # 格式：backup_操作名称_YYYYMMDD_HHMMSS.db
                parts = filename.replace('.db', '').split('_')
                if len(parts) >= 4:
                    operation_part = '_'.join(parts[1:-2])  # 操作名称部分

                    # 🔧 备份功能优化：操作类型映射
                    operation_mapping = {
                        '数据导入': '数据导入操作',
                        '导入前备份': '导入前自动备份',
                        '退款处理': '退款处理操作',
                        '退款前备份': '退款前自动备份',
                        '手动备份': '手动创建备份',
                        '恢复前备份': '恢复前安全备份',
                        '数据修复': '数据修复备份',
                        '系统维护': '系统维护备份'
                    }

                    operation_type = operation_mapping.get(operation_part, operation_part)

                    return {
                        'operation_type': operation_type,
                        'human_readable_name': f"{operation_type} ({parts[-2]}_{parts[-1]})",
                        'timestamp_part': f"{parts[-2]}_{parts[-1]}"
                    }

            # 默认情况
            return {
                'operation_type': '未知操作',
                'human_readable_name': filename,
                'timestamp_part': ''
            }

        except Exception as e:
            self.logger.warning(f"解析备份文件名失败: {filename}, {e}")
            return {
                'operation_type': '未知操作',
                'human_readable_name': filename,
                'timestamp_part': ''
            }

    def get_latest_backup(self, operation_filter: str = None) -> Optional[str]:
        """
        🔧 备份功能优化：获取最新的备份文件

        Args:
            operation_filter: 操作类型筛选（可选）

        Returns:
            最新备份文件的完整路径，如果没有找到则返回None
        """
        try:
            backup_list = self.get_backup_list()

            if not backup_list:
                self.logger.warning("没有找到任何备份文件")
                return None

            # 🔧 备份功能优化：根据操作类型筛选
            if operation_filter:
                filtered_backups = [
                    backup for backup in backup_list
                    if operation_filter in backup.get('operation_type', '')
                ]
                if filtered_backups:
                    backup_list = filtered_backups

            # 返回最新的备份（列表已按时间倒序排列）
            latest_backup = backup_list[0]
            self.logger.info(f"找到最新备份: {latest_backup['human_readable_name']}")

            return latest_backup['path']

        except Exception as e:
            self.logger.error(f"获取最新备份失败: {e}")
            return None
    
    def restore_from_backup(self, backup_path: str, confirm_callback=None) -> bool:
        """
        🔧 备份功能修复：线程安全的数据库恢复，失败时返回False而不抛出异常

        Args:
            backup_path: 备份文件路径
            confirm_callback: 确认回调函数，如果为None则跳过确认（主应用模式）

        Returns:
            恢复是否成功
        """
        # 🔧 恢复并发控制修复：使用锁防止并发恢复冲突
        with self._restore_lock:
            try:
                backup_file = Path(backup_path)

                if not backup_file.exists():
                    # 🔧 备份功能修复：文件不存在时返回False而不抛出异常
                    self.logger.error(f"备份文件不存在: {backup_path}")
                    return False

                # 🔧 数据库诊断增强：恢复前诊断备份文件
                self.logger.info("开始恢复前备份文件诊断...")
                pre_restore_diagnosis = self.diagnose_database(backup_file, "full")

                if pre_restore_diagnosis["overall_status"] == "error":
                    self.logger.error("恢复前诊断发现备份文件严重问题，无法恢复")
                    for issue in pre_restore_diagnosis["issues"]:
                        self.logger.error(f"  - {issue}")
                    return False

                elif pre_restore_diagnosis["overall_status"] == "warning":
                    self.logger.warning("恢复前诊断发现备份文件警告")
                    for warning in pre_restore_diagnosis["warnings"]:
                        self.logger.warning(f"  - {warning}")

                    # 询问用户是否继续
                    if GUI_AVAILABLE:
                        continue_restore = messagebox.askyesno(
                            "备份文件诊断警告",
                            f"恢复前诊断发现以下问题：\n" +
                            "\n".join(pre_restore_diagnosis["warnings"]) +
                            "\n\n是否仍要继续恢复？"
                        )
                        if not continue_restore:
                            self.logger.info("用户选择取消恢复")
                            return False
                    else:
                        self.logger.warning("发现备份文件问题但继续恢复（非GUI模式）")
                else:
                    self.logger.info("恢复前诊断通过，备份文件状态健康")

                # 验证备份文件
                try:
                    if not self._verify_backup(backup_file):
                        # 🔧 备份功能修复：验证失败时返回False而不抛出异常
                        self.logger.error(f"备份文件无效或损坏: {backup_path}")
                        return False
                except Exception as verify_error:
                    self.logger.error(f"备份文件验证过程出错: {verify_error}")
                    return False

                # 用户确认
                if confirm_callback:
                    try:
                        if not confirm_callback(f"确定要从备份恢复数据库吗？\n备份文件: {backup_file.name}\n当前数据库将被覆盖！"):
                            self.logger.info("用户取消了数据库恢复操作")
                            return False
                    except Exception as confirm_error:
                        self.logger.error(f"用户确认过程出错: {confirm_error}")
                        return False

                # 🔧 死锁修复：创建当前数据库的备份（避免锁嵌套）
                current_backup = None
                if self.db_path.exists():
                    try:
                        # 🔧 死锁修复：临时释放恢复锁，避免与备份锁冲突
                        self._restore_lock.release()
                        try:
                            current_backup = self.create_backup("恢复前备份")
                            if current_backup:
                                self.logger.info(f"已创建当前数据库备份: {current_backup}")
                            else:
                                self.logger.warning("创建恢复前备份失败，但继续恢复操作")
                        finally:
                            # 重新获取恢复锁
                            self._restore_lock.acquire()
                    except Exception as backup_error:
                        # 确保重新获取锁
                        try:
                            self._restore_lock.acquire()
                        except:
                            pass
                        self.logger.warning(f"创建恢复前备份出错: {backup_error}，但继续恢复操作")

                # 🔧 恢复安全增强：完整的安全恢复机制
                try:
                    return self._safe_atomic_restore(backup_file, current_backup)
                except Exception as restore_error:
                    self.logger.error(f"原子性恢复失败: {restore_error}")
                    return False

            except Exception as e:
                # 🔧 备份功能修复：异常时返回False而不抛出异常
                self.logger.error(f"数据库恢复失败: {e}")
                return False

    def _safe_atomic_restore(self, backup_file: Path, current_backup: Optional[str]) -> bool:
        """
        🔧 恢复安全增强：安全的原子性恢复实现

        Args:
            backup_file: 备份文件路径
            current_backup: 当前数据库备份路径

        Returns:
            恢复是否成功
        """
        import tempfile
        import time

        # 🔧 文件占用修复：彻底关闭数据库连接，确保文件不被占用
        self.logger.info("开始彻底关闭数据库连接以释放文件锁")

        # 🔧 文件占用修复：强制释放文件句柄
        self._force_release_file_handles()

        # 多次关闭连接，确保彻底
        for _ in range(2):
            self._close_all_database_connections()
            time.sleep(0.3)  # 每次关闭后等待

        # 🔧 文件占用修复：额外的垃圾回收和等待
        import gc
        for _ in range(2):
            gc.collect()

        # 🔧 文件占用修复：等待足够时间让系统释放文件句柄
        time.sleep(0.8)  # 增加等待时间

        # 使用临时文件进行原子性恢复
        with tempfile.NamedTemporaryFile(delete=False, suffix='.db.tmp') as temp_file:
            temp_restore_path = temp_file.name

        try:
            # 复制备份到临时文件
            try:
                shutil.copy2(backup_file, temp_restore_path)
            except Exception as copy_error:
                self.logger.error(f"复制备份文件到临时位置失败: {copy_error}")
                return False

            # 验证临时恢复文件
            try:
                if not self._verify_backup(Path(temp_restore_path)):
                    # 🔧 备份功能修复：验证失败时返回False而不抛出异常
                    self.logger.error("临时恢复文件验证失败")
                    return False
            except Exception as verify_error:
                self.logger.error(f"临时恢复文件验证过程出错: {verify_error}")
                return False

            # 🔧 恢复卡住紧急修复：超快速数据库解锁检查
            try:
                # 🔧 紧急修复：使用更短的超时时间，避免用户感觉卡住
                if not self._wait_for_database_unlock(timeout=3):
                    # 🔧 紧急修复：快速强制解锁
                    self.logger.warning("数据库解锁等待超时，尝试快速强制解锁")

                    if not self._force_database_unlock():
                        self.logger.warning("强制解锁失败，但继续尝试恢复")
                        # 🔧 紧急修复：即使强制解锁失败，也继续尝试恢复
                    else:
                        self.logger.info("强制解锁成功，继续恢复")

            except Exception as unlock_error:
                self.logger.warning(f"解锁检查过程出错: {unlock_error}")
                # 🔧 紧急修复：任何解锁问题都不阻塞恢复过程
                self.logger.info("跳过解锁检查，直接尝试恢复")

            # 🔧 数据安全修复：真正的原子性移动到最终位置
            try:
                # 创建原数据库的安全备份
                safety_backup_path = None
                if self.db_path.exists():
                    safety_backup_path = str(self.db_path) + ".safety_backup"
                    try:
                        shutil.copy2(self.db_path, safety_backup_path)
                        self.logger.info(f"已创建安全备份: {safety_backup_path}")
                    except Exception as safety_error:
                        self.logger.error(f"创建安全备份失败: {safety_error}")
                        return False

                # 🔧 文件占用修复：带重试机制的原子性替换
                final_temp_path = str(self.db_path) + ".new"
                try:
                    shutil.move(temp_restore_path, final_temp_path)

                    # 🔧 文件占用修复：带重试的文件删除和重命名
                    max_retries = 5
                    for attempt in range(max_retries):
                        try:
                            # 如果原文件存在，删除它
                            if self.db_path.exists():
                                self.db_path.unlink()

                            # 将新文件重命名为最终名称
                            Path(final_temp_path).rename(self.db_path)

                            # 成功则跳出重试循环
                            break

                        except (OSError, PermissionError) as file_error:
                            if attempt < max_retries - 1:
                                self.logger.warning(f"文件操作失败，重试 {attempt + 1}/{max_retries}: {file_error}")

                                # 🔧 文件占用修复：重试前强制释放文件句柄
                                self._force_release_file_handles()

                                # 🔧 文件占用修复：再次强制关闭连接
                                self._close_all_database_connections()

                                # 等待更长时间让系统释放文件句柄
                                import time
                                time.sleep(1.5 * (attempt + 1))  # 递增等待时间
                            else:
                                # 最后一次尝试失败，抛出异常
                                raise file_error

                    # 验证恢复后的数据库
                    if not self._verify_backup(self.db_path):
                        self.logger.error("恢复后数据库验证失败，回滚到安全备份")

                        # 回滚到安全备份
                        if safety_backup_path and os.path.exists(safety_backup_path):
                            if self.db_path.exists():
                                self.db_path.unlink()
                            shutil.move(safety_backup_path, self.db_path)
                            self.logger.info("已回滚到安全备份")

                        return False

                    # 恢复成功，删除安全备份
                    if safety_backup_path and os.path.exists(safety_backup_path):
                        os.unlink(safety_backup_path)
                        self.logger.info("恢复成功，已删除安全备份")

                except Exception as atomic_error:
                    self.logger.error(f"原子性移动失败: {atomic_error}")

                    # 清理可能的临时文件
                    if os.path.exists(final_temp_path):
                        try:
                            os.unlink(final_temp_path)
                        except:
                            pass

                    # 如果有安全备份，尝试恢复
                    if safety_backup_path and os.path.exists(safety_backup_path):
                        try:
                            if self.db_path.exists():
                                self.db_path.unlink()
                            shutil.move(safety_backup_path, self.db_path)
                            self.logger.info("原子性移动失败，已回滚到安全备份")
                        except Exception as rollback_error:
                            self.logger.critical(f"回滚到安全备份也失败: {rollback_error}")

                    return False

            except Exception as move_error:
                self.logger.error(f"移动恢复文件到最终位置失败: {move_error}")
                return False

            # 验证最终恢复的数据库
            try:
                if self._verify_backup(self.db_path):
                    # 🔧 数据库诊断增强：恢复后诊断恢复的数据库
                    self.logger.info("开始恢复后数据库诊断...")
                    post_restore_diagnosis = self.diagnose_database(self.db_path, "full")

                    if post_restore_diagnosis["overall_status"] == "error":
                        self.logger.error("恢复后诊断发现严重问题")
                        for issue in post_restore_diagnosis["issues"]:
                            self.logger.error(f"  - {issue}")

                        # 如果有当前备份，尝试回滚
                        if current_backup and os.path.exists(current_backup):
                            self.logger.warning("尝试回滚到恢复前状态")
                            try:
                                if self.db_path.exists():
                                    self.db_path.unlink()
                                shutil.move(current_backup, self.db_path)
                                self.logger.info("已回滚到恢复前状态")
                            except Exception as rollback_error:
                                self.logger.critical(f"回滚失败: {rollback_error}")

                        return False

                    elif post_restore_diagnosis["overall_status"] == "warning":
                        self.logger.warning("恢复后诊断发现警告")
                        for warning in post_restore_diagnosis["warnings"]:
                            self.logger.warning(f"  - {warning}")
                    else:
                        self.logger.info("恢复后诊断通过，数据库状态健康")

                    # 🔧 恢复安全增强：重新初始化连接池
                    try:
                        self._reinitialize_connection_pool()
                    except Exception as reinit_error:
                        self.logger.warning(f"重新初始化连接池失败: {reinit_error}")

                    self.logger.info(f"数据库恢复成功: {backup_file}")
                    return True
                else:
                    # 🔧 备份功能修复：最终验证失败时返回False而不抛出异常
                    self.logger.error("最终数据库验证失败")
                    return False
            except Exception as final_verify_error:
                self.logger.error(f"最终数据库验证过程出错: {final_verify_error}")
                return False

        except Exception as restore_error:
            # 🔧 恢复错误处理修复：清理临时文件
            if Path(temp_restore_path).exists():
                try:
                    Path(temp_restore_path).unlink()
                except:
                    pass

            # 🔧 恢复回滚修复：增强回滚机制
            if current_backup and Path(current_backup).exists():
                try:
                    if self.db_path.exists():
                        self.db_path.unlink()
                    shutil.copy2(current_backup, self.db_path)

                    # 验证回滚是否成功
                    try:
                        if self._verify_backup(self.db_path):
                            self.logger.error(f"数据库恢复失败，已成功回滚到原始状态")
                        else:
                            self.logger.critical(f"数据库恢复失败，回滚也失败！数据库可能已损坏")
                    except Exception as rollback_verify_error:
                        self.logger.critical(f"回滚验证失败: {rollback_verify_error}")

                    # 🔧 恢复安全增强：回滚后重新初始化连接池
                    try:
                        self._reinitialize_connection_pool()
                    except Exception as rollback_reinit_error:
                        self.logger.warning(f"回滚后重新初始化连接池失败: {rollback_reinit_error}")

                except Exception as rollback_error:
                    self.logger.critical(f"数据库恢复失败，回滚过程也失败: {rollback_error}")

            # 🔧 备份功能修复：异常时返回False而不抛出异常
            self.logger.error(f"数据库恢复失败: {restore_error}")
            return False

    def _close_all_database_connections(self):
        """
        🔧 文件占用紧急修复：彻底关闭所有数据库连接和文件句柄
        """
        try:
            self.logger.info("开始彻底关闭数据库连接")

            # 🔧 文件占用修复：使用超时机制，避免任何操作卡住
            import threading
            import time
            import gc
            import sqlite3

            def close_with_timeout():
                try:
                    # 方法1：关闭连接池
                    try:
                        from .connection_pool import close_connection_pool, force_close_all_connections
                        close_connection_pool()
                        force_close_all_connections()
                        self.logger.info("连接池已关闭")
                    except Exception as e:
                        self.logger.warning(f"连接池关闭失败: {e}")

                    # 🔧 文件占用修复：强制关闭所有SQLite连接
                    try:
                        # 获取所有SQLite连接对象并强制关闭
                        closed_count = 0
                        for obj in gc.get_objects():
                            if isinstance(obj, sqlite3.Connection):
                                try:
                                    obj.close()
                                    closed_count += 1
                                except:
                                    pass
                        if closed_count > 0:
                            self.logger.info(f"强制关闭了 {closed_count} 个SQLite连接")
                    except Exception as e:
                        self.logger.warning(f"强制关闭SQLite连接失败: {e}")

                    # 🔧 文件占用修复：执行多次垃圾回收
                    for _ in range(3):
                        gc.collect()
                    self.logger.debug("垃圾回收完成")

                    # 🔧 文件占用修复：尝试关闭WAL模式相关文件
                    try:
                        # 如果数据库使用WAL模式，尝试关闭相关文件
                        wal_file = str(self.db_path) + "-wal"
                        shm_file = str(self.db_path) + "-shm"

                        # 尝试删除WAL和SHM文件（如果存在且未被锁定）
                        import os
                        for temp_file in [wal_file, shm_file]:
                            if os.path.exists(temp_file):
                                try:
                                    os.remove(temp_file)
                                    self.logger.debug(f"已删除临时文件: {temp_file}")
                                except:
                                    pass
                    except Exception as e:
                        self.logger.debug(f"WAL文件处理失败: {e}")

                except Exception as e:
                    self.logger.warning(f"彻底关闭过程出错: {e}")

            # 🔧 文件占用修复：在单独线程中执行关闭操作，主线程等待最多3秒
            close_thread = threading.Thread(target=close_with_timeout, daemon=True)
            close_thread.start()
            close_thread.join(timeout=3.0)  # 增加到3秒，确保彻底关闭

            if close_thread.is_alive():
                self.logger.warning("连接关闭操作超时，但继续执行")
            else:
                self.logger.info("连接关闭操作完成")

            # 🔧 文件占用修复：增加等待时间，让系统释放文件句柄
            time.sleep(0.5)  # 增加到0.5秒

        except Exception as e:
            self.logger.warning(f"关闭数据库连接时出错: {e}")
            # 🔧 紧急修复：即使出错也不阻塞，立即返回

    def _wait_for_database_unlock(self, timeout: int = 30) -> bool:
        """
        🔧 恢复卡住紧急修复：快速等待数据库解锁

        Args:
            timeout: 超时时间（秒）

        Returns:
            数据库是否已解锁
        """
        import time
        import sqlite3

        start_time = time.time()
        attempt_count = 0
        max_attempts = timeout * 4  # 每0.25秒尝试一次

        while time.time() - start_time < timeout and attempt_count < max_attempts:
            try:
                # 🔧 紧急修复：使用更短的连接超时
                with sqlite3.connect(self.db_path, timeout=0.5) as conn:
                    conn.execute("BEGIN IMMEDIATE;")
                    conn.rollback()
                self.logger.info("数据库已解锁")
                return True
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    # 🔧 紧急修复：减少等待间隔，增加检查频率
                    time.sleep(0.25)
                    attempt_count += 1
                    continue
                else:
                    self.logger.warning(f"数据库访问错误: {e}")
                    return False
            except Exception as e:
                self.logger.warning(f"检查数据库锁定状态失败: {e}")
                return False

        self.logger.warning(f"等待数据库解锁超时 ({timeout}秒, {attempt_count}次尝试)")
        return False

    def _force_database_unlock(self) -> bool:
        """
        🔧 文件占用修复：强力解锁数据库文件

        Returns:
            是否成功解锁
        """
        try:
            import time
            import gc
            import os

            self.logger.info("开始强力解锁数据库文件")

            # 🔧 文件占用修复：多次彻底关闭连接
            for _ in range(3):
                self._close_all_database_connections()
                time.sleep(0.5)  # 每次关闭后等待

            # 🔧 文件占用修复：强制垃圾回收
            for _ in range(3):
                gc.collect()

            # 🔧 文件占用修复：尝试删除WAL和SHM文件
            try:
                wal_file = str(self.db_path) + "-wal"
                shm_file = str(self.db_path) + "-shm"

                for temp_file in [wal_file, shm_file]:
                    if os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                            self.logger.info(f"已删除锁定文件: {temp_file}")
                        except Exception as e:
                            self.logger.warning(f"无法删除锁定文件 {temp_file}: {e}")
            except Exception as e:
                self.logger.warning(f"清理WAL/SHM文件失败: {e}")

            # 🔧 文件占用修复：等待更长时间让系统释放文件句柄
            time.sleep(1.0)

            # 🔧 数据安全修复：使用更安全的文件锁定检测方法
            try:
                if self.db_path.exists():
                    # 方法1：尝试以独占模式打开文件（更安全的锁定检测）
                    try:
                        with open(self.db_path, 'r+b') as test_file:
                            # 尝试获取文件锁（如果系统支持）
                            try:
                                import fcntl
                                fcntl.flock(test_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                                fcntl.flock(test_file.fileno(), fcntl.LOCK_UN)
                                self.logger.info("文件锁定测试成功（fcntl方法）")
                                return True
                            except ImportError:
                                # Windows系统没有fcntl，使用其他方法
                                pass
                            except (OSError, IOError):
                                # 文件被锁定
                                self.logger.warning("文件仍被锁定（fcntl测试）")

                        # 方法2：尝试SQLite连接测试（更直接的数据库锁定检测）
                        try:
                            import sqlite3
                            with sqlite3.connect(str(self.db_path), timeout=1.0) as test_conn:
                                test_conn.execute("SELECT 1").fetchone()
                            self.logger.info("数据库连接测试成功，数据库已解锁")
                            return True
                        except sqlite3.OperationalError as db_error:
                            if "locked" in str(db_error).lower():
                                self.logger.warning(f"数据库仍被锁定: {db_error}")
                                return False
                            else:
                                # 其他数据库错误，但不是锁定问题
                                self.logger.info("数据库连接成功（非锁定错误）")
                                return True

                    except (OSError, IOError) as file_error:
                        self.logger.warning(f"文件访问测试失败: {file_error}")
                        return False
                else:
                    self.logger.info("数据库文件不存在，视为已解锁")
                    return True

            except Exception as file_test_error:
                self.logger.warning(f"文件锁定检测失败: {file_test_error}")
                return False

        except Exception as e:
            self.logger.error(f"强制解锁过程出错: {e}")
            return False

    def _force_release_file_handles(self) -> bool:
        """
        🔧 文件占用修复：强制释放数据库文件句柄（Windows专用）

        Returns:
            是否成功释放文件句柄
        """
        try:
            import os
            import time
            import gc
            import sqlite3

            self.logger.info("开始强制释放数据库文件句柄")

            # 1. 强制关闭所有SQLite连接
            closed_connections = 0
            try:
                for obj in gc.get_objects():
                    if isinstance(obj, sqlite3.Connection):
                        try:
                            obj.close()
                            closed_connections += 1
                        except:
                            pass
                self.logger.info(f"强制关闭了 {closed_connections} 个SQLite连接")
            except Exception as e:
                self.logger.warning(f"强制关闭SQLite连接失败: {e}")

            # 2. 多次垃圾回收
            for _ in range(5):
                gc.collect()

            # 3. 尝试删除SQLite的临时文件
            try:
                temp_files = [
                    str(self.db_path) + "-wal",
                    str(self.db_path) + "-shm",
                    str(self.db_path) + "-journal"
                ]

                for temp_file in temp_files:
                    if os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                            self.logger.info(f"已删除临时文件: {temp_file}")
                        except Exception as e:
                            self.logger.warning(f"无法删除临时文件 {temp_file}: {e}")
            except Exception as e:
                self.logger.warning(f"清理临时文件失败: {e}")

            # 4. 等待系统释放文件句柄
            time.sleep(1.5)

            # 5. 测试文件是否可以访问
            try:
                # 尝试以独占模式打开文件
                with open(self.db_path, 'r+b'):
                    pass
                self.logger.info("文件句柄已成功释放")
                return True
            except Exception as e:
                self.logger.warning(f"文件仍被占用: {e}")
                return False

        except Exception as e:
            self.logger.error(f"强制释放文件句柄失败: {e}")
            return False

    def _reinitialize_connection_pool(self):
        """
        🔧 恢复安全增强：重新初始化连接池
        """
        try:
            # 重新初始化连接池
            try:
                from .connection_pool import reinitialize_connection_pool
                reinitialize_connection_pool(str(self.db_path))
                self.logger.info("连接池已重新初始化")
            except Exception as e:
                self.logger.warning(f"重新初始化连接池失败: {e}")

        except Exception as e:
            self.logger.warning(f"重新初始化连接池时出错: {e}")

    def auto_backup_before_operation(self, operation_name: str) -> str:
        """
        操作前自动备份
        
        Args:
            operation_name: 操作名称
            
        Returns:
            备份文件路径
        """
        try:
            backup_path = self.create_backup(f"操作前_{operation_name}")
            if backup_path:
                self.logger.info(f"操作前自动备份完成: {operation_name}")
                return backup_path
            else:
                raise BackupError(f"操作前自动备份失败: {operation_name}")
        except Exception as e:
            self.logger.error(f"操作前自动备份失败: {operation_name}, 错误: {e}")
            raise
    
    def handle_operation_error(self, operation_name: str, error: Exception, 
                             backup_path: Optional[str] = None) -> bool:
        """
        处理操作错误，提供恢复选项
        
        Args:
            operation_name: 操作名称
            error: 发生的错误
            backup_path: 操作前的备份路径
            
        Returns:
            是否进行了恢复
        """
        try:
            self.logger.error(f"操作失败: {operation_name}, 错误: {error}")
            
            # 如果有备份，询问是否恢复
            if backup_path and Path(backup_path).exists():
                def confirm_restore():
                    return messagebox.askyesno(
                        "操作失败 - 数据库恢复",
                        f"操作失败: {operation_name}\n"
                        f"错误: {str(error)}\n\n"
                        f"是否恢复到操作前的备份？\n"
                        f"备份时间: {Path(backup_path).stat().st_mtime}\n\n"
                        f"选择'是'将恢复数据库到操作前状态\n"
                        f"选择'否'将保持当前状态",
                        icon='warning'
                    )
                
                if confirm_restore():
                    try:
                        self.restore_from_backup(backup_path, confirm_callback=lambda _: True)
                        messagebox.showinfo("恢复成功", "数据库已恢复到操作前状态")
                        return True
                    except Exception as restore_error:
                        messagebox.showerror("恢复失败", f"数据库恢复失败: {restore_error}")
                        return False
            else:
                messagebox.showerror(
                    "操作失败",
                    f"操作失败: {operation_name}\n"
                    f"错误: {str(error)}\n\n"
                    f"没有可用的备份文件进行恢复"
                )
            
            return False
            
        except Exception as e:
            self.logger.error(f"处理操作错误时出错: {e}")
            return False

    # 🔧 应用集成优化：兼容性方法，支持主应用调用
    def backup_database(self, operation_name: str = "手动备份") -> Optional[str]:
        """
        兼容主应用的备份方法

        Args:
            operation_name: 操作名称

        Returns:
            备份文件路径
        """
        return self.create_backup(operation_name)

    def list_backups(self) -> list:
        """
        兼容主应用的备份列表方法

        Returns:
            备份文件列表
        """
        return self.get_backup_list()

    def restore_backup(self, backup_path: str, confirm_callback=None) -> bool:
        """
        🔧 应用调用兼容：兼容主应用的恢复方法

        Args:
            backup_path: 备份文件路径（可以是完整路径或文件名）
            confirm_callback: 确认回调函数

        Returns:
            恢复是否成功
        """
        try:
            # 🔧 应用调用兼容：处理文件名和完整路径
            if not os.path.isabs(backup_path):
                # 如果是文件名，构建完整路径
                backup_path = str(self._backup_dir_path / backup_path)

            return self.restore_from_backup(backup_path, confirm_callback)

        except Exception as e:
            self.logger.error(f"兼容性恢复方法调用失败: {e}")
            return False


# 🔧 应用集成优化：全局实例管理
_global_backup_manager: Optional[DatabaseBackupManager] = None


def get_backup_manager(db_path: str = None, backup_dir: str = None,
                      config_manager=None, gui_updater=None) -> DatabaseBackupManager:
    """
    获取全局备份管理器实例

    Args:
        db_path: 数据库路径
        backup_dir: 备份目录
        config_manager: 配置管理器
        gui_updater: GUI更新器

    Returns:
        备份管理器实例
    """
    global _global_backup_manager

    if _global_backup_manager is None:
        if db_path is None:
            # 动态获取项目根目录
            import os
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(script_dir))))
            db_path = os.path.join(project_root, "database", "sales_reports.db")

        _global_backup_manager = DatabaseBackupManager(
            db_path, backup_dir, config_manager, gui_updater
        )

    return _global_backup_manager


def reset_backup_manager():
    """重置全局备份管理器实例"""
    global _global_backup_manager
    _global_backup_manager = None


# 自定义备份异常
class BackupError(DatabaseError):
    """备份操作异常"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, operation="备份操作", error_code="BACKUP_ERROR", **kwargs)
    
    def get_user_message(self) -> str:
        return f"备份操作失败: {self.message}\n请检查磁盘空间和文件权限。"
