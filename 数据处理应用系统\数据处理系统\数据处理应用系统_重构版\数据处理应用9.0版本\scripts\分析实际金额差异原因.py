#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析实际金额差异原因 - 基于代码逻辑和用户反馈
"""

import os
import sys
import pandas as pd
from pathlib import Path

def analyze_amount_difference_logic():
    """分析金额差异的逻辑原因"""
    print("🔍 分析实际金额差异原因")
    print("=" * 60)
    
    print("📊 用户提供的实际数据:")
    print("- 第一文件总金额: RM26707.08")
    print("- 第二文件最终金额: RM26937.08")
    print("- 金额差异: RM230.00")
    print("- 用户指出: API订单总金额有600多，不是230")
    
    print("\n🔍 代码逻辑分析:")
    print("1. exclude_api_orders函数逻辑:")
    print("   - 检查是否有'Order types'列")
    print("   - 如果没有，直接返回原DataFrame（第一文件情况）")
    print("   - 如果有，排除包含'api'的订单（第二文件情况）")
    
    print("\n2. 第一文件金额计算:")
    print("   - 筛选'settled'状态记录")
    print("   - 计算'Bill Amt'总和")
    print("   - 如果有'Order types'列，会排除API订单")
    print("   - 如果没有'Order types'列，包含所有settled记录")
    
    print("\n3. 第二文件金额计算:")
    print("   - 筛选'finish'状态记录")
    print("   - 排除API订单（通过exclude_api_orders函数）")
    print("   - 计算'Order price'总和")

def analyze_possible_causes():
    """分析可能的原因"""
    print("\n💡 可能的金额差异原因分析")
    print("=" * 60)
    
    print("📋 原因1: 第一文件包含API订单，第二文件排除了API订单")
    print("   - 如果第一文件没有'Order types'列")
    print("   - 第一文件金额包含所有settled记录（包括API订单）")
    print("   - 第二文件金额排除了API订单")
    print("   - 差异 = 第一文件中API订单的金额")
    print("   - 但用户说API订单有600多，不是230")
    
    print("\n📋 原因2: 状态筛选条件不同")
    print("   - 第一文件筛选'settled'状态")
    print("   - 第二文件筛选'finish'状态")
    print("   - 可能存在状态不一致的记录")
    
    print("\n📋 原因3: 数据处理过程中的修改")
    print("   - Transaction ID匹配过程中可能修改了某些记录")
    print("   - 自动修正过程可能影响了金额")
    print("   - 插入的33条新记录可能影响总金额")
    
    print("\n📋 原因4: 数据范围不同")
    print("   - 第一文件和第二文件的数据时间范围可能不完全一致")
    print("   - 第二文件可能包含第一文件没有的记录")
    print("   - 或者第一文件包含第二文件没有的记录")

def analyze_log_clues():
    """分析日志线索"""
    print("\n🔍 从日志中分析线索")
    print("=" * 60)
    
    print("📋 关键日志信息:")
    print("- 处理: 3620 条")
    print("- 匹配: 3587 条")
    print("- 插入: 33 条")
    print("- Transaction ID匹配: 3104条")
    print("- 'Finish': 3736 条")
    print("- 'Close': 456 条")
    print("- 'Refunding': 1 条")
    
    print("\n💡 分析:")
    print("1. 插入了33条新记录")
    print("   - 这些记录来自第一文件但第二文件没有")
    print("   - 可能影响最终金额")
    
    print("2. 第二文件有4193条记录（3736+456+1）")
    print("   - 但只有3736条是'finish'状态")
    print("   - 金额计算只包含'finish'状态的记录")
    
    print("3. Transaction ID匹配了3104条")
    print("   - 说明大部分记录都能匹配")
    print("   - 但仍有差异")

def provide_investigation_plan():
    """提供调查计划"""
    print("\n🔧 调查计划")
    print("=" * 60)
    
    print("📋 需要检查的具体点:")
    print("1. 第一文件是否有'Order types'列")
    print("   - 如果有，API订单是否被正确排除")
    print("   - 如果没有，是否应该排除某些记录")
    
    print("2. 插入的33条记录的金额")
    print("   - 这33条记录的总金额是多少")
    print("   - 是否正好等于或接近230")
    
    print("3. API订单的实际金额")
    print("   - 第二文件中API订单的总金额")
    print("   - 验证是否真的有600多")
    
    print("4. 状态筛选的一致性")
    print("   - 'settled' vs 'finish'状态的记录是否一致")
    print("   - 是否存在状态转换问题")

def suggest_debugging_approach():
    """建议调试方法"""
    print("\n🛠️ 建议的调试方法")
    print("=" * 60)
    
    print("📋 方法1: 添加详细的金额分解日志")
    print("   - 在脚本中添加更详细的金额计算日志")
    print("   - 分别显示各种状态和类型的金额")
    print("   - 显示插入记录的金额")
    
    print("📋 方法2: 创建金额差异分析脚本")
    print("   - 专门分析第一文件和第二文件的金额构成")
    print("   - 逐步排除各种因素")
    print("   - 找出确切的差异来源")
    
    print("📋 方法3: 检查数据文件本身")
    print("   - 如果可能，直接检查Excel文件")
    print("   - 验证API订单的实际数量和金额")
    print("   - 确认数据的完整性")

def create_enhanced_logging():
    """创建增强的日志记录建议"""
    print("\n📝 增强日志记录建议")
    print("=" * 60)
    
    print("建议在脚本中添加以下日志:")
    print("""
# 在第一文件处理后添加:
print(f"🔍 第一文件详细分析:")
print(f"   总记录数: {len(df1)}")
print(f"   settled记录数: {len(df1_filtered)}")
if "Order types" in df1_filtered.columns:
    api_count = len(df1_filtered[df1_filtered["Order types"].str.contains("api", na=False)])
    api_amount = df1_filtered[df1_filtered["Order types"].str.contains("api", na=False)]["Bill Amt"].sum()
    print(f"   API订单数: {api_count}")
    print(f"   API订单金额: RM{api_amount:.2f}")
    print(f"   非API订单金额: RM{total_bill_amt - api_amount:.2f}")
else:
    print(f"   没有Order types列，无法区分API订单")

# 在第二文件处理后添加:
print(f"🔍 第二文件详细分析:")
print(f"   总记录数: {len(df2)}")
finish_count = len(df2[df2["Order status"].str.lower() == "finish"])
print(f"   finish记录数: {finish_count}")
if "Order types" in df2.columns:
    api_count = len(df2[(df2["Order status"].str.lower() == "finish") & 
                       df2["Order types"].str.contains("api", na=False)])
    api_amount = df2[(df2["Order status"].str.lower() == "finish") & 
                     df2["Order types"].str.contains("api", na=False)]["Order price"].sum()
    print(f"   finish状态API订单数: {api_count}")
    print(f"   finish状态API订单金额: RM{api_amount:.2f}")

# 在插入记录后添加:
if inserted_records > 0:
    inserted_amount = # 计算插入记录的金额
    print(f"🔍 插入记录分析:")
    print(f"   插入记录数: {inserted_records}")
    print(f"   插入记录金额: RM{inserted_amount:.2f}")
""")

def main():
    """主函数"""
    print("🔧 分析实际金额差异原因")
    print("=" * 80)
    
    try:
        # 1. 分析金额差异逻辑
        analyze_amount_difference_logic()
        
        # 2. 分析可能原因
        analyze_possible_causes()
        
        # 3. 分析日志线索
        analyze_log_clues()
        
        # 4. 提供调查计划
        provide_investigation_plan()
        
        # 5. 建议调试方法
        suggest_debugging_approach()
        
        # 6. 创建增强日志建议
        create_enhanced_logging()
        
        print("\n" + "=" * 80)
        print("🎯 总结")
        print("=" * 80)
        
        print("✅ 基于代码逻辑的分析已完成")
        print("✅ 提供了多种可能的原因")
        print("✅ 建议了具体的调查方法")
        print("\n💡 下一步: 需要添加更详细的日志来确定确切原因")
        print("🔧 或者直接检查实际的数据文件内容")
        
        return 0
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
