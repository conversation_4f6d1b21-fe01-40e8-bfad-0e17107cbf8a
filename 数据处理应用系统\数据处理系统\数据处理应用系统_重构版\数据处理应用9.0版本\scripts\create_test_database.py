# -*- coding: utf-8 -*-
"""
创建测试数据库脚本
用于测试Refunding和Close表功能
"""

import os
import sys
import sqlite3
from datetime import datetime, timedelta
import random

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def create_test_database():
    """创建测试数据库和数据"""
    db_path = r"C:\Users\<USER>\Desktop\Day report 3\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\database\sales_reports.db"
    
    # 确保目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建IOT_Sales表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS IOT_Sales (
        ID INTEGER PRIMARY KEY AUTOINCREMENT,
        Copartner_name TEXT,
        Order_No TEXT,
        Order_types TEXT,
        Order_status TEXT,
        Order_price TEXT,
        Payment TEXT,
        Order_time TEXT,
        Equipment_ID TEXT,
        Equipment_name TEXT,
        Branch_name TEXT,
        Payment_date TEXT,
        User_name TEXT,
        Time TEXT,
        Matched_Order_ID TEXT,
        OrderTime_dt TEXT,
        Transaction_Num TEXT,
        Import_Date TEXT,
        Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    
    # 创建ZERO_Sales表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS ZERO_Sales (
        ID INTEGER PRIMARY KEY AUTOINCREMENT,
        Copartner_name TEXT,
        Order_No TEXT,
        Order_types TEXT,
        Order_status TEXT,
        Order_price TEXT,
        Payment TEXT,
        Order_time TEXT,
        Equipment_ID TEXT,
        Equipment_name TEXT,
        Branch_name TEXT,
        Payment_date TEXT,
        User_name TEXT,
        Time TEXT,
        Matched_Order_ID TEXT,
        OrderTime_dt TEXT,
        Transaction_Num TEXT,
        Import_Date TEXT,
        Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    
    # 创建Current_Equipment表（用于测试视图关联）
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS Current_Equipment (
        Chair_Serial_No TEXT PRIMARY KEY,
        STATE TEXT,
        Location TEXT,
        Quantity INTEGER,
        Layer TEXT,
        Effective_From TEXT,
        Effective_To TEXT,
        Rental TEXT,
        DATE TEXT
    )
    """)
    
    print("✅ 基础表结构创建完成")
    
    # 插入测试数据
    print("📊 开始插入测试数据...")
    
    # 设备数据
    equipment_data = [
        ('EQ001', 'Active', 'Location A', 1, 'Layer 1', '2024-01-01', '2024-12-31', None, '2024-01-01'),
        ('EQ002', 'Active', 'Location B', 1, 'Layer 2', '2024-01-01', '2024-12-31', None, '2024-01-01'),
        ('EQ003', 'Active', 'Location C', 1, 'Layer 1', '2024-01-01', '2024-12-31', None, '2024-01-01'),
        ('EQ004', 'Active', 'Location D', 1, 'Layer 3', '2024-01-01', '2024-12-31', None, '2024-01-01'),
        ('EQ005', 'Active', 'Location E', 1, 'Layer 2', '2024-01-01', '2024-12-31', None, '2024-01-01'),
    ]
    
    cursor.executemany("""
    INSERT OR REPLACE INTO Current_Equipment 
    (Chair_Serial_No, STATE, Location, Quantity, Layer, Effective_From, Effective_To, Rental, DATE)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, equipment_data)
    
    # IOT销售数据
    base_date = datetime(2024, 6, 1)
    statuses = ['Finished', 'Refunded', 'Refunding', 'Close', 'Closed']
    
    iot_data = []
    for i in range(100):
        order_date = base_date + timedelta(days=random.randint(0, 30))
        equipment_id = f"EQ{random.randint(1, 5):03d}"
        status = random.choice(statuses)
        price = random.randint(10, 100)
        
        iot_data.append((
            f"IOT_Partner_{i%5}",  # Copartner_name
            f"IOT_ORDER_{i:04d}",  # Order_No
            "IOT_Order",  # Order_types
            status,  # Order_status
            str(price),  # Order_price
            "Credit Card",  # Payment
            order_date.strftime("%Y-%m-%d %H:%M:%S"),  # Order_time
            equipment_id,  # Equipment_ID
            f"Equipment_{equipment_id}",  # Equipment_name
            f"Branch_{i%3}",  # Branch_name
            order_date.strftime("%Y-%m-%d"),  # Payment_date
            f"User_{i%10}",  # User_name
            order_date.strftime("%H:%M:%S"),  # Time
            f"MATCH_{i:04d}",  # Matched_Order_ID
            order_date.strftime("%Y-%m-%d"),  # OrderTime_dt
            f"TXN_{i:06d}",  # Transaction_Num
            datetime.now().strftime("%Y-%m-%d")  # Import_Date
        ))
    
    cursor.executemany("""
    INSERT INTO IOT_Sales 
    (Copartner_name, Order_No, Order_types, Order_status, Order_price, Payment, Order_time, 
     Equipment_ID, Equipment_name, Branch_name, Payment_date, User_name, Time, 
     Matched_Order_ID, OrderTime_dt, Transaction_Num, Import_Date)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, iot_data)
    
    # ZERO销售数据
    zero_data = []
    for i in range(80):
        order_date = base_date + timedelta(days=random.randint(0, 30))
        equipment_id = f"EQ{random.randint(1, 5):03d}"
        status = random.choice(statuses)
        price = random.randint(15, 120)
        
        zero_data.append((
            f"ZERO_Partner_{i%4}",  # Copartner_name
            f"ZERO_ORDER_{i:04d}",  # Order_No
            "ZERO_Order",  # Order_types
            status,  # Order_status
            str(price),  # Order_price
            "Mobile Payment",  # Payment
            order_date.strftime("%Y-%m-%d %H:%M:%S"),  # Order_time
            equipment_id,  # Equipment_ID
            f"Equipment_{equipment_id}",  # Equipment_name
            f"Branch_{i%4}",  # Branch_name
            order_date.strftime("%Y-%m-%d"),  # Payment_date
            f"User_{i%8}",  # User_name
            order_date.strftime("%H:%M:%S"),  # Time
            f"MATCH_{i:04d}",  # Matched_Order_ID
            order_date.strftime("%Y-%m-%d"),  # OrderTime_dt
            f"TXN_{i+1000:06d}",  # Transaction_Num
            datetime.now().strftime("%Y-%m-%d")  # Import_Date
        ))
    
    cursor.executemany("""
    INSERT INTO ZERO_Sales 
    (Copartner_name, Order_No, Order_types, Order_status, Order_price, Payment, Order_time, 
     Equipment_ID, Equipment_name, Branch_name, Payment_date, User_name, Time, 
     Matched_Order_ID, OrderTime_dt, Transaction_Num, Import_Date)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, zero_data)
    
    conn.commit()
    
    # 统计插入的数据
    cursor.execute("SELECT COUNT(*) FROM IOT_Sales")
    iot_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM ZERO_Sales")
    zero_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM Current_Equipment")
    equipment_count = cursor.fetchone()[0]
    
    # 统计各状态的数据
    cursor.execute("SELECT Order_status, COUNT(*) FROM IOT_Sales GROUP BY Order_status")
    iot_status_stats = cursor.fetchall()
    
    cursor.execute("SELECT Order_status, COUNT(*) FROM ZERO_Sales GROUP BY Order_status")
    zero_status_stats = cursor.fetchall()
    
    conn.close()
    
    print(f"✅ 测试数据插入完成")
    print(f"📊 数据统计:")
    print(f"  • IOT_Sales: {iot_count} 条记录")
    print(f"  • ZERO_Sales: {zero_count} 条记录")
    print(f"  • Current_Equipment: {equipment_count} 条记录")
    
    print(f"\n📋 IOT_Sales状态分布:")
    for status, count in iot_status_stats:
        print(f"  • {status}: {count} 条")
    
    print(f"\n📋 ZERO_Sales状态分布:")
    for status, count in zero_status_stats:
        print(f"  • {status}: {count} 条")
    
    print(f"\n🎯 测试数据库创建完成: {db_path}")


if __name__ == "__main__":
    create_test_database()
