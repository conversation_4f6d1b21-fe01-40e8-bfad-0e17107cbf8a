#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 专门测试数据库操作是否生效
"""

import sqlite3
import pandas as pd
import os
import datetime
import shutil

def get_db_path():
    """获取数据库路径"""
    import configparser
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")
    default_db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db")
    
    if os.path.exists(config_path):
        config.read(config_path, encoding='utf-8')
        if 'Database' in config and 'db_path' in config['Database']:
            return config['Database']['db_path']
    
    return default_db_path

def main():
    print("=== 简单数据库操作测试 ===")
    
    db_path = get_db_path()
    print(f"数据库路径: {db_path}")
    
    # 备份数据库
    backup_path = f"{db_path}_simple_test_backup.db"
    shutil.copy2(db_path, backup_path)
    print(f"数据库已备份到: {backup_path}")
    
    try:
        # 1. 记录原始状态
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT SUM(Order_price) FROM IOT_Sales")
            original_amount = cursor.fetchone()[0] or 0
            print(f"原始总金额: {original_amount}")
        
        # 2. 插入测试数据
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO IOT_Sales (Equipment_ID, Order_time, Order_price, Order_No)
                VALUES (?, ?, ?, ?)
            """, ('SIMPLE_TEST', current_time, 100.0, 'SIMPLE_TEST_ORDER'))
            conn.commit()
            print(f"测试数据已插入: Equipment_ID=SIMPLE_TEST, Order_price=100.0, Order_time={current_time}")
        
        # 3. 验证插入
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT SUM(Order_price) FROM IOT_Sales")
            after_insert_amount = cursor.fetchone()[0] or 0
            print(f"插入后总金额: {after_insert_amount}")
            print(f"金额变化: {after_insert_amount - original_amount}")
        
        # 4. 创建测试退款文件
        test_data = {
            'Transaction Date': [current_time],
            'Settlement Date': [current_time[:10]],
            'Refund Date': [current_time[:10]],
            'Merchant Ref ID': ['IOT_TEST'],
            'Transaction ID': ['SIMPLE_TEST_TXN'],
            'Channel': ['Online'],
            'Order ID': ['SIMPLE_TEST'],
            'Currency': ['MYR'],
            'Billing': [100.0],
            'Actual': [100.0],
            'Refund': [50.0],  # 部分退款
            'MDR': [3.0],
            'GST': [4.5],
            'Status': ['Completed'],
            'Refund Fee': [1.0],
            'Quantity': [1],
            'Reference1': ['SIMPLE_REF1'],
            'Reference2': ['SIMPLE_REF2']
        }
        
        test_df = pd.DataFrame(test_data)
        test_file = "简单测试退款文件.xlsx"
        test_df.to_excel(test_file, index=False, engine='openpyxl')
        print(f"测试退款文件已创建: {test_file}")
        
        # 5. 运行退款脚本
        print("\n=== 运行退款脚本 ===")
        import subprocess
        result = subprocess.run([
            'python', 'Refund_process_修复版.py', 
            '--file', test_file, 
            '--platform', 'IOT'
        ], capture_output=True, text=True, encoding='utf-8')
        
        print("脚本输出:")
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        # 6. 验证结果
        print("\n=== 验证结果 ===")
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查IOT_Sales表
            cursor.execute("SELECT SUM(Order_price) FROM IOT_Sales")
            final_amount = cursor.fetchone()[0] or 0
            print(f"最终总金额: {final_amount}")
            print(f"总变化: {final_amount - original_amount}")
            
            # 检查测试数据
            cursor.execute("SELECT * FROM IOT_Sales WHERE Equipment_ID = 'SIMPLE_TEST'")
            test_records = cursor.fetchall()
            print(f"测试数据记录数: {len(test_records)}")
            for record in test_records:
                print(f"  {record}")
            
            # 检查REFUND_LIST
            cursor.execute("SELECT * FROM REFUND_LIST WHERE [Order ID] = 'SIMPLE_TEST'")
            refund_records = cursor.fetchall()
            print(f"REFUND_LIST记录数: {len(refund_records)}")
            for record in refund_records:
                print(f"  {record}")
        
        # 7. 分析结果
        print("\n=== 结果分析 ===")
        expected_final = original_amount + 100.0 - 50.0  # 插入100，退款50
        if abs(final_amount - expected_final) < 0.01:
            print("✅ 成功: 数据库操作生效，金额变化符合预期")
        elif abs(final_amount - (original_amount + 100.0)) < 0.01:
            print("⚠️ 部分成功: 测试数据插入成功，但退款未生效")
        else:
            print("❌ 失败: 数据库操作异常")
        
        if len(refund_records) > 0:
            print("✅ REFUND_LIST记录正常")
        else:
            print("❌ REFUND_LIST无记录")
    
    finally:
        # 8. 恢复数据库
        print("\n=== 恢复数据库 ===")
        shutil.copy2(backup_path, db_path)
        print("数据库已恢复")
        
        # 清理文件
        if os.path.exists(test_file):
            os.remove(test_file)
            print("测试文件已删除")
        
        if os.path.exists(backup_path):
            os.remove(backup_path)
            print("备份文件已删除")

if __name__ == "__main__":
    main()
