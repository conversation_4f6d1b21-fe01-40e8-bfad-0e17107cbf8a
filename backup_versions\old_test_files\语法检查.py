# -*- coding: utf-8 -*-
"""
语法检查脚本
"""

import ast
import sys

def check_syntax():
    script_path = r"数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析语法
        ast.parse(content)
        print("✅ 语法检查通过！")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误:")
        print(f"   文件: {e.filename}")
        print(f"   行号: {e.lineno}")
        print(f"   列号: {e.offset}")
        print(f"   错误: {e.msg}")
        print(f"   代码: {e.text}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    check_syntax()
