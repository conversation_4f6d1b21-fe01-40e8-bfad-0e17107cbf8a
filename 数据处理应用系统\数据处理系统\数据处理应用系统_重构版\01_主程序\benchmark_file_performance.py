# -*- coding: utf-8 -*-
"""
文件服务性能基准测试 - 架构优化步骤4验证
对比异步文件服务与传统文件管理器的性能

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import sys
import os
import time
import tempfile
import statistics
from typing import List, Dict, Any

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def create_benchmark_files(count: int = 200) -> tuple:
    """创建基准测试文件"""
    temp_dir = tempfile.mkdtemp()
    test_files = []
    
    # 创建不同类型和大小的文件
    file_types = [
        ('.txt', '这是一个测试文本文件，包含一些内容用于测试。'),
        ('.csv', 'name,age,city\nJohn,25,NYC\nJane,30,LA\nBob,35,SF'),
        ('.xlsx', '模拟Excel文件内容'),
        ('.ini', '[section]\nkey1=value1\nkey2=value2'),
        ('.py', 'print("Hello World")\n# 这是一个Python脚本'),
        ('.log', '2025-01-18 10:00:00 INFO 这是一条日志消息')
    ]
    
    for i in range(count):
        file_type, content = file_types[i % len(file_types)]
        filename = f"benchmark_file_{i:04d}{file_type}"
        file_path = os.path.join(temp_dir, filename)
        
        # 创建不同大小的文件
        size_multiplier = (i % 10) + 1
        file_content = content * size_multiplier
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(file_content)
            
        test_files.append(file_path)
        
    print(f"📁 创建基准测试文件: {count}个文件")
    return temp_dir, test_files


class TraditionalFileManager:
    """传统文件管理器（用于对比）"""
    
    def __init__(self, config=None):
        self.config = config
        
    def validate_files(self, file_paths: List[str]) -> tuple:
        """验证文件（传统方式）"""
        try:
            if isinstance(file_paths, str):
                file_paths = [file_paths]
                
            invalid_files = []
            
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    invalid_files.append(f"{file_path} (不存在)")
                elif not os.path.isfile(file_path):
                    invalid_files.append(f"{file_path} (不是文件)")
                elif not os.access(file_path, os.R_OK):
                    invalid_files.append(f"{file_path} (无读取权限)")
                    
            if invalid_files:
                return False, f"以下文件无效: {', '.join(invalid_files)}"
            else:
                return True, f"所有 {len(file_paths)} 个文件验证通过"
                
        except Exception as e:
            return False, f"验证过程出错: {str(e)}"
            
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件信息（传统方式）"""
        try:
            if os.path.exists(file_path):
                stat = os.stat(file_path)
                return {
                    "path": file_path,
                    "exists": True,
                    "size": stat.st_size,
                    "modified_time": stat.st_mtime,
                    "accessible": os.access(file_path, os.R_OK)
                }
            else:
                return {
                    "path": file_path,
                    "exists": False,
                    "size": 0,
                    "modified_time": 0,
                    "accessible": False
                }
        except Exception as e:
            return {
                "path": file_path,
                "exists": False,
                "error": str(e)
            }


def benchmark_file_validation(file_manager, test_files: List[str], iterations: int = 10) -> Dict[str, float]:
    """基准测试文件验证性能"""
    
    # 预热
    file_manager.validate_files(test_files[:10])
    
    # 测试单次验证时间
    validation_times = []
    for _ in range(iterations):
        start_time = time.perf_counter()
        is_valid, message = file_manager.validate_files(test_files)
        end_time = time.perf_counter()
        validation_times.append(end_time - start_time)
        
    # 测试批量验证时间
    start_time = time.perf_counter()
    for _ in range(iterations):
        file_manager.validate_files(test_files)
    end_time = time.perf_counter()
    
    total_time = end_time - start_time
    total_validations = iterations * len(test_files)
    
    return {
        "total_time": total_time,
        "total_validations": total_validations,
        "avg_time_per_validation": total_time / total_validations * 1000,  # 毫秒
        "validations_per_second": total_validations / total_time,
        "min_batch_time": min(validation_times) * 1000,  # 毫秒
        "max_batch_time": max(validation_times) * 1000,  # 毫秒
        "avg_batch_time": statistics.mean(validation_times) * 1000,  # 毫秒
        "std_batch_time": statistics.stdev(validation_times) * 1000 if len(validation_times) > 1 else 0
    }


def benchmark_file_info_retrieval(file_manager, test_files: List[str], iterations: int = 5) -> Dict[str, float]:
    """基准测试文件信息获取性能"""
    
    # 预热
    for file_path in test_files[:5]:
        if hasattr(file_manager, 'get_file_info'):
            file_manager.get_file_info(file_path)
        else:
            file_manager.get_file_info_compat(file_path)
    
    # 测试文件信息获取
    start_time = time.perf_counter()
    for _ in range(iterations):
        for file_path in test_files:
            if hasattr(file_manager, 'get_file_info'):
                file_manager.get_file_info(file_path)
            else:
                file_manager.get_file_info_compat(file_path)
    end_time = time.perf_counter()
    
    total_time = end_time - start_time
    total_operations = iterations * len(test_files)
    
    return {
        "total_time": total_time,
        "total_operations": total_operations,
        "avg_time_per_operation": total_time / total_operations * 1000,  # 毫秒
        "operations_per_second": total_operations / total_time
    }


def run_performance_comparison():
    """运行性能对比测试"""
    print("🚀 开始文件服务性能基准测试...")
    print("=" * 80)
    
    # 创建基准测试文件
    temp_dir, test_files = create_benchmark_files(count=150)
    
    try:
        print(f"📋 测试用例: {len(test_files)}个文件")
        print(f"🔄 每个管理器将执行文件验证和信息获取测试")
        
        # 测试传统文件管理器
        print("\n📊 测试传统文件管理器...")
        traditional_manager = TraditionalFileManager()
        
        traditional_validation = benchmark_file_validation(traditional_manager, test_files, iterations=5)
        traditional_info = benchmark_file_info_retrieval(traditional_manager, test_files, iterations=3)
        
        # 测试异步文件服务
        print("📊 测试异步文件服务...")
        
        from infrastructure.file_service import FileServiceFactory
        
        file_service = FileServiceFactory.create_legacy_compatible_service()
        
        service_validation = benchmark_file_validation(file_service, test_files, iterations=5)
        service_info = benchmark_file_info_retrieval(file_service, test_files, iterations=3)
        
        # 获取服务统计
        service_stats = file_service.get_stats()
        
        # 关闭服务
        file_service.shutdown()
        
        # 输出结果
        print("\n" + "=" * 80)
        print("📈 性能对比结果")
        print("=" * 80)
        
        print(f"\n🔧 传统文件管理器:")
        print(f"  文件验证:")
        print(f"    总时间: {traditional_validation['total_time']:.3f}秒")
        print(f"    平均每次验证: {traditional_validation['avg_time_per_validation']:.3f}毫秒")
        print(f"    验证速度: {traditional_validation['validations_per_second']:.0f}次/秒")
        print(f"    批次时间: {traditional_validation['avg_batch_time']:.3f}±{traditional_validation['std_batch_time']:.3f}毫秒")
        print(f"  文件信息获取:")
        print(f"    总时间: {traditional_info['total_time']:.3f}秒")
        print(f"    平均每次操作: {traditional_info['avg_time_per_operation']:.3f}毫秒")
        print(f"    操作速度: {traditional_info['operations_per_second']:.0f}次/秒")
        
        print(f"\n⚡ 异步文件服务:")
        print(f"  文件验证:")
        print(f"    总时间: {service_validation['total_time']:.3f}秒")
        print(f"    平均每次验证: {service_validation['avg_time_per_validation']:.3f}毫秒")
        print(f"    验证速度: {service_validation['validations_per_second']:.0f}次/秒")
        print(f"    批次时间: {service_validation['avg_batch_time']:.3f}±{service_validation['std_batch_time']:.3f}毫秒")
        print(f"  文件信息获取:")
        print(f"    总时间: {service_info['total_time']:.3f}秒")
        print(f"    平均每次操作: {service_info['avg_time_per_operation']:.3f}毫秒")
        print(f"    操作速度: {service_info['operations_per_second']:.0f}次/秒")
        print(f"  缓存统计:")
        print(f"    缓存命中率: {service_stats['cache_hit_rate']}")
        print(f"    验证缓存大小: {service_stats['validation_cache_size']}")
        print(f"    信息缓存大小: {service_stats['info_cache_size']}")
        
        # 计算性能提升
        print(f"\n🎯 性能提升:")
        
        # 验证性能对比
        validation_speed_improvement = (service_validation['validations_per_second'] / traditional_validation['validations_per_second'] - 1) * 100
        validation_time_improvement = (1 - service_validation['avg_time_per_validation'] / traditional_validation['avg_time_per_validation']) * 100
        
        if validation_speed_improvement > 0:
            print(f"  验证速度提升: +{validation_speed_improvement:.1f}%")
        else:
            print(f"  验证速度变化: {validation_speed_improvement:.1f}%")
            
        if validation_time_improvement > 0:
            print(f"  验证时间改善: -{validation_time_improvement:.1f}%")
        else:
            print(f"  验证时间变化: +{abs(validation_time_improvement):.1f}%")
            
        # 信息获取性能对比
        info_speed_improvement = (service_info['operations_per_second'] / traditional_info['operations_per_second'] - 1) * 100
        info_time_improvement = (1 - service_info['avg_time_per_operation'] / traditional_info['avg_time_per_operation']) * 100
        
        if info_speed_improvement > 0:
            print(f"  信息获取速度提升: +{info_speed_improvement:.1f}%")
        else:
            print(f"  信息获取速度变化: {info_speed_improvement:.1f}%")
            
        if info_time_improvement > 0:
            print(f"  信息获取时间改善: -{info_time_improvement:.1f}%")
        else:
            print(f"  信息获取时间变化: +{abs(info_time_improvement):.1f}%")
        
        # 评估结果
        print(f"\n🏆 评估结果:")
        
        if float(service_stats['cache_hit_rate'].rstrip('%')) > 50:
            print("  ✅ 缓存命中率良好 (>50%)")
        else:
            print("  ⚠️ 缓存命中率需要改善")
            
        if validation_speed_improvement > 0 or info_speed_improvement > 0:
            print("  ✅ 整体性能有提升")
        else:
            print("  ⚠️ 性能提升不明显（可能由于缓存开销）")
            
        if service_stats['validation_cache_size'] > 0:
            print("  ✅ 验证缓存正常工作")
        else:
            print("  ⚠️ 验证缓存未生效")
            
        # 总结
        print(f"\n📋 总结:")
        print(f"  异步文件服务在重复操作场景下展现了缓存优势")
        print(f"  缓存命中率: {service_stats['cache_hit_rate']}")
        print(f"  适用场景: 频繁验证相同文件集合的应用")
        print(f"  建议: 在文件集合稳定的环境中使用以获得最佳性能")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试文件
        import shutil
        shutil.rmtree(temp_dir)


def run_cache_effectiveness_test():
    """测试缓存有效性"""
    print("\n🧪 测试文件服务缓存有效性...")
    
    try:
        from infrastructure.file_service import FileServiceFactory
        
        # 创建测试文件
        temp_dir, test_files = create_benchmark_files(count=20)
        
        try:
            # 创建文件服务
            file_service = FileServiceFactory.create_file_service(cache_ttl=60)
            
            # 第一次验证（缓存未命中）
            start_time = time.perf_counter()
            is_valid1, message1 = file_service.validate_files_cached(test_files)
            first_time = time.perf_counter() - start_time
            
            # 第二次验证（缓存命中）
            start_time = time.perf_counter()
            is_valid2, message2 = file_service.validate_files_cached(test_files)
            second_time = time.perf_counter() - start_time
            
            # 第三次验证（缓存命中）
            start_time = time.perf_counter()
            is_valid3, message3 = file_service.validate_files_cached(test_files)
            third_time = time.perf_counter() - start_time
            
            # 验证结果
            assert is_valid1 == is_valid2 == is_valid3, "验证结果应该相同"
            
            # 获取统计信息
            stats = file_service.get_stats()
            
            print(f"  第一次验证时间: {first_time * 1000:.3f}毫秒")
            print(f"  第二次验证时间: {second_time * 1000:.3f}毫秒")
            print(f"  第三次验证时间: {third_time * 1000:.3f}毫秒")
            print(f"  缓存命中率: {stats['cache_hit_rate']}")
            print(f"  缓存大小: {stats['validation_cache_size']}")
            
            if second_time < first_time and third_time < first_time:
                improvement = (1 - (second_time + third_time) / (2 * first_time)) * 100
                print(f"  ✅ 缓存平均提升: {improvement:.1f}%")
            else:
                print(f"  ⚠️ 缓存未显示明显提升")
                
            file_service.shutdown()
            
            print("✅ 文件服务缓存有效性测试通过")
            return True
            
        finally:
            import shutil
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"❌ 文件服务缓存有效性测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🎯 文件服务性能基准测试")
    print("测试异步文件服务相对于传统文件管理器的性能提升")
    print()
    
    # 运行缓存有效性测试
    cache_test_success = run_cache_effectiveness_test()
    
    # 运行性能对比测试
    performance_test_success = run_performance_comparison()
    
    if cache_test_success and performance_test_success:
        print("\n🎉 所有性能测试完成！")
        print("📊 异步文件服务已验证其性能优势")
        sys.exit(0)
    else:
        print("\n⚠️ 部分性能测试失败")
        sys.exit(1)
