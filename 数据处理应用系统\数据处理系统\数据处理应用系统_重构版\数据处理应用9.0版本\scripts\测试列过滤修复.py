#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试列过滤修复脚本
"""

import os
import sys
import pandas as pd

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_table_column_definitions():
    """测试表列定义"""
    print("🔧 测试表列定义")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试所有表的列定义
        test_tables = [
            'IOT_Sales',
            'IOT_Sales_Refunding',
            'IOT_Sales_Close',
            'ZERO_Sales',
            'ZERO_Sales_Refunding',
            'ZERO_Sales_Close',
            'APP_Sales',
            'APP_Sales_Refunding',
            'APP_Sales_Close',
        ]
        
        # 创建包含source_table列的测试数据
        test_df = pd.DataFrame([
            {
                'Copartner_name': 'Test Partner',
                'Order_No': 'ORD001',
                'Order_status': 'Close',
                'Order_price': 100.0,
                'source_table': 'IOT_Sales',  # 这个列应该被过滤掉
                'invalid_column': 'should_be_removed'  # 这个列也应该被过滤掉
            }
        ])
        
        print("原始测试数据列:")
        print(f"  {list(test_df.columns)}")
        
        for table_name in test_tables:
            try:
                filtered_df = processor._filter_columns_for_table(test_df, table_name)
                
                print(f"\n✅ {table_name}:")
                print(f"  过滤后列数: {len(filtered_df.columns)}")
                print(f"  过滤后列名: {list(filtered_df.columns)}")
                
                # 验证source_table列被移除
                if 'source_table' in filtered_df.columns:
                    print(f"  ❌ source_table 列未被移除")
                    return False
                else:
                    print(f"  ✅ source_table 列已正确移除")
                
                # 验证invalid_column列被移除
                if 'invalid_column' in filtered_df.columns:
                    print(f"  ❌ invalid_column 列未被移除")
                    return False
                else:
                    print(f"  ✅ invalid_column 列已正确移除")
                    
            except Exception as e:
                print(f"❌ {table_name}: 过滤失败 - {e}")
                return False
        
        print("\n✅ 表列定义测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_source_table_column_handling():
    """测试source_table列处理"""
    print("\n🔧 测试source_table列处理")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建包含source_table列的数据
        test_data = pd.DataFrame([
            {
                'Copartner_name': 'Test Partner 1',
                'Order_No': 'ORD001',
                'Order_status': 'Finished',
                'Order_price': 100.0,
                'Order_time': '2025-07-03 10:00:00',
                'source_table': 'IOT_Sales'
            },
            {
                'Copartner_name': 'Test Partner 2',
                'Order_No': 'ORD002',
                'Order_status': 'Close',
                'Order_price': 200.0,
                'Order_time': '2025-07-03 11:00:00',
                'source_table': 'IOT_Sales_Close'
            }
        ])
        
        print("测试数据:")
        print(test_data[['Order_No', 'Order_status', 'source_table']].to_string(index=False))
        
        # 测试不同表的数据过滤
        test_cases = [
            ('IOT_Sales', 'Finished'),
            ('IOT_Sales_Close', 'Close'),
        ]
        
        for table_name, expected_status in test_cases:
            # 获取该表的数据
            table_data = processor._get_data_for_table(test_data, table_name, 'IOT')
            print(f"\n{table_name} 的数据:")
            if not table_data.empty:
                print(f"  记录数: {len(table_data)}")
                print(f"  包含source_table列: {'source_table' in table_data.columns}")
            
            # 过滤列
            filtered_data = processor._filter_columns_for_table(table_data, table_name)
            print(f"  过滤后记录数: {len(filtered_data)}")
            print(f"  过滤后包含source_table列: {'source_table' in filtered_data.columns}")
            
            if 'source_table' in filtered_data.columns:
                print(f"  ❌ source_table 列未被正确过滤")
                return False
            else:
                print(f"  ✅ source_table 列已正确过滤")
        
        print("\n✅ source_table列处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_data_flow():
    """测试完整数据流"""
    print("\n🔧 测试完整数据流")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 模拟完整的数据处理流程
        original_data = pd.DataFrame([
            {
                'Copartner_name': 'Test Partner',
                'Order_No': 'ORD001',
                'Order_status': 'Close',
                'Order_price': 100.0,
                'Order_time': '2025-07-03 10:00:00',
                'Equipment_ID': 'EQ001'
            }
        ])
        
        print("原始数据:")
        print(original_data[['Order_No', 'Order_status']].to_string(index=False))
        
        # 1. 数据分布分析
        distribution = processor._analyze_data_distribution(original_data, 'IOT')
        print(f"\n数据分布: {distribution}")
        
        # 2. 为每个表提取数据
        for table_name, count in distribution.items():
            if count > 0:
                print(f"\n处理表: {table_name}")
                
                # 提取数据
                table_data = processor._get_data_for_table(original_data, table_name, 'IOT')
                print(f"  提取的数据: {len(table_data)} 条")
                
                # 过滤列
                filtered_data = processor._filter_columns_for_table(table_data, table_name)
                print(f"  过滤后数据: {len(filtered_data)} 条")
                print(f"  列数: {len(filtered_data.columns)}")
                
                # 验证没有无效列
                invalid_columns = [col for col in filtered_data.columns if col in ['source_table', 'invalid_column']]
                if invalid_columns:
                    print(f"  ❌ 发现无效列: {invalid_columns}")
                    return False
                else:
                    print(f"  ✅ 所有列都有效")
        
        print("\n✅ 完整数据流测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 测试列过滤修复效果")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("表列定义", test_table_column_definitions),
        ("source_table列处理", test_source_table_column_handling),
        ("完整数据流", test_complete_data_flow),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 修复效果验证结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 列过滤修复成功！")
        print("\n修复内容:")
        print("1. ✅ 添加了所有缺失表的列定义")
        print("2. ✅ IOT_Sales_Close、IOT_Sales_Refunding 等表现在有正确的列定义")
        print("3. ✅ source_table 临时列会被正确过滤掉")
        print("4. ✅ 只有数据库表中实际存在的列会被保留")
        
        print("\n预期效果:")
        print("- 不再出现 'table has no column named source_table' 错误")
        print("- 数据能够正确插入到 IOT_Sales_Close 表")
        print("- 用户的IOT文件应该能够成功导入")
    else:
        print("⚠️ 部分修复需要进一步完善")

if __name__ == "__main__":
    main()
