#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查应用调用脚本Order_time格式
确保检查的是主应用程序实际使用的data_import_optimized.py脚本
"""

import os
import sys
import pandas as pd
from pathlib import Path

def verify_script_path():
    """验证脚本路径，确保检查的是应用调用的脚本"""
    print("🔧 1. 验证脚本路径")
    print("-" * 60)
    
    # 主应用程序的脚本路径逻辑
    app_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 01_主程序目录
    script_path = os.path.join(app_root, "scripts", "data_import_optimized.py")
    
    print(f"📁 应用根目录: {app_root}")
    print(f"📁 脚本路径: {script_path}")
    
    if os.path.exists(script_path):
        print("✅ 确认：这是主应用程序调用的脚本")
        return script_path
    else:
        print("❌ 错误：脚本文件不存在")
        return None

def test_actual_script_standardize_date():
    """测试应用调用脚本的standardize_date方法"""
    print("\n🔧 2. 测试应用调用脚本的standardize_date方法")
    print("-" * 60)
    
    script_path = verify_script_path()
    if not script_path:
        return False
    
    try:
        # 确保导入的是正确的脚本
        script_dir = os.path.dirname(script_path)
        sys.path.insert(0, script_dir)
        
        # 添加项目根目录到路径
        project_root = os.path.dirname(script_dir)
        sys.path.insert(0, project_root)
        
        print(f"📍 从路径导入: {script_path}")
        
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试各种时间格式（模拟用户文件中的格式）
        test_cases = [
            ("用户文件格式1", "2025-07-07 00:00:00"),
            ("用户文件格式2", "2025-07-08 12:30:45"),
            ("用户文件格式3", "2025-07-09 23:59:59"),
            ("只有日期", "2025-07-07"),
            ("Excel格式", "2025/07/07"),
        ]
        
        print("📋 测试结果（应用实际使用的脚本）:")
        all_correct = True
        
        for case_name, input_date in test_cases:
            try:
                result = processor.standardize_date(input_date)
                
                # 检查结果格式
                if result and len(result) == 10 and result.count('-') == 2 and ':' not in result:
                    print(f"✅ {case_name}: '{input_date}' → '{result}' (只有日期)")
                else:
                    print(f"❌ {case_name}: '{input_date}' → '{result}' (包含时间或格式错误)")
                    all_correct = False
                    
            except Exception as e:
                print(f"❌ {case_name}: '{input_date}' → 错误: {e}")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 应用脚本standardize_date测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_script_data_processing():
    """测试应用调用脚本的完整数据处理流程"""
    print("\n🔧 3. 测试应用调用脚本的完整数据处理流程")
    print("-" * 60)
    
    try:
        # 设置环境变量（模拟应用调用）
        os.environ['NON_INTERACTIVE'] = '1'
        os.environ['AUTO_DUPLICATE_HANDLING'] = 'overwrite'
        os.environ['AUTO_MISSING_HANDLING'] = 'ignore'
        os.environ['FORCE_CONSOLE'] = '1'
        
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建模拟用户Excel文件的数据（包含时间）
        test_data = pd.DataFrame({
            'Copartner name': ['Partner1', 'Partner2'],
            'Order No.': ['ORD001', 'ORD002'],
            'Transaction Num': ['TXN001', 'TXN002'],
            'Order types': ['普通', '普通'],
            'Order price': [100.0, 200.0],
            'Payment': [100.0, 200.0],
            'Order time': ['2025-07-07 00:00:00', '2025-07-08 12:30:45'],  # 用户文件中的格式
            'Equipment ID': ['EQ001', 'EQ002'],
            'Equipment name': ['Device1', 'Device2'],
            'Branch name': ['Branch1', 'Branch2'],
            'Payment date': ['2025-07-07', '2025-07-08'],
            'User name': ['User1', 'User2'],
            'Time': ['10:00:00', '11:00:00'],
            'Matched Order ID': ['', ''],
            'OrderTime_dt': ['2025-07-07 10:00:00', '2025-07-08 11:00:00']
        })
        
        print(f"📊 模拟用户文件数据:")
        print(f"   原始Order time: {list(test_data['Order time'])}")
        
        # 执行应用的数据处理流程
        try:
            # 1. 数据清洗
            cleaned_data = processor._clean_data(test_data.copy())
            print(f"📊 清洗后Order_time: {list(cleaned_data.get('Order_time', []))}")
            
            # 2. 数据标准化
            standardized_data = processor._standardize_data_types(cleaned_data.copy())
            
            if 'Order_time' in standardized_data.columns:
                order_times = list(standardized_data['Order_time'])
                print(f"📊 标准化后Order_time: {order_times}")
                
                # 3. 检查最终格式
                all_date_only = True
                for order_time in order_times:
                    if pd.notna(order_time):
                        time_str = str(order_time)
                        if len(time_str) != 10 or time_str.count('-') != 2 or ':' in time_str:
                            all_date_only = False
                            print(f"❌ 发现包含时间: '{time_str}'")
                            break
                
                if all_date_only:
                    print("✅ 应用处理后：所有Order_time都只包含日期")
                    return True
                else:
                    print("❌ 应用处理后：发现包含时间的Order_time")
                    return False
            else:
                print("❌ Order_time列不存在")
                return False
                
        except Exception as e:
            print(f"❌ 应用数据处理流程失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 应用脚本数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_script_version_info():
    """检查脚本版本信息"""
    print("\n🔧 4. 检查脚本版本信息")
    print("-" * 60)
    
    try:
        script_path = verify_script_path()
        if not script_path:
            return False
        
        # 读取脚本文件的前几行，检查版本信息
        with open(script_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()[:20]  # 读取前20行
        
        print("📋 脚本文件信息:")
        for i, line in enumerate(lines[:10], 1):
            if line.strip():
                print(f"  {i:2d}: {line.strip()}")
        
        # 检查是否包含我们的修复标记
        script_content = ''.join(lines)
        if "恢复原始：标准化日期格式，只保留日期部分" in script_content:
            print("✅ 确认：脚本包含Order_time格式修复")
            return True
        else:
            print("❌ 警告：脚本可能不包含最新的Order_time格式修复")
            return False
        
    except Exception as e:
        print(f"❌ 脚本版本检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 检查应用调用脚本的Order_time格式")
    print("=" * 80)
    print("🎯 目标：确认主应用程序实际使用的脚本中Order_time格式处理")
    
    tests = [
        ("脚本版本信息", check_script_version_info),
        ("standardize_date方法", test_actual_script_standardize_date),
        ("完整数据处理流程", test_actual_script_data_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*25} {test_name} {'='*25}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 检查通过")
            else:
                print(f"❌ {test_name} 检查失败")
        except Exception as e:
            print(f"❌ {test_name} 检查异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 应用调用脚本Order_time格式检查结果")
    print("=" * 80)
    
    print(f"📊 通过检查: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 检查结果：应用调用的脚本Order_time格式正确")
        print("✅ 确认：导入后只包含日期，不包含时间")
        print("✅ 格式：统一的 YYYY-MM-DD 格式")
        print("✅ 修复：已应用最新的格式修复")
        
        print("\n💡 具体表现：")
        print("  - 用户文件：'2025-07-07 12:30:45' → 数据库：'2025-07-07'")
        print("  - 用户文件：'2025-07-07 00:00:00' → 数据库：'2025-07-07'")
        print("  - 用户文件：'2025-07-07' → 数据库：'2025-07-07'")
        
    else:
        print("\n❌ 检查发现问题")
        print("🔧 应用调用的脚本可能仍有Order_time格式问题")
        print("💡 建议：")
        print("  - 确认脚本是否为最新版本")
        print("  - 检查standardize_date方法实现")
        print("  - 验证数据处理流程")
    
    # 最终答案
    print(f"\n🎯 最终答案（基于应用实际调用的脚本）：")
    if passed == total:
        print("✅ 不会出现时间 - Order_time导入后只包含日期部分")
    else:
        print("⚠️ 可能会出现时间 - 需要检查应用调用的脚本")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 检查{'完成' if success else '发现问题'}")
    input("按回车键退出...")
