#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试缺失方法修复脚本
"""

import os
import sys
import pandas as pd

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_get_data_for_table_method():
    """测试_get_data_for_table方法"""
    print("🔧 测试_get_data_for_table方法")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        test_df = pd.DataFrame([
            {'Order_No': 'ORD001', 'Order_status': 'Finished', 'Order_price': 100.0},
            {'Order_No': 'ORD002', 'Order_status': 'Refunding', 'Order_price': 200.0},
            {'Order_No': 'ORD003', 'Order_status': 'Close', 'Order_price': 300.0},
            {'Order_No': 'ORD004', 'Order_status': 'Finished', 'Order_price': 400.0},
        ])
        
        print("测试数据:")
        print(test_df.to_string(index=False))
        
        # 测试不同表的数据提取
        test_cases = [
            ('APP_Sales', 'APP'),
            ('APP_Sales_Refunding', 'APP'),
            ('APP_Sales_Close', 'APP'),
        ]
        
        for table_name, platform in test_cases:
            try:
                result_df = processor._get_data_for_table(test_df, table_name, platform)
                print(f"\n✅ {table_name}: 提取了 {len(result_df)} 条记录")
                if not result_df.empty:
                    print("提取的数据:")
                    print(result_df[['Order_No', 'Order_status']].to_string(index=False))
            except Exception as e:
                print(f"❌ {table_name}: 提取失败 - {e}")
                return False
        
        print("\n✅ _get_data_for_table方法测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analyze_data_distribution():
    """测试数据分布分析"""
    print("\n🔧 测试数据分布分析")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        test_df = pd.DataFrame([
            {'Order_No': 'ORD001', 'Order_status': 'Finished', 'Order_price': 100.0},
            {'Order_No': 'ORD002', 'Order_status': 'Refunding', 'Order_price': 200.0},
            {'Order_No': 'ORD003', 'Order_status': 'Close', 'Order_price': 300.0},
            {'Order_No': 'ORD004', 'Order_status': 'Finished', 'Order_price': 400.0},
            {'Order_No': 'ORD005', 'Order_status': 'Refunding', 'Order_price': 500.0},
        ])
        
        print("测试数据:")
        print(test_df['Order_status'].value_counts().to_string())
        
        # 测试分布分析
        distribution = processor._analyze_data_distribution(test_df, 'APP')
        
        print(f"\n数据分布分析结果:")
        for table_name, count in distribution.items():
            print(f"  {table_name}: {count} 条记录")
        
        # 验证分布结果
        expected_total = len(test_df)
        actual_total = sum(distribution.values())
        
        if actual_total == expected_total:
            print(f"✅ 分布统计正确: 总计 {actual_total} 条记录")
        else:
            print(f"❌ 分布统计错误: 期望 {expected_total}，实际 {actual_total}")
            return False
        
        print("✅ 数据分布分析测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_order_processing_flow():
    """测试API订单处理流程"""
    print("\n🔧 测试API订单处理流程")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建API订单测试数据
        api_df = pd.DataFrame([
            {'Order_No': 'API001', 'Order_status': 'Finished', 'Order_types': 'API', 'Order_price': 100.0},
            {'Order_No': 'API002', 'Order_status': 'Refunding', 'Order_types': 'API', 'Order_price': 200.0},
        ])
        
        print("API订单测试数据:")
        print(api_df.to_string(index=False))
        
        # 测试分布分析
        distribution = processor._analyze_data_distribution(api_df, 'APP')
        print(f"\nAPI订单分布: {distribution}")
        
        # 测试数据提取
        for table_name, count in distribution.items():
            if count > 0:
                table_data = processor._get_data_for_table(api_df, table_name, 'APP')
                print(f"\n{table_name}: 提取 {len(table_data)} 条记录")
                
                if len(table_data) != count:
                    print(f"❌ 数据提取数量不匹配: 期望 {count}，实际 {len(table_data)}")
                    return False
                else:
                    print(f"✅ 数据提取数量正确")
        
        print("✅ API订单处理流程测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_method_existence():
    """测试方法是否存在"""
    print("\n🔧 测试方法是否存在")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 检查关键方法是否存在
        required_methods = [
            '_get_data_for_table',
            '_analyze_data_distribution',
            '_determine_target_table',
            'smart_insert_data',
        ]
        
        for method_name in required_methods:
            if hasattr(processor, method_name):
                print(f"✅ {method_name} 方法存在")
            else:
                print(f"❌ {method_name} 方法不存在")
                return False
        
        print("✅ 所有必需方法都存在")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 测试缺失方法修复效果")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("方法存在性检查", test_method_existence),
        ("_get_data_for_table方法", test_get_data_for_table_method),
        ("数据分布分析", test_analyze_data_distribution),
        ("API订单处理流程", test_api_order_processing_flow),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 修复效果验证结果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 缺失方法修复成功！")
        print("\n修复内容:")
        print("1. ✅ 实现了 _get_data_for_table 方法")
        print("2. ✅ 支持根据Order_status筛选数据")
        print("3. ✅ 支持多表数据分发")
        print("4. ✅ 完善了API订单处理流程")
        
        print("\n预期效果:")
        print("- API订单能够正确分发到对应的表")
        print("- 不同状态的订单路由到正确的表")
        print("- 导入过程不会再出现方法缺失错误")
        print("- 用户的IOT文件应该能够成功导入")
    else:
        print("⚠️ 部分修复需要进一步完善")

if __name__ == "__main__":
    main()
