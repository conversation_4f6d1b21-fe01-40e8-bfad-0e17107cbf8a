#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的数据库路径记忆功能
为main_app.py添加更智能的数据库路径管理
"""

import os
import configparser
import shutil
from datetime import datetime
from pathlib import Path

class DatabasePathMemory:
    """数据库路径记忆管理器"""
    
    def __init__(self, config_file="config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()
        
        # 历史路径记录文件
        self.history_file = "db_path_history.txt"
        
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
        
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def get_current_db_path(self):
        """获取当前数据库路径"""
        if not self.config.has_section('Database'):
            return None
        return self.config.get('Database', 'db_path', fallback=None)
    
    def get_default_db_path(self):
        """获取默认数据库路径"""
        script_dir = os.path.dirname(os.path.abspath(__file__))
        return os.path.join(script_dir, "database", "sales_reports.db")
    
    def save_db_path(self, new_path, auto_backup=True):
        """
        保存新的数据库路径
        
        Args:
            new_path: 新的数据库路径
            auto_backup: 是否自动备份旧数据库
        """
        # 验证路径
        if not new_path or not isinstance(new_path, str):
            raise ValueError("数据库路径不能为空")
        
        # 获取当前路径
        current_path = self.get_current_db_path()
        
        # 如果路径没有变化，直接返回
        if current_path == new_path:
            print(f"✅ 数据库路径未变化: {new_path}")
            return True
        
        # 记录到历史
        self._add_to_history(current_path, new_path)
        
        # 自动备份旧数据库
        if auto_backup and current_path and os.path.exists(current_path):
            self._backup_old_database(current_path)
        
        # 确保新数据库目录存在
        new_dir = os.path.dirname(new_path)
        if new_dir and not os.path.exists(new_dir):
            os.makedirs(new_dir, exist_ok=True)
            print(f"✅ 创建数据库目录: {new_dir}")
        
        # 保存到配置
        if not self.config.has_section('Database'):
            self.config.add_section('Database')
        
        self.config.set('Database', 'db_path', new_path)
        self.save_config()
        
        print(f"✅ 数据库路径已保存: {new_path}")
        return True
    
    def _add_to_history(self, old_path, new_path):
        """添加路径变更到历史记录"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        history_entry = f"{timestamp} | {old_path or 'None'} -> {new_path}\n"
        
        try:
            with open(self.history_file, 'a', encoding='utf-8') as f:
                f.write(history_entry)
        except Exception as e:
            print(f"⚠️ 无法写入历史记录: {e}")
    
    def _backup_old_database(self, old_path):
        """备份旧数据库"""
        if not os.path.exists(old_path):
            return
        
        try:
            # 创建备份目录
            backup_dir = os.path.join(os.path.dirname(old_path), "path_change_backups")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"sales_reports_backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_name)
            
            # 复制数据库文件
            shutil.copy2(old_path, backup_path)
            print(f"✅ 已备份旧数据库: {backup_path}")
            
        except Exception as e:
            print(f"⚠️ 备份旧数据库失败: {e}")
    
    def get_path_history(self, limit=10):
        """获取路径变更历史"""
        if not os.path.exists(self.history_file):
            return []
        
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 返回最近的记录
            return lines[-limit:] if lines else []
            
        except Exception as e:
            print(f"⚠️ 读取历史记录失败: {e}")
            return []
    
    def validate_current_path(self):
        """验证当前数据库路径是否有效"""
        current_path = self.get_current_db_path()
        
        if not current_path:
            return False, "未配置数据库路径"
        
        # 检查目录是否存在
        db_dir = os.path.dirname(current_path)
        if not os.path.exists(db_dir):
            return False, f"数据库目录不存在: {db_dir}"
        
        # 检查是否可写
        if not os.access(db_dir, os.W_OK):
            return False, f"数据库目录不可写: {db_dir}"
        
        return True, "数据库路径有效"
    
    def reset_to_default(self):
        """重置为默认数据库路径"""
        default_path = self.get_default_db_path()
        return self.save_db_path(default_path, auto_backup=True)
    
    def suggest_paths(self):
        """建议可能的数据库路径"""
        suggestions = []
        
        # 默认路径
        default_path = self.get_default_db_path()
        suggestions.append(("默认路径", default_path))
        
        # 用户文档目录
        try:
            from pathlib import Path
            docs_path = Path.home() / "Documents" / "DataProcessing" / "sales_reports.db"
            suggestions.append(("文档目录", str(docs_path)))
        except:
            pass
        
        # 桌面目录
        try:
            desktop_path = Path.home() / "Desktop" / "database" / "sales_reports.db"
            suggestions.append(("桌面目录", str(desktop_path)))
        except:
            pass
        
        return suggestions

def test_db_memory():
    """测试数据库路径记忆功能"""
    print("🔍 测试数据库路径记忆功能...")
    
    memory = DatabasePathMemory()
    
    # 显示当前路径
    current = memory.get_current_db_path()
    print(f"📍 当前数据库路径: {current}")
    
    # 验证当前路径
    is_valid, message = memory.validate_current_path()
    print(f"✅ 路径验证: {message}")
    
    # 显示历史记录
    history = memory.get_path_history(5)
    if history:
        print("\n📚 最近的路径变更历史:")
        for entry in history:
            print(f"  {entry.strip()}")
    else:
        print("\n📚 暂无路径变更历史")
    
    # 显示建议路径
    suggestions = memory.suggest_paths()
    print("\n💡 建议的数据库路径:")
    for name, path in suggestions:
        exists = "✅" if os.path.exists(os.path.dirname(path)) else "❌"
        print(f"  {exists} {name}: {path}")
    
    return memory

def enhance_main_app_db_memory():
    """为main_app.py增强数据库路径记忆功能"""
    print("🔧 增强main_app.py的数据库路径记忆功能...")
    
    # 检查main_app.py是否存在
    if not os.path.exists("main_app.py"):
        print("❌ main_app.py文件不存在")
        return False
    
    # 创建数据库路径记忆管理器
    memory = DatabasePathMemory()
    
    # 验证当前配置
    is_valid, message = memory.validate_current_path()
    print(f"📍 当前数据库路径验证: {message}")
    
    if not is_valid:
        print("🔧 检测到数据库路径问题，尝试修复...")
        
        # 尝试重置为默认路径
        if memory.reset_to_default():
            print("✅ 已重置为默认数据库路径")
        else:
            print("❌ 重置失败")
            return False
    
    print("✅ 数据库路径记忆功能增强完成")
    return True

if __name__ == "__main__":
    print("🚀 数据库路径记忆功能测试...")
    print("=" * 60)
    
    # 测试功能
    memory = test_db_memory()
    
    print("\n" + "=" * 60)
    
    # 增强main_app
    enhance_main_app_db_memory()
    
    print("\n💡 使用建议:")
    print("  1. 数据库路径会自动保存到config.ini")
    print("  2. 路径变更历史保存在db_path_history.txt")
    print("  3. 旧数据库会自动备份到path_change_backups目录")
    print("  4. 可以随时重置为默认路径")
