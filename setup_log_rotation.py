#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
建立日志轮转策略
"""

import os
import json
from datetime import datetime

def create_log_rotation_config():
    """创建日志轮转配置"""
    
    print("⚙️ 建立日志轮转策略...")
    
    # 日志轮转配置
    log_config = {
        "log_rotation": {
            "enabled": True,
            "max_file_size_mb": 10,
            "max_files": 30,
            "retention_days": 30,
            "compression": True,
            "log_levels": {
                "app": "INFO",
                "error": "ERROR", 
                "debug": "DEBUG"
            }
        },
        "log_directories": {
            "main_logs": "logs",
            "detailed_logs": "详细日志",
            "backup_logs": "backup_versions/old_logs"
        },
        "cleanup_schedule": {
            "daily_cleanup": True,
            "weekly_archive": True,
            "monthly_purge": True
        },
        "file_patterns": {
            "keep_patterns": [
                "app.log",
                "app_error.log", 
                "database.log",
                "database_error.log"
            ],
            "archive_patterns": [
                "*_20*.log",
                "data_app_*.log",
                "data_import_*.log"
            ],
            "delete_patterns": [
                "test_*.log",
                "temp_*.log",
                "debug_*.log"
            ]
        }
    }
    
    # 保存配置到文件
    config_file = "log_rotation_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(log_config, f, indent=4, ensure_ascii=False)
    
    print(f"✅ 日志轮转配置已保存: {config_file}")
    
    return config_file

def create_log_cleanup_script():
    """创建日志清理脚本"""
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动日志清理脚本
定期运行以维护日志文件
"""

import os
import json
import shutil
import gzip
from datetime import datetime, timedelta
from pathlib import Path

def load_config():
    """加载日志轮转配置"""
    config_file = "log_rotation_config.json"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None

def compress_old_logs():
    """压缩旧日志文件"""
    config = load_config()
    if not config or not config.get('log_rotation', {}).get('compression'):
        return
    
    logs_dir = config['log_directories']['main_logs']
    if not os.path.exists(logs_dir):
        return
    
    cutoff_date = datetime.now() - timedelta(days=7)
    
    for file in os.listdir(logs_dir):
        if file.endswith('.log') and not file.endswith('.gz'):
            file_path = os.path.join(logs_dir, file)
            if os.path.isfile(file_path):
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                if file_time < cutoff_date:
                    # 压缩文件
                    gz_path = file_path + '.gz'
                    with open(file_path, 'rb') as f_in:
                        with gzip.open(gz_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    os.remove(file_path)
                    print(f"压缩日志: {file} -> {file}.gz")

def cleanup_old_logs():
    """清理过期日志"""
    config = load_config()
    if not config:
        return
    
    retention_days = config['log_rotation']['retention_days']
    cutoff_date = datetime.now() - timedelta(days=retention_days)
    
    logs_dir = config['log_directories']['main_logs']
    if os.path.exists(logs_dir):
        for file in os.listdir(logs_dir):
            file_path = os.path.join(logs_dir, file)
            if os.path.isfile(file_path):
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                if file_time < cutoff_date:
                    os.remove(file_path)
                    print(f"删除过期日志: {file}")

if __name__ == "__main__":
    print("🔄 开始日志维护...")
    compress_old_logs()
    cleanup_old_logs()
    print("✅ 日志维护完成")
'''
    
    script_file = "auto_log_cleanup.py"
    with open(script_file, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ 自动日志清理脚本已创建: {script_file}")
    
    return script_file

def create_directory_structure():
    """创建标准目录结构"""
    
    print("📁 创建标准目录结构...")
    
    directories = [
        "src",           # 源代码
        "config",        # 配置文件
        "logs",          # 日志文件
        "data",          # 数据文件
        "backup_versions", # 备份版本
        "scripts",       # 工具脚本
        "docs"           # 文档
    ]
    
    created_count = 0
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"  📁 创建目录: {directory}")
            created_count += 1
        else:
            print(f"  ✅ 目录已存在: {directory}")
    
    return created_count

def main():
    """主函数"""
    
    print("🚀 设置日志管理和目录结构...")
    
    # 创建配置
    config_file = create_log_rotation_config()
    
    # 创建清理脚本
    script_file = create_log_cleanup_script()
    
    # 创建目录结构
    created_dirs = create_directory_structure()
    
    print(f"\n📊 设置完成！")
    print(f"  ⚙️ 配置文件: {config_file}")
    print(f"  🔧 清理脚本: {script_file}")
    print(f"  📁 创建目录: {created_dirs} 个")
    
    print(f"\n💡 使用建议:")
    print(f"  - 定期运行 python {script_file} 进行日志维护")
    print(f"  - 查看 {config_file} 了解日志轮转配置")
    print(f"  - 将主要代码文件移动到 src/ 目录")

if __name__ == "__main__":
    main()
