# -*- coding: utf-8 -*-
"""
日志和GUI服务测试脚本 - 架构优化步骤3验证
测试异步日志服务、GUI更新服务和循环依赖解决

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import sys
import os
import time
import threading
import tkinter as tk
from typing import Any, Dict

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def test_logging_service():
    """测试异步日志服务"""
    print("\n🧪 测试异步日志服务...")
    
    try:
        from infrastructure.logging_service import LoggingService, LoggingServiceFactory, LogLevel, TabType
        from infrastructure.event_bus import EventBus
        
        # 创建事件总线
        event_bus = EventBus(max_queue_size=100, worker_threads=1)
        
        # 创建日志服务
        logging_service = LoggingServiceFactory.create_logging_service(event_bus)
        
        # 测试1：基本日志记录
        success = logging_service.log("测试信息日志", LogLevel.INFO, TabType.GENERAL)
        assert success, "日志记录应该成功"
        
        success = logging_service.log("测试警告日志", LogLevel.WARNING, TabType.PROCESSING)
        assert success, "警告日志记录应该成功"
        
        success = logging_service.log("测试错误日志", LogLevel.ERROR, TabType.BACKUP)
        assert success, "错误日志记录应该成功"
        
        # 测试2：便捷方法
        logging_service.log_info("信息消息", TabType.MANAGEMENT)
        logging_service.log_warning("警告消息", TabType.IMPORT)
        logging_service.log_error("错误消息", TabType.EXPORT)
        
        # 等待日志处理
        time.sleep(0.2)
        
        # 测试3：统计信息
        stats = logging_service.get_stats()
        assert stats["messages_processed"] >= 6, f"应该处理至少6条消息，实际: {stats['messages_processed']}"
        assert stats["average_processing_time"] > 0, "应该记录处理时间"
        
        # 测试4：消息历史
        history = logging_service.get_message_history()
        assert len(history) >= 6, f"历史记录应该有至少6条消息，实际: {len(history)}"
        
        # 测试5：自定义处理器
        custom_messages = []
        
        def custom_handler(log_msg):
            custom_messages.append(log_msg.message)
            
        logging_service.add_handler(custom_handler)
        logging_service.log_info("自定义处理器测试")
        
        time.sleep(0.1)
        assert len(custom_messages) > 0, "自定义处理器应该接收到消息"
        
        # 关闭服务
        logging_service.shutdown()
        event_bus.shutdown()
        
        print("✅ 异步日志服务测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 异步日志服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_service():
    """测试GUI更新服务"""
    print("\n🧪 测试GUI更新服务...")
    
    try:
        from infrastructure.gui_service import GUIUpdateService, GUIServiceFactory, GUIUpdateType
        from infrastructure.event_bus import EventBus
        
        # 创建简单的Tkinter根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建事件总线
        event_bus = EventBus(max_queue_size=100, worker_threads=1)
        
        # 创建GUI服务
        gui_service = GUIServiceFactory.create_gui_service(root, event_bus)
        
        # 测试1：基本GUI更新
        success = gui_service.safe_update(
            GUIUpdateType.LOG_MESSAGE,
            {"message": "测试日志消息", "tab_type": "general"}
        )
        assert success, "GUI更新应该成功"
        
        # 测试2：进度更新
        success = gui_service.update_progress(0.5, "处理中...")
        assert success, "进度更新应该成功"
        
        # 测试3：状态更新
        success = gui_service.update_status("就绪", "info")
        assert success, "状态更新应该成功"
        
        # 测试4：组件注册
        test_widget = tk.Label(root, text="测试组件")
        gui_service.register_widget("test_label", test_widget)
        
        # 测试5：兼容性接口
        gui_service.log_message("兼容性测试消息", "processing")
        
        # 等待GUI更新处理
        root.update()
        time.sleep(0.1)
        root.update()
        
        # 测试6：统计信息
        stats = gui_service.get_stats()
        assert stats["updates_processed"] >= 4, f"应该处理至少4个更新，实际: {stats['updates_processed']}"
        assert stats["registered_widgets"] >= 1, "应该有注册的组件"
        
        # 测试7：事件驱动更新
        event_bus.publish("log_message", {
            "message": "事件驱动日志",
            "tab_type": "general",
            "level": "INFO"
        })
        
        time.sleep(0.1)
        root.update()
        
        # 关闭服务
        gui_service.shutdown()
        event_bus.shutdown()
        root.destroy()
        
        print("✅ GUI更新服务测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI更新服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_logging_gui_integration():
    """测试日志和GUI服务集成"""
    print("\n🧪 测试日志和GUI服务集成...")
    
    try:
        from infrastructure.logging_service import LoggingServiceFactory, LogLevel, TabType
        from infrastructure.gui_service import GUIServiceFactory
        from infrastructure.event_bus import EventBus
        
        # 创建Tkinter根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建事件总线
        event_bus = EventBus(max_queue_size=100, worker_threads=1)
        
        # 创建日志服务
        logging_service = LoggingServiceFactory.create_logging_service(event_bus)
        
        # 创建GUI服务
        gui_service = GUIServiceFactory.create_gui_service(root, event_bus)
        
        # 创建模拟的日志文本框
        log_text = tk.Text(root)
        gui_service.register_widget("log_text_general", log_text)
        
        # 测试集成：日志服务 -> 事件总线 -> GUI服务
        logging_service.log_info("集成测试消息1", TabType.GENERAL)
        logging_service.log_warning("集成测试消息2", TabType.PROCESSING)
        logging_service.log_error("集成测试消息3", TabType.BACKUP)
        
        # 等待处理
        time.sleep(0.2)
        root.update()
        
        # 检查GUI是否收到更新
        gui_stats = gui_service.get_stats()
        log_stats = logging_service.get_stats()
        
        assert log_stats["messages_processed"] >= 3, "日志服务应该处理消息"
        assert gui_stats["updates_processed"] >= 3, "GUI服务应该收到更新"
        
        # 测试事件总线统计
        event_stats = event_bus.get_stats()
        assert event_stats["events_published"] >= 3, "事件总线应该发布事件"
        assert event_stats["events_processed"] >= 3, "事件总线应该处理事件"
        
        # 关闭服务
        logging_service.shutdown()
        gui_service.shutdown()
        event_bus.shutdown()
        root.destroy()
        
        print("✅ 日志和GUI服务集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 日志和GUI服务集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_circular_dependency_resolution():
    """测试循环依赖解决方案"""
    print("\n🧪 测试循环依赖解决方案...")
    
    try:
        from infrastructure import initialize_infrastructure, get_infrastructure, shutdown_infrastructure
        
        # 初始化基础设施
        config = {
            "feature_flags_config": "test_logging_flags.json"
        }
        
        success = initialize_infrastructure(config)
        assert success, "基础设施初始化应该成功"
        
        infrastructure = get_infrastructure()
        
        # 启用异步日志特性
        feature_flags = infrastructure.get_feature_flags()
        feature_flags.enable("use_async_logging", "测试异步日志")
        
        # 重新注册服务以应用特性开关
        infrastructure._register_core_services()
        
        # 测试服务获取
        container = infrastructure.get_container()
        
        # 检查日志服务是否已注册
        if container.has("logging_service"):
            logging_service = container.get("logging_service")
            assert logging_service is not None, "应该能获取日志服务"
            
            # 测试日志功能
            logging_service.log_info("循环依赖解决测试")
            
            time.sleep(0.1)
            
            stats = logging_service.get_stats()
            assert stats["messages_processed"] >= 1, "日志服务应该处理消息"
            
            print("✅ 循环依赖解决方案测试通过")
        else:
            print("⚠️ 日志服务未注册，可能特性开关未生效")
            
        # 关闭基础设施
        shutdown_infrastructure()
        
        # 清理测试文件
        if os.path.exists("test_logging_flags.json"):
            os.unlink("test_logging_flags.json")
            
        return True
        
    except Exception as e:
        print(f"❌ 循环依赖解决方案测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_benchmarks():
    """测试性能基准"""
    print("\n🧪 测试性能基准...")
    
    try:
        from infrastructure.logging_service import LoggingServiceFactory, LogLevel, TabType
        from infrastructure.event_bus import EventBus
        
        # 创建事件总线和日志服务
        event_bus = EventBus(max_queue_size=1000, worker_threads=2)
        logging_service = LoggingServiceFactory.create_logging_service(event_bus)
        
        # 性能测试：大量日志消息
        message_count = 1000
        start_time = time.perf_counter()
        
        for i in range(message_count):
            logging_service.log_info(f"性能测试消息 {i}", TabType.GENERAL)
            
        # 等待处理完成
        time.sleep(1)
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        
        # 检查统计信息
        stats = logging_service.get_stats()
        
        print(f"  📊 性能测试结果:")
        print(f"    消息数量: {message_count}")
        print(f"    总时间: {total_time:.3f}秒")
        print(f"    处理速度: {message_count/total_time:.0f}消息/秒")
        print(f"    平均处理时间: {stats['average_processing_time']*1000:.3f}毫秒")
        print(f"    队列高水位: {stats['queue_high_water_mark']}")
        print(f"    丢弃消息: {stats['messages_dropped']}")
        
        # 验证性能要求
        assert stats["average_processing_time"] < 0.01, "平均处理时间应该 < 10ms"
        assert stats["messages_dropped"] == 0, "不应该有消息丢弃"
        assert message_count/total_time > 100, "处理速度应该 > 100消息/秒"
        
        # 关闭服务
        logging_service.shutdown()
        event_bus.shutdown()
        
        print("✅ 性能基准测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 性能基准测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有日志和GUI服务测试"""
    print("🚀 开始日志和GUI服务测试...")
    print("=" * 60)
    
    tests = [
        ("异步日志服务", test_logging_service),
        ("GUI更新服务", test_gui_service),
        ("日志GUI集成", test_logging_gui_integration),
        ("循环依赖解决", test_circular_dependency_resolution),
        ("性能基准", test_performance_benchmarks)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            failed += 1
            
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}个通过, {failed}个失败")
    
    if failed == 0:
        print("🎉 所有日志和GUI服务测试通过！架构优化步骤3完成")
        return True
    else:
        print("⚠️ 部分测试失败，需要修复问题")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
