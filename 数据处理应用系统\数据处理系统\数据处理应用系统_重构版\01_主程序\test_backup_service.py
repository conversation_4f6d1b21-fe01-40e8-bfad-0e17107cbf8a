# -*- coding: utf-8 -*-
"""
备份服务测试脚本 - 架构优化步骤5验证
测试异步备份服务、任务队列管理和增量备份功能

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import sys
import os
import time
import tempfile
import sqlite3
import threading
from typing import Any, Dict, List

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def create_test_database(db_path: str):
    """创建测试数据库"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建测试表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS test_data (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            value INTEGER,
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 插入测试数据
    test_data = [
        ('测试数据1', 100),
        ('测试数据2', 200),
        ('测试数据3', 300),
        ('测试数据4', 400),
        ('测试数据5', 500)
    ]
    
    cursor.executemany('INSERT INTO test_data (name, value) VALUES (?, ?)', test_data)
    conn.commit()
    conn.close()
    
    print(f"✅ 创建测试数据库: {db_path}")


def create_test_files():
    """创建测试文件"""
    temp_dir = tempfile.mkdtemp()
    test_files = []
    
    # 创建测试数据库
    db_path = os.path.join(temp_dir, "test_database.db")
    create_test_database(db_path)
    test_files.append(db_path)
    
    # 创建其他测试文件
    files_to_create = [
        ("test_config.ini", "[section]\nkey1=value1\nkey2=value2"),
        ("test_data.csv", "name,age,city\nJohn,25,NYC\nJane,30,LA"),
        ("test_script.py", "print('Hello World')\n# 这是一个测试脚本"),
        ("test_document.txt", "这是一个测试文档，包含一些内容。")
    ]
    
    for filename, content in files_to_create:
        file_path = os.path.join(temp_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        test_files.append(file_path)
        
    return temp_dir, test_files


def test_backup_service_basic():
    """测试备份服务基本功能"""
    print("\n🧪 测试备份服务基本功能...")
    
    try:
        from infrastructure.backup_service import BackupServiceFactory, BackupType, BackupPriority, BackupStatus
        
        # 创建测试文件
        temp_dir, test_files = create_test_files()
        
        try:
            # 创建备份服务
            backup_service = BackupServiceFactory.create_backup_service()
            
            # 测试1：创建备份任务
            task_id = backup_service.create_backup_task(
                source_path=test_files[0],  # 数据库文件
                backup_type=BackupType.FULL,
                priority=BackupPriority.HIGH
            )
            
            assert task_id is not None, "任务ID不应该为空"
            
            # 测试2：获取任务状态
            task_status = backup_service.get_task_status(task_id)
            assert task_status is not None, "应该能获取任务状态"
            assert task_status["status"] in ["pending", "running"], "任务应该处于待处理或运行状态"
            
            # 等待任务完成
            max_wait = 30  # 最多等待30秒
            waited = 0
            
            while waited < max_wait:
                task_status = backup_service.get_task_status(task_id)
                if task_status and task_status["status"] in ["completed", "failed"]:
                    break
                time.sleep(1)
                waited += 1
                
            # 验证任务完成
            final_status = backup_service.get_task_status(task_id)
            assert final_status["status"] == "completed", f"任务应该完成，实际状态: {final_status['status']}"
            
            # 验证备份文件存在
            backup_path = final_status["backup_path"]
            assert os.path.exists(backup_path), f"备份文件应该存在: {backup_path}"
            
            # 测试3：获取活跃任务
            active_tasks = backup_service.get_active_tasks()
            print(f"  活跃任务数量: {len(active_tasks)}")
            
            # 测试4：获取已完成任务
            completed_tasks = backup_service.get_completed_tasks()
            assert len(completed_tasks) >= 1, "应该有已完成的任务"
            
            # 测试5：统计信息
            stats = backup_service.get_stats()
            assert stats["tasks_created"] >= 1, "应该有创建的任务"
            assert stats["tasks_completed"] >= 1, "应该有完成的任务"
            
            print(f"  任务创建: {stats['tasks_created']}")
            print(f"  任务完成: {stats['tasks_completed']}")
            print(f"  平均备份时间: {stats['average_backup_time']:.3f}秒")
            
            # 关闭服务
            backup_service.shutdown()
            
            print("✅ 备份服务基本功能测试通过")
            return True
            
        finally:
            # 清理测试文件
            import shutil
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"❌ 备份服务基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_backup_task_queue():
    """测试备份任务队列"""
    print("\n🧪 测试备份任务队列...")
    
    try:
        from infrastructure.backup_service import BackupServiceFactory, BackupType, BackupPriority
        
        # 创建测试文件
        temp_dir, test_files = create_test_files()
        
        try:
            # 创建备份服务
            backup_service = BackupServiceFactory.create_backup_service(max_workers=2)
            
            # 创建多个备份任务
            task_ids = []
            for i, file_path in enumerate(test_files):
                priority = BackupPriority.HIGH if i == 0 else BackupPriority.NORMAL
                task_id = backup_service.create_backup_task(
                    source_path=file_path,
                    backup_type=BackupType.FULL,
                    priority=priority
                )
                task_ids.append(task_id)
                
            print(f"  创建了 {len(task_ids)} 个备份任务")
            
            # 等待所有任务完成
            max_wait = 60  # 最多等待60秒
            waited = 0
            
            while waited < max_wait:
                completed_count = 0
                for task_id in task_ids:
                    status = backup_service.get_task_status(task_id)
                    if status and status["status"] in ["completed", "failed"]:
                        completed_count += 1
                        
                if completed_count == len(task_ids):
                    break
                    
                time.sleep(1)
                waited += 1
                
            # 验证所有任务完成
            completed_tasks = backup_service.get_completed_tasks()
            assert len(completed_tasks) >= len(task_ids), "所有任务应该完成"
            
            # 检查统计信息
            stats = backup_service.get_stats()
            print(f"  队列高水位: {stats['queue_high_water_mark']}")
            print(f"  工作线程数: {stats['worker_threads']}")
            
            # 关闭服务
            backup_service.shutdown()
            
            print("✅ 备份任务队列测试通过")
            return True
            
        finally:
            # 清理测试文件
            import shutil
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"❌ 备份任务队列测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_incremental_backup():
    """测试增量备份"""
    print("\n🧪 测试增量备份...")
    
    try:
        from infrastructure.backup_service import BackupServiceFactory, BackupType
        
        # 创建测试文件
        temp_dir, test_files = create_test_files()
        test_file = test_files[1]  # 使用文本文件
        
        try:
            # 创建备份服务
            backup_service = BackupServiceFactory.create_backup_service()
            
            # 第一次全量备份
            task_id1 = backup_service.create_backup_task(
                source_path=test_file,
                backup_type=BackupType.FULL
            )
            
            # 等待第一次备份完成
            max_wait = 30
            waited = 0
            
            while waited < max_wait:
                status = backup_service.get_task_status(task_id1)
                if status and status["status"] in ["completed", "failed"]:
                    break
                time.sleep(1)
                waited += 1
                
            first_status = backup_service.get_task_status(task_id1)
            assert first_status["status"] == "completed", "第一次备份应该完成"
            
            # 修改源文件
            time.sleep(1)  # 确保修改时间不同
            with open(test_file, 'a', encoding='utf-8') as f:
                f.write("\n新增内容用于测试增量备份")
                
            # 第二次增量备份
            task_id2 = backup_service.create_backup_task(
                source_path=test_file,
                backup_type=BackupType.INCREMENTAL
            )
            
            # 等待第二次备份完成
            waited = 0
            while waited < max_wait:
                status = backup_service.get_task_status(task_id2)
                if status and status["status"] in ["completed", "failed"]:
                    break
                time.sleep(1)
                waited += 1
                
            second_status = backup_service.get_task_status(task_id2)
            assert second_status["status"] == "completed", "第二次备份应该完成"
            
            # 验证两次备份的路径不同
            assert first_status["backup_path"] != second_status["backup_path"], "备份路径应该不同"
            
            # 第三次增量备份（文件未变化）
            task_id3 = backup_service.create_backup_task(
                source_path=test_file,
                backup_type=BackupType.INCREMENTAL
            )
            
            # 等待第三次备份完成
            waited = 0
            while waited < max_wait:
                status = backup_service.get_task_status(task_id3)
                if status and status["status"] in ["completed", "failed"]:
                    break
                time.sleep(1)
                waited += 1
                
            third_status = backup_service.get_task_status(task_id3)
            print(f"  第三次备份状态: {third_status['status']}")
            
            # 获取备份历史
            history = backup_service.get_backup_history()
            assert len(history) >= 2, "应该有备份历史记录"
            
            print(f"  备份历史记录: {len(history)}条")
            
            # 关闭服务
            backup_service.shutdown()
            
            print("✅ 增量备份测试通过")
            return True
            
        finally:
            # 清理测试文件
            import shutil
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"❌ 增量备份测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_backup_service_integration():
    """测试备份服务集成"""
    print("\n🧪 测试备份服务集成...")
    
    try:
        from infrastructure import initialize_infrastructure, get_infrastructure, shutdown_infrastructure
        
        # 初始化基础设施
        config = {
            "feature_flags_config": "test_backup_service_flags.json"
        }
        
        success = initialize_infrastructure(config)
        assert success, "基础设施初始化应该成功"
        
        infrastructure = get_infrastructure()
        
        # 启用备份服务特性
        feature_flags = infrastructure.get_feature_flags()
        feature_flags.enable("use_backup_service", "测试备份服务")
        
        # 重新注册服务以应用特性开关
        infrastructure._register_core_services()
        
        # 测试服务获取
        container = infrastructure.get_container()
        
        # 检查备份服务是否已注册
        if container.has("backup_manager"):
            backup_manager = container.get("backup_manager")
            assert backup_manager is not None, "应该能获取备份管理器"
            
            # 创建测试数据库
            temp_dir, test_files = create_test_files()
            
            try:
                # 测试兼容性接口
                success = backup_manager.backup_database()
                print(f"  数据库备份结果: {success}")
                
                # 测试恢复接口（使用测试文件）
                test_backup = test_files[0]  # 使用测试数据库作为备份文件
                success = backup_manager.restore_database(test_backup)
                print(f"  数据库恢复结果: {success}")
                
                print("✅ 备份服务集成测试通过")
                
            finally:
                # 清理测试文件
                import shutil
                shutil.rmtree(temp_dir)
                
        else:
            print("⚠️ 备份服务未注册，可能特性开关未生效")
            
        # 关闭基础设施
        shutdown_infrastructure()
        
        # 清理测试文件
        if os.path.exists("test_backup_service_flags.json"):
            os.unlink("test_backup_service_flags.json")
            
        return True
        
    except Exception as e:
        print(f"❌ 备份服务集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_backup_policies():
    """测试备份策略"""
    print("\n🧪 测试备份策略...")
    
    try:
        from infrastructure.backup_service import BackupServiceFactory, BackupPolicy, BackupType
        
        # 创建测试文件
        temp_dir, test_files = create_test_files()
        
        try:
            # 创建备份服务
            backup_service = BackupServiceFactory.create_backup_service()
            
            # 创建自定义备份策略
            custom_policy = BackupPolicy(
                name="test_policy",
                backup_type=BackupType.INCREMENTAL,
                schedule_interval=1800,  # 30分钟
                max_backups=5,
                compression=True,
                verify_backup=True,
                cleanup_old=True
            )
            
            # 添加策略
            backup_service.add_backup_policy(custom_policy)
            
            # 使用策略创建备份任务
            task_id = backup_service.create_backup_task(
                source_path=test_files[0],
                backup_type=BackupType.INCREMENTAL,
                policy_name="test_policy"
            )
            
            # 等待任务完成
            max_wait = 30
            waited = 0
            
            while waited < max_wait:
                status = backup_service.get_task_status(task_id)
                if status and status["status"] in ["completed", "failed"]:
                    break
                time.sleep(1)
                waited += 1
                
            # 验证任务使用了正确的策略
            final_status = backup_service.get_task_status(task_id)
            assert final_status["status"] == "completed", "任务应该完成"
            
            policy_info = final_status["metadata"]["policy"]
            assert policy_info["name"] == "test_policy", "应该使用正确的策略"
            assert policy_info["verify_backup"] == True, "策略配置应该正确"
            
            print(f"  使用策略: {policy_info['name']}")
            print(f"  策略配置: 验证备份={policy_info['verify_backup']}, 压缩={policy_info['compression']}")
            
            # 关闭服务
            backup_service.shutdown()
            
            print("✅ 备份策略测试通过")
            return True
            
        finally:
            # 清理测试文件
            import shutil
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"❌ 备份策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有备份服务测试"""
    print("🚀 开始备份服务测试...")
    print("=" * 60)
    
    tests = [
        ("备份服务基本功能", test_backup_service_basic),
        ("备份任务队列", test_backup_task_queue),
        ("增量备份", test_incremental_backup),
        ("备份服务集成", test_backup_service_integration),
        ("备份策略", test_backup_policies)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            failed += 1
            
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}个通过, {failed}个失败")
    
    if failed == 0:
        print("🎉 所有备份服务测试通过！架构优化步骤5完成")
        return True
    else:
        print("⚠️ 部分测试失败，需要修复问题")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
