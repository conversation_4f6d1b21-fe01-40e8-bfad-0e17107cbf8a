#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终路径验证脚本
全面检查所有配置文件和脚本中的硬编码路径问题
"""

import os
import re
import configparser
import json
from pathlib import Path

def scan_hardcoded_paths():
    """扫描所有文件中的硬编码路径"""
    print("🔍 扫描硬编码路径...")
    
    # 要检查的文件模式
    file_patterns = [
        "*.py",
        "*.ini", 
        "*.json",
        "*.bat",
        "*.cmd"
    ]
    
    # 硬编码路径模式
    hardcoded_patterns = [
        r'["\']C:[\\\/]Users[\\\/]User[\\\/]Desktop[\\\/]Day Report[\\\/][^"\']*["\']',  # 旧路径
        r'["\']C:[\\\/]Users[\\\/]User[\\\/]Desktop[\\\/]June[\\\/][^"\']*["\']',       # June路径
        r'["\']C:[\\\/]Users[\\\/]User[\\\/]Desktop[\\\/]July[\\\/][^"\']*["\']',      # July路径
        r'r["\']C:[\\\/]Users[\\\/]User[\\\/]Desktop[\\\/]Day Report[\\\/][^"\']*["\']', # raw string
    ]
    
    issues = []
    
    # 扫描所有文件
    for root, dirs, files in os.walk("."):
        # 跳过备份目录
        if any(skip in root for skip in ["backup_versions", "__pycache__", ".git", "node_modules"]):
            continue
            
        for file in files:
            file_path = os.path.join(root, file)
            
            # 检查文件扩展名
            if any(file.endswith(pattern[1:]) for pattern in file_patterns):
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    # 检查硬编码路径
                    for pattern in hardcoded_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            for match in matches:
                                issues.append({
                                    'file': file_path,
                                    'pattern': match,
                                    'type': 'hardcoded_path'
                                })
                                
                except Exception as e:
                    print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    return issues

def check_config_files():
    """检查配置文件的正确性"""
    print("\n🔍 检查配置文件...")
    
    config_files = [
        "config.ini",
        "config.json",
        "数据处理应用系统/config.ini",
        "数据处理应用系统/config.json",
        "数据处理应用系统/数据处理系统/数据处理应用系统_重构版/03_配置文件/config.ini",
        "数据处理应用系统/数据处理系统/数据处理应用系统_重构版/01_主程序/config.ini"
    ]
    
    results = {}
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"\n📄 检查: {config_file}")
            
            if config_file.endswith('.ini'):
                # 检查INI文件
                config = configparser.ConfigParser()
                try:
                    config.read(config_file, encoding='utf-8')
                    
                    # 检查数据库路径
                    if config.has_section('Database'):
                        db_path = config.get('Database', 'db_path', fallback='')
                        if 'Day report 3' in db_path:
                            print(f"  ✅ 数据库路径正确: {db_path}")
                            results[config_file] = True
                        elif 'Day Report' in db_path:
                            print(f"  ❌ 数据库路径错误（旧路径）: {db_path}")
                            results[config_file] = False
                        else:
                            print(f"  ℹ️ 数据库路径: {db_path}")
                            results[config_file] = True
                    else:
                        print(f"  ⚠️ 缺少Database配置段")
                        results[config_file] = False
                        
                except Exception as e:
                    print(f"  ❌ 解析失败: {e}")
                    results[config_file] = False
                    
            elif config_file.endswith('.json'):
                # 检查JSON文件
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 检查路径
                    has_old_path = False
                    json_str = json.dumps(data)
                    if 'Day Report' in json_str and 'Day report 3' not in json_str:
                        has_old_path = True
                    
                    if has_old_path:
                        print(f"  ❌ 包含旧路径")
                        results[config_file] = False
                    else:
                        print(f"  ✅ 路径正确")
                        results[config_file] = True
                        
                except Exception as e:
                    print(f"  ❌ 解析失败: {e}")
                    results[config_file] = False
        else:
            print(f"❌ 文件不存在: {config_file}")
            results[config_file] = False
    
    return results

def check_database_memory():
    """检查数据库路径记忆功能"""
    print("\n🔍 检查数据库路径记忆功能...")
    
    # 检查主配置文件
    config_file = "config.ini"
    if os.path.exists(config_file):
        config = configparser.ConfigParser()
        config.read(config_file, encoding='utf-8')
        
        if config.has_section('Database'):
            db_path = config.get('Database', 'db_path', fallback='')
            print(f"📍 当前配置的数据库路径: {db_path}")
            
            # 检查路径是否存在
            if db_path:
                db_dir = os.path.dirname(db_path)
                if os.path.exists(db_dir):
                    print(f"✅ 数据库目录存在: {db_dir}")
                    
                    if os.path.exists(db_path):
                        print(f"✅ 数据库文件存在")
                    else:
                        print(f"ℹ️ 数据库文件不存在（首次使用时会创建）")
                    
                    return True
                else:
                    print(f"❌ 数据库目录不存在: {db_dir}")
                    return False
            else:
                print(f"❌ 数据库路径未配置")
                return False
        else:
            print(f"❌ 缺少Database配置段")
            return False
    else:
        print(f"❌ 主配置文件不存在: {config_file}")
        return False

def generate_summary_report(hardcoded_issues, config_results, db_memory_ok):
    """生成总结报告"""
    print("\n" + "="*60)
    print("📊 最终验证报告")
    print("="*60)
    
    # 硬编码路径问题
    print(f"\n🔍 硬编码路径检查:")
    if hardcoded_issues:
        print(f"  ❌ 发现 {len(hardcoded_issues)} 个硬编码路径问题:")
        for issue in hardcoded_issues[:10]:  # 显示前10个
            print(f"    📄 {issue['file']}: {issue['pattern']}")
        if len(hardcoded_issues) > 10:
            print(f"    ... 还有 {len(hardcoded_issues) - 10} 个问题")
    else:
        print(f"  ✅ 未发现硬编码路径问题")
    
    # 配置文件检查
    print(f"\n📄 配置文件检查:")
    passed_configs = sum(1 for result in config_results.values() if result)
    total_configs = len(config_results)
    print(f"  📊 通过: {passed_configs}/{total_configs}")
    
    for config_file, result in config_results.items():
        status = "✅" if result else "❌"
        print(f"    {status} {config_file}")
    
    # 数据库记忆功能
    print(f"\n💾 数据库路径记忆功能:")
    if db_memory_ok:
        print(f"  ✅ 数据库路径记忆功能正常")
    else:
        print(f"  ❌ 数据库路径记忆功能异常")
    
    # 总体评估
    print(f"\n🎯 总体评估:")
    issues_count = len(hardcoded_issues)
    config_issues = total_configs - passed_configs
    
    if issues_count == 0 and config_issues == 0 and db_memory_ok:
        print(f"  🎉 所有检查都通过！项目配置完全正确！")
        return True
    else:
        print(f"  ⚠️ 发现问题:")
        if issues_count > 0:
            print(f"    - {issues_count} 个硬编码路径问题")
        if config_issues > 0:
            print(f"    - {config_issues} 个配置文件问题")
        if not db_memory_ok:
            print(f"    - 数据库路径记忆功能问题")
        return False

def main():
    """主函数"""
    print("🚀 开始最终路径验证...")
    
    # 1. 扫描硬编码路径
    hardcoded_issues = scan_hardcoded_paths()
    
    # 2. 检查配置文件
    config_results = check_config_files()
    
    # 3. 检查数据库记忆功能
    db_memory_ok = check_database_memory()
    
    # 4. 生成总结报告
    all_ok = generate_summary_report(hardcoded_issues, config_results, db_memory_ok)
    
    if all_ok:
        print("\n🎊 恭喜！所有路径配置都已正确修复！")
    else:
        print("\n🔧 请根据上述报告修复发现的问题")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
