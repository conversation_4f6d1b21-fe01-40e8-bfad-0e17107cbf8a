#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Transaction ID匹配逻辑单元测试
测试退款脚本中Transaction ID匹配的各种场景和边缘情况
"""

import os
import sys
import unittest
import pandas as pd
from unittest.mock import Mock, patch
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置测试环境
os.environ['NON_INTERACTIVE'] = '1'

class TestTransactionIDLogic(unittest.TestCase):
    """Transaction ID匹配逻辑测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 模拟RefundProcessor，避免数据库依赖
        from refund_process_optimized import RefundProcessor
        
        # 创建模拟的处理器
        with patch('refund_process_optimized.get_logger'):
            self.processor = RefundProcessor()
            self.processor.logger = Mock()
    
    def test_determine_matching_strategy_with_transaction_id(self):
        """测试确定匹配策略 - 有Transaction ID"""
        # 准备测试数据
        refund_df = pd.DataFrame({
            'Transaction ID': ['TXN001', 'TXN002', None],
            'Order ID': ['ORD001', 'ORD002', 'ORD003'],
            'Refund': [100.0, 200.0, 300.0]
        })
        
        sales_df = pd.DataFrame({
            'Transaction_Num': ['TXN001', 'TXN002'],
            'Order_No': ['ORD001', 'ORD002'],
            'Order_price': [100.0, 200.0]
        })
        
        # 执行测试
        strategy = self.processor._determine_matching_strategy(refund_df, sales_df)
        
        # 验证结果
        self.assertEqual(strategy, 'transaction_id')
    
    def test_determine_matching_strategy_without_transaction_id(self):
        """测试确定匹配策略 - 无Transaction ID"""
        # 准备测试数据
        refund_df = pd.DataFrame({
            'Order ID': ['ORD001', 'ORD002'],
            'Refund': [100.0, 200.0]
        })
        
        sales_df = pd.DataFrame({
            'Order_No': ['ORD001', 'ORD002'],
            'Order_price': [100.0, 200.0]
        })
        
        # 执行测试
        strategy = self.processor._determine_matching_strategy(refund_df, sales_df)
        
        # 验证结果
        self.assertEqual(strategy, 'order_id')
    
    def test_match_by_transaction_id_success(self):
        """测试Transaction ID匹配 - 成功场景"""
        # 准备测试数据
        refund_row = pd.Series({
            'Transaction ID': 'TXN001',
            'Order ID': 'ORD001',
            'Refund': 100.0
        })
        
        sales_df = pd.DataFrame({
            'Transaction_Num': ['TXN001', 'TXN002'],
            'Order_No': ['ORD001', 'ORD002'],
            'Order_price': [100.0, 200.0],
            'Order_status': ['Finished', 'Finished']
        })
        
        # 执行测试
        result = self.processor._match_by_transaction_id(refund_row, sales_df)
        
        # 验证结果
        self.assertTrue(result['matched'])
        self.assertEqual(len(result['sales_records']), 1)
        self.assertEqual(result['sales_records'][0]['Transaction_Num'], 'TXN001')
        self.assertEqual(result['strategy_used'], 'transaction_id')
    
    def test_match_by_transaction_id_not_found(self):
        """测试Transaction ID匹配 - 未找到匹配"""
        # 准备测试数据
        refund_row = pd.Series({
            'Transaction ID': 'TXN999',
            'Order ID': 'ORD999',
            'Refund': 100.0
        })
        
        sales_df = pd.DataFrame({
            'Transaction_Num': ['TXN001', 'TXN002'],
            'Order_No': ['ORD001', 'ORD002'],
            'Order_price': [100.0, 200.0]
        })
        
        # 执行测试
        result = self.processor._match_by_transaction_id(refund_row, sales_df)
        
        # 验证结果
        self.assertFalse(result['matched'])
        self.assertEqual(len(result['sales_records']), 0)
        self.assertIn('TXN999 未找到匹配', result['reason'])
    
    def test_match_by_transaction_id_invalid_id(self):
        """测试Transaction ID匹配 - 无效Transaction ID"""
        # 准备测试数据
        invalid_ids = [None, '', 'nan', 'None', pd.NA]
        
        sales_df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_No': ['ORD001'],
            'Order_price': [100.0]
        })
        
        for invalid_id in invalid_ids:
            with self.subTest(invalid_id=invalid_id):
                refund_row = pd.Series({
                    'Transaction ID': invalid_id,
                    'Order ID': 'ORD001',
                    'Refund': 100.0
                })
                
                # 执行测试
                result = self.processor._match_by_transaction_id(refund_row, sales_df)
                
                # 验证结果
                self.assertFalse(result['matched'])
                self.assertIn('Transaction ID为空或无效', result['reason'])
    
    def test_match_by_order_id_success(self):
        """测试Order ID匹配 - 成功场景"""
        # 准备测试数据
        refund_row = pd.Series({
            'Order ID': 'ORD001',
            'Refund': 100.0
        })
        
        sales_df = pd.DataFrame({
            'Order_No': ['ORD001', 'ORD002'],
            'Order_price': [100.0, 200.0],
            'Order_status': ['Finished', 'Finished']
        })
        
        # 执行测试
        result = self.processor._match_by_order_id(refund_row, sales_df)
        
        # 验证结果
        self.assertTrue(result['matched'])
        self.assertEqual(len(result['sales_records']), 1)
        self.assertEqual(result['sales_records'][0]['Order_No'], 'ORD001')
        self.assertEqual(result['strategy_used'], 'order_id')
    
    def test_match_by_order_id_not_found(self):
        """测试Order ID匹配 - 未找到匹配"""
        # 准备测试数据
        refund_row = pd.Series({
            'Order ID': 'ORD999',
            'Refund': 100.0
        })
        
        sales_df = pd.DataFrame({
            'Order_No': ['ORD001', 'ORD002'],
            'Order_price': [100.0, 200.0]
        })
        
        # 执行测试
        result = self.processor._match_by_order_id(refund_row, sales_df)
        
        # 验证结果
        self.assertFalse(result['matched'])
        self.assertEqual(len(result['sales_records']), 0)
        self.assertIn('ORD999 未找到匹配', result['reason'])
    
    def test_create_matched_record(self):
        """测试创建匹配记录"""
        # 准备测试数据
        sales_row = pd.Series({
            'Order_No': 'ORD001',
            'Order_price': 100.0,
            'Order_status': 'Finished',
            'Equipment_ID': 'EQ001'
        })
        
        refund_row = pd.Series({
            'Order ID': 'ORD001',
            'Refund': 50.0,
            'Transaction Date': '2025-01-01'
        })
        
        # 执行测试
        matched_record = self.processor._create_matched_record(sales_row, refund_row)
        
        # 验证结果
        self.assertEqual(matched_record['Order_No'], 'ORD001')
        self.assertEqual(matched_record['Refund_Amount'], 50.0)
        self.assertEqual(matched_record['Original_Order_Status'], 'Finished')
        self.assertIn('Refund_Date', matched_record)
    
    def test_match_single_refund_record_transaction_id_strategy(self):
        """测试单条记录匹配 - Transaction ID策略"""
        # 准备测试数据
        refund_row = pd.Series({
            'Transaction ID': 'TXN001',
            'Order ID': 'ORD001',
            'Refund': 100.0
        })
        
        sales_df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_No': ['ORD001'],
            'Order_price': [100.0],
            'Order_status': ['Finished']
        })
        
        # 执行测试
        result = self.processor._match_single_refund_record(refund_row, sales_df, 'transaction_id')
        
        # 验证结果
        self.assertTrue(result['matched'])
        self.assertEqual(result['strategy_used'], 'transaction_id')
        self.assertEqual(len(result['sales_records']), 1)
    
    def test_match_single_refund_record_order_id_strategy(self):
        """测试单条记录匹配 - Order ID策略"""
        # 准备测试数据
        refund_row = pd.Series({
            'Order ID': 'ORD001',
            'Refund': 100.0
        })
        
        sales_df = pd.DataFrame({
            'Order_No': ['ORD001'],
            'Order_price': [100.0],
            'Order_status': ['Finished']
        })
        
        # 执行测试
        result = self.processor._match_single_refund_record(refund_row, sales_df, 'order_id')
        
        # 验证结果
        self.assertTrue(result['matched'])
        self.assertEqual(result['strategy_used'], 'order_id')
        self.assertEqual(len(result['sales_records']), 1)
    
    def test_edge_case_empty_dataframes(self):
        """测试边缘情况 - 空DataFrame"""
        # 空的退款数据
        empty_refund_df = pd.DataFrame(columns=['Transaction ID', 'Order ID', 'Refund'])
        sales_df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_No': ['ORD001'],
            'Order_price': [100.0]
        })
        
        strategy = self.processor._determine_matching_strategy(empty_refund_df, sales_df)
        self.assertEqual(strategy, 'order_id')  # 因为没有有效的Transaction ID
        
        # 空的销售数据
        refund_df = pd.DataFrame({
            'Transaction ID': ['TXN001'],
            'Order ID': ['ORD001'],
            'Refund': [100.0]
        })
        empty_sales_df = pd.DataFrame(columns=['Transaction_Num', 'Order_No', 'Order_price'])
        
        strategy = self.processor._determine_matching_strategy(refund_df, empty_sales_df)
        self.assertEqual(strategy, 'order_id')  # 因为销售数据为空
    
    def test_edge_case_special_characters_in_transaction_id(self):
        """测试边缘情况 - Transaction ID包含特殊字符"""
        # 准备测试数据
        special_ids = ['TXN-001', 'TXN_001', 'TXN.001', 'TXN 001']
        
        for special_id in special_ids:
            with self.subTest(special_id=special_id):
                refund_row = pd.Series({
                    'Transaction ID': special_id,
                    'Order ID': 'ORD001',
                    'Refund': 100.0
                })
                
                sales_df = pd.DataFrame({
                    'Transaction_Num': [special_id.strip()],  # 销售数据中的ID已清理
                    'Order_No': ['ORD001'],
                    'Order_price': [100.0]
                })
                
                # 执行测试
                result = self.processor._match_by_transaction_id(refund_row, sales_df)
                
                # 验证结果（应该匹配，因为都会被strip()处理）
                self.assertTrue(result['matched'], f"特殊字符ID {special_id} 应该能匹配")

def run_tests():
    """运行所有测试"""
    print("🧪 开始Transaction ID匹配逻辑单元测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestTransactionIDLogic)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors
    
    print(f"📊 总测试数: {total_tests}")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failures}")
    print(f"💥 错误: {errors}")
    
    success_rate = (passed / total_tests * 100) if total_tests > 0 else 0
    print(f"📈 成功率: {success_rate:.1f}%")
    
    if failures > 0:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if errors > 0:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    if passed == total_tests:
        print("\n🎉 所有测试通过！Transaction ID匹配逻辑工作正常")
        print("✅ 代码质量: 优秀")
        print("✅ 边缘情况处理: 完善")
        print("✅ 错误处理: 健壮")
    elif success_rate >= 80:
        print("\n✅ 大部分测试通过，代码质量良好")
        print("⚠️ 建议修复失败的测试用例")
    else:
        print("\n❌ 多个测试失败，需要检查代码逻辑")
        print("🔧 建议重新审查Transaction ID匹配实现")
    
    return passed == total_tests

if __name__ == "__main__":
    success = run_tests()
    print(f"\n🎯 测试{'通过' if success else '需要改进'}")
    input("按回车键退出...")
