#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Transaction Num修复效果
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_safe_convert_transaction_num():
    """测试安全转换Transaction Num方法"""
    print("🔧 测试Transaction Num安全转换...")
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试用例
        test_cases = [
            # (输入值, 期望输出, 描述)
            ('603100001', '603100001', '正常数字字符串'),
            (603100001, '603100001', '数字类型'),
            (603100001.0, '603100001', '浮点数类型'),
            ('603100001.0', '603100001', '浮点数字符串'),
            (np.nan, '', 'NaN值'),
            ('nan', '', 'nan字符串'),
            ('', '', '空字符串'),
            ('None', '', 'None字符串'),
            ('abc123def456', '123456', '包含字母的混合字符串'),
            ('2983619124', '2983619124', '长数字'),
            ('12345', '', '太短的数字（应该被过滤）'),
            ('603100001abc', '603100001', '数字+字母'),
            (0, '', '零值'),
            ('0', '', '零字符串'),
        ]
        
        print("测试用例结果：")
        print("-" * 60)
        
        passed = 0
        total = len(test_cases)
        
        for i, (input_val, expected, description) in enumerate(test_cases, 1):
            try:
                result = processor._safe_convert_transaction_num(input_val)
                status = "✅ PASS" if result == expected else "❌ FAIL"
                
                print(f"{i:2d}. {description}")
                print(f"    输入: {repr(input_val)} ({type(input_val).__name__})")
                print(f"    期望: {repr(expected)}")
                print(f"    实际: {repr(result)}")
                print(f"    状态: {status}")
                print()
                
                if result == expected:
                    passed += 1
                    
            except Exception as e:
                print(f"{i:2d}. {description}")
                print(f"    输入: {repr(input_val)}")
                print(f"    错误: {e}")
                print(f"    状态: ❌ ERROR")
                print()
        
        print("=" * 60)
        print(f"测试结果: {passed}/{total} 通过")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dataframe_processing():
    """测试DataFrame处理"""
    print("\n🔧 测试DataFrame中Transaction Num处理...")
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试DataFrame
        test_data = {
            'Transaction_Num': [
                '603100001',
                603100002,
                603100003.0,
                np.nan,
                'nan',
                '',
                '2983619124',
                'abc603100004def'
            ],
            'Order_No': ['A001', 'A002', 'A003', 'A004', 'A005', 'A006', 'A007', 'A008'],
            'Order_price': [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0]
        }
        
        df = pd.DataFrame(test_data)
        
        print("原始数据:")
        print(df)
        print()
        
        # 应用安全转换
        df['Transaction_Num'] = df['Transaction_Num'].apply(processor._safe_convert_transaction_num)
        
        print("转换后数据:")
        print(df)
        print()
        
        # 统计结果
        valid_count = (df['Transaction_Num'] != '').sum()
        empty_count = (df['Transaction_Num'] == '').sum()
        
        print(f"统计结果:")
        print(f"  有效Transaction Num: {valid_count}")
        print(f"  空Transaction Num: {empty_count}")
        print(f"  总记录数: {len(df)}")
        
        # 验证结果
        expected_valid = ['603100001', '603100002', '603100003', '2983619124', '603100004']
        actual_valid = df[df['Transaction_Num'] != '']['Transaction_Num'].tolist()
        
        print(f"\n期望有效值: {expected_valid}")
        print(f"实际有效值: {actual_valid}")
        
        success = len(actual_valid) >= 4  # 至少应该有4个有效值
        print(f"测试结果: {'✅ PASS' if success else '❌ FAIL'}")
        
        return success
        
    except Exception as e:
        print(f"❌ DataFrame测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 Transaction Num修复效果测试")
    print("=" * 60)
    
    tests = [
        ("安全转换方法", test_safe_convert_transaction_num),
        ("DataFrame处理", test_dataframe_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 总体测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Transaction Num修复生效。")
        return 0
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
