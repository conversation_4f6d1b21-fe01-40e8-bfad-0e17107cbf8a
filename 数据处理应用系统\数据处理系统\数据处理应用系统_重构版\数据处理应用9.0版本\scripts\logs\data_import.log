2025-07-09 09:20:29 - data_import - INFO - All database table structures created/verified
2025-07-09 09:20:29 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 09:20:29 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 16:06:14 - data_import - INFO - All database table structures created/verified
2025-07-09 16:06:14 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 16:06:14 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 16:06:14 - data_import - INFO - 测试导入处理器
2025-07-09 16:17:40 - data_import - INFO - All database table structures created/verified
2025-07-09 16:17:40 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 16:17:40 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 16:19:19 - data_import - INFO - All database table structures created/verified
2025-07-09 16:19:19 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 16:19:19 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 16:19:19 - data_import - WARNING - 无法设置基于文件位置的日志：文件路径无效
2025-07-09 16:22:44 - data_import - INFO - All database table structures created/verified
2025-07-09 16:22:44 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 16:22:44 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 16:33:24 - data_import - INFO - All database table structures created/verified
2025-07-09 16:33:24 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 16:33:24 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 16:33:24 - data_import - INFO - 测试处理器日志
2025-07-09 16:33:24 - data_import - INFO - All database table structures created/verified
2025-07-09 16:33:24 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 16:33:24 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 16:33:24 - data_import - WARNING - 无法设置基于文件位置的日志：文件路径无效
2025-07-09 16:33:25 - data_import - INFO - All database table structures created/verified
2025-07-09 16:33:25 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 16:33:25 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 16:33:25 - data_import - WARNING - 无法设置基于文件位置的日志：文件路径无效
2025-07-09 16:55:35 - data_import - INFO - All database table structures created/verified
2025-07-09 16:55:35 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 16:55:35 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 16:55:35 - data_import - INFO - 测试导入处理器
2025-07-09 16:55:35 - data_import - WARNING - 测试警告
2025-07-09 16:55:35 - data_import - ERROR - 测试错误
2025-07-09 16:58:36 - data_import - INFO - All database table structures created/verified
2025-07-09 16:58:36 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 16:58:36 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 16:58:36 - data_import - INFO - 测试导入处理器
2025-07-09 16:58:36 - data_import - WARNING - 测试警告
2025-07-09 16:58:36 - data_import - ERROR - 测试错误
2025-07-09 17:14:10 - data_import - INFO - All database table structures created/verified
2025-07-09 17:14:10 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 17:14:10 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 17:14:10 - data_import - INFO - All database table structures created/verified
2025-07-09 17:14:10 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 17:14:10 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 17:14:10 - data_import - INFO - All database table structures created/verified
2025-07-09 17:14:10 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 17:14:10 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 17:14:10 - data_import - INFO - 数据完整性验证完成 - 质量评分: 94/100
2025-07-09 17:21:52 - data_import - INFO - All database table structures created/verified
2025-07-09 17:21:52 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 17:21:52 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 17:21:52 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-09 17:21:52 - data_import - INFO - All database table structures created/verified
2025-07-09 17:21:52 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 17:21:52 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 17:21:52 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-09 17:21:52 - data_import - INFO - All database table structures created/verified
2025-07-09 17:21:52 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 17:21:52 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 17:21:52 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-09 17:21:52 - data_import - INFO - All database table structures created/verified
2025-07-09 17:21:52 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-09 17:21:52 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-09 17:21:52 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-09 17:21:52 - data_import - INFO - 开始智能增量重复检测，数据量: 4
2025-07-09 17:21:52 - data_import - INFO - 检查表 IOT_Sales: 2 条记录
2025-07-09 17:21:52 - data_import - INFO - 使用标准匹配策略: Transaction_Num + Order_time
2025-07-09 17:21:53 - data_import - INFO - 使用第一级检测：Transaction_Num + Order_time
2025-07-09 17:21:53 - data_import - INFO - 检查表 IOT_Sales_Close: 1 条记录
2025-07-09 17:21:53 - data_import - INFO - 使用Close匹配策略: Order_time + Equipment_ID + Payment
2025-07-09 17:21:53 - data_import - WARNING - 缺少必需列 ['Payment']，使用标准检测
2025-07-09 17:21:53 - data_import - INFO - 使用标准匹配策略: Transaction_Num + Order_time
2025-07-09 17:21:54 - data_import - INFO - 使用第一级检测：Transaction_Num + Order_time
2025-07-09 17:21:54 - data_import - INFO - 检查表 IOT_Sales_Refunding: 1 条记录
2025-07-09 17:21:54 - data_import - INFO - 使用Refunding匹配策略: Order_time + Transaction_Num
2025-07-09 17:21:54 - data_import - INFO - Refunding重复检测: 重复 0 条, 新数据 1 条
2025-07-09 17:21:54 - data_import - INFO - 智能增量检测完成 - 重复: 0, 部分重复: 0, 新数据: 4
2025-07-10 08:52:39 - data_import - INFO - All database table structures created/verified
2025-07-10 08:52:39 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 08:52:39 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 08:52:39 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 08:53:18 - data_import - WARNING - 检测到缺失记录: 数据库有465497条，文件有3条，缺失129814条
2025-07-10 08:53:19 - data_import - INFO - All database table structures created/verified
2025-07-10 08:53:19 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 08:53:19 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 08:53:19 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 08:53:19 - data_import - INFO - All database table structures created/verified
2025-07-10 08:53:19 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 08:53:19 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 08:53:19 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 08:53:57 - data_import - WARNING - 检测到缺失记录: 数据库有465497条，文件有3条，缺失129814条
2025-07-10 08:54:39 - data_import - WARNING - 检测到缺失记录: 数据库有465497条，文件有2条，缺失129814条
2025-07-10 08:55:21 - data_import - WARNING - 检测到缺失记录: 数据库有465497条，文件有3条，缺失129814条
2025-07-10 08:55:22 - data_import - INFO - 未检测到缺失记录: 数据库有0条，文件有0条
2025-07-10 09:39:44 - data_import - INFO - All database table structures created/verified
2025-07-10 09:39:44 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 09:39:44 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 09:39:44 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 09:39:44 - data_import - INFO - 测试日志记录
2025-07-10 09:39:44 - data_import - INFO - All database table structures created/verified
2025-07-10 09:39:44 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 09:39:44 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 09:39:44 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 09:39:44 - data_import - INFO - All database table structures created/verified
2025-07-10 09:39:44 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 09:39:44 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 09:39:44 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 09:39:44 - data_import - INFO - All database table structures created/verified
2025-07-10 09:39:44 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 09:39:44 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 09:39:44 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 09:39:44 - data_import - INFO - All database table structures created/verified
2025-07-10 09:39:44 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 09:39:44 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 09:39:44 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 09:40:25 - data_import - WARNING - 检测到缺失记录: 数据库有465497条，文件有2条，缺失129814条
2025-07-10 09:42:22 - data_import - INFO - All database table structures created/verified
2025-07-10 09:42:22 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 09:42:22 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 09:42:22 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 09:43:01 - data_import - WARNING - 检测到缺失记录: 数据库有465497条，文件有1条，缺失129814条
2025-07-10 09:45:41 - data_import - INFO - All database table structures created/verified
2025-07-10 09:45:41 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 09:45:41 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 09:45:41 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 09:46:16 - data_import - WARNING - 检测到缺失记录: 数据库有465497条，文件有1条，缺失129814条
2025-07-10 09:47:19 - data_import - INFO - All database table structures created/verified
2025-07-10 09:47:19 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 09:47:19 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 09:47:19 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 10:41:45 - data_import - INFO - All database table structures created/verified
2025-07-10 10:41:45 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 10:41:45 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 10:41:45 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 10:41:46 - data_import - INFO - All database table structures created/verified
2025-07-10 10:41:46 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 10:41:46 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 10:41:46 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 10:48:58 - data_import - INFO - All database table structures created/verified
2025-07-10 10:48:58 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 10:48:58 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 10:48:58 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 10:48:58 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 10:48:58 - data_import - WARNING - Legacy merge failed: 'Payment_date', treating all data as new
2025-07-10 10:48:58 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 10:48:58 - data_import - WARNING - Legacy merge failed: 'Payment_date', treating all data as new
2025-07-10 10:48:58 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 10:48:58 - data_import - WARNING - Legacy merge failed: 'Payment_date', treating all data as new
2025-07-10 10:48:58 - data_import - INFO - All database table structures created/verified
2025-07-10 10:48:58 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 10:48:58 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 10:48:58 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 10:48:58 - data_import - INFO - 使用第一级检测：Transaction_Num + Order_time
2025-07-10 10:48:58 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 10:48:58 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 10:48:58 - data_import - WARNING - Legacy merge failed: 'Payment_date', treating all data as new
2025-07-10 10:48:58 - data_import - INFO - All database table structures created/verified
2025-07-10 10:48:58 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 10:48:58 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 10:48:58 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 10:48:58 - data_import - INFO - 开始智能增量重复检测，数据量: 2
2025-07-10 10:48:58 - data_import - INFO - 检查表 IOT_Sales: 2 条记录
2025-07-10 10:48:58 - data_import - INFO - 使用标准匹配策略: Transaction_Num + Order_time
2025-07-10 10:48:59 - data_import - INFO - 使用第一级检测：Transaction_Num + Order_time
2025-07-10 10:48:59 - data_import - INFO - 智能增量检测完成 - 重复: 0, 部分重复: 0, 新数据: 2
2025-07-10 10:48:59 - data_import - INFO - All database table structures created/verified
2025-07-10 10:48:59 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 10:48:59 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 10:48:59 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:49:34 - data_import - INFO - All database table structures created/verified
2025-07-10 11:49:34 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:49:34 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:49:34 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:49:34 - data_import - INFO - All database table structures created/verified
2025-07-10 11:49:34 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:49:34 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:49:34 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:49:34 - data_import - INFO - 使用第一级检测：Transaction_Num + Order_time
2025-07-10 11:49:34 - data_import - INFO - All database table structures created/verified
2025-07-10 11:49:34 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:49:34 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:49:34 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:49:34 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 11:49:34 - data_import - WARNING - Legacy merge failed: 'Payment_date', treating all data as new
2025-07-10 11:49:34 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 11:49:34 - data_import - WARNING - Legacy merge failed: 'Payment_date', treating all data as new
2025-07-10 11:49:34 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 11:49:34 - data_import - WARNING - Legacy merge failed: 'Payment_date', treating all data as new
2025-07-10 11:49:34 - data_import - INFO - All database table structures created/verified
2025-07-10 11:49:34 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:49:34 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:49:34 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:49:34 - data_import - INFO - All database table structures created/verified
2025-07-10 11:49:34 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:49:34 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:49:34 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:49:34 - data_import - INFO - 开始智能增量重复检测，数据量: 2
2025-07-10 11:49:34 - data_import - INFO - 检查表 IOT_Sales: 2 条记录
2025-07-10 11:49:34 - data_import - INFO - 使用标准匹配策略: Transaction_Num + Order_time
2025-07-10 11:49:35 - data_import - INFO - 使用第一级检测：Transaction_Num + Order_time
2025-07-10 11:49:35 - data_import - INFO - 智能增量检测完成 - 重复: 0, 部分重复: 0, 新数据: 2
2025-07-10 11:50:40 - data_import - INFO - All database table structures created/verified
2025-07-10 11:50:40 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:50:40 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:50:40 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:50:40 - data_import - INFO - All database table structures created/verified
2025-07-10 11:50:40 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:50:40 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:50:40 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:50:40 - data_import - INFO - 使用第一级检测：Transaction_Num + Order_time
2025-07-10 11:50:40 - data_import - INFO - All database table structures created/verified
2025-07-10 11:50:40 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:50:40 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:50:40 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:50:40 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 11:50:40 - data_import - WARNING - Legacy merge failed: 'Payment_date', treating all data as new
2025-07-10 11:50:40 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 11:50:40 - data_import - WARNING - Legacy merge failed: 'Payment_date', treating all data as new
2025-07-10 11:50:40 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 11:50:40 - data_import - WARNING - Legacy merge failed: 'Payment_date', treating all data as new
2025-07-10 11:50:40 - data_import - INFO - All database table structures created/verified
2025-07-10 11:50:40 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:50:40 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:50:40 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:50:40 - data_import - INFO - All database table structures created/verified
2025-07-10 11:50:40 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:50:40 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:50:40 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:50:40 - data_import - INFO - 开始智能增量重复检测，数据量: 2
2025-07-10 11:50:40 - data_import - INFO - 检查表 IOT_Sales: 2 条记录
2025-07-10 11:50:40 - data_import - INFO - 使用标准匹配策略: Transaction_Num + Order_time
2025-07-10 11:50:41 - data_import - INFO - 使用第一级检测：Transaction_Num + Order_time
2025-07-10 11:50:41 - data_import - INFO - 智能增量检测完成 - 重复: 0, 部分重复: 0, 新数据: 2
2025-07-10 11:55:56 - data_import - INFO - All database table structures created/verified
2025-07-10 11:55:56 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:55:56 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:55:56 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:55:56 - data_import - INFO - Data types standardized for database compatibility
2025-07-10 11:55:56 - data_import - INFO - 开始智能增量重复检测，数据量: 3
2025-07-10 11:55:56 - data_import - INFO - 检查表 IOT_Sales: 3 条记录
2025-07-10 11:55:56 - data_import - INFO - 使用标准匹配策略: Transaction_Num + Order_time
2025-07-10 11:55:56 - data_import - INFO - Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID
2025-07-10 11:55:57 - data_import - WARNING - Legacy merge failed: 'Order_time', treating all data as new
2025-07-10 11:55:57 - data_import - INFO - 智能增量检测完成 - 重复: 0, 部分重复: 0, 新数据: 3
2025-07-10 11:55:57 - data_import - INFO - All database table structures created/verified
2025-07-10 11:55:57 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:55:57 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:55:57 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:55:57 - data_import - INFO - All database table structures created/verified
2025-07-10 11:55:57 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 11:55:57 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 11:55:57 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 11:55:57 - data_import - INFO - 使用第一级检测：Transaction_Num + Order_time
2025-07-10 12:08:35 - data_import - INFO - All database table structures created/verified
2025-07-10 12:08:35 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 12:08:35 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 12:08:35 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 12:08:35 - data_import - INFO - All database table structures created/verified
2025-07-10 12:08:35 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 12:08:35 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 12:08:35 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 12:08:35 - data_import - INFO - All database table structures created/verified
2025-07-10 12:08:35 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 12:08:35 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 12:08:35 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 12:08:35 - data_import - INFO - All database table structures created/verified
2025-07-10 12:08:35 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 12:08:35 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 12:08:35 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 12:08:35 - data_import - INFO - Data types standardized for database compatibility
2025-07-10 12:20:49 - data_import - INFO - All database table structures created/verified
2025-07-10 12:20:49 - data_import - INFO - New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close
2025-07-10 12:20:49 - data_import - INFO - Smart data import processor initialized, DB path: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-07-10 12:20:49 - data_import - INFO - Performance config: batch_size=2000, max_memory=500MB
2025-07-10 12:20:49 - data_import - INFO - Data types standardized for database compatibility
