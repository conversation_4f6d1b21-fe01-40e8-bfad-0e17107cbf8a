#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志显示修复脚本
修复应用中日志完全不显示的问题
"""

import os
import re
import shutil
from datetime import datetime

def fix_log_display_issues():
    """修复日志显示问题"""
    print("🔧 开始修复日志显示问题...")
    
    main_app_path = "数据处理与导入应用_完整版.py"
    
    if not os.path.exists(main_app_path):
        print(f"❌ 找不到主应用文件: {main_app_path}")
        return False
    
    # 创建备份
    backup_path = f"{main_app_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(main_app_path, backup_path)
    print(f"✅ 已创建备份: {backup_path}")
    
    try:
        with open(main_app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复1: 确保初始日志消息不被过滤
        print("🔧 修复1: 优化初始日志消息显示...")
        
        # 在 _handle_data_processing_log 方法中添加对初始消息的特殊处理
        old_pattern = r'(def _handle_data_processing_log\(self, widget, message: str\):\s*""".*?""".*?try:)'
        new_replacement = r'''\1
            # 🚀 特殊处理：初始提示消息直接显示，不进行过滤
            initial_messages = [
                "请使用浏览按钮选择文件",
                "请选择包含REFUND_LIST工作表的Excel文件进行退款处理", 
                "数据库设置已加载",
                "当前数据库路径:"
            ]
            
            if any(init_msg in message for init_msg in initial_messages):
                self._display_log_message(widget, message)
                return'''
        
        if re.search(old_pattern, content, re.DOTALL):
            content = re.sub(old_pattern, new_replacement, content, flags=re.DOTALL)
            print("   ✅ 已添加初始消息特殊处理")
        else:
            print("   ⚠️ 未找到 _handle_data_processing_log 方法")
        
        # 修复2: 确保日志控件状态正确
        print("🔧 修复2: 确保日志控件状态正确...")
        
        # 在 _display_log_message 方法中添加状态检查
        old_display_pattern = r'(def _display_log_message\(self, widget, message: str\):\s*""".*?""".*?widget\.config\(state=tk\.NORMAL\))'
        new_display_replacement = r'''\1
        
        # 🔧 确保控件状态正确
        try:
            current_state = widget.cget('state')
            if current_state == tk.DISABLED:
                widget.config(state=tk.NORMAL)
        except Exception:
            pass'''
        
        if re.search(old_display_pattern, content, re.DOTALL):
            content = re.sub(old_display_pattern, new_display_replacement, content, flags=re.DOTALL)
            print("   ✅ 已添加控件状态检查")
        else:
            print("   ⚠️ 未找到 _display_log_message 方法")
        
        # 修复3: 添加调试输出
        print("🔧 修复3: 添加调试输出...")
        
        # 在 safe_log 方法中添加调试输出
        old_safe_log_pattern = r'(def safe_log\(self, message: str, tab_type: str = "general".*?\):\s*""".*?""".*?self\.message_queue\.put\(\("log", \{"tab_type": tab_type, "message": message\}\)\))'
        new_safe_log_replacement = r'''\1
        
        # 🔧 调试输出
        print(f"[DEBUG] safe_log调用: tab_type={tab_type}, message={message[:50]}...")'''
        
        if re.search(old_safe_log_pattern, content, re.DOTALL):
            content = re.sub(old_safe_log_pattern, new_safe_log_replacement, content, flags=re.DOTALL)
            print("   ✅ 已添加safe_log调试输出")
        else:
            print("   ⚠️ 未找到 safe_log 方法")
        
        # 修复4: 在 _update_log_widget 中添加调试输出
        old_update_pattern = r'(def _update_log_widget\(self, tab_type: str, message: str\):\s*""".*?""".*?widget = self\.log_widgets\.get\(tab_type\))'
        new_update_replacement = r'''\1
        
        # 🔧 调试输出
        print(f"[DEBUG] _update_log_widget调用: tab_type={tab_type}, widget存在={widget is not None}, message={message[:50]}...")'''
        
        if re.search(old_update_pattern, content, re.DOTALL):
            content = re.sub(old_update_pattern, new_update_replacement, content, flags=re.DOTALL)
            print("   ✅ 已添加_update_log_widget调试输出")
        else:
            print("   ⚠️ 未找到 _update_log_widget 方法")
        
        # 修复5: 确保所有标签页的初始消息都能显示
        print("🔧 修复5: 强化初始消息显示...")
        
        # 在每个标签页的构造函数中添加延迟日志显示
        tab_constructors = [
            "class UnifiedProcessingTab",
            "class DataImportTab", 
            "class RefundProcessingTab",
            "class DatabaseSettingsTab"
        ]
        
        for tab_class in tab_constructors:
            # 查找构造函数结尾，添加延迟日志显示
            pattern = rf'({tab_class}.*?def __init__.*?# 显示初始.*?self\.log_message\([^)]+\))'
            replacement = r'''\1
        
        # 🔧 延迟确保日志显示
        self.root.after(500, self._ensure_initial_log_display)'''
            
            if re.search(pattern, content, re.DOTALL):
                content = re.sub(pattern, replacement, content, flags=re.DOTALL)
                print(f"   ✅ 已为 {tab_class} 添加延迟日志显示")
        
        # 修复6: 添加 _ensure_initial_log_display 方法到基类
        print("🔧 修复6: 添加确保日志显示的方法...")
        
        base_tab_pattern = r'(class BaseTab:.*?def log_message\(self, message: str\):.*?self\.gui_updater\.safe_log\(message, self\.tab_type\))'
        base_tab_replacement = r'''\1
    
    def _ensure_initial_log_display(self):
        """确保初始日志消息能够显示"""
        try:
            if hasattr(self, 'log_text') and self.log_text:
                # 检查日志控件是否为空
                content = self.log_text.get(1.0, tk.END).strip()
                if not content:
                    # 如果为空，重新发送初始消息
                    print(f"[DEBUG] 重新发送初始消息到 {self.tab_type}")
                    if hasattr(self, 'tab_type'):
                        if self.tab_type == "processing":
                            self.log_message("请使用浏览按钮选择文件")
                        elif self.tab_type == "import":
                            self.log_message("请使用浏览按钮选择文件")
                        elif self.tab_type == "refund":
                            self.log_message("请选择包含REFUND_LIST工作表的Excel文件进行退款处理")
                        elif self.tab_type == "settings":
                            self.log_message("数据库设置已加载")
        except Exception as e:
            print(f"[DEBUG] _ensure_initial_log_display错误: {e}")'''
        
        if re.search(base_tab_pattern, content, re.DOTALL):
            content = re.sub(base_tab_pattern, base_tab_replacement, content, flags=re.DOTALL)
            print("   ✅ 已添加 _ensure_initial_log_display 方法")
        else:
            print("   ⚠️ 未找到 BaseTab 类")
        
        # 保存修复后的文件
        with open(main_app_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 日志显示修复完成！")
        print("\n📋 修复内容总结:")
        print("   1. ✅ 初始消息特殊处理 - 跳过过滤直接显示")
        print("   2. ✅ 日志控件状态检查 - 确保控件可写入")
        print("   3. ✅ 调试输出添加 - 便于问题诊断")
        print("   4. ✅ 延迟日志显示 - 确保控件完全初始化")
        print("   5. ✅ 初始消息重发机制 - 防止消息丢失")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        # 恢复备份
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, main_app_path)
            print(f"✅ 已从备份恢复: {backup_path}")
        return False

if __name__ == "__main__":
    print("🚀 日志显示修复工具")
    print("=" * 50)
    
    success = fix_log_display_issues()
    
    if success:
        print("\n🎉 修复完成！请重新启动应用测试日志显示效果。")
        print("💡 如果问题仍然存在，请查看控制台的[DEBUG]输出信息。")
    else:
        print("\n❌ 修复失败！请检查错误信息并手动修复。")
