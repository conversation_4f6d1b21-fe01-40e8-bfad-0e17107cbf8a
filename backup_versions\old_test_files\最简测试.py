# coding: utf-8
print("开始测试...")

# 测试1: 基本Python功能
try:
    import pandas as pd
    print("✅ pandas导入成功")
except Exception as e:
    print(f"❌ pandas导入失败: {e}")

# 测试2: 文件存在性
import os
files_to_check = [
    "config.ini",
    "数据处理系统/数据验证器.py",
    "数据处理系统/数据匹配器.py",
    "数据处理系统/数据处理脚本.py",
    "数据处理与导入应用_完整版.py"
]

for file_path in files_to_check:
    if os.path.exists(file_path):
        print(f"✅ {file_path} 存在")
    else:
        print(f"❌ {file_path} 不存在")

# 测试3: 配置文件内容
try:
    with open("config.ini", "r", encoding="utf-8") as f:
        content = f.read()
        if "数据处理系统/数据处理脚本.py" in content:
            print("✅ 配置文件包含新脚本路径")
        else:
            print("❌ 配置文件不包含新脚本路径")
except Exception as e:
    print(f"❌ 读取配置文件失败: {e}")

# 测试4: 主应用配置
try:
    with open("数据处理与导入应用_完整版.py", "r", encoding="utf-8") as f:
        content = f.read()
        if "数据处理系统/数据处理脚本.py" in content:
            print("✅ 主应用包含新脚本路径")
        else:
            print("❌ 主应用不包含新脚本路径")
except Exception as e:
    print(f"❌ 读取主应用失败: {e}")

print("测试完成")
