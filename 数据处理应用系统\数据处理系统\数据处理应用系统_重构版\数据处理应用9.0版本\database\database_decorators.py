#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库操作装饰器
提供安全的数据库操作包装
"""

import functools
import sqlite3
import time
import threading
from typing import Any, Callable, Optional
import logging

# 全局锁，确保数据库操作的线程安全
_db_operation_lock = threading.RLock()

def safe_database_operation(timeout: float = 30.0, 
                          retry_count: int = 3,
                          retry_delay: float = 1.0):
    """
    安全数据库操作装饰器
    
    Args:
        timeout: 操作超时时间（秒）
        retry_count: 重试次数
        retry_delay: 重试间隔（秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(retry_count):
                try:
                    with _db_operation_lock:
                        # 设置超时
                        start_time = time.time()
                        
                        # 执行操作
                        result = func(*args, **kwargs)
                        
                        # 检查是否超时
                        elapsed = time.time() - start_time
                        if elapsed > timeout:
                            raise TimeoutError(f"数据库操作超时: {elapsed:.2f}秒")
                        
                        return result
                        
                except (sqlite3.OperationalError, sqlite3.DatabaseError) as e:
                    last_exception = e
                    error_msg = str(e).lower()
                    
                    # 检查是否是可重试的错误
                    if any(keyword in error_msg for keyword in [
                        'database is locked',
                        'database is busy',
                        'disk i/o error',
                        'temporary failure'
                    ]):
                        if attempt < retry_count - 1:
                            logging.warning(f"数据库操作失败，第{attempt+1}次重试: {e}")
                            time.sleep(retry_delay * (attempt + 1))  # 递增延迟
                            continue
                    
                    # 不可重试的错误，直接抛出
                    raise e
                    
                except Exception as e:
                    # 其他异常不重试
                    raise e
            
            # 所有重试都失败
            raise last_exception or Exception("数据库操作失败")
        
        return wrapper
    return decorator

def transaction_wrapper(isolation_level: Optional[str] = None):
    """
    事务包装装饰器
    
    Args:
        isolation_level: 事务隔离级别
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 查找连接对象
            conn = None
            for arg in args:
                if isinstance(arg, sqlite3.Connection):
                    conn = arg
                    break
            
            if not conn:
                # 如果没有找到连接，尝试从kwargs中获取
                conn = kwargs.get('conn') or kwargs.get('connection')
            
            if not conn:
                raise ValueError("事务装饰器需要sqlite3.Connection对象")
            
            # 保存原始隔离级别
            original_isolation = conn.isolation_level
            
            try:
                # 设置隔离级别
                if isolation_level is not None:
                    conn.isolation_level = isolation_level
                
                # 开始事务
                if conn.isolation_level is None:
                    conn.execute("BEGIN")
                
                # 执行函数
                result = func(*args, **kwargs)
                
                # 提交事务
                conn.commit()
                
                return result
                
            except Exception as e:
                # 回滚事务
                try:
                    conn.rollback()
                except Exception:
                    pass  # 忽略回滚错误
                raise e
                
            finally:
                # 恢复原始隔离级别
                conn.isolation_level = original_isolation
        
        return wrapper
    return decorator

def connection_manager(db_path: str, 
                      timeout: float = 30.0,
                      check_same_thread: bool = False):
    """
    连接管理装饰器
    自动管理数据库连接的创建和关闭
    
    Args:
        db_path: 数据库路径
        timeout: 连接超时时间
        check_same_thread: 是否检查同一线程
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 检查是否已经有连接
            has_connection = False
            for arg in args:
                if isinstance(arg, sqlite3.Connection):
                    has_connection = True
                    break
            
            if has_connection or 'conn' in kwargs or 'connection' in kwargs:
                # 已有连接，直接执行
                return func(*args, **kwargs)
            
            # 创建新连接
            conn = None
            try:
                conn = sqlite3.connect(
                    db_path,
                    timeout=timeout,
                    check_same_thread=check_same_thread
                )
                
                # 设置安全的数据库配置
                conn.execute("PRAGMA journal_mode=DELETE")
                conn.execute("PRAGMA synchronous=FULL")
                conn.execute("PRAGMA foreign_keys=ON")
                conn.execute("PRAGMA temp_store=MEMORY")
                conn.execute("PRAGMA cache_size=10000")
                
                # 将连接作为第一个参数传递
                return func(conn, *args, **kwargs)
                
            finally:
                if conn:
                    try:
                        conn.close()
                    except Exception:
                        pass  # 忽略关闭错误
        
        return wrapper
    return decorator

def batch_operation(batch_size: int = 1000):
    """
    批量操作装饰器
    将大量数据分批处理，避免长时间占用数据库
    
    Args:
        batch_size: 批次大小
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 查找数据参数
            data = kwargs.get('data')
            if not data:
                # 如果kwargs中没有data，尝试从args中查找
                for arg in args:
                    if isinstance(arg, (list, tuple)) and len(arg) > batch_size:
                        data = arg
                        break
            
            if not data or len(data) <= batch_size:
                # 数据量小，直接执行
                return func(*args, **kwargs)
            
            # 分批处理
            results = []
            total_batches = (len(data) + batch_size - 1) // batch_size
            
            for i in range(0, len(data), batch_size):
                batch_data = data[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                logging.info(f"处理批次 {batch_num}/{total_batches}, 大小: {len(batch_data)}")
                
                # 更新kwargs中的数据
                batch_kwargs = kwargs.copy()
                batch_kwargs['data'] = batch_data
                
                # 执行批次
                batch_result = func(*args, **batch_kwargs)
                results.append(batch_result)
                
                # 短暂休息，释放资源
                time.sleep(0.01)
            
            return results
        
        return wrapper
    return decorator

def performance_monitor(log_slow_queries: bool = True, 
                       slow_query_threshold: float = 1.0):
    """
    性能监控装饰器
    监控数据库操作的执行时间
    
    Args:
        log_slow_queries: 是否记录慢查询
        slow_query_threshold: 慢查询阈值（秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                return result
                
            finally:
                elapsed = time.time() - start_time
                
                if log_slow_queries and elapsed > slow_query_threshold:
                    logging.warning(
                        f"慢查询检测: {func.__name__} 执行时间 {elapsed:.2f}秒"
                    )
                elif elapsed > 0.1:  # 记录所有超过100ms的操作
                    logging.info(
                        f"数据库操作: {func.__name__} 执行时间 {elapsed:.2f}秒"
                    )
        
        return wrapper
    return decorator

# 组合装饰器：安全的数据库操作
def safe_db_operation(db_path: str = None,
                     timeout: float = 30.0,
                     retry_count: int = 3,
                     batch_size: int = 1000,
                     monitor_performance: bool = True):
    """
    组合装饰器：提供完整的数据库操作保护
    
    Args:
        db_path: 数据库路径（如果需要自动管理连接）
        timeout: 操作超时时间
        retry_count: 重试次数
        batch_size: 批处理大小
        monitor_performance: 是否监控性能
    """
    def decorator(func: Callable) -> Callable:
        # 应用多个装饰器
        wrapped_func = func
        
        if monitor_performance:
            wrapped_func = performance_monitor()(wrapped_func)
        
        if batch_size > 0:
            wrapped_func = batch_operation(batch_size)(wrapped_func)
        
        wrapped_func = safe_database_operation(timeout, retry_count)(wrapped_func)
        
        if db_path:
            wrapped_func = connection_manager(db_path, timeout)(wrapped_func)
        
        return wrapped_func
    
    return decorator
