#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Order_time格式 - 验证导入后Order_time是否只包含日期
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_standardize_date_method():
    """测试standardize_date方法"""
    print("🔧 测试standardize_date方法")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试各种时间格式
        test_cases = [
            ("完整日期时间", "2025-07-07 00:00:00"),
            ("完整日期时间2", "2025-07-07 12:30:45"),
            ("只有日期", "2025-07-07"),
            ("斜杠分隔", "2025/07/07"),
            ("点分隔", "2025.07.07"),
            ("带T的ISO格式", "2025-07-07T12:30:45"),
            ("Excel格式", "2025-07-07 10:15:30"),
        ]
        
        print("📋 测试结果:")
        all_correct = True
        
        for case_name, input_date in test_cases:
            try:
                result = processor.standardize_date(input_date)
                expected_format = "YYYY-MM-DD"
                
                # 检查结果格式
                if result and len(result) == 10 and result.count('-') == 2:
                    print(f"✅ {case_name}: '{input_date}' → '{result}' (正确格式)")
                else:
                    print(f"❌ {case_name}: '{input_date}' → '{result}' (格式错误)")
                    all_correct = False
                    
            except Exception as e:
                print(f"❌ {case_name}: '{input_date}' → 错误: {e}")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ standardize_date方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_processing_pipeline():
    """测试完整的数据处理流程"""
    print("\n🔧 测试完整的数据处理流程")
    print("-" * 60)
    
    try:
        # 设置非交互模式
        os.environ['NON_INTERACTIVE'] = '1'
        os.environ['AUTO_DUPLICATE_HANDLING'] = 'overwrite'
        os.environ['AUTO_MISSING_HANDLING'] = 'ignore'
        
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建包含时间的测试数据
        test_data = pd.DataFrame({
            'Copartner name': ['Partner1', 'Partner2'],
            'Order No.': ['ORD001', 'ORD002'],
            'Transaction Num': ['TXN001', 'TXN002'],
            'Order types': ['普通', '普通'],
            'Order price': [100.0, 200.0],
            'Payment': [100.0, 200.0],
            'Order time': ['2025-07-07 00:00:00', '2025-07-08 12:30:45'],  # 包含时间
            'Equipment ID': ['EQ001', 'EQ002'],
            'Equipment name': ['Device1', 'Device2'],
            'Branch name': ['Branch1', 'Branch2'],
            'Payment date': ['2025-07-07', '2025-07-08'],
            'User name': ['User1', 'User2'],
            'Time': ['10:00:00', '11:00:00'],
            'Matched Order ID': ['', ''],
            'OrderTime_dt': ['2025-07-07 10:00:00', '2025-07-08 11:00:00']
        })
        
        print(f"📊 测试数据: {len(test_data)} 条记录")
        print("📋 原始Order time格式: 包含完整时间")
        
        # 测试数据处理流程
        try:
            # 数据清洗
            cleaned_data = processor._clean_data(test_data.copy())
            print(f"✅ 数据清洗成功: {len(cleaned_data)} 条记录")
            
            # 数据标准化
            standardized_data = processor._standardize_data_types(cleaned_data.copy())
            print(f"✅ 数据标准化成功: {len(standardized_data)} 条记录")
            
            # 检查Order_time格式
            if 'Order_time' in standardized_data.columns:
                print("\n📋 处理后的Order_time格式检查:")
                for i, order_time in enumerate(standardized_data['Order_time'].head()):
                    print(f"  {i+1}. '{order_time}' (长度: {len(str(order_time))})")
                
                # 验证所有Order_time都是日期格式
                all_date_format = True
                for order_time in standardized_data['Order_time']:
                    if pd.notna(order_time):
                        time_str = str(order_time)
                        if len(time_str) != 10 or time_str.count('-') != 2:
                            all_date_format = False
                            print(f"❌ 发现非日期格式: '{time_str}'")
                            break
                
                if all_date_format:
                    print("✅ 所有Order_time都是正确的日期格式 (YYYY-MM-DD)")
                    return True
                else:
                    print("❌ 发现包含时间的Order_time格式")
                    return False
            else:
                print("❌ Order_time列不存在")
                return False
                
        except Exception as e:
            print(f"❌ 数据处理流程失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smart_insert_data():
    """测试智能插入数据时的Order_time格式"""
    print("\n🔧 测试智能插入数据时的Order_time格式")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'Copartner_name': ['Partner1'],
            'Order_No': ['ORD001'],
            'Transaction_Num': ['TXN001'],
            'Order_types': ['普通'],
            'Order_price': [100.0],
            'Payment': [100.0],
            'Order_time': ['2025-07-07'],  # 已经是日期格式
            'Equipment_ID': ['EQ001'],
            'Equipment_name': ['Device1'],
            'Branch_name': ['Branch1'],
            'Payment_date': ['2025-07-07'],
            'User_name': ['User1'],
            'Time': ['10:00:00'],
            'Matched_Order_ID': [''],
            'OrderTime_dt': ['2025-07-07 10:00:00']
        })
        
        print(f"📊 测试数据: {len(test_data)} 条记录")
        print(f"📋 Order_time格式: '{test_data['Order_time'].iloc[0]}'")
        
        # 模拟智能插入过程中的排序逻辑
        try:
            # 模拟排序逻辑
            if 'Order_time' in test_data.columns:
                # 使用临时列进行排序
                test_data['Order_time_temp'] = pd.to_datetime(test_data['Order_time'], errors='coerce')
                test_data = test_data.sort_values('Order_time_temp', ascending=True)
                # 删除临时列，保持原格式
                test_data.drop('Order_time_temp', axis=1, inplace=True)
                
                print(f"✅ 排序后Order_time格式: '{test_data['Order_time'].iloc[0]}'")
                
                # 验证格式
                order_time_str = str(test_data['Order_time'].iloc[0])
                if len(order_time_str) == 10 and order_time_str.count('-') == 2:
                    print("✅ Order_time保持正确的日期格式")
                    return True
                else:
                    print(f"❌ Order_time格式错误: '{order_time_str}'")
                    return False
            else:
                print("❌ Order_time列不存在")
                return False
                
        except Exception as e:
            print(f"❌ 排序逻辑测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 智能插入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Order_time格式测试")
    print("=" * 80)
    
    tests = [
        ("standardize_date方法", test_standardize_date_method),
        ("完整数据处理流程", test_data_processing_pipeline),
        ("智能插入数据格式", test_smart_insert_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 Order_time格式测试结果")
    print("=" * 80)
    
    print(f"📊 通过测试: {passed}/{total}")
    success_rate = (passed / total) * 100
    print(f"📊 成功率: {success_rate:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过")
        print("✅ Order_time格式修复成功")
        print("✅ 导入后只包含日期，不包含时间")
        print("✅ 数据处理流程保持日期格式")
        print("✅ 排序逻辑不会改变格式")
        print("\n💡 结论:")
        print("  - Order_time导入后只会显示日期 (YYYY-MM-DD)")
        print("  - 不会出现时间部分 (HH:MM:SS)")
        print("  - 格式统一，便于数据匹配")
    elif passed >= total * 0.75:
        print("✅ 大部分测试通过")
        print("⚠️ 少量格式问题可能仍存在")
    else:
        print("❌ 多个测试失败")
        print("🔧 Order_time格式问题仍然存在")
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 测试{'通过' if success else '需要改进'}")
    input("按回车键退出...")
