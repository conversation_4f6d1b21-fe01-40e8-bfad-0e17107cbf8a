#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备份命名工具
提供备份文件命名和管理功能
"""

import os
import re
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class BackupNamingUtils:
    """备份命名工具类"""
    
    @staticmethod
    def generate_backup_name(base_name="sales_reports", reason="manual", timestamp=None):
        """生成备份文件名"""
        if timestamp is None:
            timestamp = datetime.now()
        
        timestamp_str = timestamp.strftime('%Y%m%d_%H%M%S')
        
        if reason and reason != "manual":
            return f"{base_name}_backup_{reason}_{timestamp_str}.db"
        else:
            return f"{base_name}_backup_{timestamp_str}.db"
    
    @staticmethod
    def parse_backup_name(filename):
        """解析备份文件名"""
        try:
            # 匹配模式: base_backup_[reason_]timestamp.db
            pattern = r'(.+)_backup_(?:(.+)_)?(\d{8}_\d{6})\.db$'
            match = re.match(pattern, filename)
            
            if match:
                base_name = match.group(1)
                reason = match.group(2) or "manual"
                timestamp_str = match.group(3)
                
                try:
                    timestamp = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
                except ValueError:
                    timestamp = None
                
                return {
                    'base_name': base_name,
                    'reason': reason,
                    'timestamp': timestamp,
                    'timestamp_str': timestamp_str,
                    'valid': True
                }
            else:
                return {'valid': False}
                
        except Exception as e:
            logger.error(f"解析备份文件名失败: {e}")
            return {'valid': False}
    
    @staticmethod
    def is_backup_file(filename):
        """检查是否为备份文件"""
        return BackupNamingUtils.parse_backup_name(filename)['valid']
    
    @staticmethod
    def get_backup_age(filename):
        """获取备份文件的年龄（天数）"""
        info = BackupNamingUtils.parse_backup_name(filename)
        if info['valid'] and info['timestamp']:
            age = datetime.now() - info['timestamp']
            return age.days
        return None
    
    @staticmethod
    def sort_backups_by_time(backup_files, reverse=True):
        """按时间排序备份文件"""
        def get_timestamp(filename):
            info = BackupNamingUtils.parse_backup_name(filename)
            if info['valid'] and info['timestamp']:
                return info['timestamp']
            return datetime.min
        
        return sorted(backup_files, key=get_timestamp, reverse=reverse)
    
    @staticmethod
    def filter_backups_by_reason(backup_files, reason):
        """按原因过滤备份文件"""
        filtered = []
        for filename in backup_files:
            info = BackupNamingUtils.parse_backup_name(filename)
            if info['valid'] and info['reason'] == reason:
                filtered.append(filename)
        return filtered
    
    @staticmethod
    def get_backup_summary(backup_dir):
        """获取备份目录摘要"""
        if not os.path.exists(backup_dir):
            return {
                'total_count': 0,
                'total_size': 0,
                'by_reason': {},
                'oldest': None,
                'newest': None
            }
        
        try:
            backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.db')]
            backup_files = [f for f in backup_files if BackupNamingUtils.is_backup_file(f)]
            
            if not backup_files:
                return {
                    'total_count': 0,
                    'total_size': 0,
                    'by_reason': {},
                    'oldest': None,
                    'newest': None
                }
            
            total_size = 0
            by_reason = {}
            timestamps = []
            
            for filename in backup_files:
                file_path = os.path.join(backup_dir, filename)
                file_size = os.path.getsize(file_path)
                total_size += file_size
                
                info = BackupNamingUtils.parse_backup_name(filename)
                if info['valid']:
                    reason = info['reason']
                    if reason not in by_reason:
                        by_reason[reason] = {'count': 0, 'size': 0}
                    by_reason[reason]['count'] += 1
                    by_reason[reason]['size'] += file_size
                    
                    if info['timestamp']:
                        timestamps.append(info['timestamp'])
            
            return {
                'total_count': len(backup_files),
                'total_size': total_size,
                'by_reason': by_reason,
                'oldest': min(timestamps) if timestamps else None,
                'newest': max(timestamps) if timestamps else None
            }
            
        except Exception as e:
            logger.error(f"获取备份摘要失败: {e}")
            return {
                'total_count': 0,
                'total_size': 0,
                'by_reason': {},
                'oldest': None,
                'newest': None
            }

# 便捷函数
def generate_backup_name(reason="manual", timestamp=None):
    """生成备份文件名的便捷函数"""
    return BackupNamingUtils.generate_backup_name(reason=reason, timestamp=timestamp)

def parse_backup_name(filename):
    """解析备份文件名的便捷函数"""
    return BackupNamingUtils.parse_backup_name(filename)

def is_backup_file(filename):
    """检查是否为备份文件的便捷函数"""
    return BackupNamingUtils.is_backup_file(filename)
