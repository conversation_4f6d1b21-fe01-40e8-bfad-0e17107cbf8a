#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入修复效果
"""

import sys
import os
import time

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_connection_pool():
    """测试连接池功能"""
    print("🔧 测试连接池功能...")
    
    try:
        from database.connection_pool import get_connection, close_all_connections
        
        print("✅ 连接池模块导入成功")
        
        # 测试获取连接
        print("🔧 测试获取连接...")
        start_time = time.time()
        
        with get_connection() as conn:
            print(f"✅ 连接获取成功，耗时: {time.time() - start_time:.2f}秒")
            
            # 测试简单查询
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print(f"✅ 简单查询成功: {result}")
        
        print("✅ 连接已正确释放")
        
        # 测试关闭连接池
        print("🔧 测试关闭连接池...")
        close_all_connections()
        print("✅ 连接池关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接池测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_import_processor():
    """测试数据导入处理器"""
    print("\n🔧 测试数据导入处理器...")
    
    try:
        from data_import_optimized import DataImportProcessor
        
        print("✅ DataImportProcessor导入成功")
        
        # 测试初始化
        print("🔧 测试初始化...")
        start_time = time.time()
        
        processor = DataImportProcessor()
        print(f"✅ 初始化成功，耗时: {time.time() - start_time:.2f}秒")
        
        # 测试批次大小
        print(f"✅ 批次大小: {processor.batch_size}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据导入处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """测试数据库操作"""
    print("\n🔧 测试数据库操作...")
    
    try:
        from database.connection_pool import get_connection
        
        print("🔧 测试数据库连接和基本操作...")
        start_time = time.time()
        
        with get_connection() as conn:
            cursor = conn.cursor()
            
            # 测试表查询
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 5")
            tables = cursor.fetchall()
            print(f"✅ 查询到 {len(tables)} 个表")
            
            # 测试IOT_Sales表
            cursor.execute('SELECT COUNT(*) FROM "IOT_Sales"')
            count = cursor.fetchone()[0]
            print(f"✅ IOT_Sales表记录数: {count:,}")
            
        print(f"✅ 数据库操作成功，耗时: {time.time() - start_time:.2f}秒")
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 导入修复效果测试")
    print("=" * 50)
    
    tests = [
        ("连接池功能", test_connection_pool),
        ("数据导入处理器", test_data_import_processor),
        ("数据库操作", test_database_operations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！导入修复生效。")
        return 0
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
