#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量清理调试输出脚本 - 直接优化主程序
"""

import os
import re

def clean_debug_outputs():
    """清理主程序中的调试输出"""
    file_path = "../01_主程序/数据处理与导入应用_完整版.py"
    
    print(f"🔧 清理文件: {file_path}")
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_lines = content.count('\n')
        
        # 定义要替换的调试输出模式
        replacements = [
            # 替换 print(f"[DEBUG] ...") 为注释
            (r'(\s*)print\(f"\[DEBUG\].*?\)\s*$', r'\1# 🔧 优化：移除调试输出'),
            
            # 替换 print(f"DEBUG: ...") 为注释
            (r'(\s*)print\(f"DEBUG:.*?\)\s*$', r'\1# 🔧 优化：移除调试输出'),
            
            # 替换 print(f"🚀 SAFE DEBUG: ...") 为注释
            (r'(\s*)print\(f"🚀 SAFE DEBUG:.*?\)\s*$', r'\1# 🔧 优化：移除调试输出'),
            
            # 替换其他调试相关的print
            (r'(\s*)print\(f".*?DEBUG.*?\)\s*$', r'\1# 🔧 优化：移除调试输出'),
        ]
        
        cleaned_content = content
        total_replacements = 0
        
        for pattern, replacement in replacements:
            new_content = re.sub(pattern, replacement, cleaned_content, flags=re.MULTILINE)
            replacements_made = len(re.findall(pattern, cleaned_content, flags=re.MULTILINE))
            if replacements_made > 0:
                print(f"  替换了 {replacements_made} 个匹配项: {pattern[:50]}...")
                total_replacements += replacements_made
                cleaned_content = new_content
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        new_lines = cleaned_content.count('\n')
        
        print(f"✅ 清理完成:")
        print(f"   原始行数: {original_lines}")
        print(f"   清理后行数: {new_lines}")
        print(f"   总替换数: {total_replacements}")
        
        return total_replacements > 0
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

def optimize_backup_messages():
    """优化备份相关的消息显示"""
    file_path = "../01_主程序/数据处理与导入应用_完整版.py"
    
    print(f"🔧 优化备份消息: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简化备份相关的消息
        optimizations = [
            # 简化备份完成消息
            (
                r'self\.log_message\(f"✅ 备份完成: \{.*?\}"\)',
                'self.log_message("✅ 数据库备份完成")'
            ),
            # 简化备份验证消息 - 完全移除
            (
                r'self\.log_message\(f"备份文件验证通过: \{.*?\}"\)',
                '# 备份验证通过（简化显示）'
            ),
            # 简化删除旧备份消息 - 完全移除
            (
                r'self\.log_message\(f"已删除旧备份文件: \{.*?\}"\)',
                '# 已清理旧备份文件（简化显示）'
            ),
        ]
        
        optimized_content = content
        changes_made = 0
        
        for pattern, replacement in optimizations:
            new_content = re.sub(pattern, replacement, optimized_content)
            if new_content != optimized_content:
                matches = len(re.findall(pattern, optimized_content))
                print(f"  优化了 {matches} 处: {pattern[:50]}...")
                changes_made += matches
                optimized_content = new_content
        
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(optimized_content)
            print(f"✅ 优化了 {changes_made} 处备份消息")
        else:
            print("ℹ️ 没有找到需要优化的备份消息")
        
        return changes_made > 0
        
    except Exception as e:
        print(f"❌ 优化失败: {e}")
        return False

def add_environment_check():
    """在主程序开头添加环境检查"""
    file_path = "../01_主程序/数据处理与导入应用_完整版.py"
    
    print(f"🔧 添加环境检查: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有环境检查代码
        if 'APP_DEBUG' in content:
            print("ℹ️ 环境检查代码已存在")
            return False
        
        # 在导入部分后添加环境检查
        env_check_code = '''
# 🔧 环境控制 - 优化显示效果
import os
DEBUG_MODE = os.environ.get('APP_DEBUG', '0').lower() in ['1', 'true', 'yes']

def debug_print(*args, **kwargs):
    """条件性调试输出"""
    if DEBUG_MODE:
        print(*args, **kwargs)

'''
        
        # 在第一个import后插入
        lines = content.split('\n')
        insert_index = 0
        
        # 找到第一个import语句后的位置
        for i, line in enumerate(lines):
            if line.strip().startswith('import ') or line.strip().startswith('from '):
                insert_index = i + 1
                break
        
        # 插入环境检查代码
        lines.insert(insert_index, env_check_code)
        
        new_content = '\n'.join(lines)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ 添加了环境检查代码")
        return True
        
    except Exception as e:
        print(f"❌ 添加失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 批量清理调试输出，优化主程序显示效果")
    print("=" * 60)
    
    # 执行清理任务
    tasks = [
        ("清理调试输出", clean_debug_outputs),
        ("优化备份消息", optimize_backup_messages),
        # ("添加环境检查", add_environment_check),  # 暂时注释掉，避免重复添加
    ]
    
    results = []
    for task_name, task_func in tasks:
        try:
            print(f"\n执行任务: {task_name}")
            result = task_func()
            results.append((task_name, result))
        except Exception as e:
            print(f"❌ {task_name} 失败: {e}")
            results.append((task_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 优化结果总结:")
    
    passed = 0
    for task_name, result in results:
        status = "✅ 成功" if result else "ℹ️ 跳过"
        print(f"   {task_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n总体结果: {passed}/{total} 任务完成")
    
    print("\n🎉 主程序优化完成！")
    print("\n优化效果:")
    print("1. ✅ 移除了所有 [DEBUG] 调试输出")
    print("2. ✅ 简化了备份相关的消息")
    print("3. ✅ 保留了重要的功能性信息")
    print("4. ✅ 终端输出更加简洁清晰")
    
    print("\n现在启动应用程序将看到:")
    print("- 更少的技术性调试信息")
    print("- 更清晰的用户操作反馈")
    print("- 更专业的界面显示效果")
    print("- 更好的用户体验")

if __name__ == "__main__":
    main()
