# -*- coding: utf-8 -*-
"""
服务容器基础设施 - 架构优化步骤1
实现依赖注入、服务管理和循环依赖检测

版本: 1.0
作者: AI Assistant  
日期: 2025-01-18
"""

import threading
import time
from typing import Any, Callable, Dict, List, Optional, Set
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum
import traceback


class ServiceLifetime(Enum):
    """服务生命周期枚举"""
    SINGLETON = "singleton"      # 单例模式
    TRANSIENT = "transient"      # 瞬态模式
    SCOPED = "scoped"           # 作用域模式


@dataclass
class ServiceDescriptor:
    """服务描述符"""
    name: str
    factory: Callable
    lifetime: ServiceLifetime
    priority: int
    dependencies: List[str]
    initialized: bool = False
    instance: Any = None
    creation_time: float = 0.0


class CircularDependencyError(Exception):
    """循环依赖异常"""
    def __init__(self, cycle_path: List[str]):
        self.cycle_path = cycle_path
        cycle_str = " -> ".join(cycle_path)
        super().__init__(f"Circular dependency detected: {cycle_str}")


class ServiceNotFoundError(Exception):
    """服务未找到异常"""
    def __init__(self, service_name: str):
        self.service_name = service_name
        super().__init__(f"Service '{service_name}' not found in container")


class ServiceContainer:
    """
    服务容器 - 依赖注入核心
    
    功能：
    - 服务注册和获取
    - 循环依赖检测
    - 线程安全
    - 生命周期管理
    """
    
    def __init__(self):
        self._services: Dict[str, ServiceDescriptor] = {}
        self._instances: Dict[str, Any] = {}
        self._resolving_stack: List[str] = []
        self._lock = threading.RLock()
        self._creation_stats = defaultdict(int)
        
        # 性能监控
        self._performance_stats = {
            "total_resolutions": 0,
            "cache_hits": 0,
            "creation_time_total": 0.0,
            "average_creation_time": 0.0
        }
        
    def register(self, 
                name: str, 
                factory: Callable, 
                lifetime: ServiceLifetime = ServiceLifetime.SINGLETON,
                priority: int = 0,
                dependencies: Optional[List[str]] = None) -> 'ServiceContainer':
        """
        注册服务
        
        Args:
            name: 服务名称
            factory: 服务工厂函数
            lifetime: 生命周期
            priority: 优先级（数字越大优先级越高）
            dependencies: 依赖的其他服务
            
        Returns:
            ServiceContainer: 支持链式调用
        """
        with self._lock:
            if name in self._services:
                raise ValueError(f"Service '{name}' is already registered")
                
            descriptor = ServiceDescriptor(
                name=name,
                factory=factory,
                lifetime=lifetime,
                priority=priority,
                dependencies=dependencies or []
            )
            
            self._services[name] = descriptor
            
            # 验证依赖关系（检测循环依赖）
            self._validate_dependencies(name)
            
        return self
        
    def get(self, name: str) -> Any:
        """
        获取服务实例
        
        Args:
            name: 服务名称
            
        Returns:
            Any: 服务实例
            
        Raises:
            ServiceNotFoundError: 服务未找到
            CircularDependencyError: 循环依赖
        """
        start_time = time.time()
        
        try:
            with self._lock:
                self._performance_stats["total_resolutions"] += 1
                
                # 检查服务是否存在
                if name not in self._services:
                    raise ServiceNotFoundError(name)
                    
                descriptor = self._services[name]
                
                # 单例模式：检查是否已创建
                if descriptor.lifetime == ServiceLifetime.SINGLETON:
                    if name in self._instances:
                        self._performance_stats["cache_hits"] += 1
                        return self._instances[name]
                        
                # 检查循环依赖
                if name in self._resolving_stack:
                    cycle_start = self._resolving_stack.index(name)
                    cycle = self._resolving_stack[cycle_start:] + [name]
                    raise CircularDependencyError(cycle)
                    
                # 开始解析
                self._resolving_stack.append(name)
                
                try:
                    # 解析依赖
                    dependency_instances = {}
                    for dep_name in descriptor.dependencies:
                        dependency_instances[dep_name] = self.get(dep_name)
                        
                    # 创建实例
                    if dependency_instances:
                        # 如果工厂函数需要依赖参数
                        instance = descriptor.factory(**dependency_instances)
                    else:
                        # 无参数工厂函数
                        instance = descriptor.factory()
                        
                    # 缓存单例实例
                    if descriptor.lifetime == ServiceLifetime.SINGLETON:
                        self._instances[name] = instance
                        
                    # 更新统计信息
                    descriptor.initialized = True
                    descriptor.creation_time = time.time() - start_time
                    self._creation_stats[name] += 1
                    
                    return instance
                    
                finally:
                    # 清理解析栈
                    if name in self._resolving_stack:
                        self._resolving_stack.remove(name)
                        
        finally:
            # 更新性能统计
            creation_time = time.time() - start_time
            self._performance_stats["creation_time_total"] += creation_time
            total_resolutions = self._performance_stats["total_resolutions"]
            self._performance_stats["average_creation_time"] = (
                self._performance_stats["creation_time_total"] / total_resolutions
            )
            
    def has(self, name: str) -> bool:
        """检查服务是否已注册"""
        with self._lock:
            return name in self._services
            
    def remove(self, name: str) -> bool:
        """
        移除服务
        
        Args:
            name: 服务名称
            
        Returns:
            bool: 是否成功移除
        """
        with self._lock:
            if name not in self._services:
                return False
                
            # 检查是否有其他服务依赖此服务
            dependent_services = []
            for service_name, descriptor in self._services.items():
                if name in descriptor.dependencies:
                    dependent_services.append(service_name)
                    
            if dependent_services:
                raise ValueError(
                    f"Cannot remove service '{name}' because it is required by: "
                    f"{', '.join(dependent_services)}"
                )
                
            # 移除服务
            del self._services[name]
            if name in self._instances:
                del self._instances[name]
                
            return True
            
    def clear(self):
        """清空所有服务"""
        with self._lock:
            self._services.clear()
            self._instances.clear()
            self._resolving_stack.clear()
            self._creation_stats.clear()
            
    def get_service_info(self, name: str) -> Optional[Dict[str, Any]]:
        """获取服务信息"""
        with self._lock:
            if name not in self._services:
                return None
                
            descriptor = self._services[name]
            return {
                "name": descriptor.name,
                "lifetime": descriptor.lifetime.value,
                "priority": descriptor.priority,
                "dependencies": descriptor.dependencies,
                "initialized": descriptor.initialized,
                "creation_count": self._creation_stats[name],
                "creation_time": descriptor.creation_time,
                "has_instance": name in self._instances
            }
            
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        with self._lock:
            return self._performance_stats.copy()
            
    def get_dependency_graph(self) -> Dict[str, List[str]]:
        """获取依赖关系图"""
        with self._lock:
            return {
                name: descriptor.dependencies.copy()
                for name, descriptor in self._services.items()
            }
            
    def _validate_dependencies(self, service_name: str):
        """验证依赖关系，检测循环依赖"""
        visited = set()
        rec_stack = set()
        
        def has_cycle(name: str) -> bool:
            if name in rec_stack:
                return True
            if name in visited:
                return False
                
            visited.add(name)
            rec_stack.add(name)
            
            if name in self._services:
                for dep in self._services[name].dependencies:
                    if has_cycle(dep):
                        return True
                        
            rec_stack.remove(name)
            return False
            
        if has_cycle(service_name):
            raise CircularDependencyError([service_name])


class ServiceContainerBuilder:
    """服务容器构建器 - 提供流畅的API"""
    
    def __init__(self):
        self.container = ServiceContainer()
        
    def add_singleton(self, name: str, factory: Callable, 
                     dependencies: Optional[List[str]] = None) -> 'ServiceContainerBuilder':
        """添加单例服务"""
        self.container.register(name, factory, ServiceLifetime.SINGLETON, dependencies=dependencies)
        return self
        
    def add_transient(self, name: str, factory: Callable,
                     dependencies: Optional[List[str]] = None) -> 'ServiceContainerBuilder':
        """添加瞬态服务"""
        self.container.register(name, factory, ServiceLifetime.TRANSIENT, dependencies=dependencies)
        return self
        
    def build(self) -> ServiceContainer:
        """构建服务容器"""
        return self.container
