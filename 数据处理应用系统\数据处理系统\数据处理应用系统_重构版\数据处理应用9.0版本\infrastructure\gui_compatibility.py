# -*- coding: utf-8 -*-
"""
GUI兼容性包装器 - 架构优化步骤3
提供与SafeGUIUpdater完全兼容的接口

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import tkinter as tk
import threading
import time
from typing import Any, Dict, List, Optional, Callable, Union

from .logging_service import LoggingService, LogLevel, TabType
from .gui_service import GUIUpdateService, GUIUpdateType


class SafeGUIUpdaterCompatible:
    """
    SafeGUIUpdater兼容性包装器
    
    提供与原SafeGUIUpdater完全相同的接口，
    但内部使用新的异步日志和GUI服务
    """
    
    def __init__(self, root: tk.Tk, logging_service: Optional[LoggingService] = None,
                 gui_service: Optional[GUIUpdateService] = None):
        self.root = root
        self.logging_service = logging_service
        self.gui_service = gui_service
        
        # 兼容性属性
        self.log_widgets = {}
        self.progress_widgets = {}
        self.status_widgets = {}
        
        # 线程安全锁（保持兼容性）
        self.lock = threading.RLock()
        
        # 统计信息（保持兼容性）
        self.message_count = 0
        self.last_message_time = 0
        
        print("✅ [GUI] SafeGUIUpdater兼容性包装器已创建")
        
    def log_message(self, message: str, tab_type: str = "general"):
        """
        记录日志消息（兼容原接口）
        
        Args:
            message: 日志消息
            tab_type: 选项卡类型
        """
        try:
            with self.lock:
                self.message_count += 1
                self.last_message_time = time.time()
                
            # 转换tab_type为TabType枚举
            tab_enum = self._convert_tab_type(tab_type)
            
            if self.logging_service:
                # 使用新的异步日志服务
                self.logging_service.log_info(message, tab_enum, source="GUI")
            elif self.gui_service:
                # 直接使用GUI服务
                self.gui_service.log_message(message, tab_type)
            else:
                # 回退到控制台输出
                timestamp = time.strftime("%H:%M:%S")
                print(f"[{timestamp}] [{tab_type.upper()}] {message}")
                
        except Exception as e:
            print(f"❌ [GUI] 日志消息记录失败: {e}")
            
    def safe_update(self, update_func: Callable, *args, **kwargs):
        """
        线程安全的GUI更新（兼容原接口）
        
        Args:
            update_func: 更新函数
            *args: 位置参数
            **kwargs: 关键字参数
        """
        try:
            if self.gui_service:
                # 使用新的GUI服务
                self.gui_service.safe_update(
                    GUIUpdateType.CUSTOM,
                    {"args": args, "kwargs": kwargs},
                    callback=lambda data: update_func(*data["args"], **data["kwargs"])
                )
            else:
                # 回退到直接调用
                if self.root:
                    self.root.after(0, update_func, *args, **kwargs)
                else:
                    update_func(*args, **kwargs)
                    
        except Exception as e:
            print(f"❌ [GUI] 安全更新失败: {e}")
            
    def update_progress(self, progress: float, message: str = ""):
        """
        更新进度（兼容原接口）
        
        Args:
            progress: 进度值 (0.0-1.0)
            message: 进度消息
        """
        try:
            if self.gui_service:
                self.gui_service.update_progress(progress, message)
            else:
                # 回退处理
                self._fallback_progress_update(progress, message)
                
        except Exception as e:
            print(f"❌ [GUI] 进度更新失败: {e}")
            
    def update_status(self, status: str, level: str = "info"):
        """
        更新状态（兼容原接口）
        
        Args:
            status: 状态消息
            level: 状态级别
        """
        try:
            if self.gui_service:
                self.gui_service.update_status(status, level)
            else:
                # 回退处理
                self._fallback_status_update(status, level)
                
        except Exception as e:
            print(f"❌ [GUI] 状态更新失败: {e}")
            
    def register_log_widget(self, tab_type: str, widget: tk.Widget):
        """
        注册日志组件（兼容原接口）
        
        Args:
            tab_type: 选项卡类型
            widget: 日志组件
        """
        try:
            self.log_widgets[tab_type] = widget
            
            if self.gui_service:
                widget_name = f"log_text_{tab_type}"
                self.gui_service.register_widget(widget_name, widget)
                
            print(f"✅ [GUI] 注册日志组件: {tab_type}")
            
        except Exception as e:
            print(f"❌ [GUI] 日志组件注册失败: {e}")
            
    def register_progress_widget(self, progress_bar: tk.Widget, progress_label: tk.Widget = None):
        """
        注册进度组件（兼容原接口）
        
        Args:
            progress_bar: 进度条组件
            progress_label: 进度标签组件
        """
        try:
            self.progress_widgets["bar"] = progress_bar
            if progress_label:
                self.progress_widgets["label"] = progress_label
                
            if self.gui_service:
                self.gui_service.register_widget("progress_bar", progress_bar)
                if progress_label:
                    self.gui_service.register_widget("progress_label", progress_label)
                    
            print("✅ [GUI] 注册进度组件")
            
        except Exception as e:
            print(f"❌ [GUI] 进度组件注册失败: {e}")
            
    def register_status_widget(self, status_label: tk.Widget):
        """
        注册状态组件（兼容原接口）
        
        Args:
            status_label: 状态标签组件
        """
        try:
            self.status_widgets["label"] = status_label
            
            if self.gui_service:
                self.gui_service.register_widget("status_label", status_label)
                
            print("✅ [GUI] 注册状态组件")
            
        except Exception as e:
            print(f"❌ [GUI] 状态组件注册失败: {e}")
            
    def get_message_count(self) -> int:
        """获取消息计数（兼容原接口）"""
        return self.message_count
        
    def get_last_message_time(self) -> float:
        """获取最后消息时间（兼容原接口）"""
        return self.last_message_time
        
    def clear_logs(self, tab_type: str = None):
        """
        清空日志（兼容原接口）
        
        Args:
            tab_type: 选项卡类型，None表示清空所有
        """
        try:
            if tab_type:
                # 清空特定选项卡
                if tab_type in self.log_widgets:
                    widget = self.log_widgets[tab_type]
                    if hasattr(widget, 'delete'):
                        self.safe_update(lambda: widget.delete("1.0", tk.END))
            else:
                # 清空所有选项卡
                for widget in self.log_widgets.values():
                    if hasattr(widget, 'delete'):
                        self.safe_update(lambda w=widget: w.delete("1.0", tk.END))
                        
        except Exception as e:
            print(f"❌ [GUI] 清空日志失败: {e}")
            
    def shutdown(self):
        """关闭GUI更新器（兼容原接口）"""
        try:
            if self.gui_service:
                self.gui_service.shutdown()
                
            if self.logging_service:
                # 注意：不要关闭logging_service，因为它可能被其他组件使用
                pass
                
            print("✅ [GUI] SafeGUIUpdater兼容性包装器已关闭")
            
        except Exception as e:
            print(f"❌ [GUI] 关闭失败: {e}")
            
    def _convert_tab_type(self, tab_type: str) -> TabType:
        """转换选项卡类型为枚举"""
        tab_mapping = {
            "general": TabType.GENERAL,
            "processing": TabType.PROCESSING,
            "management": TabType.MANAGEMENT,
            "backup": TabType.BACKUP,
            "import": TabType.IMPORT,
            "export": TabType.EXPORT
        }
        return tab_mapping.get(tab_type.lower(), TabType.GENERAL)
        
    def _fallback_progress_update(self, progress: float, message: str):
        """回退的进度更新"""
        if "bar" in self.progress_widgets:
            progress_bar = self.progress_widgets["bar"]
            if hasattr(progress_bar, 'set'):
                self.safe_update(lambda: progress_bar.set(progress))
                
        if "label" in self.progress_widgets and message:
            progress_label = self.progress_widgets["label"]
            if hasattr(progress_label, 'config'):
                self.safe_update(lambda: progress_label.config(text=message))
                
    def _fallback_status_update(self, status: str, level: str):
        """回退的状态更新"""
        if "label" in self.status_widgets:
            status_label = self.status_widgets["label"]
            if hasattr(status_label, 'config'):
                # 根据级别设置颜色
                colors = {
                    "info": "black",
                    "warning": "orange",
                    "error": "red",
                    "success": "green"
                }
                color = colors.get(level, "black")
                self.safe_update(lambda: status_label.config(text=status, fg=color))


class SafeGUIUpdaterFactory:
    """SafeGUIUpdater工厂"""
    
    @staticmethod
    def create_compatible_gui_updater(root: tk.Tk, 
                                    logging_service: Optional[LoggingService] = None,
                                    gui_service: Optional[GUIUpdateService] = None) -> SafeGUIUpdaterCompatible:
        """
        创建兼容的GUI更新器
        
        Args:
            root: Tkinter根窗口
            logging_service: 日志服务（可选）
            gui_service: GUI服务（可选）
            
        Returns:
            SafeGUIUpdaterCompatible: 兼容的GUI更新器
        """
        return SafeGUIUpdaterCompatible(root, logging_service, gui_service)
        
    @staticmethod
    def create_from_services(root: tk.Tk, container) -> SafeGUIUpdaterCompatible:
        """
        从服务容器创建GUI更新器
        
        Args:
            root: Tkinter根窗口
            container: 服务容器
            
        Returns:
            SafeGUIUpdaterCompatible: 兼容的GUI更新器
        """
        try:
            logging_service = None
            gui_service = None
            
            if container.has("logging_service"):
                logging_service = container.get("logging_service")
                
            if container.has("gui_service"):
                gui_service = container.get("gui_service")
                
            return SafeGUIUpdaterCompatible(root, logging_service, gui_service)
            
        except Exception as e:
            print(f"⚠️ [GUI] 从服务容器创建GUI更新器失败: {e}")
            return SafeGUIUpdaterCompatible(root)


# 全局兼容性GUI更新器实例
_global_compatible_gui_updater: Optional[SafeGUIUpdaterCompatible] = None


def get_compatible_gui_updater() -> Optional[SafeGUIUpdaterCompatible]:
    """获取全局兼容性GUI更新器实例"""
    return _global_compatible_gui_updater


def set_compatible_gui_updater(gui_updater: SafeGUIUpdaterCompatible):
    """设置全局兼容性GUI更新器实例"""
    global _global_compatible_gui_updater
    _global_compatible_gui_updater = gui_updater
