# -*- coding: utf-8 -*-
"""
统一异常处理系统
定义应用程序中使用的所有自定义异常类
"""

from datetime import datetime
from typing import Dict, Any, Optional
import traceback


class AppException(Exception):
    """应用程序基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详细信息
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.timestamp = datetime.now()
        self.traceback = traceback.format_exc()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于日志记录和序列化"""
        return {
            'error_type': self.__class__.__name__,
            'error_code': self.error_code,
            'message': self.message,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
            'traceback': self.traceback
        }
    
    def get_user_message(self) -> str:
        """获取用户友好的错误信息"""
        return self.message
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}: {self.message}"


# 数据处理相关异常
class DataProcessingError(AppException):
    """数据处理异常"""
    
    def __init__(self, message: str, file_path: str = None, stage: str = None, **kwargs):
        self.file_path = file_path
        self.stage = stage
        details = kwargs.pop('details', {})  # 从kwargs中移除details避免重复
        if file_path:
            details['file_path'] = file_path
        if stage:
            details['processing_stage'] = stage
        super().__init__(message, details=details, **kwargs)
    
    def get_user_message(self) -> str:
        msg = f"数据处理失败: {self.message}"
        if self.file_path:
            msg += f"\n文件: {self.file_path}"
        if self.stage:
            msg += f"\n阶段: {self.stage}"
        return msg


class TransactionMatchingError(DataProcessingError):
    """Transaction ID匹配异常"""

    def __init__(self, message: str, transaction_id: str = None, **kwargs):
        self.transaction_id = transaction_id
        details = kwargs.pop('details', {})  # 从kwargs中移除details避免重复
        if transaction_id:
            details['transaction_id'] = transaction_id
        super().__init__(message, stage="Transaction ID匹配", details=details, **kwargs)


class DataImportError(DataProcessingError):
    """数据导入异常"""

    def __init__(self, message: str, import_stage: str = None, **kwargs):
        self.import_stage = import_stage
        details = kwargs.pop('details', {})
        if import_stage:
            details['import_stage'] = import_stage
        super().__init__(message, stage="数据导入", details=details, **kwargs)


class PlatformError(DataProcessingError):
    """平台识别异常"""

    def __init__(self, message: str, platform: str = None, **kwargs):
        self.platform = platform
        details = kwargs.pop('details', {})
        if platform:
            details['platform'] = platform
        super().__init__(message, stage="平台识别", details=details, **kwargs)


class DuplicateDataError(DataProcessingError):
    """重复数据异常"""

    def __init__(self, message: str, duplicate_count: int = None, **kwargs):
        self.duplicate_count = duplicate_count
        details = kwargs.pop('details', {})
        if duplicate_count:
            details['duplicate_count'] = duplicate_count
        super().__init__(message, stage="重复数据检测", details=details, **kwargs)


# 文件操作相关异常
class FileOperationError(AppException):
    """文件操作异常"""
    
    def __init__(self, message: str, file_path: str = None, operation: str = None, **kwargs):
        self.file_path = file_path
        self.operation = operation
        details = kwargs.pop('details', {})  # 从kwargs中移除details避免重复
        if file_path:
            details['file_path'] = file_path
        if operation:
            details['operation'] = operation
        super().__init__(message, details=details, **kwargs)
    
    def get_user_message(self) -> str:
        msg = f"文件操作失败: {self.message}"
        if self.file_path:
            msg += f"\n文件: {self.file_path}"
        if self.operation:
            msg += f"\n操作: {self.operation}"
        return msg


class FileNotFoundError(FileOperationError):
    """文件不存在异常"""
    
    def __init__(self, file_path: str, operation: str = "访问"):
        super().__init__(
            f"文件不存在: {file_path}",
            file_path=file_path,
            operation=operation,
            error_code="FILE_NOT_FOUND"
        )
    
    def get_user_message(self) -> str:
        return f"文件不存在: {self.file_path}\n请检查文件路径是否正确。"


class FileValidationError(FileOperationError):
    """文件验证异常"""
    
    def __init__(self, file_path: str, validation_issue: str, expected: str = None):
        self.validation_issue = validation_issue
        self.expected = expected
        details = {'issue': validation_issue}
        if expected:
            details['expected'] = expected
        
        super().__init__(
            f"文件验证失败: {validation_issue}",
            file_path=file_path,
            operation="验证",
            details=details,
            error_code="FILE_VALIDATION_FAILED"
        )
    
    def get_user_message(self) -> str:
        msg = f"文件格式错误: {self.file_path}\n问题: {self.validation_issue}"
        if self.expected:
            msg += f"\n期望: {self.expected}"
        return msg


# 数据库相关异常
class DatabaseError(AppException):
    """数据库操作异常"""
    
    def __init__(self, message: str, operation: str = None, table: str = None, **kwargs):
        self.operation = operation
        self.table = table
        details = kwargs.pop('details', {})  # 从kwargs中移除details避免重复
        if operation:
            details['operation'] = operation
        if table:
            details['table'] = table
        super().__init__(message, details=details, **kwargs)
    
    def get_user_message(self) -> str:
        msg = f"数据库操作失败: {self.message}"
        if self.operation:
            msg += f"\n操作: {self.operation}"
        if self.table:
            msg += f"\n表: {self.table}"
        return msg


class ConnectionPoolError(DatabaseError):
    """数据库连接池异常"""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, operation="连接池管理", error_code="CONNECTION_POOL_ERROR", **kwargs)


class BackupError(DatabaseError):
    """备份操作异常"""

    def __init__(self, message: str, backup_path: str = None, operation: str = None, **kwargs):
        self.backup_path = backup_path
        details = kwargs.pop('details', {})
        if backup_path:
            details['backup_path'] = backup_path
        super().__init__(message, operation=operation or "备份操作", details=details, **kwargs)

    def get_user_message(self) -> str:
        msg = f"备份操作失败: {self.message}"
        if self.backup_path:
            msg += f"\n备份文件: {self.backup_path}"
        return msg


# 配置相关异常
class ConfigurationError(AppException):
    """配置异常"""
    
    def __init__(self, message: str, config_key: str = None, config_section: str = None, **kwargs):
        self.config_key = config_key
        self.config_section = config_section
        details = kwargs.pop('details', {})  # 从kwargs中移除details避免重复
        if config_key:
            details['config_key'] = config_key
        if config_section:
            details['config_section'] = config_section
        super().__init__(message, details=details, **kwargs)
    
    def get_user_message(self) -> str:
        msg = f"配置错误: {self.message}"
        if self.config_key:
            msg += f"\n配置项: {self.config_key}"
        if self.config_section:
            msg += f"\n配置节: {self.config_section}"
        msg += "\n请检查配置文件设置。"
        return msg


# 验证相关异常
class ValidationError(AppException):
    """输入验证异常"""
    
    def __init__(self, message: str, field: str = None, value: Any = None, **kwargs):
        self.field = field
        self.value = value
        details = kwargs.pop('details', {})  # 从kwargs中移除details避免重复
        if field:
            details['field'] = field
        if value is not None:
            details['value'] = str(value)
        super().__init__(message, details=details, **kwargs)
    
    def get_user_message(self) -> str:
        msg = f"输入验证失败: {self.message}"
        if self.field:
            msg += f"\n字段: {self.field}"
        return msg


# 进程执行相关异常
class ProcessExecutionError(AppException):
    """进程执行异常"""
    
    def __init__(self, message: str, command: str = None, return_code: int = None, stderr: str = None, **kwargs):
        self.command = command
        self.return_code = return_code
        self.stderr = stderr
        details = kwargs.pop('details', {})  # 从kwargs中移除details避免重复
        if command:
            details['command'] = command
        if return_code is not None:
            details['return_code'] = return_code
        if stderr:
            details['stderr'] = stderr
        super().__init__(message, details=details, **kwargs)
    
    def get_user_message(self) -> str:
        msg = f"进程执行失败: {self.message}"
        if self.command:
            msg += f"\n命令: {self.command}"
        if self.return_code is not None:
            msg += f"\n返回码: {self.return_code}"
        if self.stderr:
            msg += f"\n错误信息: {self.stderr}"
        return msg


# 异常处理装饰器
def exception_handler(operation_name: str = "操作", reraise: bool = False):
    """
    异常处理装饰器
    
    Args:
        operation_name: 操作名称，用于错误消息
        reraise: 是否重新抛出异常
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except AppException:
                # 自定义异常直接传递
                if reraise:
                    raise
                return None
            except Exception as e:
                # 将标准异常包装为自定义异常
                error_msg = f"{operation_name}时发生错误: {str(e)}"
                wrapped_exception = AppException(error_msg, details={'original_error': str(e)})
                if reraise:
                    raise wrapped_exception
                return None
        return wrapper
    return decorator
