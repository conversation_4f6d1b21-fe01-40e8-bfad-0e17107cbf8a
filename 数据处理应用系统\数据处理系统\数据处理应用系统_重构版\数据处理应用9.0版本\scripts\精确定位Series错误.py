#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确定位Series错误 - 通过猴子补丁追踪Series错误的确切位置
"""

import os
import sys
import pandas as pd
import traceback
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def install_series_monitor():
    """安装Series监控补丁，追踪所有可能导致错误的操作"""
    
    # 保存原始方法
    original_bool = pd.Series.__bool__
    original_getitem = pd.Series.__getitem__
    original_eq = pd.Series.__eq__
    original_ne = pd.Series.__ne__
    original_and = pd.Series.__and__
    original_or = pd.Series.__or__
    
    def monitored_bool(self):
        """监控Series.__bool__调用"""
        print(f"🚨 Series.__bool__() 被调用！")
        print(f"📍 Series信息: 长度={len(self)}, 类型={type(self)}")
        print(f"📍 调用栈:")
        for line in traceback.format_stack()[-5:-1]:
            print(f"  {line.strip()}")
        
        # 如果Series长度大于1，这会导致错误
        if len(self) > 1:
            print(f"❌ 错误：尝试对长度为{len(self)}的Series进行布尔值判断！")
            raise ValueError(f"The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().")
        
        return original_bool(self)
    
    def monitored_and(self, other):
        """监控Series.__and__调用"""
        print(f"🔍 Series.__and__() 被调用")
        print(f"📍 self长度={len(self)}, other类型={type(other)}")
        if hasattr(other, '__len__'):
            print(f"📍 other长度={len(other)}")
        return original_and(self, other)
    
    def monitored_or(self, other):
        """监控Series.__or__调用"""
        print(f"🔍 Series.__or__() 被调用")
        print(f"📍 self长度={len(self)}, other类型={type(other)}")
        if hasattr(other, '__len__'):
            print(f"📍 other长度={len(other)}")
        return original_or(self, other)
    
    # 安装补丁
    pd.Series.__bool__ = monitored_bool
    pd.Series.__and__ = monitored_and
    pd.Series.__or__ = monitored_or
    
    print("🔧 Series监控补丁已安装")

def test_actual_import_with_monitoring():
    """使用监控补丁测试实际导入过程"""
    print("🔧 使用监控补丁测试实际导入过程")
    print("-" * 80)
    
    try:
        # 安装监控补丁
        install_series_monitor()
        
        # 设置非交互模式
        os.environ['NON_INTERACTIVE'] = '1'
        os.environ['AUTO_DUPLICATE_HANDLING'] = 'overwrite'
        os.environ['AUTO_MISSING_HANDLING'] = 'ignore'
        
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建模拟IOT数据
        test_data = pd.DataFrame({
            'Copartner name': ['Partner1', 'Partner2', 'Partner3'],
            'Order No.': ['ORD001', 'ORD002', 'ORD003'],
            'Transaction Num': ['TXN001', 'TXN002', 'TXN003'],
            'Order types': ['普通', '普通', '普通'],
            'Order price': [100.0, 200.0, 300.0],
            'Payment': [100.0, 200.0, 300.0],
            'Order time': ['2025-07-07 00:00:00', '2025-07-08 12:30:45', '2025-07-09 15:20:30'],
            'Equipment ID': ['EQ001', 'EQ002', 'EQ003'],
            'Equipment name': ['Device1', 'Device2', 'Device3'],
            'Branch name': ['Branch1', 'Branch2', 'Branch3'],
            'Payment date': ['2025-07-07', '2025-07-08', '2025-07-09'],
            'User name': ['User1', 'User2', 'User3'],
            'Time': ['10:00:00', '11:00:00', '12:00:00'],
            'Matched Order ID': ['', '', ''],
            'OrderTime_dt': ['2025-07-07 10:00:00', '2025-07-08 11:00:00', '2025-07-09 12:00:00']
        })
        
        print(f"📊 创建测试数据: {len(test_data)} 条记录")
        
        # 逐步测试各个方法，捕获Series错误
        test_methods = [
            ("数据清洗", lambda: processor._clean_data(test_data.copy())),
            ("数据标准化", lambda: processor._standardize_data_types(test_data.copy())),
            ("列过滤", lambda: processor._filter_columns_for_database(test_data.copy())),
            ("智能增量重复检测", lambda: processor.smart_incremental_duplicate_check(test_data.copy(), 'IOT')),
        ]
        
        for method_name, method_func in test_methods:
            try:
                print(f"\n🧪 测试方法: {method_name}")
                print("-" * 40)
                result = method_func()
                print(f"✅ {method_name} 成功完成")
            except Exception as e:
                print(f"❌ {method_name} 失败: {e}")
                print("📍 错误详情:")
                traceback.print_exc()
                return False
        
        print("\n✅ 所有方法测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("📍 错误详情:")
        traceback.print_exc()
        return False

def test_specific_conditions():
    """测试特定的可能导致Series错误的条件"""
    print("\n🔧 测试特定的可能导致Series错误的条件")
    print("-" * 80)
    
    try:
        # 测试各种可能导致Series布尔值错误的情况
        test_cases = [
            ("空Series", pd.Series([])),
            ("单元素Series", pd.Series([1])),
            ("多元素Series", pd.Series([1, 2, 3])),
            ("包含NaN的Series", pd.Series([1, None, 3])),
            ("全为NaN的Series", pd.Series([None, None, None])),
            ("布尔Series", pd.Series([True, False, True])),
            ("字符串Series", pd.Series(['a', 'b', 'c'])),
        ]
        
        for case_name, test_series in test_cases:
            print(f"\n🧪 测试: {case_name}")
            try:
                # 测试各种可能的操作
                print(f"  长度: {len(test_series)}")
                print(f"  是否为空: {test_series.empty}")
                print(f"  有非空值: {test_series.notna().any() if len(test_series) > 0 else False}")
                print(f"  全为空: {test_series.isna().all() if len(test_series) > 0 else True}")
                
                # 测试可能导致错误的操作
                if len(test_series) > 1:
                    print("  ⚠️ 多元素Series，避免直接布尔值判断")
                    # 不执行 bool(test_series)，这会导致错误
                else:
                    print(f"  布尔值: {bool(test_series) if len(test_series) <= 1 else '跳过'}")
                
                print(f"✅ {case_name} 测试通过")
                
            except Exception as e:
                print(f"❌ {case_name} 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 特定条件测试失败: {e}")
        traceback.print_exc()
        return False

def test_pandas_operations():
    """测试pandas操作中可能导致Series错误的情况"""
    print("\n🔧 测试pandas操作中可能导致Series错误的情况")
    print("-" * 80)
    
    try:
        # 创建测试DataFrame
        df = pd.DataFrame({
            'Order_time': ['2025-07-07', '2025-07-08', '2025-07-09'],
            'Transaction_Num': ['TXN001', 'TXN002', 'TXN003'],
            'Order_price': [100.0, 200.0, 300.0]
        })
        
        print("🧪 测试DataFrame操作:")
        
        # 测试可能的问题操作
        operations = [
            ("df.empty", lambda: df.empty),
            ("df['Order_time'].notna().any()", lambda: df['Order_time'].notna().any()),
            ("df['Order_time'].isna().all()", lambda: df['Order_time'].isna().all()),
            ("len(df) > 0", lambda: len(df) > 0),
            ("not df.empty", lambda: not df.empty),
        ]
        
        for op_name, op_func in operations:
            try:
                result = op_func()
                print(f"  ✅ {op_name}: {result}")
            except Exception as e:
                print(f"  ❌ {op_name}: {e}")
        
        # 测试条件组合
        print("\n🧪 测试条件组合:")
        try:
            condition1 = 'Order_time' in df.columns
            condition2 = not df.empty
            condition3 = df['Order_time'].notna().any()
            
            print(f"  'Order_time' in df.columns: {condition1}")
            print(f"  not df.empty: {condition2}")
            print(f"  df['Order_time'].notna().any(): {condition3}")
            print(f"  组合条件: {condition1 and condition2 and condition3}")
            
        except Exception as e:
            print(f"  ❌ 条件组合测试失败: {e}")
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ pandas操作测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 精确定位Series错误")
    print("=" * 80)
    
    tests = [
        ("特定条件测试", test_specific_conditions),
        ("pandas操作测试", test_pandas_operations),
        ("实际导入过程监控", test_actual_import_with_monitoring),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 Series错误定位结果")
    print("=" * 80)
    
    print(f"📊 通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过")
        print("✅ 没有发现Series错误")
    else:
        print("❌ 发现Series错误")
        print("🔧 需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 错误定位{'成功' if success else '发现问题'}")
    input("按回车键退出...")
