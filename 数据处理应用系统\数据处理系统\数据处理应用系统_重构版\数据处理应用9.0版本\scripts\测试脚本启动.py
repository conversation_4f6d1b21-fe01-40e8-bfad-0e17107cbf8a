#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本启动 - 检查data_import_optimized.py是否能正常启动
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_script_startup():
    """测试脚本启动"""
    print("🔧 测试data_import_optimized.py脚本启动")
    print("=" * 80)
    
    # 设置环境变量
    env = os.environ.copy()
    env['NON_INTERACTIVE'] = '1'
    env['AUTO_DUPLICATE_HANDLING'] = 'overwrite'
    env['AUTO_MISSING_HANDLING'] = 'ignore'
    env['FORCE_CONSOLE'] = '1'
    
    script_path = "data_import_optimized.py"
    
    if not os.path.exists(script_path):
        print(f"❌ 脚本文件不存在: {script_path}")
        return False
    
    print(f"📁 脚本路径: {script_path}")
    print("🔧 设置环境变量:")
    print("   NON_INTERACTIVE=1")
    print("   AUTO_DUPLICATE_HANDLING=overwrite")
    print("   AUTO_MISSING_HANDLING=ignore")
    print("   FORCE_CONSOLE=1")
    
    # 测试1：检查脚本语法
    print("\n🧪 测试1: 检查脚本语法")
    try:
        result = subprocess.run(
            [sys.executable, "-m", "py_compile", script_path],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ 脚本语法检查通过")
        else:
            print("❌ 脚本语法错误:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print("❌ 语法检查超时")
        return False
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False
    
    # 测试2：检查脚本导入
    print("\n🧪 测试2: 检查脚本导入")
    test_import_code = '''
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("开始导入模块...")
    
    # 逐个导入模块，找出卡住的地方
    print("导入 os, sys, re...")
    import os, sys, re
    
    print("导入 pandas...")
    import pandas as pd
    
    print("导入 sqlite3...")
    import sqlite3
    
    print("导入 datetime...")
    from datetime import datetime, timedelta
    
    print("导入 pathlib...")
    from pathlib import Path
    
    print("导入 argparse...")
    import argparse
    
    print("导入 typing...")
    from typing import List, Dict, Any, Optional, Tuple
    
    print("导入 utils.logger...")
    from utils.logger import get_logger
    
    print("导入 utils.exceptions...")
    from utils.exceptions import *
    
    print("导入 utils.validators...")
    from utils.validators import input_validator
    
    print("导入 database.connection_pool...")
    from database.connection_pool import get_connection, reinitialize_connection_pool
    
    print("✅ 所有模块导入成功")
    
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
'''
    
    try:
        result = subprocess.run(
            [sys.executable, "-c", test_import_code],
            capture_output=True,
            text=True,
            timeout=60,
            env=env,
            cwd=os.path.dirname(os.path.abspath(script_path))
        )
        
        print("📋 导入测试输出:")
        if result.stdout:
            for line in result.stdout.strip().split('\n'):
                print(f"  {line}")
        
        if result.stderr:
            print("📋 导入测试错误:")
            for line in result.stderr.strip().split('\n'):
                print(f"  ❌ {line}")
        
        if result.returncode == 0:
            print("✅ 模块导入测试通过")
        else:
            print(f"❌ 模块导入测试失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 模块导入测试超时（60秒）")
        return False
    except Exception as e:
        print(f"❌ 模块导入测试异常: {e}")
        return False
    
    # 测试3：检查脚本参数解析
    print("\n🧪 测试3: 检查脚本参数解析")
    try:
        cmd = [
            sys.executable,
            script_path,
            "--help"
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,
            env=env
        )
        
        if result.returncode == 0:
            print("✅ 参数解析测试通过")
            print("📋 帮助信息:")
            for line in result.stdout.strip().split('\n')[:10]:  # 只显示前10行
                print(f"  {line}")
        else:
            print(f"❌ 参数解析测试失败，返回码: {result.returncode}")
            if result.stderr:
                print("错误信息:")
                for line in result.stderr.strip().split('\n'):
                    print(f"  {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 参数解析测试超时")
        return False
    except Exception as e:
        print(f"❌ 参数解析测试异常: {e}")
        return False
    
    # 测试4：检查脚本初始化（无参数）
    print("\n🧪 测试4: 检查脚本初始化（无参数）")
    try:
        cmd = [
            sys.executable,
            script_path
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,
            env=env
        )
        
        print(f"📋 返回码: {result.returncode}")
        
        if result.stdout:
            print("📋 标准输出:")
            for line in result.stdout.strip().split('\n'):
                print(f"  {line}")
        
        if result.stderr:
            print("📋 错误输出:")
            for line in result.stderr.strip().split('\n'):
                print(f"  {line}")
        
        # 无参数应该返回错误码1，但不应该卡住
        if result.returncode == 1:
            print("✅ 脚本正常处理无参数情况")
        else:
            print(f"⚠️ 脚本返回码异常: {result.returncode}")
            
    except subprocess.TimeoutExpired:
        print("❌ 脚本初始化超时（30秒）")
        print("🔍 这表明脚本在初始化阶段卡住了")
        return False
    except Exception as e:
        print(f"❌ 脚本初始化异常: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔧 data_import_optimized.py脚本启动测试")
    print("=" * 80)
    
    success = test_script_startup()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果")
    print("=" * 80)
    
    if success:
        print("🎉 脚本启动测试通过")
        print("✅ 脚本可以正常启动和初始化")
        print("✅ 模块导入正常")
        print("✅ 参数解析正常")
        print("💡 问题可能出现在具体的文件处理逻辑中")
    else:
        print("❌ 脚本启动测试失败")
        print("🔧 脚本在启动阶段就有问题")
        print("💡 需要修复脚本的基础问题")
    
    return success

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 测试{'通过' if success else '失败'}")
    input("按回车键退出...")
