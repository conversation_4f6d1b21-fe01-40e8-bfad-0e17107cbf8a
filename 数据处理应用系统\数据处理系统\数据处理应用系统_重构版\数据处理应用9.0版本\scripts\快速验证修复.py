#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证修复 - 验证关键修复不影响代码运行
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def quick_test():
    """快速测试关键功能"""
    print("🔍 快速验证修复后的代码")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 0
    
    # 测试1：基本导入
    total_tests += 1
    try:
        from data_import_optimized import DataImportProcessor
        print("✅ 1. DataImportProcessor 导入成功")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 1. DataImportProcessor 导入失败: {e}")
    
    # 测试2：实例创建
    total_tests += 1
    try:
        processor = DataImportProcessor()
        print("✅ 2. DataImportProcessor 实例创建成功")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 2. DataImportProcessor 实例创建失败: {e}")
        return tests_passed, total_tests
    
    # 测试3：内存监控
    total_tests += 1
    try:
        memory_usage = processor._check_memory_usage()
        print(f"✅ 3. 内存监控功能正常: {memory_usage // 1024 // 1024}MB")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 3. 内存监控功能失败: {e}")
    
    # 测试4：时间标准化
    total_tests += 1
    try:
        import pandas as pd
        test_df = pd.DataFrame({
            'Order_time': ['2025-01-01 10:00:00'],
            'Payment_date': ['2025-01-01']
        })
        result = processor._standardize_datetime_format(test_df)
        print("✅ 4. 时间格式标准化功能正常")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 4. 时间格式标准化失败: {e}")
    
    # 测试5：字段验证
    total_tests += 1
    try:
        test_df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_No': ['ORD001'],
            'Order_price': [100.0],
            'Equipment_ID': ['EQ001'],
            'Order_time': ['2025-01-01 10:00:00']
        })
        validation = processor._validate_critical_fields(test_df)
        print("✅ 5. 字段验证功能正常")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 5. 字段验证失败: {e}")
    
    # 测试6：缺失记录检测
    total_tests += 1
    try:
        test_df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_No': ['ORD001'],
            'Order_time': ['2025-01-01 10:00:00'],
            'Order_price': [100.0],
            'Order_status': ['Finished'],
            'Equipment_ID': ['EQ001']
        })
        missing_report = processor._detect_missing_records(test_df, 'IOT')
        print("✅ 6. 缺失记录检测功能正常")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 6. 缺失记录检测失败: {e}")
    
    # 测试7：Logger功能
    total_tests += 1
    try:
        from utils.logger import get_logger
        logger = get_logger('test')
        logger.info("测试日志")
        print("✅ 7. Logger功能正常")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 7. Logger功能失败: {e}")
    
    # 测试8：数据库连接
    total_tests += 1
    try:
        from database.connection_pool import get_connection
        with get_connection() as conn:
            cursor = conn.connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        print("✅ 8. 数据库连接功能正常")
        tests_passed += 1
    except Exception as e:
        print(f"❌ 8. 数据库连接失败: {e}")
    
    return tests_passed, total_tests

def main():
    """主函数"""
    try:
        passed, total = quick_test()
        
        print("\n" + "=" * 50)
        print("🎯 快速验证结果")
        print("=" * 50)
        
        print(f"📊 通过测试: {passed}/{total}")
        success_rate = (passed / total) * 100
        print(f"📊 成功率: {success_rate:.1f}%")
        
        if passed == total:
            print("🎉 所有测试通过")
            print("✅ 修复后的代码完全正常")
            print("✅ 所有关键功能都正常工作")
            print("✅ 修复没有引入新问题")
        elif passed >= total * 0.8:
            print("✅ 大部分测试通过")
            print("⚠️ 少量功能可能需要检查")
        else:
            print("❌ 多个测试失败")
            print("🔧 需要检查修复是否引入问题")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 验证{'成功' if success else '失败'}")
