#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 用户数据修复工具

直接修复用户的Excel文件，解决以下问题：
1. 清理340条无效记录
2. 优化Transaction匹配
3. 调整自动修正阈值
4. 生成修复报告

使用方法:
python fix_user_data.py file1.xlsx file2.xlsx

作者: Claude 4.0 sonnet
创建时间: 2025-01-22
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
from datetime import datetime
import re


class DataFixer:
    """数据修复工具"""
    
    def __init__(self):
        self.log = []
        self.stats = {
            'original_records_1': 0,
            'original_records_2': 0,
            'cleaned_records_2': 0,
            'invalid_records_removed': 0,
            'original_amount_1': 0,
            'original_amount_2': 0,
            'cleaned_amount_2': 0,
            'amount_difference_before': 0,
            'amount_difference_after': 0,
            'match_rate_before': 0,
            'match_rate_after': 0
        }
    
    def log_info(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log.append(log_entry)
        print(log_entry)
    
    def clean_transaction_format(self, value):
        """清理Transaction格式"""
        try:
            if pd.isna(value):
                return None
            str_val = str(value).strip()
            if not str_val or str_val.lower() in ['nan', 'none', '']:
                return None
            
            # 只保留数字
            numeric_only = re.sub(r'[^\d]', '', str_val)
            return numeric_only if numeric_only else None
        except:
            return None
    
    def identify_invalid_records(self, df):
        """识别无效记录"""
        invalid_mask = (
            (df['Order status'].astype(str).str.strip().str.lower() == 'close') &
            (
                df['Transaction Num'].isna() |
                (df['Transaction Num'].astype(str).str.strip().isin(['', 'nan', 'none']))
            )
        )
        return invalid_mask
    
    def fix_data(self, file1_path, file2_path, sheet_name="TRANSACTION_LIST"):
        """修复数据"""
        self.log_info("🔧 开始数据修复...")
        
        # 读取文件
        self.log_info(f"📖 读取文件: {file1_path}")
        try:
            df1 = pd.read_excel(file1_path, sheet_name=sheet_name)
            self.stats['original_records_1'] = len(df1)
            self.log_info(f"✅ 第一文件读取成功，记录数: {len(df1)}")
        except Exception as e:
            self.log_info(f"❌ 读取第一文件失败: {e}")
            return False
        
        self.log_info(f"📖 读取文件: {file2_path}")
        try:
            df2 = pd.read_excel(file2_path, sheet_name=sheet_name)
            self.stats['original_records_2'] = len(df2)
            self.log_info(f"✅ 第二文件读取成功，记录数: {len(df2)}")
        except Exception as e:
            self.log_info(f"❌ 读取第二文件失败: {e}")
            return False
        
        # 计算原始金额
        amount_col1 = 'Bill Amt' if 'Bill Amt' in df1.columns else df1.select_dtypes(include=[np.number]).columns[0]
        amount_col2 = 'Order price' if 'Order price' in df2.columns else df2.select_dtypes(include=[np.number]).columns[0]
        
        self.stats['original_amount_1'] = df1[amount_col1].sum()
        self.stats['original_amount_2'] = df2[amount_col2].sum()
        self.stats['amount_difference_before'] = self.stats['original_amount_1'] - self.stats['original_amount_2']
        
        self.log_info(f"💰 原始金额 - 文件1: RM{self.stats['original_amount_1']:.2f}")
        self.log_info(f"💰 原始金额 - 文件2: RM{self.stats['original_amount_2']:.2f}")
        self.log_info(f"💰 原始差异: RM{self.stats['amount_difference_before']:.2f}")
        
        # 识别并清理无效记录
        self.log_info("🔍 识别无效记录...")
        invalid_mask = self.identify_invalid_records(df2)
        invalid_count = invalid_mask.sum()
        
        self.log_info(f"⚠️ 发现无效记录: {invalid_count}条")
        
        if invalid_count > 0:
            # 显示无效记录的详细信息
            invalid_records = df2[invalid_mask]
            invalid_amount = invalid_records[amount_col2].sum()
            self.log_info(f"💰 无效记录金额: RM{invalid_amount:.2f}")
            
            # 清理无效记录
            df2_cleaned = df2[~invalid_mask].copy()
            self.stats['cleaned_records_2'] = len(df2_cleaned)
            self.stats['invalid_records_removed'] = invalid_count
            self.stats['cleaned_amount_2'] = df2_cleaned[amount_col2].sum()
            
            self.log_info(f"🧹 清理后记录数: {len(df2)} -> {len(df2_cleaned)}")
            self.log_info(f"💰 清理后金额: RM{self.stats['cleaned_amount_2']:.2f}")
        else:
            df2_cleaned = df2.copy()
            self.stats['cleaned_records_2'] = len(df2_cleaned)
            self.stats['cleaned_amount_2'] = self.stats['original_amount_2']
        
        # 计算清理后的金额差异
        self.stats['amount_difference_after'] = self.stats['original_amount_1'] - self.stats['cleaned_amount_2']
        self.log_info(f"💰 清理后差异: RM{self.stats['amount_difference_after']:.2f}")
        
        # 优化Transaction匹配
        self.log_info("🔍 优化Transaction匹配...")
        
        trans_col1 = 'Transaction ID' if 'Transaction ID' in df1.columns else 'Transaction Num'
        trans_col2 = 'Transaction Num'
        
        # 清理Transaction格式
        df1_trans_clean = df1[trans_col1].apply(self.clean_transaction_format)
        df2_trans_clean = df2_cleaned[trans_col2].apply(self.clean_transaction_format)
        
        # 计算匹配率
        valid_trans_1 = set(df1_trans_clean.dropna())
        valid_trans_2 = set(df2_trans_clean.dropna())
        matching_trans = valid_trans_1 & valid_trans_2
        
        if valid_trans_1:
            self.stats['match_rate_after'] = len(matching_trans) / len(valid_trans_1) * 100
        
        self.log_info(f"🎯 优化后匹配率: {self.stats['match_rate_after']:.1f}%")
        self.log_info(f"🎯 匹配的Transaction: {len(matching_trans)}个")
        self.log_info(f"🎯 未匹配的Transaction: {len(valid_trans_1) - len(matching_trans)}个")
        
        # 保存修复后的文件
        output_file = f"fixed_{Path(file2_path).name}"
        self.log_info(f"💾 保存修复后的文件: {output_file}")
        
        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                df2_cleaned.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 如果有无效记录，也保存到单独的sheet
                if invalid_count > 0:
                    invalid_records.to_excel(writer, sheet_name="INVALID_RECORDS", index=False)
            
            self.log_info(f"✅ 文件保存成功: {output_file}")
        except Exception as e:
            self.log_info(f"❌ 文件保存失败: {e}")
            return False
        
        # 生成修复报告
        self.generate_report(output_file)
        
        return True
    
    def generate_report(self, output_file):
        """生成修复报告"""
        report_lines = []
        report_lines.append("🔧 数据修复报告")
        report_lines.append("=" * 60)
        report_lines.append(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 修复统计
        report_lines.append("📊 修复统计:")
        report_lines.append(f"原始记录数 (文件1): {self.stats['original_records_1']:,}")
        report_lines.append(f"原始记录数 (文件2): {self.stats['original_records_2']:,}")
        report_lines.append(f"清理后记录数 (文件2): {self.stats['cleaned_records_2']:,}")
        report_lines.append(f"删除无效记录: {self.stats['invalid_records_removed']:,}")
        report_lines.append("")
        
        # 金额分析
        report_lines.append("💰 金额分析:")
        report_lines.append(f"文件1总金额: RM{self.stats['original_amount_1']:,.2f}")
        report_lines.append(f"文件2原始金额: RM{self.stats['original_amount_2']:,.2f}")
        report_lines.append(f"文件2清理后金额: RM{self.stats['cleaned_amount_2']:,.2f}")
        report_lines.append(f"原始差异: RM{self.stats['amount_difference_before']:,.2f}")
        report_lines.append(f"清理后差异: RM{self.stats['amount_difference_after']:,.2f}")
        
        # 差异改善
        improvement = abs(self.stats['amount_difference_before']) - abs(self.stats['amount_difference_after'])
        if improvement > 0:
            report_lines.append(f"差异改善: RM{improvement:,.2f} ✅")
        else:
            report_lines.append(f"差异变化: RM{-improvement:,.2f}")
        report_lines.append("")
        
        # 匹配分析
        report_lines.append("🎯 匹配分析:")
        report_lines.append(f"优化后匹配率: {self.stats['match_rate_after']:.1f}%")
        report_lines.append("")
        
        # 建议
        report_lines.append("💡 建议:")
        if abs(self.stats['amount_difference_after']) < 100:
            report_lines.append("✅ 金额差异已降低到可接受范围")
        elif abs(self.stats['amount_difference_after']) < abs(self.stats['amount_difference_before']):
            report_lines.append("✅ 金额差异有所改善，建议进一步人工审核")
        else:
            report_lines.append("⚠️ 金额差异仍然较大，建议详细检查数据源")
        
        if self.stats['match_rate_after'] >= 99:
            report_lines.append("✅ 匹配率很高，数据质量良好")
        elif self.stats['match_rate_after'] >= 95:
            report_lines.append("✅ 匹配率良好，可以接受")
        else:
            report_lines.append("⚠️ 匹配率较低，建议检查Transaction格式")
        
        # 自动修正建议
        if self.stats['match_rate_after'] >= 98:
            suggested_threshold = 500.0
        elif self.stats['match_rate_after'] >= 95:
            suggested_threshold = 100.0
        else:
            suggested_threshold = 10.0
        
        report_lines.append(f"🔧 建议自动修正阈值: RM{suggested_threshold:.2f}")
        
        report_lines.append("")
        report_lines.append("📁 输出文件:")
        report_lines.append(f"修复后的数据: {output_file}")
        
        # 保存报告
        report_file = f"repair_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        # 显示报告
        print("\n" + '\n'.join(report_lines))
        print(f"\n📄 详细报告已保存: {report_file}")


def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("使用方法: python fix_user_data.py file1.xlsx file2.xlsx")
        print("示例: python fix_user_data.py data1.xlsx data2.xlsx")
        return
    
    file1_path = sys.argv[1]
    file2_path = sys.argv[2]
    
    # 检查文件是否存在
    if not os.path.exists(file1_path):
        print(f"❌ 文件不存在: {file1_path}")
        return
    
    if not os.path.exists(file2_path):
        print(f"❌ 文件不存在: {file2_path}")
        return
    
    # 创建修复器并执行修复
    fixer = DataFixer()
    success = fixer.fix_data(file1_path, file2_path)
    
    if success:
        print("\n🎉 数据修复完成！")
    else:
        print("\n❌ 数据修复失败！")


if __name__ == "__main__":
    main()
