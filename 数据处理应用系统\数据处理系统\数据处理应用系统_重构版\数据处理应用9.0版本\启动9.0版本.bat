@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    数据处理应用9.0版本 启动脚本
echo ========================================
echo.
echo 🚀 正在启动数据处理应用9.0版本...
echo.

cd /d "%~dp0"
python "数据处理应用9.0版本.py"

if %errorlevel% neq 0 (
    echo.
    echo ❌ 启动失败，错误代码: %errorlevel%
    echo.
    echo 💡 可能的解决方案：
    echo    1. 确保已安装Python
    echo    2. 确保已安装所需的Python包
    echo    3. 检查配置文件是否正确
    echo.
    pause
) else (
    echo.
    echo ✅ 应用已正常关闭
    echo.
)

pause
