#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接池
管理SQLite数据库连接
"""

import sqlite3
import threading
import queue
import os
import configparser
import logging
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class ConnectionPool:
    """SQLite连接池"""
    
    def __init__(self, db_path, max_connections=10):
        self.db_path = db_path
        self.max_connections = max_connections
        self.pool = queue.Queue(maxsize=max_connections)
        self.lock = threading.Lock()
        self.created_connections = 0
        
        # 预创建一些连接
        self._create_initial_connections()
    
    def _create_initial_connections(self):
        """创建初始连接"""
        try:
            for _ in range(min(3, self.max_connections)):
                conn = self._create_connection()
                if conn:
                    self.pool.put(conn)
        except Exception as e:
            logger.error(f"创建初始连接失败: {e}")
    
    def _create_connection(self):
        """创建新的数据库连接"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30)
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA foreign_keys=ON")
            conn.execute("PRAGMA synchronous=NORMAL")
            self.created_connections += 1
            return conn
        except Exception as e:
            logger.error(f"创建数据库连接失败: {e}")
            return None
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            # 尝试从池中获取连接
            conn = self.pool.get_nowait()
            # 测试连接是否有效
            conn.execute("SELECT 1")
            return conn
        except queue.Empty:
            # 池中没有连接，创建新连接
            if self.created_connections < self.max_connections:
                return self._create_connection()
            else:
                # 等待连接可用
                return self.pool.get(timeout=10)
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            return self._create_connection()
    
    def return_connection(self, conn):
        """归还数据库连接"""
        try:
            if conn and self.pool.qsize() < self.max_connections:
                self.pool.put_nowait(conn)
            else:
                conn.close()
                self.created_connections -= 1
        except Exception as e:
            logger.error(f"归还数据库连接失败: {e}")
            try:
                conn.close()
                self.created_connections -= 1
            except:
                pass

# 全局连接池实例
_connection_pool = None

def initialize_connection_pool(db_path=None):
    """初始化连接池"""
    global _connection_pool
    
    if db_path is None:
        # 从配置文件读取数据库路径
        config_paths = [
            "config.ini",
            os.path.join("数据处理应用系统", "数据处理系统", "数据处理应用系统_重构版", "03_配置文件", "config.ini"),
            os.path.join("数据处理应用系统", "数据处理系统", "数据处理应用系统_重构版", "01_主程序", "config.ini")
        ]
        
        db_path_found = False
        for config_path in config_paths:
            if os.path.exists(config_path):
                try:
                    config = configparser.ConfigParser()
                    config.read(config_path, encoding='utf-8')
                    if config.has_section('Database'):
                        db_path = config.get('Database', 'db_path', fallback=None)
                        if db_path:
                            logger.info(f"从配置文件读取数据库路径: {db_path}")
                            db_path_found = True
                            break
                except Exception as e:
                    logger.warning(f"读取配置文件失败 {config_path}: {e}")
        
        # 如果配置文件中没有找到路径，使用默认路径
        if not db_path_found:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(script_dir)
            db_path = os.path.join(project_root, "database", "sales_reports.db")
            logger.info(f"使用默认数据库路径: {db_path}")

        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            db_dir = os.path.dirname(db_path)
            if not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)
                logger.info(f"创建数据库目录: {db_dir}")
            logger.info(f"数据库文件不存在，将在首次使用时创建: {db_path}")
    
    _connection_pool = ConnectionPool(db_path)
    logger.info(f"连接池初始化完成: {db_path}")
    return _connection_pool

def get_connection_pool():
    """获取连接池实例"""
    global _connection_pool
    if _connection_pool is None:
        _connection_pool = initialize_connection_pool()
    return _connection_pool

@contextmanager
def get_connection():
    """获取数据库连接的上下文管理器"""
    pool = get_connection_pool()
    conn = pool.get_connection()
    try:
        yield conn
    finally:
        pool.return_connection(conn)
