# -*- coding: utf-8 -*-
"""
迁移系统测试脚本
测试PostgreSQL安装、管理和迁移功能
"""

import os
import sys
from pathlib import Path

def test_postgresql_setup():
    """测试PostgreSQL安装"""
    print("🧪 测试PostgreSQL安装...")
    
    try:
        from postgresql_setup import PostgreSQLPortableInstaller
        
        installer = PostgreSQLPortableInstaller()
        
        # 检查目录结构
        print("📁 检查目录结构...")
        installer.create_directory_structure()
        
        print("✅ PostgreSQL安装测试通过")
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL安装测试失败: {e}")
        return False

def test_postgresql_manager():
    """测试PostgreSQL管理"""
    print("\n🧪 测试PostgreSQL管理...")
    
    try:
        from postgresql_manager import PostgreSQLManager
        
        manager = PostgreSQLManager()
        
        # 检查状态
        print("📊 检查PostgreSQL状态...")
        status = manager.get_status()
        print(f"  安装状态: {status['installed']}")
        print(f"  运行状态: {status['running']}")
        print(f"  连接状态: {status['connection']}")
        
        print("✅ PostgreSQL管理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL管理测试失败: {e}")
        return False

def test_migration_config():
    """测试迁移配置"""
    print("\n🧪 测试迁移配置...")
    
    try:
        import json
        
        config_file = Path("migration_config.json")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("📋 迁移配置:")
            for key, value in config.items():
                if isinstance(value, dict):
                    print(f"  {key}: {len(value)} 项配置")
                else:
                    print(f"  {key}: {value}")
            
            print("✅ 迁移配置测试通过")
            return True
        else:
            print("❌ 迁移配置文件不存在")
            return False
        
    except Exception as e:
        print(f"❌ 迁移配置测试失败: {e}")
        return False

def test_migration_system():
    """测试迁移系统"""
    print("\n🧪 测试迁移系统...")
    
    try:
        from intelligent_migration import IntelligentMigrator
        
        # 使用示例数据库路径
        sqlite_db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
        
        if not os.path.exists(sqlite_db_path):
            print(f"⚠️ SQLite数据库不存在: {sqlite_db_path}")
            print("   使用测试模式...")
            sqlite_db_path = ":memory:"  # 内存数据库用于测试
        
        migrator = IntelligentMigrator(sqlite_db_path)
        
        print("📋 迁移配置:")
        for key, value in migrator.migration_config.items():
            print(f"  {key}: {value}")
        
        print("📊 支持的表:")
        for table in migrator.supported_tables:
            print(f"  - {table}")
        
        print("✅ 迁移系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 迁移系统测试失败: {e}")
        return False

def test_ui_system():
    """测试UI系统"""
    print("\n🧪 测试UI系统...")
    
    try:
        # 只导入不运行UI
        import migration_ui
        
        print("✅ UI系统导入成功")
        print("💡 运行 'python migration_ui.py' 启动图形界面")
        return True
        
    except Exception as e:
        print(f"❌ UI系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试迁移系统...")
    print("=" * 60)
    
    tests = [
        ("PostgreSQL安装", test_postgresql_setup),
        ("PostgreSQL管理", test_postgresql_manager),
        ("迁移配置", test_migration_config),
        ("迁移系统", test_migration_system),
        ("UI系统", test_ui_system)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常 {test_name}: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        print("\n📋 下一步操作:")
        print("1. 运行 'python postgresql_setup.py' 安装PostgreSQL")
        print("2. 运行 'python postgresql_manager.py start' 启动PostgreSQL")
        print("3. 运行 'python migration_ui.py' 启动图形界面")
        print("4. 或运行 'python intelligent_migration.py --help' 查看命令行选项")
    else:
        print("⚠️ 部分测试失败，请检查错误信息并修复问题。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
