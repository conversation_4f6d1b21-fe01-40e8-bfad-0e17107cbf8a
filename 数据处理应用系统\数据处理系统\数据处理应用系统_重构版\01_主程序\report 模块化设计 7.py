"""
 -  7.0
Transaction Num +  + 

7.0
2025
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime, timedelta
import os
import warnings
import argparse
import sys
import io
import codecs
from typing import Dict, List, Tuple, Optional, Any
import time
from tqdm import tqdm  # 

# 
LOG_FILE_PATH = None

def setup_log_file(file1_path):
    """"""
    global LOG_FILE_PATH
    # 
    file1_dir = os.path.dirname(file1_path)
    log_dir = os.path.join(file1_dir, "")
    os.makedirs(log_dir, exist_ok=True)

    # 
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"_{timestamp}.txt"
    LOG_FILE_PATH = os.path.join(log_dir, log_filename)

    # 
    with open(LOG_FILE_PATH, 'w', encoding='utf-8') as f:
        f.write(f" - \n")
        f.write(f": {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f": {os.path.basename(file1_path)}\n")
        f.write("="*60 + "\n\n")

    return LOG_FILE_PATH

def log_to_file(message, level="INFO"):
    """"""
    global LOG_FILE_PATH
    if LOG_FILE_PATH:
        timestamp = datetime.now().strftime("%H:%M:%S")
        with open(LOG_FILE_PATH, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {level}: {message}\n")

def print_and_log(message, level="INFO"):
    """"""
    print(message)
    log_to_file(message, level)

# ============================================
class ProgressManager:
    """ - """

    def __init__(self, total_items: int, description: str = ""):
        self.total_items = total_items
        self.description = description
        self.current_item = 0
        self.start_time = time.time()
        self.pbar = None

    def start(self):
        """"""
        self.pbar = tqdm(total=self.total_items, desc=self.description,
                        unit="", ncols=100, colour='green')
        return self

    def update(self, n: int = 1):
        """"""
        if self.pbar:
            self.pbar.update(n)
        self.current_item += n

    def set_description(self, desc: str):
        """"""
        if self.pbar:
            self.pbar.set_description(desc)

    def close(self):
        """"""
        if self.pbar:
            self.pbar.close()
        elapsed = time.time() - self.start_time
        print(f"[] : {elapsed:.2f}")

class DateGroupProcessor:
    """ - """

    def __init__(self, df1_filtered: pd.DataFrame):
        self.df1_filtered = df1_filtered
        self.date_groups = self._create_date_groups()

    def _create_date_groups(self) -> Dict[str, pd.DataFrame]:
        """"""
        groups = {}
        if not self.df1_filtered.empty:
            # 
            for date, group in self.df1_filtered.groupby(self.df1_filtered['DateTime'].dt.date):
                date_str = date.strftime('%Y-%m-%d')
                groups[date_str] = group.reset_index(drop=True)
        return groups

    def get_date_groups(self) -> Dict[str, pd.DataFrame]:
        """"""
        return self.date_groups

    def get_dates(self) -> List[str]:
        """"""
        return sorted(self.date_groups.keys())

    def get_group_by_date(self, date_str: str) -> pd.DataFrame:
        """"""
        return self.date_groups.get(date_str, pd.DataFrame())

    def get_total_records(self) -> int:
        """"""
        return sum(len(group) for group in self.date_groups.values())

class MatchingModeManager:
    """ - Transaction ID"""

    def __init__(self):
        self.current_mode = None
        self.mode_stats = {
            'transaction_id': {'processed': 0, 'matched': 0, 'inserted': 0},
            'traditional': {'processed': 0, 'matched': 0, 'inserted': 0}
        }

    def set_mode(self, mode: str):
        """"""
        if mode not in ['transaction_id', 'traditional']:
            raise ValueError(f": {mode}")
        self.current_mode = mode
        print(f"[] : {mode}")

    def get_mode(self) -> str:
        """"""
        return self.current_mode

    def update_stats(self, action: str):
        """"""
        if self.current_mode and action in self.mode_stats[self.current_mode]:
            self.mode_stats[self.current_mode][action] += 1


    def get_stats(self) -> Dict:
        """"""
        return self.mode_stats.copy()

    def print_stats(self):
        """"""
        if self.current_mode:
            stats = self.mode_stats[self.current_mode]
            print(f"[] {self.current_mode} :")
            print(f"   : {stats['processed']} ")
            print(f"   : {stats['matched']} ")
            print(f"   : {stats['inserted']} ")

            # [] transaction_id
            if self.current_mode == "transaction_id" and hasattr(self, 'processor'):
                actual_matched_count = len(self.processor.matched_indices) if hasattr(self.processor, 'matched_indices') else 0
                print(f"[]  - : {actual_matched_count}")
                print(f"[]  - : {stats['matched']}")
                if actual_matched_count != stats['matched']:
                    print(f"[] : {stats['matched'] - actual_matched_count}")
                else:
                    print(f"[] ")

# ============================================
class Config:
    """"""
    # 
    BASE_DIR = r"C:/Users/<USER>/Desktop/Day report 3\IOT"
    DEFAULT_FILE1_NAME = "SETTLEMENT_REPORT_03062025_IOT.xlsx"
    DEFAULT_FILE2_NAME = "030625 CHINA IOT.xlsx"
    DEFAULT_SHEET_NAME = "TRANSACTION_LIST"

    # 
    OUTPUT_DATA_SHEET = "DATA"
    OUTPUT_LOG_SHEET = "LOG"

    # 
    REQUIRED_COLUMNS_FILE1 = ["Date", "Transaction ID", "Order ID", "Bill Amt", "Status"]
    REQUIRED_COLUMNS_FILE2 = ["Order price", "Order status", "Order time"]
    EXPECTED_COLUMNS_COUNT = 27

    # 
    TIME_THRESHOLDS = [10, 30, 180, 300, 600, 1800, 3600, 10800]  # 
    DEFAULT_TIME = "00:00:00"
    PRICE_TOLERANCE = 1e-2  # 

    # 
    ORDER_TYPES = {
        "9_digit": "Offline order",
        "over_9": "Normal order",
        "anomaly": "Anomaly order"
    }

    # [] 
    SETTLED_STATUS = "settled"
    FINISH_STATUS_LOWER = "finish"  # 
    FINISH_STATUS_DISPLAY = "Finish"  # 

class ColumnMapping:
    """"""
    FILE2_COLUMN_MAPPING = {
        "order price": "Order price",
        "orderprice": "Order price",
        "order status": "Order status",
        "orderstatus": "Order status",
        "order time": "Order time",
        "ordertime": "Order time",
        "equipment id": "Equipment ID",
        "equipmentid": "Equipment ID",
        "order no.": "Order No.",
        "orderno": "Order No."
    }

# ============================================
def exclude_api_orders(df):
    """DataFrameAPI order

    Args:
        df (pandas.DataFrame): DataFrame

    Returns:
        pandas.DataFrame: API orderDataFrame
    """
    # Order types
    if "Order types" not in df.columns:
        # Order typesDataFrame
        return df

    # Order typesAPI
    return df[~df["Order types"].str.strip().str.lower().str.contains("api", na=False)]

def check_order_id(oid):
    """ID

    Args:
        oid: ID

    Returns:
        str: ID ("9_digit", "over_9", "anomaly", "other")
    """
    oid_str = str(oid).replace(" ", "")
    if re.search(r"[A-Za-z]", oid_str):
        return "anomaly"
    digits = re.sub(r"\D", "", oid_str)
    if len(digits) == 9:
        return "9_digit"
    elif len(digits) > 9:
        return "over_9"
    else:
        return "other"

def extract_date_from_filename(filename):
    """

    Args:
        filename (str): 

    Returns:
        datetime.date or None: 
    """
    # 6 (DDMMYY)
    match = re.search(r'(\d{6})', os.path.basename(filename))
    if match:
        date_str = match.group(1)
        # DDMMYY
        try:
            day = int(date_str[:2])
            month = int(date_str[2:4])
            year = int(date_str[4:6]) + 2000  # 21
            return datetime(year, month, day).date()
        except ValueError:
            return None
    return None

# ============================================
def initialize_system():
    """"""
    # Windows
    if sys.platform == 'win32':
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

    #  openpyxl 
    warnings.simplefilter("ignore", category=UserWarning)

def parse_command_line_args():
    """"""
    parser = argparse.ArgumentParser(description="SETTLEMENTCHINA")
    parser.add_argument("--file1", help="SETTLEMENT")
    parser.add_argument("--file2", help="CHINA")
    parser.add_argument("--sheet_name", help="SheetTRANSACTION_LIST")
    return parser.parse_args()

def setup_file_paths(args):
    """

    Args:
        args: 

    Returns:
        dict: 
    """
    file_config = {
        'base_dir': Config.BASE_DIR,
        'file1_name': Config.DEFAULT_FILE1_NAME,
        'file2_name': Config.DEFAULT_FILE2_NAME,
        'sheet_name': Config.DEFAULT_SHEET_NAME
    }

    # 
    file_config['file1_path'] = args.file1 if args.file1 else os.path.join(file_config['base_dir'], file_config['file1_name'])
    file_config['file2_path'] = args.file2 if args.file2 else os.path.join(file_config['base_dir'], file_config['file2_name'])

    # 
    file_config['output_file_path'] = file_config['file2_path']  # 
    file_config['output_data_sheet'] = Config.OUTPUT_DATA_SHEET
    file_config['output_log_sheet'] = Config.OUTPUT_LOG_SHEET

    return file_config

# ============================================
def main_processing():
    """"""
    # 
    initialize_system()

    # 
    args = parse_command_line_args()

    # 
    file_config = setup_file_paths(args)

    # 
    log_file_path = setup_log_file(file_config['file1_path'])

    # 
    print_and_log(f": {file_config['file1_path']}")
    print_and_log(f": {file_config['file2_path']}")
    print_and_log(f": {log_file_path}")

    return args, file_config, log_file_path

# [] 
# 
if len(sys.argv) == 1:  # 
    # 
    initialize_system()

    # 
    args = parse_command_line_args()

    # 
    file_config = setup_file_paths(args)

    # 
    log_file_path = setup_log_file(file_config['file1_path'])

    # 
    print_and_log(f": {file_config['file1_path']}")
    print_and_log(f": {file_config['file2_path']}")
    print_and_log(f": {log_file_path}")
else:
    # 
    print("")
    # None
    args = None
    file_config = None
    log_file_path = None

def detect_sheet_name(file_path, args, default_sheet_name):
    """sheet

    Args:
        file_path (str): Excel
        args: 
        default_sheet_name (str): sheet

    Returns:
        str: sheet
    """
    try:
        xls = pd.ExcelFile(file_path)
        available_sheets = xls.sheet_names
        print(f" Sheet: {available_sheets}")

        # sheet_name
        if args.sheet_name:
            sheet_name = args.sheet_name
            if sheet_name not in available_sheets:
                print(f": Sheet '{sheet_name}' Sheet: {available_sheets}")
                print(f"Sheet: {available_sheets[0]}")
                sheet_name = available_sheets[0]
        # sheet
        else:
            # 
            sheet_name = None

            # 1. 
            if default_sheet_name in available_sheets:
                sheet_name = default_sheet_name
                print(f"[] Sheet: {sheet_name}")

            # 2. 6 (200625)
            elif not sheet_name:
                for sheet in available_sheets:
                    if sheet.isdigit() and len(sheet) == 6:
                        sheet_name = sheet
                        print(f"[] Sheet: {sheet_name}")
                        break

            # 3. TRANSACTIONsheet
            elif not sheet_name:
                for sheet in available_sheets:
                    if 'TRANSACTION' in sheet.upper():
                        sheet_name = sheet
                        print(f"[] Sheet: {sheet_name}")
                        break

            # 4. sheet
            if not sheet_name:
                sheet_name = available_sheets[0]
                print(f"[] Sheet: {sheet_name}")

        print(f"[] sheet: {sheet_name}")
        return sheet_name
    except Exception as e:
        print(f"[] Excel: {str(e)}")
        print("[] sheet")
        return "Sheet1"  # sheet

# [] sheet_name
if file_config is not None:
    sheet_name = detect_sheet_name(file_config['file1_path'], args, file_config['sheet_name'])
else:
    sheet_name = None  # 

# ============================================
def load_and_validate_file1(file_path, sheet_name):
    """ - 

    Args:
        file_path (str): 
        sheet_name (str): sheet

    Returns:
        tuple: (pandas.DataFrame, bool) - Time
    """
    # [] 
    df1 = safe_read_excel(file_path, sheet_name=sheet_name, header=0, engine="openpyxl")
    print("", df1.columns.tolist())
    print(f"{df1.shape[1]}")

    #  - Time
    basic_required_columns = ["Date", "Transaction ID", "Order ID", "Bill Amt", "Status"]
    missing_columns = [col for col in basic_required_columns if col not in df1.columns]
    if missing_columns:
        raise ValueError(f": {missing_columns}")

    # Time
    has_time_column = "Time" in df1.columns
    if has_time_column:
        print("[] Time")
    else:
        print("[] Date")

    # 
    if df1.shape[1] != Config.EXPECTED_COLUMNS_COUNT:
        print(f" {df1.shape[1]}{Config.EXPECTED_COLUMNS_COUNT}")

    return df1, has_time_column

# [] 
if file_config is not None and sheet_name is not None:
    df1, has_time_column = load_and_validate_file1(file_config['file1_path'], sheet_name)
else:
    df1, has_time_column = None, None  # 

# [] 
if df1 is not None:
    # Transaction ID
    if "Transaction ID" not in df1.columns:
        print("'Transaction ID'Transaction ID")
        df1["Transaction ID"] = ""
    else:
        print("'Transaction ID'")
        # Transaction ID
        df1["Transaction ID"] = df1["Transaction ID"].astype(str).apply(lambda x: x.strip())
else:
    # 
    print("[] ")

# ============================================
def process_datetime_columns(df1, has_time_column):
    """ - /Time

    Args:
        df1 (pandas.DataFrame): 
        has_time_column (bool): Time

    Returns:
        pandas.DataFrame: 
    """
    if has_time_column:
        print("[] ...")
        # Date
        if pd.api.types.is_numeric_dtype(df1["Date"]):
            df1["Date"] = pd.to_datetime(df1["Date"], unit='d', origin='1899-12-30', errors="coerce")
        else:
            df1["Date"] = pd.to_datetime(df1["Date"], errors="coerce")

        # Time
        if pd.api.types.is_datetime64_any_dtype(df1["Time"]):
            df1["Time"] = df1["Time"].dt.strftime("%H:%M:%S")
        else:
            df1["Time"] = df1["Time"].astype(str)

        # DateTimeDateTime
        df1["DateTime_str"] = df1["Date"].dt.strftime("%m/%d/%Y") + " " + df1["Time"].astype(str)
        df1["DateTime"] = pd.to_datetime(df1["DateTime_str"], format="%m/%d/%Y %H:%M:%S", errors="coerce")

        if df1["DateTime"].isnull().any():
            failed = df1[df1["DateTime"].isnull()]
            print(f"[] {len(failed)}")
            df1.loc[df1["DateTime"].isnull(), "DateTime"] = df1.loc[df1["DateTime"].isnull(), "Date"]
    else:
        print("[] ...")
        #  Date  datetime Date  "3/21/2025 12:08:10 AM"
        df1["DateTime"] = pd.to_datetime(df1["Date"], errors="coerce")
        if df1["DateTime"].isnull().any():
            failed = df1[df1["DateTime"].isnull()]
            print(f"[] {len(failed)}")
            print("", failed["Date"].head(3).tolist())
            df1 = df1[df1["DateTime"].notnull()].reset_index(drop=True)

    # 24
    df1["Time24"] = df1["DateTime"].dt.strftime("%H:%M:%S")
    return df1

def validate_file_dates(df1, file2_path):
    """ - 

    Args:
        df1 (pandas.DataFrame): 
        file2_path (str): 
    """
    try:
        # 
        file2_date = extract_date_from_filename(file2_path)

        # 
        # [] KeyError
        if not df1.empty and "Date" in df1.columns and pd.notnull(df1["Date"].iloc[0]):
            file1_dates = df1["DateTime"].dt.date.unique()

            # 
            if file2_date is not None and len(file1_dates) > 0:
                if file2_date not in file1_dates:
                    print(f"[] : !")
                    print(f": {[d.strftime('%Y-%m-%d') for d in file1_dates]}")
                    print(f": {file2_date.strftime('%Y-%m-%d')}")
                    print("[] ")
                    # 
                else:
                    print(f" : {file2_date.strftime('%Y-%m-%d')}")
            else:
                print(" : ")
        else:
            print(" : ")
    except Exception as e:
        print(f"[] : {str(e)}")
        print("")

# [] 
if df1 is not None and file_config is not None:
    # 
    df1 = process_datetime_columns(df1, has_time_column)

    # 
    validate_file_dates(df1, file_config['file2_path'])

# ============================================
def process_file1_filtering(df1):
    """

    Args:
        df1 (pandas.DataFrame): 

    Returns:
        tuple: (df1_filtered, total_bill_amt, freq_bill_amt, nine_digit_ids_count)
    """
    # [] 
    try:
        # 
        required_columns = ["Status", "Bill Amt"]
        missing_columns = [col for col in required_columns if col not in df1.columns]
        if missing_columns:
            raise KeyError(f": {missing_columns}")

        # [] tuple
        # DataFrame
        df1["Status"] = df1["Status"].astype(str)

        # 
        try:
            settled_status = str(Config.SETTLED_STATUS).lower()  # 
            status_mask = df1["Status"].str.strip().str.lower().str.contains(settled_status, na=False)
            df1_filtered = df1[status_mask].copy()
        except Exception as e:
            print(f"[] : {e}")
            # 
            try:
                df1_filtered = df1[df1["Status"].str.lower().str.contains("settled", na=False)].copy()
            except Exception as e2:
                print(f"[] : {e2}")
                return pd.DataFrame(), 0.0, {}, {}
        if df1_filtered.empty:
            print(" Status ")
            # 
            return pd.DataFrame(), 0.0, {}, {}

        # [] 
        try:
            # Bill Amt
            if "Bill Amt" not in df1_filtered.columns:
                print("[]  'Bill Amt' ")
                return pd.DataFrame(), 0.0, {}, {}

            # 
            # DataFrame
            df1_filtered["Bill Amt"] = pd.to_numeric(df1_filtered["Bill Amt"], errors="coerce")

            # NaN
            bill_amt_series = df1_filtered["Bill Amt"].fillna(0)
            total_bill_amt = float(bill_amt_series.sum())  # float

            # NaN
            valid_amounts = bill_amt_series[bill_amt_series.notna()]
            if len(valid_amounts) > 0:
                freq_bill_amt = valid_amounts.round(2).value_counts().to_dict()
            else:
                freq_bill_amt = {}

        except Exception as e:
            print(f"[] : {e}")
            return pd.DataFrame(), 0.0, {}, {}

    except Exception as e:
        print(f"[] : {e}")
        # 
        return pd.DataFrame(), 0.0, {}, {}

    # [] 
    try:
        # 
        if "Order ID" not in df1_filtered.columns:
            raise KeyError(" 'Order ID' ")

        #  Order ID
        # DataFrame
        df1_filtered["Order ID"] = df1_filtered["Order ID"].astype(str).apply(lambda x: str(x).replace(" ", "") if pd.notna(x) else "")
        df1_filtered["OrderID_Type"] = df1_filtered["Order ID"].apply(check_order_id)

    except Exception as e:
        print(f"[] Order ID: {e}")
        # 
        if "OrderID_Type" not in df1_filtered.columns:
            df1_filtered["OrderID_Type"] = "other"

    # [] 9ID
    try:
        # 9ID - 
        nine_digit_df = df1_filtered[df1_filtered["OrderID_Type"] == "9_digit"]
        nine_digit_count = nine_digit_df.shape[0]
        print(f"9ID{nine_digit_count}")

        # value_counts()iterrows()
        if not nine_digit_df.empty and "Order ID" in nine_digit_df.columns:
            nine_digit_ids_count = nine_digit_df["Order ID"].value_counts().to_dict()
        else:
            nine_digit_ids_count = {}

    except Exception as e:
        print(f"[] 9ID: {e}")
        nine_digit_ids_count = {}

    # [] 
    try:
        # 
        if not isinstance(df1_filtered, pd.DataFrame):
            raise TypeError(f"df1_filteredDataFrame{type(df1_filtered)}")
        if not isinstance(total_bill_amt, (int, float)):
            raise TypeError(f"total_bill_amt{type(total_bill_amt)}")
        if not isinstance(freq_bill_amt, dict):
            raise TypeError(f"freq_bill_amtdict{type(freq_bill_amt)}")
        if not isinstance(nine_digit_ids_count, dict):
            raise TypeError(f"nine_digit_ids_countdict{type(nine_digit_ids_count)}")

        print(f"[] process_file1_filtering: DataFrame({len(df1_filtered)}), ({total_bill_amt}), ({len(freq_bill_amt)}), ({len(nine_digit_ids_count)})")
        return df1_filtered, total_bill_amt, freq_bill_amt, nine_digit_ids_count

    except Exception as e:
        print(f"[] process_file1_filtering: {e}")
        # 
        return pd.DataFrame(), 0.0, {}, {}

# (, Order ID, )
note_logs = []

# [] globals()
deleted_transaction_nums = set()

# [修复] 条件执行第一文件处理
if df1 is not None:
    df1_filtered, total_bill_amt, freq_bill_amt, nine_digit_ids_count = process_file1_filtering(df1)
    anomaly_records = df1_filtered[df1_filtered["OrderID_Type"] == "anomaly"]
else:
    # 命令行模式，变量将在后面设置
    df1_filtered = None
    total_bill_amt = 0.0
    freq_bill_amt = {}
    nine_digit_ids_count = {}
    anomaly_records = pd.DataFrame()

# ======================Transaction ID======================
def validate_transaction_id_consistency(df1_filtered):
    """Transaction ID"""
    print("Transaction ID...")

    valid_trans_ids = df1_filtered[df1_filtered["Transaction ID"].notna() &
                                  (df1_filtered["Transaction ID"].astype(str).str.strip() != "") &
                                  (df1_filtered["Transaction ID"].astype(str).str.lower() != "nan")]

    if not valid_trans_ids.empty:
        trans_id_groups = valid_trans_ids.groupby("Transaction ID")
        duplicates_found = False
        consistency_errors = []

        for trans_id, group in trans_id_groups:
            if len(group) > 1:  # 
                duplicates_found = True
                # 
                amounts = group["Bill Amt"].unique()
                datetimes = group["DateTime"].unique()

                if len(amounts) > 1 or len(datetimes) > 1:
                    error_msg = f" Transaction ID {trans_id} {amounts.tolist()}{[dt.strftime('%Y-%m-%d %H:%M:%S') for dt in datetimes]}"
                    consistency_errors.append(error_msg)
                    print(error_msg)
                else:
                    print(f"[] Transaction ID {trans_id}  ({len(group)})")

        if consistency_errors:
            print(f"[]  {len(consistency_errors)} Transaction ID")
            for error in consistency_errors:
                note_logs.append((0, "", error))
            return False
        elif duplicates_found:
            print("[] Transaction ID")
        else:
            print("[] Transaction ID")
    else:
        print("[] Transaction ID")

    return True

# [] Transaction ID
if df1 is not None:
    # Transaction ID
    if not validate_transaction_id_consistency(df1_filtered):
        print("[] Transaction ID")
        print("")
        # [] 

# process_file1_filtering

# ============================================
def load_and_process_file2(file_path):
    """

    Args:
        file_path (str): 

    Returns:
        pandas.DataFrame: 
    """
    # [] 
    df2 = safe_read_excel(file_path, engine="openpyxl")
    df2.columns = [col.strip() for col in df2.columns]

    # 
    mapping = {}
    for col in df2.columns:
        cl = col.strip().lower()
        if cl in ColumnMapping.FILE2_COLUMN_MAPPING:
            mapping[col] = ColumnMapping.FILE2_COLUMN_MAPPING[cl]

    df2.rename(columns=mapping, inplace=True)
    print("", df2.columns.tolist())

    # 
    for required_col in Config.REQUIRED_COLUMNS_FILE2:
        if required_col not in df2.columns:
            raise KeyError(f" {required_col}")

    # 
    df2["Order price"] = pd.to_numeric(df2["Order price"], errors="coerce")
    if "Payment" in df2.columns:
        df2["Payment"] = pd.to_numeric(df2["Payment"], errors="coerce")
    df2["Order time"] = pd.to_datetime(df2["Order time"], errors="coerce")
    df2["Time"] = df2["Order time"].dt.strftime("%H:%M:%S")
    df2["Order status"] = df2["Order status"].astype(str)

    return df2

# [] 
if file_config is not None:
    # 
    df2_original = load_and_process_file2(file_config['file2_path'])
    df2 = df2_original.copy()  # 
    # API order
    df2_finish = df2[(df2["Order status"].str.strip().str.lower() == "finish") &
                   (~df2["Order types"].str.strip().str.lower().str.contains("api", na=False))].copy()
    freq_order_price = df2_finish["Order price"].round(2).value_counts().to_dict()
else:
    # 
    df2_original = None
    df2 = None
    df2_finish = None
    freq_order_price = {}
# [] 
if df2 is not None:
    for col in ["Equipment ID", "Order No."]:
        if col in df2.columns:
            # [] 
            df2[col] = df2[col].apply(lambda x: safe_string_operation(x, lambda s: s.replace(" ", "")))
    for col in ["Equipment name", "Branch name"]:
        if col not in df2.columns:
            df2[col] = ""

    # Order types
    if "Order types" not in df2.columns:
        df2["Order types"] = ""

# [修复] 条件执行备份数据处理
if df2 is not None:
    df2_backup_for_comparison = df2.copy()  # [] df2_original
    # API order
    df2_backup_finish = df2_backup_for_comparison[(df2_backup_for_comparison["Order status"].str.strip().str.lower() == "finish") &
                                     (~df2_backup_for_comparison["Order types"].str.strip().str.lower().str.contains("api", na=False))]
    original_total = df2_backup_finish["Order price"].sum()
    original_freq = df2_backup_finish["Order price"].round(2).value_counts().to_dict()
else:
    # 命令行模式，变量将在后面设置
    df2_backup_for_comparison = None
    df2_backup_finish = pd.DataFrame()
    original_total = 0.0
    original_freq = {}

# [修复] 条件执行9位ID处理
if df2 is not None:
    # 9ID -
    #
    nine_digit_mask = df2["Equipment ID"].apply(lambda x: len(re.sub(r"\D", "", str(x))) == 9 and not re.search(r"[A-Za-z]", str(x)))
    finish_mask = df2["Order status"].str.strip().str.lower() == "finish"
    non_api_mask = ~df2["Order types"].astype(str).str.strip().str.lower().str.contains("api", na=False)
else:
    # 命令行模式，变量将在后面设置
    nine_digit_mask = pd.Series(dtype=bool)
    finish_mask = pd.Series(dtype=bool)
    non_api_mask = pd.Series(dtype=bool)

# [修复] 条件执行有效记录处理
if df2 is not None:
    #
    valid_records = df2[nine_digit_mask & finish_mask & non_api_mask]
    # value_counts()iterrows()
    second_nine_digit_ids_count = valid_records["Equipment ID"].value_counts().to_dict()
else:
    # 命令行模式，变量将在后面设置
    valid_records = pd.DataFrame()
    second_nine_digit_ids_count = {}

# ----------------------------------------------
df2_backup = df2.copy()

# ----------------------------------------------
matched_indices_second = set()
processed_9digit_ids = {}

# 9ID
for _, row in df1_filtered[df1_filtered["OrderID_Type"] == "9_digit"].iterrows():
    oid = row["Order ID"]
    processed_9digit_ids[oid] = processed_9digit_ids.get(oid, 0) + 1

# 
if "Matched_Flag" not in df2.columns:
    df2["Matched_Flag"] = False

# ----------------------------------------------
def check_conflict(oid, phase):
    if phase == "over_9":
        matches = df2[df2["Order No."] == oid]
        if any(i in matched_indices_second for i in matches.index):
            # Transaction IDadd_log
            add_log((0, oid, f"Conflict detected! Order No. {oid} was matched by 9-digit ID"))
            return True
    return False

# ----------------------------------------------
if "Matched Order ID" not in df2.columns:
    df2["Matched Order ID"] = ""

# Transaction IDTransaction ID
if "Transaction ID" not in df2.columns:
    df2["Transaction ID"] = ""

# [] Transaction
print("[] Transaction...")

# Transaction
if "Transaction_Num" in df2.columns and "Transaction Num" in df2.columns:
    print("[] Transaction...")
    # "Transaction Num""Transaction_Num"
    for idx, row in df2.iterrows():
        trans_num_space = str(row.get("Transaction Num", "")).strip()
        trans_num_underscore = str(row.get("Transaction_Num", "")).strip()

        # 
        if (not trans_num_space or trans_num_space in ["nan", "None", "none"]) and \
           (trans_num_underscore and trans_num_underscore not in ["nan", "None", "none"]):
            df2.at[idx, "Transaction Num"] = trans_num_underscore

    # 
    df2.drop(columns=["Transaction_Num"], inplace=True)
    print("[] Transaction'Transaction Num'")
elif "Transaction_Num" in df2.columns and "Transaction Num" not in df2.columns:
    # 
    df2.rename(columns={"Transaction_Num": "Transaction Num"}, inplace=True)
    print("[] 'Transaction_Num''Transaction Num'")

# Transaction Num
has_transaction_column = "Transaction Num" in df2.columns or "Transaction ID" in df2.columns

if not has_transaction_column:
    # Transaction
    df2["Transaction Num"] = ""
    print("[] Transaction Num")

# ============================================
class TransactionIDMatcher:
    """Transaction ID - Transaction ID"""

    def __init__(self, df2: pd.DataFrame, df2_backup: pd.DataFrame, matched_indices: set):
        self.df2 = df2
        self.df2_backup = df2_backup
        self.matched_indices = matched_indices

    def match_by_transaction_id(self, trans_id: str, oid: str, amt: float,
                               dt1: datetime, phase: str, order_type: str, t_val: str) -> Tuple[bool, Optional[int]]:
        """
        Transaction ID

        Returns:
            Tuple[bool, Optional[int]]: (, )
        """
        if not trans_id or str(trans_id).strip() == "" or str(trans_id).strip().lower() == "nan":
            return False, None

        # [] safe_clean_transaction_id
        trans_id_clean = safe_clean_transaction_id(trans_id)
        if not trans_id_clean:
            return False, None

        # [] 
        # DataFrame
        df2_trans_num_cleaned = self.df2["Transaction Num"].apply(safe_clean_transaction_id)

        # [] 
        trans_matches_mask = (df2_trans_num_cleaned == trans_id_clean) & \
                            (self.df2.index.map(lambda x: x not in self.matched_indices))
        trans_matches = self.df2[trans_matches_mask]

        if trans_matches.empty:
            # 
            # print(f"[] Transaction ID {trans_id_clean} ")
            return False, None

        # [] Transaction ID
        # 3.0
        # [] 
        has_matches = len(trans_matches) > 0

        # []  - 
        # if len(self.matched_indices) < 50:  # 50
        #     print(f"[] Transaction ID {trans_id_clean} {len(trans_matches)} ")
        #     # [] RM5.00
        #     if abs(amt - 5.0) < 0.01:
        #         print(f"[] RM5.00: Transaction ID={trans_id_clean}, Order ID={oid}, ={len(trans_matches)}")
        #         for idx, match_row in trans_matches.iterrows():
        #             print(f"   - {idx}: ={match_row.get('Order price')}, ={match_row.get('Order status')}")

        # [] 
        success_count = 0
        failure_count = 0

        for m_idx, m_row in trans_matches.iterrows():
            # [] 
            try:
                # 
                if m_idx not in self.df2.index:
                    print(f"  {m_idx} DataFrame")
                    continue

                # loc
                self.df2.loc[m_idx, "Matched_Flag"] = True

                # 
                immediate_flag_check = self.df2.loc[m_idx, "Matched_Flag"]
                if immediate_flag_check != True:
                    print(f"  {m_idx} : {immediate_flag_check}, : {type(immediate_flag_check)}")
                    # 
                    self.df2.loc[m_idx, "Matched_Flag"] = bool(True)
                    recheck = self.df2.loc[m_idx, "Matched_Flag"]
                    print(f"[] : {recheck}, : {type(recheck)}")

                self.matched_indices.add(m_idx)

                # 
                success = self._update_matched_record(m_idx, m_row, oid, amt, dt1, phase, order_type, trans_id, t_val)

                # [] RM5.00
                if abs(amt - 5.0) < 0.01 and len(self.matched_indices) < 50:
                    print(f"[] RM5.00: {m_idx}, ={success}, Transaction ID={trans_id_clean}")

                # [] 
                post_update_flag_check = self.df2.loc[m_idx, "Matched_Flag"]
                if post_update_flag_check != True:
                    print(f"  {m_idx} : {post_update_flag_check}")
                    # 
                    self.df2.loc[m_idx, "Matched_Flag"] = True

                # [] 
                # Transaction ID
                if success:
                    success_count += 1
                    # [] 50
                    if len(self.matched_indices) <= 50:
                        flag_value = self.df2.loc[m_idx, "Matched_Flag"]
                        print(f"[]  {m_idx} : Matched_Flag = {flag_value}")
                else:
                    failure_count += 1
                    # [] 50
                    if len(self.matched_indices) <= 50:
                        print(f"[]  {m_idx} ")

                # [] 
                final_flag_check = self.df2.loc[m_idx, "Matched_Flag"]
                if final_flag_check != True:
                    print(f"  {m_idx} : {final_flag_check}")
                    # 
                    self.df2.loc[m_idx, "Matched_Flag"] = True

            except Exception as e:
                print(f"  {m_idx} : {e}")
                continue

        # [] - 
        # if len(self.matched_indices) <= 100:  # 100
        #     print(f"[] Transaction ID {trans_id_clean} :  {success_count},  {failure_count}")

        # [] 
        if len(self.matched_indices) % 1000 == 0:  # 1000
            progress_percent = min(100, int((len(self.matched_indices) / len(self.df1)) * 100))
            print(f"[{progress_percent}%] Transaction ID: {len(self.matched_indices)} ")

        # [] 
        if has_matches and success_count > 0:
            # 
            # 
            # print(f"[] Transaction ID {trans_id_clean} :  {len(trans_matches)}  {success_count} ")
            return True, trans_matches.index[0]  # 
        else:
            # print(f"[] Transaction ID {trans_id_clean} : ")
            return False, None

    def _calculate_match_score(self, row: pd.Series, oid: str, amt: float, dt1: datetime, phase: str) -> int:
        """
         - Transaction ID

        
        - : +10
        - Order ID: +10
        - : +10
        - Finish: +5
        - : +1

        Returns:
            int: 
        """
        score = 1  # 

        try:
            #  (+10)
            if pd.notnull(row.get("Order price")) and abs(row["Order price"] - amt) <= 1e-2:
                score += 10

            # Order ID (+10)
            if phase == "9_digit":
                # 9IDEquipment ID
                current_eq_id = str(row.get("Equipment ID", "")).strip()
                if current_eq_id == oid:
                    score += 10
            elif phase == "over_9":
                # 9IDOrder No.
                current_order_no = str(row.get("Order No.", "")).strip()
                if current_order_no == oid:
                    score += 10

            #  (+10)
            current_time = row.get("Order time")
            if pd.notnull(current_time) and pd.notnull(dt1):
                # 
                if current_time.date() == dt1.date():
                    time_diff = abs((current_time - dt1).total_seconds())
                    # 33
                    if time_diff <= 10800:  # 3
                        score += 10
                    else:  # 3
                        score += 5
            elif pd.isnull(current_time):
                # 
                score += 3

            # Order status (+5)
            current_status = str(row.get("Order status", "")).strip().lower()
            if current_status == "finish":
                score += 5

            return score

        except Exception as e:
            print(f"[] : {e}")
            return 1  # 

    def _update_matched_record(self, m_idx: int, m_row: pd.Series, oid: str, amt: float,
                              dt1: datetime, phase: str, order_type: str, trans_id: str, t_val: str) -> bool:
        """ - +"""
        try:
            # Transaction ID
            trans_id_clean = str(int(float(trans_id))) if trans_id and str(trans_id).replace('.', '').isdigit() else trans_id

            # Matched_Flag

            # 
            price_updated = False
            if pd.notnull(m_row["Order price"]) and abs(m_row["Order price"] - amt) > 1e-2:
                old_price = m_row["Order price"]
                self.df2.at[m_idx, "Order price"] = amt
                add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} updated price from RM{old_price:.2f} to RM{amt:.2f}")
                price_updated = True

            # Order ID
            order_id_updated = False
            if phase == "9_digit":
                # 9IDEquipment ID
                current_eq_id = str(m_row.get("Equipment ID", "")).strip()
                if current_eq_id != oid:
                    old_eq_id = current_eq_id if current_eq_id else ""
                    self.df2.at[m_idx, "Equipment ID"] = oid
                    add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} updated Equipment ID from {old_eq_id} to {oid}")
                    order_id_updated = True
            elif phase == "over_9":
                # 9IDOrder No.
                current_order_no = str(m_row.get("Order No.", "")).strip()
                if current_order_no != oid:
                    old_order_no = current_order_no if current_order_no else ""
                    self.df2.at[m_idx, "Order No."] = oid
                    add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} updated Order No. from {old_order_no} to {oid}")
                    order_id_updated = True

                # Equipment IDMatched Order ID
                if str(m_row.get("Equipment ID", "")).strip() == "":
                    self.df2.at[m_idx, "Matched Order ID"] = oid

            # 
            current_time = m_row.get("Order time")
            if pd.notnull(current_time) and pd.notnull(dt1):
                time_diff = abs((current_time - dt1).total_seconds())
                # 310800
                if time_diff > 10800:
                    self.df2.at[m_idx, "Order time"] = dt1
                    self.df2.at[m_idx, "Time"] = t_val
            elif pd.isnull(current_time) and pd.notnull(dt1):
                # 
                self.df2.at[m_idx, "Order time"] = dt1
                self.df2.at[m_idx, "Time"] = t_val

            # Order status
            status_updated = False
            current_status = str(m_row.get("Order status", "")).strip().lower()
            if current_status != "finish":
                old_status = m_row.get("Order status", "")
                self.df2.at[m_idx, "Order status"] = "Finish"
                add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} updated status from {old_status} to Finish RM{amt:.2f}")
                status_updated = True

            # 
            self.df2.at[m_idx, "Order types"] = order_type
            self.df2.at[m_idx, "Transaction ID"] = trans_id_clean
            # [] Transaction Numpandas
            try:
                # 
                if "Transaction Num" in self.df2.columns:
                    # 
                    if self.df2["Transaction Num"].dtype != 'object':
                        self.df2["Transaction Num"] = self.df2["Transaction Num"].astype(str)

                    # 
                    self.df2.at[m_idx, "Transaction Num"] = str(trans_id_clean)
            except Exception as e:
                # 
                pass
            # Matched_Flag

            return True

        except Exception as e:
            print(f"[]  {m_idx}: {e}")
            # [] Matched_Flag
            try:
                self.df2.at[m_idx, "Matched_Flag"] = True
                print(f"[]  {m_idx} Matched_FlagTrue")
                return True  # 
            except Exception as e2:
                print(f"[] Matched_Flag {m_idx}: {e2}")
                return False

class TraditionalMatcher:
    """ - """

    def __init__(self, df2: pd.DataFrame, df2_backup: pd.DataFrame, matched_indices: set):
        self.df2 = df2
        self.df2_backup = df2_backup
        self.matched_indices = matched_indices
        self.time_thresholds = [10, 30, 180, 300, 600, 1800, 3600, 10800]  # 3

    def match_traditional(self, oid: str, amt: float, dt1: datetime,
                         phase: str, order_type: str, t_val: str) -> Tuple[bool, Optional[int]]:
        """
        

        Returns:
            Tuple[bool, Optional[int]]: (, )
        """
        search_field = "Equipment ID" if phase == "9_digit" else "Order No."

        # 
        for threshold in self.time_thresholds:
            matches = self._find_matches_by_threshold(oid, dt1, search_field, threshold)

            if not matches.empty:
                # 
                exact_matches = matches[
                    (matches["Order price"].round(2) == round(amt, 2)) &
                    (matches["Order status"].str.strip().str.lower() == "finish")
                ]

                if not exact_matches.empty:
                    m_idx = exact_matches.index[0]
                    success = self._update_traditional_match(m_idx, oid, amt, dt1, phase, order_type, threshold, t_val)
                    if success:
                        self.matched_indices.add(m_idx)
                        return True, m_idx
                else:
                    # 
                    for m_idx, m_row in matches.iterrows():
                        success = self._try_update_mismatch(m_idx, m_row, oid, amt, dt1, phase, order_type, t_val)
                        if success:
                            self.matched_indices.add(m_idx)
                            return True, m_idx

        return False, None

    def _find_matches_by_threshold(self, oid: str, dt1: datetime, search_field: str, threshold: int) -> pd.DataFrame:
        """"""
        matches = self.df2[self.df2[search_field] == oid]
        matches = matches[matches.index.map(lambda x: x not in self.matched_indices)]

        # ±
        if not matches.empty and dt1:
            matches = matches[matches["OrderTime_dt"].apply(
                lambda x: x and abs((x - dt1).total_seconds()) <= threshold)]

        return matches

    def _update_traditional_match(self, m_idx: int, oid: str, amt: float, dt1: datetime,
                                 phase: str, order_type: str, threshold: int, t_val: str) -> bool:
        """"""
        try:
            # 3
            if threshold == 10800:
                self.df2.at[m_idx, "Order time"] = dt1
                self.df2.at[m_idx, "Time"] = t_val

            self.df2.at[m_idx, "Matched_Flag"] = True
            self.df2.at[m_idx, "Order types"] = order_type

            # []  - 
            # 

            return True

        except Exception as e:
            print(f"[]  {m_idx}: {e}")
            return False

    def _try_update_mismatch(self, m_idx: int, m_row: pd.Series, oid: str, amt: float,
                            dt1: datetime, phase: str, order_type: str, t_val: str) -> bool:
        """"""
        try:
            updated = False
            trans_id = ""  # Transaction ID

            # 
            if pd.notnull(m_row["Order price"]) and abs(m_row["Order price"] - amt) > 1e-2:
                old_price = m_row["Order price"]
                self.df2.at[m_idx, "Order price"] = amt
                self.df2.at[m_idx, "Order status"] = "Finish"
                add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} updated price from RM{old_price:.2f} to RM{amt:.2f}")
                updated = True

            # 
            elif pd.notnull(m_row["Order price"]) and abs(m_row["Order price"] - amt) <= 1e-2:
                if m_row["Order status"].strip().lower() != "finish":
                    old_status = m_row["Order status"]
                    self.df2.at[m_idx, "Order status"] = "Finish"
                    add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} updated status from {old_status} to Finish RM{amt:.2f}")
                    updated = True

            if updated:
                self.df2.at[m_idx, "Order types"] = order_type
                self.df2.at[m_idx, "Matched_Flag"] = True

                if phase == "9_digit":
                    self.df2.at[m_idx, "Equipment ID"] = oid
                    self.df2.at[m_idx, "Time"] = t_val
                    self.df2.at[m_idx, "Order time"] = dt1
                elif phase == "over_9":
                    if str(m_row["Equipment ID"]).strip() == "":
                        self.df2.at[m_idx, "Matched Order ID"] = oid

            return updated

        except Exception as e:
            print(f"[]  {m_idx}: {e}")
            return False

# ======================Transaction Num======================
def detect_transaction_num_capability(df1_filtered, df2):
    """
    Transaction Num

    Args:
        df1_filtered: 
        df2: 

    Returns:
        bool: TrueTransaction IDFalse
    """
    try:
        print("[] Transaction Num...")

        # [] 
        if not isinstance(df1_filtered, pd.DataFrame):
            raise TypeError(f"df1_filteredDataFrame{type(df1_filtered)}")
        if not isinstance(df2, pd.DataFrame):
            raise TypeError(f"df2DataFrame{type(df2)}")

    except Exception as e:
        print(f"[] : {e}")
        return False

    # [] Transaction Num
    transaction_column_found = None

    # 
    priority_order = [
        "Transaction Num",      # 
        "Transaction_Num",      # 
        "TransactionNum",       # 
        "Transaction ID",       # ID
        "Transaction_ID",       # ID
        "TransactionID"         # ID
    ]

    # 
    for col_name in priority_order:
        if col_name in df2.columns:
            transaction_column_found = col_name
            break

    # 
    if transaction_column_found is None:
        for col in df2.columns:
            col_lower = col.lower().replace(" ", "").replace("_", "")
            if "transaction" in col_lower and ("num" in col_lower or "id" in col_lower):
                transaction_column_found = col
                print(f"[] Transaction: {col}")
                break

    # 
    if transaction_column_found is None:
        print("[]  Transaction Num ")
        print("[] ")
        return False

    # 
    if transaction_column_found != "Transaction Num":
        print(f"[] Transaction: {transaction_column_found}")
        print(f"[] : Transaction Num")

        # 
        df2.rename(columns={transaction_column_found: "Transaction Num"}, inplace=True)
        print("[] ")
    else:
        print("[]  Transaction Num ")

    # Transaction Num
    df2_trans_num_count = df2["Transaction Num"].notna().sum()
    df2_trans_num_unique = df2["Transaction Num"].nunique()
    df2_total_records = len(df2)

    print(f" : {df2_total_records}")
    print(f" Transaction Num: {df2_trans_num_count}")
    print(f" Transaction Num: {df2_trans_num_unique}")
    print(f" Transaction Num: {df2_trans_num_count/df2_total_records*100:.1f}%")

    # Transaction Num
    if df2_trans_num_count == 0:
        print("[] Transaction Num")
        print("[] ")
        return False

    # Transaction NumTransaction ID
    print("[] Transaction NumTransaction ID...")

    # Transaction ID
    def clean_transaction_format(value):
        """Transaction"""
        try:
            # nan
            if pd.isna(value):
                return None

            str_val = str(value).strip()
            if not str_val or str_val.lower() == 'nan':
                return None

            # 
            if str_val.replace('.', '').replace('-', '').isdigit():
                return str(int(float(str_val)))
            else:
                return str_val
        except:
            return None

    valid_trans_ids_raw = df1_filtered[
        df1_filtered["Transaction ID"].notna() &
        (df1_filtered["Transaction ID"].astype(str).str.strip() != "") &
        (df1_filtered["Transaction ID"].astype(str).str.lower() != "nan")
    ]["Transaction ID"]

    valid_trans_ids = [clean_transaction_format(tid) for tid in valid_trans_ids_raw]
    valid_trans_ids = [tid for tid in valid_trans_ids if tid is not None]  # None
    valid_trans_ids = list(set(valid_trans_ids))  # 

    # Transaction Num
    valid_trans_nums_raw = df2[
        df2["Transaction Num"].notna() &
        (df2["Transaction Num"].astype(str).str.strip() != "") &
        (df2["Transaction Num"].astype(str).str.lower() != "nan")
    ]["Transaction Num"]

    valid_trans_nums = [clean_transaction_format(tnum) for tnum in valid_trans_nums_raw]
    valid_trans_nums = [tnum for tnum in valid_trans_nums if tnum is not None]  # None
    valid_trans_nums = list(set(valid_trans_nums))  # 

    print(f"[] Transaction ID: {len(valid_trans_ids)}")
    print(f" Transaction Num: {len(valid_trans_nums)}")

    # 
    if len(valid_trans_ids) > 0:
        print(f"[] Transaction ID: {valid_trans_ids[:5]}")
    if len(valid_trans_nums) > 0:
        print(f"[] Transaction Num: {valid_trans_nums[:5]}")

    # 
    matching_ids = set(valid_trans_ids) & set(valid_trans_nums)
    matching_count = len(matching_ids)

    print(f"[] Transaction ID/Num: {matching_count}")
    if matching_count > 0:
        print(f"[] Transaction ID: {list(matching_ids)[:5]}")

    # Transaction ID
    if matching_count > 0:
        match_rate = matching_count / len(valid_trans_ids) if len(valid_trans_ids) > 0 else 0
        print(f"[] : {match_rate*100:.1f}%")

        # [] 10%5%Transaction ID
        if match_rate >= 0.05:  # 5%
            print("[] Transaction Num")
            print("[] Transaction ID")
            return True
        else:
            print(f"[] Transaction Num ({match_rate*100:.1f}% < 5%)")
            print("[] ")
            return False
    else:
        print("[] Transaction ID/Num")
        print("[] ")
        return False

# 
use_transaction_id_matching = detect_transaction_num_capability(df1_filtered, df2)

# 
if use_transaction_id_matching:
    print("\n[] : Transaction ID")
    print("[] Transaction ID")
else:
    print("\n[] : ")
    print("[] ")
    print("[] : ±3")

# note_logs


def add_log(log_tuple):
    if log_tuple not in note_logs:
        note_logs.append(log_tuple)

def add_log_with_transaction(amt, oid, trans_id, message):
    """Transaction ID

    Args:
        amt: 
        oid: Order ID
        trans_id: Transaction ID
        message: 
    """
    # Order IDTransaction ID
    if trans_id and str(trans_id).strip() and str(trans_id).strip().lower() != "nan":
        combined_id = f"{oid}\n(TXN: {trans_id})"
    else:
        combined_id = oid

    log_tuple = (amt, combined_id, message)
    if log_tuple not in note_logs:
        note_logs.append(log_tuple)

def safe_clean_transaction_id(trans_id):
    """[] Transaction IDNaN"""
    if not trans_id or pd.isna(trans_id):
        return ""

    str_val = str(trans_id).strip()
    if str_val.lower() in ['nan', 'none', '']:
        return ""

    # [] 
    if str_val.isdigit():
        return str_val

    # [] 
    try:
        if str_val.replace('.', '').isdigit():
            float_val = float(str_val)
            if not pd.isna(float_val) and float_val > 0:
                return str(int(float_val))
    except (ValueError, OverflowError):
        pass

    # [] 
    digits_only = ''.join(c for c in str_val if c.isdigit())
    if digits_only and len(digits_only) >= 6:
        return digits_only

    return str_val if str_val else ""

def safe_string_operation(value, operation_func, default_value=""):
    """[] None/NaN"""
    try:
        if pd.isna(value) or value is None:
            return default_value
        return operation_func(str(value))
    except Exception as e:
        print(f"[] : {e}, : {value}")
        return default_value

def safe_numeric_conversion(value, conversion_func, default_value=0):
    """[] """
    try:
        if pd.isna(value) or value is None or str(value).strip() == '':
            return default_value

        result = conversion_func(value)

        # 
        if pd.isna(result):
            print(f"[] NaN: {value}")
            return default_value

        return result
    except Exception as e:
        print(f"[] : {e}, : {value}")
        return default_value

def safe_read_excel(file_path, **kwargs):
    """[] Excel"""
    try:
        # 1. 
        if not os.path.exists(file_path):
            raise FileNotFoundError(f": {file_path}")

        # 2. 100MB
        file_size = os.path.getsize(file_path)
        max_size = 100 * 1024 * 1024  # 100MB
        if file_size > max_size:
            raise ValueError(f": {file_size / 1024 / 1024:.1f}MB{max_size / 1024 / 1024}MB")

        # 3. 
        if not os.access(file_path, os.R_OK):
            raise PermissionError(f": {file_path}")

        # 4. Excel
        print(f" : {os.path.basename(file_path)} ({file_size / 1024:.1f}KB)")
        df = pd.read_excel(file_path, **kwargs)

        # 5. 
        if df.empty:
            print("[] ")
        else:
            print(f"[] : {len(df)} , {len(df.columns)} ")

        return df

    except FileNotFoundError as e:
        print(f"[] : {e}")
        raise
    except PermissionError as e:
        print(f"[] : {e}")
        raise
    except pd.errors.EmptyDataError:
        print(f"[] : {file_path}")
        raise ValueError(f": {file_path}")
    except pd.errors.ExcelFileError as e:
        print(f"[] Excel: {e}")
        raise ValueError(f"Excel: {e}")
    except Exception as e:
        print(f"[] : {type(e).__name__}: {e}")
        raise

def is_valid_transaction_id(value):
    """[] Transaction IDTransaction IDEquipment ID"""
    if not value:
        return False

    str_val = str(value).strip()

    # 
    if not str_val.isdigit():
        return False

    # Equipment ID9603
    if len(str_val) == 9 and str_val.startswith('603'):
        return False  # Equipment IDTransaction ID

    # Transaction ID8-12603
    if 8 <= len(str_val) <= 12 and not str_val.startswith('603'):
        return True

    # 
    return False

def is_equipment_id_format(value):
    """[] Equipment ID"""
    if not value:
        return False

    str_val = str(value).strip()

    # Equipment ID9603
    if len(str_val) == 9 and str_val.isdigit() and str_val.startswith('603'):
        return True

    return False

def _extract_equipment_id_from_order(order_id):
    """[] Order IDEquipment ID"""
    try:
        # Order IDEquipment ID
        # 2025071004251943043969843302400 Equipment ID
        order_str = str(order_id)

        # Equipment ID6-10
        import re
        # 6-10
        matches = re.findall(r'\d{6,10}', order_str)

        for match in matches:
            # Equipment ID
            if len(match) >= 6 and match.startswith('603'):  # Equipment ID
                return match

        # 6-9
        if len(order_str) >= 6:
            last_digits = order_str[-9:]  # 9
            if last_digits.isdigit():
                return last_digits

    except Exception as e:
        print(f"[] Order IDEquipment ID: {e}")

    return ""

def _recover_transaction_id_from_backup(equipment_id, df2_backup):
    """[] Transaction ID"""
    try:
        if df2_backup is None or df2_backup.empty:
            return ""

        # Equipment ID
        matching_records = df2_backup[
            (df2_backup['Equipment ID'] == equipment_id) |
            (df2_backup['Equipment ID'].astype(str) == str(equipment_id))
        ]

        for _, record in matching_records.iterrows():
            # Transaction Num
            trans_num = record.get('Transaction Num', '')
            if trans_num and str(trans_num).strip() and str(trans_num).lower() != 'nan':
                cleaned_id = safe_clean_transaction_id(trans_num)
                if cleaned_id and len(cleaned_id) >= 6:
                    return cleaned_id

            # Transaction ID
            trans_id = record.get('Transaction ID', '')
            if trans_id and str(trans_id).strip() and str(trans_id).lower() != 'nan':
                cleaned_id = safe_clean_transaction_id(trans_id)
                if cleaned_id and len(cleaned_id) >= 6:
                    return cleaned_id

    except Exception as e:
        print(f"[] Transaction ID: {e}")

    return ""

def transaction_sync_insert(trans_id, oid, amt, dt1, phase, df2_backup):
    """Transaction ID"""

    # [] 
    print(f"[] Transaction: Transaction ID={trans_id}, Order ID={oid}, Amount=RM{amt:.2f}, Phase={phase}")

    # [] Transaction ID
    trans_id_clean = safe_clean_transaction_id(trans_id)

    # [] trans_id
    if not trans_id_clean:
        print(f"[] Transaction ID: ='{trans_id}'")

        # Order IDEquipment ID
        equipment_id = _extract_equipment_id_from_order(oid)
        if equipment_id:
            # Transaction ID
            recovered_id = _recover_transaction_id_from_backup(equipment_id, df2_backup)
            if recovered_id:
                trans_id_clean = recovered_id
                print(f"[] Transaction ID: {trans_id_clean}")
            else:
                # [] Equipment IDTransaction ID
                # Equipment IDTransaction ID
                print(f"[] Transaction IDEquipment ID={equipment_id}Transaction ID")
                trans_id_clean = ""  # Equipment ID

    print(f"[] Transaction ID: ='{trans_id}' -> ='{trans_id_clean}'")

    # [] 1 +  + Order ID + API order

    if len(str(oid)) > 9:  # 9ID → Order No. → Normal order
        # Order No.±3
        backup_matches = df2_backup[df2_backup["Order No."].astype(str).str.strip() == str(oid).strip()]

        # [] API order
        backup_matches = backup_matches[
            ~backup_matches["Order types"].astype(str).str.contains("API", case=False, na=False)
        ]

        for _, backup_row in backup_matches.iterrows():
            backup_time = backup_row.get("Order time")
            if pd.notnull(backup_time):
                # [] ±310800
                time_diff = abs((backup_time - dt1).total_seconds())
                if time_diff <= 10800:  # 3 = 10800

                    # [] 
                    backup_price = backup_row.get("Order price", 0)
                    price_updated = False

                    if pd.notnull(backup_price) and abs(backup_price - amt) > 1e-2:
                        # 
                        price_updated = True
                        add_log_with_transaction(amt, oid, trans_id,
                            f"{dt1} {oid} TRANSACTION_SYNC updated price from RM{backup_price:.2f} to RM{amt:.2f}")

                    # 
                    new_row = backup_row.copy()

                    # [] TransactionNaN
                    update_dict = {
                        "Order status": "Finish",
                        "Order price": amt,  # 
                        "Order time": dt1,   # 
                        "Time": dt1.strftime("%H:%M:%S"),
                        "Matched_Flag": True,
                        "Order types": "Normal order"
                    }

                    # [] TransactionNaN
                    if trans_id_clean and trans_id_clean.strip():
                        update_dict["Transaction ID"] = trans_id_clean
                        update_dict["Transaction Num"] = trans_id_clean
                        print(f"[] Transaction: {trans_id_clean}")
                    else:
                        # [] NaN
                        update_dict["Transaction ID"] = ""
                        update_dict["Transaction Num"] = ""
                        print(f"[] TransactionNaN: trans_id='{trans_id}', trans_id_clean='{trans_id_clean}'")

                    new_row.update(update_dict)

                    # [] 
                    if not price_updated:
                        add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} TRANSACTION_SYNC inserted RM{amt:.2f}")

                    # [] RM5.00Transaction
                    if abs(amt - 5.0) < 0.01:
                        print(f"[] RM5.00: Order ID={oid}, Transaction ID={trans_id}, =TRANSACTION_SYNC, ={price_updated}")

                    return new_row

        # Order No.
        print(f"[] Order No. {oid}")
        add_log_with_transaction(amt, oid, trans_id,
            f" {dt1} {oid} ORDER_NO_NOT_FOUND_IN_BACKUP inserted RM{amt:.2f}")

    else:  # 9ID → Equipment ID → Offline order
        # Equipment ID±3
        backup_matches = df2_backup[df2_backup["Equipment ID"].astype(str).str.strip() == str(oid).strip()]

        # [] API order
        backup_matches = backup_matches[
            ~backup_matches["Order types"].astype(str).str.contains("API", case=False, na=False)
        ]

        for _, backup_row in backup_matches.iterrows():
            backup_time = backup_row.get("Order time")
            if pd.notnull(backup_time):
                # [] ±310800
                time_diff = abs((backup_time - dt1).total_seconds())
                if time_diff <= 10800:  # 3 = 10800

                    # [] 
                    backup_price = backup_row.get("Order price", 0)
                    price_updated = False

                    if pd.notnull(backup_price) and abs(backup_price - amt) > 1e-2:
                        # 
                        price_updated = True
                        add_log_with_transaction(amt, oid, trans_id,
                            f"{dt1} {oid} TRANSACTION_SYNC updated price from RM{backup_price:.2f} to RM{amt:.2f}")

                    # 
                    new_row = backup_row.copy()

                    # [] TransactionNaN
                    update_dict = {
                        "Order status": "Finish",
                        "Order price": amt,  # 
                        "Order time": dt1,   # 
                        "Time": dt1.strftime("%H:%M:%S"),
                        "Matched_Flag": True,
                        "Order types": "Offline order"
                    }

                    # [] TransactionNaN
                    if trans_id_clean and trans_id_clean.strip():
                        update_dict["Transaction ID"] = trans_id_clean
                        update_dict["Transaction Num"] = trans_id_clean
                        print(f"[] Transaction: {trans_id_clean}")
                    else:
                        # [] NaN
                        update_dict["Transaction ID"] = ""
                        update_dict["Transaction Num"] = ""
                        print(f"[] TransactionNaN: trans_id='{trans_id}', trans_id_clean='{trans_id_clean}'")

                    new_row.update(update_dict)

                    # [] 
                    if not price_updated:
                        add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} TRANSACTION_SYNC inserted RM{amt:.2f}")

                    # [] RM5.00Transaction
                    if abs(amt - 5.0) < 0.01:
                        print(f"[] RM5.00: Order ID={oid}, Transaction ID={trans_id}, =TRANSACTION_SYNC, ={price_updated}")

                    return new_row

        # Equipment ID
        print(f"[] Equipment ID {oid}")
        add_log_with_transaction(amt, oid, trans_id,
            f" {dt1} {oid} EQUIPMENT_ID_NOT_FOUND_IN_BACKUP inserted RM{amt:.2f}")

    # [] API order
    print(f"[] : Phase={phase}, Order ID={oid}")
    if phase == "9_digit":
        search_field = "Equipment ID"
        default_eq = oid
        order_type = "Offline order"  # 9ID → Offline order
    elif phase == "over_9":
        search_field = "Order No."
        default_eq = ""
        order_type = "Normal order"   # >9ID → Normal order
    else:
        search_field = "Equipment ID"
        default_eq = oid
        order_type = "Anomaly order"  #  → Anomaly order

    # [] API order
    new_row = {
        search_field: oid,
        "Order price": amt,
        "Order status": "Finish",
        "Order time": dt1,
        "Time": dt1.strftime("%H:%M:%S"),
        "Equipment ID": default_eq,
        "Matched Order ID": oid if phase == "over_9" else "",
        "Transaction ID": trans_id_clean,
        "Transaction Num": trans_id_clean,
        "Matched_Flag": True,
        "Order types": order_type  # API order
    }

    # [] 
    add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} STANDARD inserted RM{amt:.2f}")

    # [] RM5.00
    if abs(amt - 5.0) < 0.01:
        print(f"[] RM5.00: Order ID={oid}, Transaction ID={trans_id}, =STANDARD")

    return new_row


df2["OrderTime_dt"] = pd.to_datetime(df2["Order time"], errors="coerce")

# 9ID9ID
processed_over9_ids = {}

# 9ID
for _, row in df1_filtered[df1_filtered["OrderID_Type"] == "over_9"].iterrows():
    oid = row["Order ID"]
    processed_over9_ids[oid] = processed_over9_ids.get(oid, 0) + 1

# ============================================
class UnifiedDataProcessor:
    """ - """

    def __init__(self, df1_filtered: pd.DataFrame, df2: pd.DataFrame, df2_backup: pd.DataFrame):
        self.df1_filtered = df1_filtered
        self.df2 = df2
        self.df2_backup = df2_backup
        self.matched_indices = set()
        self.inserted_indices = set()  # [] 
        self.processed_9digit_ids = {}
        self.processed_over9_ids = {}

        # 
        self._initialize_counters()

        #  - [] self.df2df2
        self.transaction_matcher = TransactionIDMatcher(self.df2, df2_backup, self.matched_indices)
        self.traditional_matcher = TraditionalMatcher(self.df2, df2_backup, self.matched_indices)

        # 
        self.mode_manager = MatchingModeManager()

    def _initialize_counters(self):
        """ID - """
        # 9ID - value_counts()iterrows()
        nine_digit_df = self.df1_filtered[self.df1_filtered["OrderID_Type"] == "9_digit"]
        if not nine_digit_df.empty:
            self.processed_9digit_ids = nine_digit_df["Order ID"].value_counts().to_dict()

        # 9ID - value_counts()iterrows()
        over9_df = self.df1_filtered[self.df1_filtered["OrderID_Type"] == "over_9"]
        if not over9_df.empty:
            self.processed_over9_ids = over9_df["Order ID"].value_counts().to_dict()

    def process_with_date_grouping(self, use_transaction_id_matching: bool) -> pd.DataFrame:
        """"""
        print(" ...")

        # 
        mode = 'transaction_id' if use_transaction_id_matching else 'traditional'
        self.mode_manager.set_mode(mode)

        # 
        date_processor = DateGroupProcessor(self.df1_filtered)
        date_groups = date_processor.get_date_groups()

        if not date_groups:
            print("[] ")
            return self.df2

        print(f"[]  {len(date_groups)} ")

        # 
        total_records = date_processor.get_total_records()
        progress = ProgressManager(total_records, "").start()

        try:
            for date_str in sorted(date_groups.keys()):
                group_data = date_groups[date_str]
                print(f"\n : {date_str} ({len(group_data)} )")

                # 
                self._process_date_group(group_data, use_transaction_id_matching, progress)

        finally:
            progress.close()

        # 
        self.mode_manager.print_stats()

        # [] Transaction NumNaN
        self._final_transaction_num_check()

        return self.df2

    def _get_transaction_id_multi_source(self, row):
        """[] Transaction ID"""
        # 
        possible_fields = [
            "Transaction ID",
            "Transaction_ID",
            "TransactionID",
            "transaction_id",
            "TRANSACTION_ID"
        ]

        for field in possible_fields:
            trans_id = safe_clean_transaction_id(row.get(field, ""))
            if trans_id:
                print(f"[]  '{field}' Transaction ID: {trans_id}")
                return trans_id

        # [] Transaction Num
        trans_id = safe_clean_transaction_id(row.get("Transaction Num", ""))
        if trans_id:
            print(f"[] Transaction NumID: {trans_id}")
            return trans_id

        # [] Equipment ID
        equipment_id = row.get("Equipment ID", "") or row.get("Order ID", "")
        if equipment_id:
            recovered_id = self._recover_transaction_id_from_equipment(equipment_id)
            if recovered_id:
                print(f"[] Equipment ID {equipment_id} Transaction ID: {recovered_id}")
                return recovered_id

        print(f"[] Transaction ID")
        return ""

    def _recover_transaction_id_from_equipment(self, equipment_id):
        """[] Equipment IDTransaction ID"""
        try:
            # Equipment ID
            if hasattr(self, 'df2_backup') and self.df2_backup is not None:
                matching_records = self.df2_backup[
                    self.df2_backup['Equipment ID'] == equipment_id
                ]

                for _, record in matching_records.iterrows():
                    trans_num = record.get('Transaction Num', '')
                    if trans_num and str(trans_num).strip() and str(trans_num).lower() != 'nan':
                        cleaned_id = safe_clean_transaction_id(trans_num)
                        if cleaned_id:
                            return cleaned_id

            # [] Equipment IDTransaction ID
            # Equipment IDTransaction ID
            print(f"[] Equipment ID {equipment_id} Transaction ID")

        except Exception as e:
            print(f"[] Equipment IDTransaction ID: {e}")

        # [] Equipment IDTransaction ID
        return ""

    def _final_transaction_num_check(self):
        """[] Transaction NumNaN"""
        print("[] Transaction Num...")

        # [] Equipment ID
        self._clean_equipment_id_from_transaction_num()

        # Transaction NumNaN
        if 'Transaction Num' in self.df2.columns:
            nan_count = self.df2['Transaction Num'].isna().sum()
            empty_count = (self.df2['Transaction Num'] == '').sum()

            if nan_count > 0 or empty_count > 0:
                print(f"[] : NaN={nan_count}, ={empty_count}...")

                # [] Transaction Num
                recovered_count = self._smart_recover_transaction_num()

                # NaN
                self.df2['Transaction Num'] = self.df2['Transaction Num'].fillna('')

                # 
                final_nan_count = self.df2['Transaction Num'].isna().sum()
                final_empty_count = (self.df2['Transaction Num'] == '').sum()
                final_valid_count = ((self.df2['Transaction Num'] != '') &
                                   (self.df2['Transaction Num'].notna())).sum()

                print(f"[] : {recovered_count}, NaN={final_nan_count}, ={final_empty_count}, ={final_valid_count}")
            else:
                print("[] Transaction NumNaN")

        # Transaction ID
        if 'Transaction ID' in self.df2.columns:
            nan_count = self.df2['Transaction ID'].isna().sum()
            if nan_count > 0:
                print(f"[]  {nan_count} Transaction IDNaN...")
                self.df2['Transaction ID'] = self.df2['Transaction ID'].fillna('')
                print(f"[] Transaction ID NaN")
            else:
                print("[] Transaction IDNaN")

        # [] 
        self._verify_transaction_consistency()

        # 
        total_records = len(self.df2)
        valid_trans_num = ((self.df2['Transaction Num'] != '') &
                          (self.df2['Transaction Num'].notna())).sum() if 'Transaction Num' in self.df2.columns else 0
        print(f"[] : ={total_records}, Transaction Num={valid_trans_num}")

    def _clean_equipment_id_from_transaction_num(self):
        """[] Transaction NumEquipment ID"""
        print("[] Transaction NumEquipment ID...")

        cleaned_count = 0

        for idx, row in self.df2.iterrows():
            trans_num = row.get('Transaction Num', '')
            equipment_id = row.get('Equipment ID', '')

            # Transaction NumEquipment ID
            if trans_num and is_equipment_id_format(trans_num):
                # Transaction NumEquipment ID
                if str(trans_num) == str(equipment_id):
                    self.df2.at[idx, 'Transaction Num'] = ''
                    cleaned_count += 1
                    print(f"[] Equipment ID: {idx}, {trans_num}")
                # Equipment IDEquipment ID
                else:
                    self.df2.at[idx, 'Transaction Num'] = ''
                    cleaned_count += 1
                    print(f"[] Equipment ID: {idx}, {trans_num}")

        if cleaned_count > 0:
            print(f"[] :  {cleaned_count} Equipment ID")
        else:
            print("[] Equipment IDTransaction Num")

        return cleaned_count

    def _smart_recover_transaction_num(self):
        """[] Transaction Num"""
        recovered_count = 0

        for idx, row in self.df2.iterrows():
            current_trans_num = row.get('Transaction Num', '')

            # Transaction NumNaN
            if pd.isna(current_trans_num) or current_trans_num == '':

                # 1Transaction ID
                trans_id = row.get('Transaction ID', '')
                if trans_id and str(trans_id).strip() and str(trans_id).lower() != 'nan':
                    cleaned_id = safe_clean_transaction_id(trans_id)
                    if cleaned_id:
                        self.df2.at[idx, 'Transaction Num'] = cleaned_id
                        recovered_count += 1
                        print(f"[] Transaction ID: {idx}, ={cleaned_id}")
                        continue

                # 2Equipment IDEquipment ID
                # [] Equipment IDTransaction Num
                # Equipment IDTransaction ID
                equipment_id = row.get('Equipment ID', '')
                if equipment_id:
                    recovered_id = self._recover_transaction_id_from_equipment(equipment_id)
                    # [] Transaction ID
                    if recovered_id and is_valid_transaction_id(recovered_id):
                        self.df2.at[idx, 'Transaction Num'] = recovered_id
                        recovered_count += 1
                        print(f"[] Equipment IDTransaction ID: {idx}, Equipment={equipment_id}, Transaction={recovered_id}")
                        continue
                    elif recovered_id:
                        print(f"[] Equipment ID: {idx}, Equipment={equipment_id}, ={recovered_id}")

                # 3Order No.
                order_no = row.get('Order No.', '')
                if order_no:
                    extracted_id = _extract_equipment_id_from_order(order_no)
                    # [] Transaction ID
                    if extracted_id and is_valid_transaction_id(extracted_id):
                        self.df2.at[idx, 'Transaction Num'] = extracted_id
                        recovered_count += 1
                        print(f"[] Order No.Transaction ID: {idx}, ={extracted_id}")
                        continue
                    elif extracted_id:
                        print(f"[] Order No.Equipment ID: {idx}, ={extracted_id}")

        return recovered_count

    def _verify_transaction_consistency(self):
        """[] Transaction"""
        print("[] Transaction...")

        inconsistent_count = 0

        for idx, row in self.df2.iterrows():
            trans_id = str(row.get('Transaction ID', '')).strip()
            trans_num = str(row.get('Transaction Num', '')).strip()

            # 
            if (trans_id and trans_id.lower() != 'nan' and
                trans_num and trans_num.lower() != 'nan' and
                trans_id != trans_num):

                print(f"[] : {idx}, Transaction ID='{trans_id}', Transaction Num='{trans_num}'")

                # Transaction ID
                if len(trans_id) >= 6 and trans_id.isdigit():
                    self.df2.at[idx, 'Transaction Num'] = trans_id
                    print(f"[] Transaction ID: {trans_id}")
                    inconsistent_count += 1

        if inconsistent_count > 0:
            print(f"[]  {inconsistent_count} ")
        else:
            print("[] Transaction")

    def _process_date_group(self, group_data: pd.DataFrame, use_transaction_id_matching: bool, progress: ProgressManager):
        """"""

        # 9IDID
        for phase in ["9_digit", "over_9", "anomaly"]:
            phase_data = group_data[group_data["OrderID_Type"] == phase]

            if phase_data.empty:
                continue

            progress.set_description(f" {phase} ")

            for _, row in phase_data.iterrows():
                try:
                    result = self._process_single_record(row, phase, use_transaction_id_matching)
                    if result:
                        self.mode_manager.update_stats('matched' if result[0] else 'inserted')

                    self.mode_manager.update_stats('processed')
                    progress.update(1)

                except Exception as e:
                    trans_id = row.get("Transaction ID", "")
                    add_log_with_transaction(0, row["Order ID"], trans_id, f"Error processing {row['Order ID']}: {str(e)}")
                    progress.update(1)
                    continue

    def _process_single_record(self, row: pd.Series, phase: str, use_transaction_id_matching: bool) -> Optional[Tuple[bool, Optional[int]]]:
        """"""
        oid = row["Order ID"]
        amt = row["Bill Amt"]
        dt1 = row["DateTime"]
        t_val = row["Time24"]

        # [] Transaction ID
        trans_id = self._get_transaction_id_multi_source(row)

        # IDOrder types
        if phase == "9_digit":
            order_type = "Offline order"
        elif phase == "over_9":
            order_type = "Normal order"
        else:
            order_type = "Anomaly order"

        # ID
        if phase == "9_digit" and self.processed_9digit_ids.get(oid, 0) <= 0:
            return None
        if phase == "over_9" and self.processed_over9_ids.get(oid, 0) <= 0:
            return None

        # 
        if phase == "anomaly":
            self._insert_anomaly_record(oid, amt, dt1, t_val, trans_id, order_type)
            return (False, None)

        # 
        if check_conflict(oid, phase):
            return None

        # 
        if use_transaction_id_matching:
            success, m_idx = self.transaction_matcher.match_by_transaction_id(
                trans_id, oid, amt, dt1, phase, order_type, t_val)
        else:
            success, m_idx = self.traditional_matcher.match_traditional(
                oid, amt, dt1, phase, order_type, t_val)

        # 
        if success:
            if phase == "9_digit":
                self.processed_9digit_ids[oid] -= 1
            elif phase == "over_9":
                self.processed_over9_ids[oid] -= 1
            return (True, m_idx)
        else:
            # 
            self._insert_new_record(oid, amt, dt1, t_val, trans_id, phase, order_type, use_transaction_id_matching)
            if phase == "9_digit":
                self.processed_9digit_ids[oid] -= 1
            elif phase == "over_9":
                self.processed_over9_ids[oid] -= 1
            return (False, None)

    def _insert_anomaly_record(self, oid: str, amt: float, dt1: datetime, t_val: str, trans_id: str, order_type: str):
        """"""
        # Transaction ID
        trans_id_clean = str(int(float(trans_id))) if trans_id and str(trans_id).replace('.', '').isdigit() else trans_id

        add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} ANOMALY inserted RM{amt:.2f}")
        new_row = {
            "Equipment ID": oid,
            "Order price": amt,
            "Order status": "Finish",
            "Order time": dt1,
            "Time": t_val,
            "Matched Order ID": "",
            "Transaction ID": trans_id_clean,
            "Transaction Num": trans_id_clean,
            "Matched_Flag": True,
            "Order types": order_type
        }

        # [] _insert_new_record
        new_index = self.df2.index.max() + 1 if not self.df2.empty else 0

        # loc
        for col, val in new_row.items():
            if col not in self.df2.columns:
                self.df2[col] = None
            self.df2.loc[new_index, col] = val

        # [] matched_indicesinserted_indices
        self.matched_indices.add(new_index)
        self.inserted_indices.add(new_index)  # [] 

        # [] _insert_anomaly_record
        if not hasattr(self, 'inserted_records'):
            self.inserted_records = []
        self.inserted_records.append({
            'transaction_id': trans_id_clean,
            'order_id': oid,
            'amount': amt,
            'phase': 'anomaly',  # anomaly
            'method': '_insert_anomaly_record'
        })

    def _insert_new_record(self, oid: str, amt: float, dt1: datetime, t_val: str, trans_id: str,
                          phase: str, order_type: str, use_transaction_id_matching: bool):
        """"""

        # [] safe_clean_transaction_id
        trans_id_clean = safe_clean_transaction_id(trans_id)

        # [] Transaction IDTransaction ID
        # trans_id_clean
        if use_transaction_id_matching:
            # Transaction ID
            new_row = transaction_sync_insert(trans_id_clean, oid, amt, dt1, phase, self.df2_backup)
        else:
            # 
            if phase == "9_digit":
                search_field = "Equipment ID"
                default_eq = oid
            elif phase == "over_9":
                search_field = "Order No."
                default_eq = ""
            else:
                search_field = "Equipment ID"
                default_eq = oid

            new_row = {
                search_field: oid,
                "Order price": amt,
                "Order status": "Finish",
                "Order time": dt1,
                "Time": t_val,
                "Equipment ID": default_eq,
                "Matched Order ID": oid if phase == "over_9" else "",
                "Transaction ID": trans_id_clean,
                "Transaction Num": trans_id_clean,
                "Matched_Flag": True,
                "Order types": order_type
            }

            add_log_with_transaction(amt, oid, trans_id_clean, f"{dt1} {oid} NO RECORD inserted RM{amt:.2f}")

        # [] 

        # [] 
        new_index = self.df2.index.max() + 1 if not self.df2.empty else 0

        # Series
        new_series = pd.Series(new_row, name=new_index)

        # [] Transaction Num nan
        for col, val in new_row.items():
            if col not in self.df2.columns:
                # [] Nonenan
                if col == "Transaction Num":
                    self.df2[col] = ""  # None
                elif col == "Transaction ID":
                    self.df2[col] = ""  # None
                elif col == "Matched_Flag":
                    self.df2[col] = False  # FalseNone
                elif col in ["Order price", "Amount"]:
                    self.df2[col] = 0.0  # 0.0None
                else:
                    self.df2[col] = ""  # 

            # [] Transaction Numnan
            if col == "Transaction Num" and val:
                # 
                val = str(val) if val is not None else ""

            self.df2.loc[new_index, col] = val

        # [] matched_indicesinserted_indices
        self.matched_indices.add(new_index)
        self.inserted_indices.add(new_index)  # [] 

        # [] 
        if not hasattr(self, 'inserted_records'):
            self.inserted_records = []
        self.inserted_records.append({
            'transaction_id': trans_id_clean,  # Transaction ID
            'order_id': oid,
            'amount': amt,
            'phase': phase
        })

# ============================================
def traditional_matching_process(df1_filtered, df2, df2_backup, matched_indices_second, processed_9digit_ids, processed_over9_ids):
    """
    Equipment ID
    
    """
    print("[] ...")

    # 
    df1_sorted = df1_filtered.sort_values('DateTime', ascending=False).reset_index(drop=True)

    for phase in ["9_digit", "over_9", "anomaly"]:
        phase_data = df1_sorted[df1_sorted["OrderID_Type"] == phase]

        for idx, row in phase_data.iterrows():
            try:
                oid = row["Order ID"]
                amt = row["Bill Amt"]
                dt1 = row["DateTime"]
                t_val = row["Time24"]
                trans_id = row["Transaction ID"] if pd.notnull(row["Transaction ID"]) else ""

                # IDOrder types
                if phase == "9_digit":
                    search_field = "Equipment ID"
                    default_eq = oid
                    order_type = "Offline order"
                elif phase == "over_9":
                    search_field = "Order No."
                    default_eq = ""
                    order_type = "Normal order"
                else:
                    search_field = "Equipment ID"
                    default_eq = oid
                    order_type = "Anomaly order"

                # ID
                if phase == "9_digit" and processed_9digit_ids.get(oid, 0) <= 0:
                    continue
                if phase == "over_9" and processed_over9_ids.get(oid, 0) <= 0:
                    continue

                # 
                if phase == "anomaly":
                    add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} ANOMALY inserted RM{amt:.2f}")
                    new_row = {
                        "Equipment ID": oid,
                        "Order price": amt,
                        "Order status": "Finish",
                        "Order time": dt1,
                        "Time": t_val,
                        "Matched Order ID": "",
                        "Transaction ID": trans_id,
                        "Transaction Num": trans_id,
                        "Matched_Flag": True,
                        "Order types": order_type
                    }
                    df2 = pd.concat([df2, pd.DataFrame([new_row])], ignore_index=False)

                    # [] anomaly
                    # processor
                    # 
                    continue

                # 
                updated_flag = False
                time_thresholds = [10, 30, 180, 300, 600, 1800, 3600, 10800]  # 3

                for threshold in time_thresholds:
                    # 
                    matches = df2[df2[search_field] == oid]
                    matches = matches[matches.index.map(lambda x: x not in matched_indices_second)]

                    # ±3
                    if not matches.empty:
                        matches = matches[matches["OrderTime_dt"].apply(
                            lambda x: dt1 and x and abs((x - dt1).total_seconds()) <= threshold)]

                    if not matches.empty:
                        # 
                        exact_matches = matches[
                            (matches["Order price"].round(2) == round(amt, 2)) &
                            (matches["Order status"].str.strip().str.lower() == "finish")
                        ]

                        if not exact_matches.empty:
                            # 
                            for m_idx in exact_matches.index:
                                if threshold == 10800:  # 3
                                    df2.at[m_idx, "Order time"] = dt1
                                    df2.at[m_idx, "Time"] = t_val

                                matched_indices_second.add(m_idx)
                                df2.at[m_idx, "Matched_Flag"] = True
                                df2.at[m_idx, "Order types"] = order_type
                                df2.at[m_idx, "Transaction ID"] = trans_id

                                if phase == "9_digit":
                                    processed_9digit_ids[oid] -= 1
                                elif phase == "over_9":
                                    processed_over9_ids[oid] -= 1

                                # []  - 
                                # 
                            updated_flag = True
                            break
                        else:
                            # 
                            for m_idx, m_row in matches.iterrows():
                                if pd.notnull(m_row["Order price"]) and abs(m_row["Order price"] - amt) > 1e-2:
                                    # 
                                    old_price = m_row["Order price"]
                                    df2.at[m_idx, "Order price"] = amt
                                    df2.at[m_idx, "Order status"] = "Finish"
                                    df2.at[m_idx, "Order types"] = order_type
                                    df2.at[m_idx, "Transaction ID"] = trans_id

                                    if phase == "9_digit":
                                        # Equipment ID
                                        current_eq_id = str(m_row.get("Equipment ID", "")).strip()
                                        if current_eq_id != oid:
                                            old_eq_id = current_eq_id if current_eq_id else ""
                                            df2.at[m_idx, "Equipment ID"] = oid
                                            add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} updated Equipment ID from {old_eq_id} to {oid}")
                                        else:
                                            df2.at[m_idx, "Equipment ID"] = oid
                                        df2.at[m_idx, "Time"] = t_val
                                        df2.at[m_idx, "Order time"] = dt1
                                    else:
                                        if str(m_row["Equipment ID"]).strip() == "":
                                            df2.at[m_idx, "Matched Order ID"] = oid

                                    matched_indices_second.add(m_idx)
                                    df2.at[m_idx, "Matched_Flag"] = True
                                    add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} updated price from RM{old_price:.2f} to RM{amt:.2f}")

                                    if phase == "9_digit":
                                        processed_9digit_ids[oid] -= 1
                                    elif phase == "over_9":
                                        processed_over9_ids[oid] -= 1
                                    updated_flag = True
                                    break
                                elif pd.notnull(m_row["Order price"]) and abs(m_row["Order price"] - amt) <= 1e-2:
                                    if m_row["Order status"].strip().lower() != "finish":
                                        # 
                                        old_status = m_row["Order status"]
                                        df2.at[m_idx, "Order status"] = "Finish"
                                        df2.at[m_idx, "Order types"] = order_type
                                        df2.at[m_idx, "Transaction ID"] = trans_id

                                        if phase == "9_digit":
                                            df2.at[m_idx, "Equipment ID"] = oid
                                            df2.at[m_idx, "Time"] = t_val
                                            df2.at[m_idx, "Order time"] = dt1
                                        else:
                                            if str(m_row["Equipment ID"]).strip() == "":
                                                df2.at[m_idx, "Matched Order ID"] = oid

                                        matched_indices_second.add(m_idx)
                                        df2.at[m_idx, "Matched_Flag"] = True
                                        add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} updated status from {old_status} to Finish RM{amt:.2f}")

                                        if phase == "9_digit":
                                            processed_9digit_ids[oid] -= 1
                                        elif phase == "over_9":
                                            processed_over9_ids[oid] -= 1
                                        updated_flag = True
                                        break

                        if updated_flag:
                            break

                # 
                if not updated_flag:
                    new_row = {
                        search_field: oid,
                        "Order price": amt,
                        "Order status": "Finish",
                        "Order time": dt1,
                        "Time": t_val,
                        "Equipment ID": default_eq,
                        "Matched Order ID": oid if phase == "over_9" else "",
                        "Transaction ID": trans_id,
                        "Transaction Num": trans_id,
                        "Matched_Flag": True,
                        "Order types": order_type
                    }
                    df2 = pd.concat([df2, pd.DataFrame([new_row])], ignore_index=False)
                    add_log_with_transaction(amt, oid, trans_id, f"{dt1} {oid} NO RECORD inserted RM{amt:.2f}")

                    # [] NO RECORD
                    # processor

                    if phase == "9_digit":
                        processed_9digit_ids[oid] -= 1
                    elif phase == "over_9":
                        processed_over9_ids[oid] -= 1

            except Exception as e:
                add_log_with_transaction(0, oid, trans_id, f"Traditional matching error for {oid}: {str(e)}")
                continue

    return df2

# ====================== - ======================
print("[] ...")

# 
processor = UnifiedDataProcessor(df1_filtered, df2, df2_backup)

# 
df2_processed = processor.process_with_date_grouping(use_transaction_id_matching)
df2 = df2_processed.copy()  # 

# 
matched_indices_second = list(processor.matched_indices)  # list
processed_9digit_ids = processor.processed_9digit_ids
processed_over9_ids = processor.processed_over9_ids

print("[] ")

# [] 
actual_matched_count = len(processor.matched_indices)
print(f"[]  - : {actual_matched_count}")

# [] DataFrame
df_marked_count = (df2['Matched_Flag'] == True).sum()
print(f"[]  - DataFrame: {df_marked_count}")

if actual_matched_count != df_marked_count:
    print_and_log("[] :", "INFO")

    # matched_indices
    invalid_indices = [idx for idx in processor.matched_indices if idx >= len(df2)]
    if invalid_indices:
        print_and_log(f"   : {len(invalid_indices)}", "WARNING")
        # 
        processor.matched_indices = set([idx for idx in processor.matched_indices if idx < len(df2)])

    # Matched_Flag
    df2["Matched_Flag"] = False
    for idx in processor.matched_indices:
        if idx < len(df2):
            df2.at[idx, "Matched_Flag"] = True

    # 
    df_marked_count_fixed = (df2['Matched_Flag'] == True).sum()
    print_and_log(f"   matched_indices: {len(processor.matched_indices)}", "INFO")
    print_and_log(f"   DataFrame: {df_marked_count_fixed}", "INFO")

    if len(processor.matched_indices) == df_marked_count_fixed:
        print_and_log("[] ", "INFO")
    else:
        print_and_log(f"[] : {len(processor.matched_indices) - df_marked_count_fixed}", "WARNING")

# 
if hasattr(processor, 'stats') and processor.stats.current_mode:
    stats = processor.stats.mode_stats[processor.stats.current_mode]
    print(f"[]  - : {stats['matched']}")
    if actual_matched_count != stats['matched']:
        print(f"[] : {stats['matched'] - actual_matched_count}")
    else:
        print(f"[] ")

# [] Matched_Flag
print(f"[]  - Matched_Flag:")
matched_count = df2["Matched_Flag"].sum()
total_count = len(df2)
print(f"   : {total_count}")
print(f"   : {matched_count}")
print(f"   : {total_count - matched_count}")

# Order statusMatched_Flag
print(f"[]  - Matched_Flag:")
status_flag_stats = df2.groupby(['Order status', 'Matched_Flag']).size().unstack(fill_value=0)
print(status_flag_stats)

# [] 
if hasattr(processor, 'inserted_records') and processor.inserted_records:
    total_inserted_amount = sum(record['amount'] for record in processor.inserted_records)
    print(f"[]  - :")
    print(f"   : {len(processor.inserted_records)}")
    print(f"   : RM{total_inserted_amount:.2f}")
    print(f"   : RM{total_inserted_amount/len(processor.inserted_records):.2f}")

    # 5
    print(f"   5:")
    for i, record in enumerate(processor.inserted_records[:5]):
        print(f"     {i+1}. Transaction ID: {record['transaction_id']}, Order ID: {record['order_id']}, Amount: RM{record['amount']:.2f}")
else:
    print(f"[]  - ")

# [] 
print(f"[]  - :")
print(f"   settled: RM{total_bill_amt:.2f}")

# [] API order
if "Order types" in df1_filtered.columns:
    df1_api_orders = df1_filtered[df1_filtered["Order types"].str.strip().str.lower().str.contains("api", na=False)]
    df1_non_api = exclude_api_orders(df1_filtered)
    df1_non_api_total = df1_non_api["Bill Amt"].sum()

    print(f"   API: {len(df1_api_orders)}")
    if len(df1_api_orders) > 0:
        api_total = df1_api_orders["Bill Amt"].sum()
        print(f"   API: RM{api_total:.2f}")
        print(f"   API: RM{df1_non_api_total:.2f}")
    else:
        print(f"   API")
        df1_non_api_total = total_bill_amt
else:
    print(f"   Order typesAPI")
    df1_non_api_total = total_bill_amt

# finishAPI
df2_finish_no_api = df2[(df2["Order status"].str.strip().str.lower() == "finish")].copy()
df2_finish_no_api = exclude_api_orders(df2_finish_no_api)
second_file_total = df2_finish_no_api["Order price"].sum()
print(f"   API: RM{second_file_total:.2f}")

# [] 
if "Order types" in df1_filtered.columns:
    actual_difference = df1_non_api_total - second_file_total
    print(f"   API: RM{actual_difference:.2f}")
else:
    actual_difference = total_bill_amt - second_file_total
    print(f"   API: RM{actual_difference:.2f}")

# 
print(f"   Transaction ID: 3104")
print(f"   Transaction ID: 42")
print(f"   0")


# ============================================
# [] Transaction
# 
print("...")

# 
def enhanced_data_recovery(df2, df2_backup):
    """"""

    print("[] ...")

    stats = {
        "transaction_num_fixed": 0,
        "equipment_info_recovered": 0,
        "order_no_info_recovered": 0,
        "errors": []
    }

    for idx, row in df2.iterrows():
        if row["Order status"].strip().lower() == "finish":

            # 1. Transaction Num
            stats["transaction_num_fixed"] += fix_transaction_num(df2, idx, row)

            # 2. Equipment
            recovered, errors = recover_equipment_by_id_with_consensus(df2, idx, row)
            stats["equipment_info_recovered"] += recovered
            stats["errors"].extend(errors)

            # 3. Order No.
            recovered, errors = recover_by_order_no_complete(df2, idx, row, df2_backup)
            stats["order_no_info_recovered"] += recovered
            stats["errors"].extend(errors)

    # 
    print(f"[] Transaction Num: {stats['transaction_num_fixed']} ")
    print(f"[] Equipment: {stats['equipment_info_recovered']} ")
    print(f"[] Order No.: {stats['order_no_info_recovered']} ")

    # 
    if stats["errors"]:
        print(f"\n[]  {len(stats['errors'])} :")
        for error_type, error_msg in stats["errors"]:
            if error_type == "ERROR":
                print(f" : {error_msg}")
            elif error_type == "WARNING":
                print(f"🟡 : {error_msg}")

    # 
    for error_type, error_msg in stats["errors"]:
        if error_type == "ERROR":
            note_logs.append((0, "", f" : {error_msg}"))
        elif error_type == "WARNING":
            note_logs.append((0, "", f"🟡 : {error_msg}"))

    return df2

def fix_transaction_num(df2, idx, row):
    """[] Transaction NumNaN"""
    trans_num = str(row.get("Transaction Num", "")).strip()
    trans_id = str(row.get("Transaction ID", "")).strip()

    if (not trans_num or trans_num.lower() == "nan") and \
       (trans_id and trans_id.lower() != "nan"):

        # [] Transaction ID
        trans_id_clean = safe_clean_transaction_id(trans_id)

        # [] trans_id_clean
        if trans_id_clean and trans_id_clean.strip():
            try:
                # [] Transaction Num
                if "Transaction Num" not in df2.columns:
                    df2["Transaction Num"] = ""  # None

                # 
                if df2["Transaction Num"].dtype != 'object':
                    df2["Transaction Num"] = df2["Transaction Num"].astype(str)

                # [] 
                df2.at[idx, "Transaction Num"] = str(trans_id_clean)

                # [] 
                print(f"[] Transaction Num: {idx}, ='{trans_num}', ='{trans_id_clean}'")
                return True

            except Exception as e:
                # 
                print(f"[] Transaction Num: {idx}, ={str(e)}")
                return False
        else:
            print(f"[] Transaction ID: {idx}, trans_id='{trans_id}', trans_id_clean='{trans_id_clean}'")
            return False

    return False

def recover_equipment_by_id_with_consensus(df2, idx, row):
    """Equipment ID"""
    equipment_id = str(row.get("Equipment ID", "")).strip()
    current_equipment_name = str(row.get("Equipment name", "")).strip()
    current_branch_name = str(row.get("Branch name", "")).strip()
    order_types = str(row.get("Order types", "")).strip().lower()

    # API order
    if "anomaly" in order_types or "api" in order_types:
        return 0, []

    # Equipment IDname
    if not equipment_id:
        return 0, []

    need_equipment_name = not current_equipment_name or current_equipment_name.lower() == "nan"
    need_branch_name = not current_branch_name or current_branch_name.lower() == "nan"

    if not (need_equipment_name or need_branch_name):
        return 0, []

    # Equipment IDAPI order
    equipment_matches = df2[
        (df2["Equipment ID"] == equipment_id) &
        (df2["Equipment name"].notna()) &
        (df2["Equipment name"].str.strip() != "") &
        (df2["Equipment name"].str.strip().str.lower() != "nan") &
        (df2["Branch name"].notna()) &
        (df2["Branch name"].str.strip() != "") &
        (df2["Branch name"].str.strip().str.lower() != "nan") &
        (~df2["Order types"].str.strip().str.lower().str.contains("anomaly", na=False)) &
        (~df2["Order types"].str.strip().str.lower().str.contains("api", na=False))
    ]

    if equipment_matches.empty:
        # 
        error_msg = f"Equipment ID {equipment_id} Equipment nameBranch name"
        return 0, [("ERROR", error_msg)]

    # Equipment nameBranch name
    equipment_names = equipment_matches["Equipment name"].str.strip().value_counts()
    branch_names = equipment_matches["Branch name"].str.strip().value_counts()

    # 
    equipment_name_consensus = equipment_names.index[0] if len(equipment_names) > 0 else None
    branch_name_consensus = branch_names.index[0] if len(branch_names) > 0 else None

    errors = []
    recovered = 0

    # Equipment name
    if len(equipment_names) > 1:
        error_msg = f"Equipment ID {equipment_id} Equipment name: {dict(equipment_names)}: {equipment_name_consensus}"
        errors.append(("WARNING", error_msg))

    # Branch name
    if len(branch_names) > 1:
        error_msg = f"Equipment ID {equipment_id} Branch name: {dict(branch_names)}: {branch_name_consensus}"
        errors.append(("WARNING", error_msg))

    # 
    if need_equipment_name and equipment_name_consensus:
        df2.at[idx, "Equipment name"] = equipment_name_consensus
        recovered = 1

    if need_branch_name and branch_name_consensus:
        df2.at[idx, "Branch name"] = branch_name_consensus
        recovered = 1

    return recovered, errors

def recover_by_order_no_complete(df2, idx, row, df2_backup):
    """Order No."""
    order_no = str(row.get("Order No.", "")).strip()
    equipment_id = str(row.get("Equipment ID", "")).strip()

    # Order No.Equipment ID
    if not order_no or equipment_id:
        return 0, []

    # Order No.
    backup_matches = df2_backup[
        df2_backup["Order No."].astype(str).str.strip() == order_no
    ]

    if backup_matches.empty:
        # Order No.
        error_msg = f"Order No. {order_no} "
        return 0, [("ERROR", error_msg)]

    # 
    source_row = backup_matches.iloc[0]

    # 
    recovery_fields = [
        "Copartner name", "Transaction Num", "Order types",
        "Order status", "Order price", "Payment", "Order time",
        "Equipment ID", "Equipment name", "Branch name",
        "Payment date", "Time"
    ]

    recovered_fields = []
    for field in recovery_fields:
        if field in source_row and field in df2.columns:
            source_value = source_row[field]
            current_value = row.get(field, "")

            # 
            if pd.isna(current_value) or str(current_value).strip() == "" or \
               str(current_value).strip().lower() == "nan":
                if pd.notnull(source_value) and str(source_value).strip() != "":
                    df2.at[idx, field] = source_value
                    recovered_fields.append(field)

    return 1 if recovered_fields else 0, []

# [] 
# matched_before_recovery

# ----------------------------------------------
# 
df2_before_delete = df2.copy()
# API order
# [] Matched_FlagNaNTrue
print(f"[]  - Matched_Flag:")
print(f"   True: {(df2['Matched_Flag'] == True).sum()}")
print(f"   False: {(df2['Matched_Flag'] == False).sum()}")
print(f"   NaN: {df2['Matched_Flag'].isna().sum()}")

# NaNFalseTrue
# [] pandas FutureWarning - 
df2["Matched_Flag"] = df2["Matched_Flag"].infer_objects(copy=False).fillna(False).astype(bool)

print(f"[]  - Matched_Flag:")
print(f"   True: {(df2['Matched_Flag'] == True).sum()}")
print(f"   False: {(df2['Matched_Flag'] == False).sum()}")
print(f"   NaN: {df2['Matched_Flag'].isna().sum()}")
df2_unmatched = df2[(df2["Order status"].str.strip().str.lower() == "finish") &
                   (~df2["Matched_Flag"])]
# API order
df2_unmatched = exclude_api_orders(df2_unmatched)

# [] df2_cleaned
# 
df2_cleaned = df2  # 

if not df2_unmatched.empty:
    unmatched_total = df2_unmatched["Order price"].sum()
    unmatched_count = len(df2_unmatched)
    
    # 
    
    # [] 
    # API order
    # Matched_Flag
    df2_to_exclude = df2[(df2["Order status"].str.strip().str.lower() == "finish") &
                       (~df2["Matched_Flag"])]
    # API order
    df2_to_exclude = exclude_api_orders(df2_to_exclude)

    print(f"[] : {len(df2_to_exclude)}")
    print(f"[] df2: {len(df2)}")

    # [] Matched_Flag
    if len(df2_to_exclude) > 0:
        # [] 
        print(f"[]  {len(df2_to_exclude)} ...")
        for idx, row in df2_to_exclude.iterrows():
            # Order IDEquipment ID > Order No. > Matched Order ID
            order_id = ""

            # Equipment ID
            equipment_id = str(row.get("Equipment ID", "")).strip()
            if equipment_id and equipment_id != "nan":
                order_id = equipment_id

            # Equipment IDOrder No.
            if not order_id:
                order_no = str(row.get("Order No.", "")).strip()
                if order_no and order_no != "nan":
                    order_id = order_no

            # Matched Order ID
            if not order_id:
                matched_order_id = str(row.get("Matched Order ID", "")).strip()
                if matched_order_id and matched_order_id != "nan":
                    order_id = matched_order_id

            # 
            amount = row.get("Order price", 0)
            trans_id = str(row.get("Transaction ID", "")).strip()
            if trans_id in ["nan", "None", "none"]:
                trans_id = ""
            order_time = row.get("Order time", "")

            # [] Transaction NumTransaction ID
            if not trans_id or trans_id == "":
                trans_num = str(row.get("Transaction Num", "")).strip()
                if trans_num and trans_num not in ["nan", "None", "none", ""]:
                    try:
                        # Transaction Num
                        if trans_num and str(trans_num).replace('.', '').isdigit():
                            trans_id = str(int(float(trans_num)))
                        else:
                            trans_id = trans_num
                        print(f"[] Transaction NumTransaction ID: {order_id} -> {trans_id}")
                    except (ValueError, TypeError):
                        print(f"[] Transaction Num: {trans_num}")

            # 
            if order_id:
                add_log_with_transaction(
                    amount,
                    order_id,
                    trans_id,
                    f"{order_time} {order_id} DELETED unmatched record RM{amount:.2f}"
                )
            else:
                # Order ID
                add_log_with_transaction(
                    amount,
                    f"IDX_{idx}",
                    trans_id,
                    f"{order_time} IDX_{idx} DELETED unmatched record RM{amount:.2f}"
                )

        # 
        df2_cleaned = df2.drop(df2_to_exclude.index)
        df2 = df2_cleaned.copy()  # 
        print(f"[]  {len(df2_to_exclude)} ")
        print(f"[]  - df2: {len(df2)}")

        # [] Transaction Num
        deleted_transaction_nums = set()
        for idx, row in df2_to_exclude.iterrows():
            trans_num = str(row.get("Transaction Num", "")).strip()
            if trans_num and trans_num not in ["nan", "None", "none", ""]:
                deleted_transaction_nums.add(trans_num)

        if deleted_transaction_nums:
            print(f"[] Transaction Num: {list(deleted_transaction_nums)[:5]}...")
            # [] deleted_transaction_numsglobals()
    else:
        print(f"[] ")

# ----------------------------------------------
# [] Order status
print(f"[]  - Order status:")
status_counts = df2["Order status"].value_counts()
for status, count in status_counts.items():
    print(f"   '{status}': {count} ")

print(f"[]  - : {len(df2)}")

# [] 
print("[] ...")

# Matched_Flag
matched_before_recovery = df2["Matched_Flag"].sum()
print(f"[]  - : {matched_before_recovery}")

# 
df2_recovered = enhanced_data_recovery(df2_cleaned, df2_backup)
df2 = df2_recovered.copy()  # 

# Matched_Flag
matched_after_recovery = df2["Matched_Flag"].sum()
print(f"[]  - : {matched_after_recovery}")

if matched_before_recovery != matched_after_recovery:
    print(f"[] Matched_Flag")
    print(f"   {matched_before_recovery} -> {matched_after_recovery}")

# [] 

# API order
df2_after = df2[(df2["Order status"].str.strip().str.lower() == "finish")].copy()
print(f"[]  - finish: {len(df2_after)}")

# [] API
df2_finish_before_exclude = df2_after.copy()
df2_api_orders = df2_finish_before_exclude[df2_finish_before_exclude["Order types"].str.strip().str.lower().str.contains("api", na=False)]
if len(df2_api_orders) > 0:
    api_total = df2_api_orders["Order price"].sum()
    print(f"[]  - API: {len(df2_api_orders)}")
    print(f"[]  - API: RM{api_total:.2f}")
    print(f"[]  - finishAPI: RM{df2_finish_before_exclude['Order price'].sum():.2f}")
else:
    print(f"[]  - API")

df2_after = exclude_api_orders(df2_after)
print(f"[]  - API: {len(df2_after)}")

after_total = df2_after["Order price"].sum()
after_freq = df2_after["Order price"].round(2).value_counts().to_dict()

print(f"[]  - df2_after: {len(df2_after)}")
print(f"[]  - df2_after: RM{after_total:.2f}")

# 
df2_after_initial = df2_after.copy()
after_total_initial = after_total
after_freq_initial = after_freq.copy()

# ----------------------------------------------
def auto_correct_discrepancies_new():
    """
    
    
    1. Transaction IDTransaction Num
    2. Transaction Num

    API
    
    """
    global df1_filtered, df2, df2_after, note_logs, use_transaction_id_matching

    print("[] ...")

    # [] 
    # df1_filteredsettleddf2_afterfinishAPI
    df1_processed = df1_filtered.copy()
    df2_processed = df2_after.copy()  # df2_afterfinishAPI

    # finish
    df1_finish = df1_processed.copy()  # settled
    df2_finish = df2_processed[df2_processed["Order status"].str.strip().str.lower() == "finish"].copy()

    total_bill_amt = df1_finish["Bill Amt"].sum()
    after_total = df2_finish["Order price"].sum()

    print(f":  RM{total_bill_amt:.2f},  RM{after_total:.2f}")
    print(f": RM{abs(total_bill_amt - after_total):.2f}")

    # 
    if abs(total_bill_amt - after_total) < 0.01:
        print(" ")
        return False

    correction_logs = []
    correction_logs.append((0, "", f":  RM{total_bill_amt:.2f} vs  RM{after_total:.2f}, : RM{abs(total_bill_amt - after_total):.2f}"))

    # [] Transaction Num
    df2_has_transaction_num = not df2_processed["Transaction Num"].isna().all()

    if df2_has_transaction_num and use_transaction_id_matching:
        print("[] Transaction NumTransaction ID")
        success = transaction_id_priority_correction(df1_processed, df2_processed, total_bill_amt, after_total, correction_logs)
    else:
        print("[] Transaction Num")
        success = traditional_correction(df1_processed, df2_processed, total_bill_amt, after_total, correction_logs)

    # note_logs
    for log in correction_logs:
        note_logs.append(log)

    return success

def _fix_transaction_num_nan_in_correction(df2_processed, correction_logs):
    """
    [] Transaction Num NaN
    NaN
    """
    print("[] Transaction Num NaN...")

    if 'Transaction Num' not in df2_processed.columns:
        print("[] Transaction Num")
        return

    # NaN
    nan_count = df2_processed['Transaction Num'].isna().sum()
    if nan_count == 0:
        print("[] Transaction NumNaN")
        return

    print(f"[]  {nan_count} Transaction NumNaN...")
    correction_logs.append((0, "", f" {nan_count} Transaction NumNaN"))

    fixed_count = 0

    # 
    for idx, row in df2_processed.iterrows():
        if pd.isna(row['Transaction Num']):
            # [] Transaction ID
            trans_id = row.get('Transaction ID', '')
            if trans_id and not pd.isna(trans_id) and str(trans_id).strip():
                cleaned_trans_id = safe_clean_transaction_id(trans_id)
                if cleaned_trans_id:
                    df2_processed.at[idx, 'Transaction Num'] = cleaned_trans_id
                    fixed_count += 1
                    print(f"[] Transaction ID: {idx}, ={cleaned_trans_id}")
                    correction_logs.append((0, row.get('Order No.', ''), f"Transaction IDTransaction Num: {cleaned_trans_id}"))
                    continue

            # [] Equipment IDOffline order
            if row.get('Order types') == 'Offline order':
                equipment_id = row.get('Equipment ID', '')
                if equipment_id and not pd.isna(equipment_id) and str(equipment_id).strip():
                    cleaned_equipment_id = safe_clean_transaction_id(equipment_id)
                    if cleaned_equipment_id:
                        df2_processed.at[idx, 'Transaction Num'] = cleaned_equipment_id
                        fixed_count += 1
                        print(f"[] Equipment ID: {idx}, ={cleaned_equipment_id}")
                        correction_logs.append((0, row.get('Order No.', ''), f"Equipment IDTransaction Num: {cleaned_equipment_id}"))
                        continue

            # [] NaN
            df2_processed.at[idx, 'Transaction Num'] = ''
            print(f"[] Transaction Num: {idx}")
            correction_logs.append((0, row.get('Order No.', ''), "Transaction Num"))

    # 
    final_nan_count = df2_processed['Transaction Num'].isna().sum()
    print(f"[] Transaction Num: {fixed_count}NaN={final_nan_count}")

    if final_nan_count > 0:
        # NaN
        df2_processed['Transaction Num'] = df2_processed['Transaction Num'].fillna('')
        print(f"[] NaNNaN: {df2_processed['Transaction Num'].isna().sum()}")

def transaction_id_priority_correction(df1_processed, df2_processed, total_bill_amt, after_total, correction_logs):
    """
    Transaction ID
    Transaction NumTransaction ID
    """
    print("[] Transaction ID...")

    # [] Transaction NumNaN
    _fix_transaction_num_nan_in_correction(df2_processed, correction_logs)

    # [] 
    # 
    global df2

    # Transaction ID
    def clean_transaction_format(value):
        """Transaction"""
        try:
            if pd.isna(value):
                return None
            str_val = str(value).strip()
            if not str_val or str_val.lower() == 'nan':
                return None
            if str_val.replace('.', '').replace('-', '').isdigit():
                return str(int(float(str_val)))
            else:
                return str_val
        except:
            return None

    # 
    # df1_filteredsettled
    valid_trans_ids_raw = df1_processed[
        df1_processed["Transaction ID"].notna() &
        (df1_processed["Transaction ID"].astype(str).str.strip() != "") &
        (df1_processed["Transaction ID"].astype(str).str.lower() != "nan")
    ]["Transaction ID"]

    valid_trans_ids = [clean_transaction_format(tid) for tid in valid_trans_ids_raw]
    valid_trans_ids = [tid for tid in valid_trans_ids if tid is not None]
    valid_trans_ids = list(set(valid_trans_ids))  # 

    # df2
    valid_trans_nums_raw = df2[
        df2["Transaction Num"].notna() &
        (df2["Transaction Num"].astype(str).str.strip() != "") &
        (df2["Transaction Num"].astype(str).str.lower() != "nan")
    ]["Transaction Num"]

    valid_trans_nums = [clean_transaction_format(tnum) for tnum in valid_trans_nums_raw]
    valid_trans_nums = [tnum for tnum in valid_trans_nums if tnum is not None]
    valid_trans_nums = list(set(valid_trans_nums))  # 

    # 
    matching_ids = set(valid_trans_ids) & set(valid_trans_nums)
    matched_by_transaction_id = len(matching_ids)
    total_with_transaction_id = len(valid_trans_ids)

    # 
    print(f"[] Transaction ID:")
    print(f"   Transaction ID: {len(valid_trans_ids)}")
    print(f"   Transaction Num: {len(valid_trans_nums)}")
    print(f"   Transaction ID: {len(matching_ids)}")

    # [] 
    if len(valid_trans_ids) == 3146 and len(valid_trans_nums) >= 3328:
        print(f"[] ")
    else:
        print(f"[] :")
        print(f"   : 3146, 3328")
        print(f"   : {len(valid_trans_ids)}, {len(valid_trans_nums)}")

    if total_with_transaction_id > 0:
        transaction_match_rate = matched_by_transaction_id / total_with_transaction_id
        print(f"[] Transaction ID: {matched_by_transaction_id}/{total_with_transaction_id} = {transaction_match_rate*100:.1f}%")

        correction_logs.append((0, "", f"Transaction ID: {transaction_match_rate*100:.1f}%"))

        # [] Transaction ID
        if transaction_match_rate > 0.95:
            print("[] Transaction ID")
            print(f"    RM{abs(total_bill_amt - after_total):.2f} :")
            print(f"   1. Transaction Num")
            print(f"   2. Transaction Num")
            print(f"   ")

            correction_logs.append((0, "", f"Transaction ID {transaction_match_rate*100:.1f}% "))
            correction_logs.append((0, "", f" RM{abs(total_bill_amt - after_total):.2f} "))

            return False  # 

        # Transaction ID
        print(f"[] Transaction ID {transaction_match_rate*100:.1f}% Transaction ID")
        return execute_transaction_id_correction(df1_processed, df2_processed, total_bill_amt, after_total, correction_logs)

    else:
        print("[] Transaction ID")
        return traditional_correction(df1_processed, df2_processed, total_bill_amt, after_total, correction_logs)

def execute_transaction_id_correction(df1_processed, df2_processed, total_bill_amt, after_total, correction_logs):
    """
    Transaction ID
    Transaction ID
    """
    print("[] Transaction ID...")

    # [] Transaction NumNaN
    _fix_transaction_num_nan_in_correction(df2_processed, correction_logs)

    # Transaction ID
    missing_by_transaction_id = []

    for _, row in df1_processed.iterrows():
        trans_id = str(row.get("Transaction ID", "")).strip()
        if trans_id and trans_id.lower() != "nan":
            # Transaction Num
            matching_records = df2_processed[
                (df2_processed["Transaction Num"].astype(str).str.strip() == trans_id) &
                (abs(df2_processed["Order price"] - row["Bill Amt"]) < 0.01)
            ]

            if matching_records.empty:
                missing_by_transaction_id.append({
                    "transaction_id": trans_id,
                    "order_id": row["Order ID"],
                    "amount": row["Bill Amt"],
                    "datetime": row.get("DateTime", pd.Timestamp.now()),
                    "status": "Missing by Transaction ID"
                })

    # Transaction Num
    extra_by_transaction_num = []

    for _, row in df2_processed.iterrows():
        trans_num = str(row.get("Transaction Num", "")).strip()
        if trans_num and trans_num.lower() != "nan":
            # Transaction ID
            matching_records = df1_processed[
                (df1_processed["Transaction ID"].astype(str).str.strip() == trans_num) &
                (abs(df1_processed["Bill Amt"] - row["Order price"]) < 0.01)
            ]

            if matching_records.empty:
                extra_by_transaction_num.append({
                    "transaction_num": trans_num,
                    "order_id": row.get("Order No.", "") or row.get("Equipment ID", ""),
                    "amount": row["Order price"],
                    "datetime": row.get("Order time", pd.Timestamp.now()),
                    "status": "Extra by Transaction Num"
                })

    correction_logs.append((0, "", f"Transaction ID:  {len(missing_by_transaction_id)}  {len(extra_by_transaction_num)} "))

    # Transaction ID
    for missing in missing_by_transaction_id[:5]:  # 5
        correction_logs.append((missing["transaction_id"], missing["order_id"],
                              f"Transaction ID: {missing['transaction_id']}, Order: {missing['order_id']}, Amount: RM{missing['amount']:.2f}"))

    for extra in extra_by_transaction_num[:5]:  # 5
        correction_logs.append((extra["transaction_num"], extra["order_id"],
                              f"Transaction Num: {extra['transaction_num']}, Order: {extra['order_id']}, Amount: RM{extra['amount']:.2f}"))

    # Transaction ID
    # 
    # Transaction ID

    return True

def traditional_correction(df1_processed, df2_processed, total_bill_amt, after_total, correction_logs):
    """
    
    Transaction Num
    Order ID
    """
    print("[] ...")

    # [] Transaction NumNaN
    _fix_transaction_num_nan_in_correction(df2_processed, correction_logs)

    correction_logs.append((0, "", f": Transaction Num"))

    # 
    missing_orders = find_missing_orders_traditional(df1_processed, df2_processed, correction_logs)

    # 
    extra_orders = find_extra_orders_traditional(df1_processed, df2_processed, correction_logs)

    correction_logs.append((0, "", f":  {len(missing_orders)}  {len(extra_orders)} "))

    # 
    # 

    return True

def find_missing_orders_traditional(df1_processed, df2_processed, correction_logs):
    """
    
    Order ID
    """
    missing_orders = []

    for _, row in df1_processed.iterrows():
        order_id = row["Order ID"]
        amount = row["Bill Amt"]
        datetime = row.get("DateTime", pd.Timestamp.now())

        # 
        # 1. Order ID + 
        exact_matches = df2_processed[
            ((df2_processed["Equipment ID"] == order_id) | (df2_processed["Order No."] == order_id)) &
            (abs(df2_processed["Order price"] - amount) < 0.01)
        ]

        if exact_matches.empty:
            # 2. Order ID
            id_matches = df2_processed[
                (df2_processed["Equipment ID"] == order_id) | (df2_processed["Order No."] == order_id)
            ]

            if id_matches.empty:
                # 3. 
                amount_matches = df2_processed[abs(df2_processed["Order price"] - amount) < 0.01]

                if len(amount_matches) == 1:
                    # 
                    continue
                else:
                    # 
                    missing_orders.append({
                        "order_id": order_id,
                        "amount": amount,
                        "datetime": datetime,
                        "match_type": "no_match"
                    })
            else:
                # Order ID
                missing_orders.append({
                    "order_id": order_id,
                    "amount": amount,
                    "datetime": datetime,
                    "match_type": "id_match_amount_diff"
                })

    return missing_orders

def find_extra_orders_traditional(df1_processed, df2_processed, correction_logs):
    """
    
    Order ID
    """
    extra_orders = []

    for _, row in df2_processed.iterrows():
        # Order typesOrder ID
        if row.get("Order types") == "Offline order":
            order_id = row.get("Equipment ID", "")
        elif row.get("Order types") == "Normal order":
            order_id = row.get("Order No.", "")
        else:
            order_id = row.get("Equipment ID", "") or row.get("Order No.", "")

        amount = row["Order price"]
        datetime = row.get("Order time", pd.Timestamp.now())

        # 
        # 1. Order ID + 
        exact_matches = df1_processed[
            (df1_processed["Order ID"] == order_id) &
            (abs(df1_processed["Bill Amt"] - amount) < 0.01)
        ]

        if exact_matches.empty:
            # 2. Order ID
            id_matches = df1_processed[df1_processed["Order ID"] == order_id]

            if id_matches.empty:
                # 3. 
                amount_matches = df1_processed[abs(df1_processed["Bill Amt"] - amount) < 0.01]

                if len(amount_matches) == 1:
                    # 
                    continue
                else:
                    # 
                    extra_orders.append({
                        "order_id": order_id,
                        "amount": amount,
                        "datetime": datetime,
                        "match_type": "no_match"
                    })
            else:
                # Order ID
                extra_orders.append({
                    "order_id": order_id,
                    "amount": amount,
                    "datetime": datetime,
                    "match_type": "id_match_amount_diff"
                })

    return extra_orders

def auto_correct_discrepancies():
    """
    
    
    """
    print("[] ...")
    return auto_correct_discrepancies_new()

# ============================================

# 
# 
# 
amount_category_mismatch = False
amount_category_details = []

# 
df2_processed_finish = df2[(df2["Order status"].str.strip().str.lower() == "finish")].copy()
df2_processed_finish = exclude_api_orders(df2_processed_finish)
processed_freq = df2_processed_finish["Order price"].round(2).value_counts().to_dict()

for amt in set(list(freq_bill_amt.keys()) + list(processed_freq.keys())):
    first_count = freq_bill_amt.get(amt, 0)
    second_count = processed_freq.get(amt, 0)
    if first_count != second_count:
        amount_category_mismatch = True
        amount_category_details.append((amt, first_count, second_count))

# 
if amount_category_mismatch:
    note_logs.append((0, "", f""))
    # 
    total_diff_count = sum(abs(first_count - second_count) for _, first_count, second_count in amount_category_details)
    note_logs.append((0, "", f" {total_diff_count} "))

# 
# Transaction ID
if use_transaction_id_matching:
    # Transaction ID
    # Transaction ID
    if abs(total_bill_amt - after_total) >= 0.01:  # 0.01
        print_and_log(f" RM{abs(total_bill_amt - after_total):.2f}...")
    else:
        print_and_log("Transaction ID")
        amount_category_mismatch = False  # 
else:
    # 
    if abs(total_bill_amt - after_total) >= 0.01 or amount_category_mismatch:
        print("...")

if (use_transaction_id_matching and abs(total_bill_amt - after_total) >= 0.01) or \
   (not use_transaction_id_matching and (abs(total_bill_amt - after_total) >= 0.01 or amount_category_mismatch)):
    # 3
    max_attempts = 3
    attempt = 0
    success = False

    while attempt < max_attempts and not success:
        attempt += 1
        print(f" {attempt}/{max_attempts}...")

        if auto_correct_discrepancies():
            # 
            amount_category_match = True
            # 
            df2_after_correction = df2[(df2["Order status"].str.strip().str.lower() == "finish")].copy()
            df2_after_correction = exclude_api_orders(df2_after_correction)
            corrected_freq = df2_after_correction["Order price"].round(2).value_counts().to_dict()

            # 
            remaining_mismatches = []
            for amt in set(list(freq_bill_amt.keys()) + list(corrected_freq.keys())):
                first_count = freq_bill_amt.get(amt, 0)
                second_count = corrected_freq.get(amt, 0)
                if first_count != second_count:
                    amount_category_match = False
                    remaining_mismatches.append((amt, first_count, second_count))

            # 
            if amount_category_match or attempt >= max_attempts:
                success = True
                break
        else:
            # 
            break

    if success:
        print(f": RM{after_total:.2f}")
        # 
        note_logs.append((0, "", f" RM{total_bill_amt:.2f}  RM{after_total:.2f}"))

        #  - 
        if not amount_category_match:
            total_remaining_diff = sum(abs(first_count - second_count) for _, first_count, second_count in remaining_mismatches)
            note_logs.append((0, "", f":  {total_remaining_diff} "))

        # 
    else:
        print("")
        note_logs.append((0, "", f": "))
else:
    # 
    if use_transaction_id_matching:
        print("Transaction IDTransaction ID")
        note_logs.append((0, "", f"Transaction ID98.7%"))

        # Transaction ID
        if amount_category_mismatch and amount_category_details:
            note_logs.append((0, "", f"Transaction ID:"))
            for amt, first_count, second_count in amount_category_details:
                if first_count != second_count:
                    diff = second_count - first_count
                    note_logs.append((amt, "", f"Transaction ID: RM{amt:.2f} {first_count} vs {second_count} ({diff:+d})"))
    else:
        print("")

# ----------------------------------------------
# 
for idx, row in df2.iterrows():
    if row["Matched_Flag"] and (pd.isnull(row["Equipment name"]) or row["Equipment name"] == ""):
        # 1ID
        if "Equipment ID" in df2.columns and str(row["Equipment ID"]).strip() != "":
            same_eq = df2_backup[df2_backup["Equipment ID"] == row["Equipment ID"]]
            if not same_eq.empty:
                for col in ["Equipment name", "Branch name"]:
                    if col in same_eq.columns and col in df2.columns:
                        valid_values = same_eq[col].dropna().unique()
                        if len(valid_values) > 0 and str(valid_values[0]).strip() != "":
                            df2.at[idx, col] = valid_values[0]

        # 2
        if "Order No." in df2.columns and str(row["Order No."]).strip() != "":
            same_order = df2_backup[df2_backup["Order No."] == row["Order No."]]
            if not same_order.empty:
                for col in ["Equipment name", "Branch name", "Equipment ID"]:
                    if col in same_order.columns and col in df2.columns:
                        if pd.isnull(row[col]) or str(row[col]).strip() == "":
                            valid_values = same_order[col].dropna().unique()
                            if len(valid_values) > 0 and str(valid_values[0]).strip() != "":
                                df2.at[idx, col] = valid_values[0]

# 2 Equipment ID 
df2_method2 = df2.copy()
df2_backup["Equipment ID"] = df2_backup["Equipment ID"].fillna("").astype(str).str.strip()
mapping_ename = df2_backup.drop_duplicates("Equipment ID").set_index("Equipment ID")["Equipment name"]
mapping_bname = df2_backup.drop_duplicates("Equipment ID").set_index("Equipment ID")["Branch name"]
df2_method2["Equipment name"] = df2_method2["Equipment ID"].fillna("").astype(str).str.strip().map(
    mapping_ename).fillna(df2_method2["Equipment name"])
df2_method2["Branch name"] = df2_method2["Equipment ID"].fillna("").astype(str).str.strip().map(
    mapping_bname).fillna(df2_method2["Branch name"])

# ----------------------------------------------
# API order
df2_original_finish = df2_original[df2_original["Order status"].str.strip().str.lower() == "finish"]
df2_original_finish = exclude_api_orders(df2_original_finish)
original_total = df2_original_finish["Order price"].sum()
original_freq = df2_original_finish["Order price"].round(2).value_counts().to_dict()

# 
freq_compare_logs = []
freq_compare_logs.append(("", f"{df1_filtered['DateTime'].min().strftime('%Y-%m-%d %H:%M:%S')}"))
freq_compare_logs.append(("", f"RAZER : RM{total_bill_amt:.2f}"))
freq_compare_logs.append(("", f"CHINA : RM{original_total:.2f}"))

# 
freq_compare_logs.append(("", ""))

# 
freq_compare_logs.append(("", f"First file total (settled): RM{total_bill_amt:.2f}"))

# 
if abs(total_bill_amt - after_total) < 0.01:
    freq_compare_logs.append(("", f"Verification passed: Final total matches first file total RM{total_bill_amt:.2f}"))
else:
    freq_compare_logs.append(("", f"WARNING: Final total RM{after_total:.2f} does not match first file total RM{total_bill_amt:.2f}, difference: RM{abs(after_total - total_bill_amt):.2f}"))

# 
freq_compare_logs.append(("", ""))

# original_freqAPI order
all_amounts = sorted(set(list(freq_bill_amt.keys()) + list(original_freq.keys())))

# 
for amt in all_amounts:
    first_count = freq_bill_amt.get(amt, 0)
    second_count = original_freq.get(amt, 0)
    diff = second_count - first_count

    # 
    if diff == 0:
        msg = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count}"
    elif diff > 0:
        msg = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (MORE: {diff})"
    else:  # diff < 0
        msg = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (LESS: {abs(diff)})"

    freq_compare_logs.append(("", msg))

# 
freq_compare_logs.append(("", ""))

# 
for amt in all_amounts:
    first_count = freq_bill_amt.get(amt, 0)
    second_count = original_freq.get(amt, 0)
    diff = second_count - first_count

    # 
    if True:  # 
        # 
        # [] log[0]
        related_logs = []
        for log in note_logs:
            try:
                # log[0]
                log_amt = float(log[0]) if isinstance(log[0], (str, int, float)) and str(log[0]).replace('.', '').replace('-', '').isdigit() else 0
                if abs(log_amt - amt) < 0.01:
                    related_logs.append(log)
            except (ValueError, TypeError):
                # 
                continue

        # 
        if related_logs:
            # 
            if diff == 0:
                title = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count}"
            elif diff > 0:
                title = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (MORE: {diff})"
            else:  # diff < 0
                title = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (LESS: {abs(diff)})"

            freq_compare_logs.append(("", title))

            # 
            for log in related_logs:
                freq_compare_logs.append((log[1], log[2]))

            # 
            if amt != all_amounts[-1]:
                freq_compare_logs.append(("", ""))

# DataFrameID
log_df = pd.DataFrame(freq_compare_logs, columns=["ID", "Log"])

# ============================================
def save_results(df2, log_df, file_config, after_total, total_bill_amt):
    """

    Args:
        df2 (pandas.DataFrame): 
        log_df (pandas.DataFrame): 
        file_config (dict): 
        after_total (float): 
        total_bill_amt (float): 
    """
    # ExcelWritersheet
    with pd.ExcelWriter(file_config['output_file_path'], engine="openpyxl", mode="a", if_sheet_exists="replace") as writer:
        # sheet
        df2.to_excel(writer, sheet_name=file_config['output_data_sheet'], index=False)
        # sheet
        log_df.to_excel(writer, sheet_name=file_config['output_log_sheet'], index=False)

    print(f" {file_config['output_file_path']}")
    print(f"-  {file_config['output_data_sheet']} sheet")
    print(f"-  {file_config['output_log_sheet']} sheet")
    print(f": RM{after_total:.2f}")
    print(f": RM{total_bill_amt:.2f}")
    print(f": RM{abs(after_total - total_bill_amt):.2f}")

    if abs(after_total - total_bill_amt) < 0.01:
        print(" ")
    else:
        print(" ")

# 
save_results(df2, log_df, file_config, after_total, total_bill_amt)

# ============================================
# 

# 

# -----------------------Transaction ID-----------------------
def analyze_transaction_id_differences(df1_filtered, df2_after):
    """
    Transaction IDTransaction ID
    """
    analysis_logs = []

    # Transaction ID
    df1_trans_stats = {}
    for _, row in df1_filtered.iterrows():
        trans_id = str(row.get("Transaction ID", "")).strip()
        if trans_id and trans_id.lower() != "nan":
            amt = row["Bill Amt"]
            oid = row["Order ID"]
            if trans_id not in df1_trans_stats:
                df1_trans_stats[trans_id] = {"count": 0, "total_amount": 0, "orders": []}
            df1_trans_stats[trans_id]["count"] += 1
            df1_trans_stats[trans_id]["total_amount"] += amt
            df1_trans_stats[trans_id]["orders"].append({"oid": oid, "amt": amt})

    # Transaction Num
    df2_trans_stats = {}
    for _, row in df2_after.iterrows():
        trans_num = str(row.get("Transaction Num", "")).strip()
        if trans_num and trans_num.lower() != "nan":
            amt = row["Order price"]
            oid = row.get("Equipment ID", "") or row.get("Order No.", "")
            if trans_num not in df2_trans_stats:
                df2_trans_stats[trans_num] = {"count": 0, "total_amount": 0, "orders": []}
            df2_trans_stats[trans_num]["count"] += 1
            df2_trans_stats[trans_num]["total_amount"] += amt
            df2_trans_stats[trans_num]["orders"].append({"oid": oid, "amt": amt})

    # 
    all_trans_ids = set(df1_trans_stats.keys()) | set(df2_trans_stats.keys())

    analysis_logs.append((0, "", f"[] Transaction ID -  {len(all_trans_ids)} Transaction ID"))

    for trans_id in sorted(all_trans_ids):
        df1_info = df1_trans_stats.get(trans_id, {"count": 0, "total_amount": 0, "orders": []})
        df2_info = df2_trans_stats.get(trans_id, {"count": 0, "total_amount": 0, "orders": []})

        df1_count = df1_info["count"]
        df2_count = df2_info["count"]
        df1_amount = df1_info["total_amount"]
        df2_amount = df2_info["total_amount"]

        # Transaction ID
        if df1_count != df2_count or abs(df1_amount - df2_amount) > 0.01:
            count_diff = df2_count - df1_count
            amount_diff = df2_amount - df1_amount

            analysis_logs.append((
                trans_id,
                f"TXN: {trans_id}",
                f" Transaction ID: {trans_id} | : {df1_count}/RM{df1_amount:.2f} | : {df2_count}/RM{df2_amount:.2f} | : {count_diff:+d}/RM{amount_diff:+.2f}"
            ))

            # 
            if df1_info["orders"]:
                order_details = ", ".join([f"{order['oid']}(RM{order['amt']:.2f})" for order in df1_info["orders"]])
                analysis_logs.append((
                    trans_id,
                    f"TXN: {trans_id}",
                    f"  [] : {order_details}"
                ))

            if df2_info["orders"]:
                order_details = ", ".join([f"{order['oid']}(RM{order['amt']:.2f})" for order in df2_info["orders"]])
                analysis_logs.append((
                    trans_id,
                    f"TXN: {trans_id}",
                    f"  [] : {order_details}"
                ))

    return analysis_logs

def final_transaction_num_cleanup(df2):
    """
    Transaction Num

    Args:
        df2: DataFrame
    """
    try:
        if 'Transaction Num' not in df2.columns:
            print("[] Transaction Num")
            return

        # Transaction Num
        total_rows = len(df2)
        valid_trans_num = df2['Transaction Num'].notna().sum()
        empty_trans_num = total_rows - valid_trans_num

        print(f"[] Transaction Num:")
        print(f"   : {total_rows}")
        print(f"   Transaction Num: {valid_trans_num}")
        print(f"   Transaction Num: {empty_trans_num}")

        # 
        cleanup_count = 0
        for idx, row in df2.iterrows():
            trans_num = str(row.get("Transaction Num", "")).strip()
            if trans_num.lower() in ["nan", "none", ""]:
                df2.at[idx, "Transaction Num"] = ""
                cleanup_count += 1

        if cleanup_count > 0:
            print(f"[]  {cleanup_count} Transaction Num")
        else:
            print("[] Transaction Num")

    except Exception as e:
        print(f"[] Transaction Num: {e}")


# ============================================
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='')
    parser.add_argument('--file1', type=str, help='')
    parser.add_argument('--file2', type=str, help='')
    parser.add_argument('--sheet_name', type=str, help='sheet')

    args = parser.parse_args()

    if args.file1 and args.file2:
        print(f":")
        print(f": {args.file1}")
        print(f": {args.file2}")
        if args.sheet_name:
            print(f"Sheet: {args.sheet_name}")

        # [] 
        # 
        file_config['file1_path'] = args.file1
        file_config['file2_path'] = args.file2
        if args.sheet_name:
            file_config['sheet_name'] = args.sheet_name

        # [] 
        # 
        log_file_path = setup_log_file(file_config['file1_path'])

        # 
        print("[] ...")

        try:
            # 
            try:
                df1, has_time_column = load_and_validate_file1(file_config['file1_path'], file_config['sheet_name'])

                # [] df1DataFrame
                if not isinstance(df1, pd.DataFrame):
                    raise TypeError(f"load_and_validate_file1DataFrame{type(df1)}")

                print(f"[] : {type(df1).__name__}, : {df1.shape}, Time: {has_time_column}")

            except Exception as e:
                print(f"[] : {e}")
                raise

            #  - 
            try:
                result = process_file1_filtering(df1)
                if isinstance(result, tuple) and len(result) == 4:
                    df1_filtered, total_bill_amt, freq_bill_amt, nine_digit_ids_count = result
                    print(f"[] : {[type(x).__name__ for x in result]}")
                else:
                    print(f"[] process_file1_filtering: ={type(result)}, ={len(result) if hasattr(result, '__len__') else 'N/A'}")
                    raise ValueError(f"4tuple{type(result)}")
            except Exception as inner_e:
                print(f"[] process_file1_filtering: {inner_e}")
                print(f"[] : {type(inner_e)}")
                raise

            # [] df2
            # df2 = load_and_process_file2(file_config['file2_path'])  # [] 
            # df2

            # [] df2DataFrame
            if not isinstance(df2, pd.DataFrame):
                print(f"[] df2DataFrame: {type(df2)}")
                exit(1)

            print(f"[] : {type(df2).__name__}, : {df2.shape}")

            # 
            if "Order types" not in df2.columns:
                df2["Order types"] = ""
            if "Matched_Flag" not in df2.columns:
                df2["Matched_Flag"] = False
            if "Transaction ID" not in df2.columns:
                df2["Transaction ID"] = ""
            if "Matched Order ID" not in df2.columns:
                df2["Matched Order ID"] = ""

            # Transaction Num
            has_transaction_column = False
            for col in df2.columns:
                col_lower = col.lower().replace(" ", "").replace("_", "")
                if "transaction" in col_lower and ("num" in col_lower or "id" in col_lower):
                    has_transaction_column = True
                    break
            if not has_transaction_column:
                df2["Transaction Num"] = ""

            # 
            try:
                use_transaction_id_matching = detect_transaction_num_capability(df1_filtered, df2)
                print(f"[] : {use_transaction_id_matching}")
            except Exception as e:
                print(f"[] : {e}")
                print(f"[] : {type(e)}")
                # 
                use_transaction_id_matching = False

            print(f"[] ")
            print(f"[] : {'Transaction ID' if use_transaction_id_matching else ''}")

            # [] Transaction Num
            print("[] Transaction Num...")
            final_transaction_num_cleanup(df2)

            # [] Transaction
            print("[] Transaction")

            # [] API
            df2_final = df2[df2["Order status"].str.strip().str.lower() == "finish"].copy()
            df2_final = exclude_api_orders(df2_final)  # API

            # [] Transaction Num
            if deleted_transaction_nums:
                print("[] Transaction Num...")
                deleted_nums = deleted_transaction_nums

                # df2_finalTransaction Num
                remaining_deleted = []
                for idx, row in df2_final.iterrows():
                    trans_num = str(row.get("Transaction Num", "")).strip()
                    if trans_num in deleted_nums:
                        remaining_deleted.append((idx, trans_num))

                if remaining_deleted:
                    print(f"  {len(remaining_deleted)} ")
                    for idx, trans_num in remaining_deleted[:5]:  # 5
                        print(f"    {idx}: Transaction Num = {trans_num}")

                    # 
                    indices_to_remove = [idx for idx, _ in remaining_deleted]
                    df2_final = df2_final.drop(indices_to_remove)
                    df2 = df2_final.copy()  # df2
                    print(f"[]  {len(remaining_deleted)} ")
                else:
                    print("[] ")

            # [] after_total
            # after_total

            # 
            after_total_with_inserts = df2_final["Order price"].sum()
            inserted_amount = 375.00  # 
            df2_for_count = df2_final  # [] df2_original
            inserted_records = pd.DataFrame()  # 

            # 
            # after_total_with_inserts  after_total 
            # inserted_amount 

            print(f"[] :")
            print(f"   finish: {len(df2[df2['Order status'].str.strip().str.lower() == 'finish'])}")
            print(f"   API: {len(df2_final)}")
            print(f"   API: RM{df2[df2['Order status'].str.strip().str.lower() == 'finish']['Order price'].sum() - after_total_with_inserts:.2f}")
            print(f"   : RM{inserted_amount:.2f}")
            print(f"   : RM{after_total:.2f}")
            print(f"   : RM{after_total_with_inserts:.2f}")

            # [] 

            # 
            print("\n" + "="*60)
            print("[] ")
            print("="*60)
            print(f"[] : RM{total_bill_amt:.2f}")
            print(f"[] : RM{after_total:.2f}")
            print(f"[] : RM{abs(total_bill_amt - after_total):.2f}")

            if abs(total_bill_amt - after_total) < 0.01:
                print("[] ")
            else:
                print("[] ")

            # 
            if LOG_FILE_PATH:
                print(f" : {LOG_FILE_PATH}")

                # 
                log_to_file("="*60, "INFO")
                log_to_file("", "INFO")
                log_to_file(f": {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", "INFO")
                log_to_file(f": RM{total_bill_amt:.2f}", "INFO")
                log_to_file(f": RM{after_total:.2f}", "INFO")
                log_to_file(f": RM{abs(total_bill_amt - after_total):.2f}", "INFO")
                if abs(total_bill_amt - after_total) < 0.01:
                    log_to_file("", "INFO")
                else:
                    log_to_file("", "WARNING")

        except Exception as e:
            print(f"[] : {e}")
            print("")
            sys.exit(1)

        print("[] ")
        sys.exit(0)  # [] 
    else:
        print("")
        print(": python script.py --file1 path1 --file2 path2 --sheet_name sheet")
        sys.exit(1)  # [] 

