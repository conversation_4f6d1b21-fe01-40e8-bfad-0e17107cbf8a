# -*- coding: utf-8 -*-
"""
测试scripts/data_import_optimized.py是否正确集成智能列名映射器
"""

import os
import sys
import pandas as pd
import tempfile
from datetime import datetime

def test_script_integration():
    """测试脚本集成"""
    print("🔍 测试scripts/data_import_optimized.py集成")
    print("=" * 80)
    
    try:
        # 导入DataImportProcessor
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from data_import_optimized import DataImportProcessor
        
        print("✅ 成功导入DataImportProcessor")
        
        # 创建测试数据（包含Transaction Num）
        test_data = {
            'Copartner name': ['商户A', '商户B', '商户C'],
            'Order No.': ['ORD001', 'ORD002', 'ORD003'],
            'Transaction Num': ['TXN001', 'TXN002', 'TXN003'],  # 关键测试列
            'Order types': ['Normal', 'Api order', 'Normal'],
            'Order status': ['Finished', 'Finished', 'Pending'],
            'Order price': [10.50, 25.00, 15.75],
            'Payment': [10.50, 25.00, 15.75],
            'Order time': ['2025-06-30 10:00:00', '2025-06-30 11:00:00', '2025-06-30 12:00:00'],
            'Equipment ID': ['EQ001', 'EQ002', 'EQ003'],
            'Equipment name': ['设备A', '设备B', '设备C'],
            'Branch name': ['分店A', '分店B', '分店C'],
            'Payment date': ['2025-06-30', '2025-06-30', '2025-06-30'],
            'User name': ['用户A', '用户B', '用户C'],
            'Time': ['10:00', '11:00', '12:00'],
            'Matched Order ID': ['MATCH001', 'MATCH002', None],
            'OrderTime_dt': ['2025-06-30 10:00:00', '2025-06-30 11:00:00', '2025-06-30 12:00:00'],
            
            # 应该被忽略的额外列
            'Serial number': ['001', '002', '003'],
            'Transaction ID': ['2951553687', '2951553688', '2951553689'],
            'Merchant name': ['Merchant A', 'Merchant B', 'Merchant C'],
        }
        
        df = pd.DataFrame(test_data)
        
        print(f"\n📊 测试数据:")
        print(f"  总列数: {len(df.columns)}")
        print(f"  Transaction Num数据: {list(df['Transaction Num'])}")
        
        # 创建处理器实例
        processor = DataImportProcessor()
        
        print(f"\n🔧 测试列名标准化...")
        
        # 测试列名标准化方法
        df_standardized = processor._standardize_column_names(df)
        
        print(f"\n📊 标准化结果:")
        print(f"  原始列数: {len(df.columns)}")
        print(f"  处理后列数: {len(df_standardized.columns)}")
        
        print(f"\n📋 处理后的列名:")
        for i, col in enumerate(df_standardized.columns, 1):
            highlight = " ← Transaction_Num!" if col == 'Transaction_Num' else ""
            print(f"  {i:2d}. {col}{highlight}")
        
        # 关键验证：Transaction_Num列是否存在
        if 'Transaction_Num' in df_standardized.columns:
            print(f"\n✅ Transaction_Num列存在于处理结果中")
            
            # 检查数据内容
            transaction_num_data = df_standardized['Transaction_Num'].tolist()
            print(f"📋 Transaction_Num数据: {transaction_num_data}")
            
            # 验证数据完整性
            original_data = ['TXN001', 'TXN002', 'TXN003']
            if transaction_num_data == original_data:
                print(f"✅ Transaction_Num数据完整保留")
                return True
            else:
                print(f"❌ Transaction_Num数据不一致")
                print(f"  原始: {original_data}")
                print(f"  处理后: {transaction_num_data}")
                return False
        else:
            print(f"\n❌ Transaction_Num列不存在于处理结果中")
            print(f"📋 可用列: {list(df_standardized.columns)}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入DataImportProcessor失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_intelligent_mapper_import():
    """测试智能映射器导入"""
    print(f"\n🔧 测试智能映射器导入:")
    print("=" * 50)
    
    try:
        # 模拟scripts目录中的导入路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        mapper_path = os.path.join(os.path.dirname(current_dir), '01_主程序')
        sys.path.insert(0, mapper_path)
        
        from intelligent_column_mapper import IntelligentColumnMapper
        
        print(f"✅ 成功导入IntelligentColumnMapper")
        print(f"📁 导入路径: {mapper_path}")
        
        # 测试创建实例
        mapper = IntelligentColumnMapper()
        print(f"✅ 成功创建映射器实例")
        
        # 测试用户指定列列表
        test_data = {
            'Copartner name': ['商户A'],
            'Order No.': ['ORD001'],
            'Transaction Num': ['TXN001'],  # 关键测试
            'Order price': [10.50],
            'Equipment ID': ['EQ001'],
            'Serial number': ['001'],  # 应该被忽略
        }
        
        df = pd.DataFrame(test_data)
        df_mapped, analysis = mapper.apply_intelligent_mapping(df)
        
        print(f"\n📊 映射测试结果:")
        print(f"  原始列数: {analysis['total_columns']}")
        print(f"  保留列数: {len(df_mapped.columns)}")
        print(f"  忽略列数: {len(analysis['ignored_columns'])}")
        
        if 'Transaction_Num' in df_mapped.columns:
            print(f"✅ Transaction_Num正确映射")
            return True
        else:
            print(f"❌ Transaction_Num映射失败")
            return False
            
    except ImportError as e:
        print(f"❌ 导入智能映射器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 智能映射器测试异常: {e}")
        return False

def main():
    """主函数"""
    success1 = test_intelligent_mapper_import()
    success2 = test_script_integration()
    
    print(f"\n📄 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 测试目标: 验证scripts/data_import_optimized.py是否正确集成Transaction_Num修复")
    
    overall_success = success1 and success2
    print(f"✅ 测试结果: {'成功' if overall_success else '失败'}")
    
    if overall_success:
        print(f"\n🎉 scripts/data_import_optimized.py集成验证成功！")
        print(f"  📋 智能映射器: 正确导入和使用")
        print(f"  🔧 Transaction_Num: 正确保留和处理")
        print(f"  💾 数据完整性: 完全保持")
    else:
        print(f"\n⚠️ scripts/data_import_optimized.py集成存在问题")
        print(f"  🔧 建议: 检查智能映射器导入路径")
        print(f"  📋 建议: 确认Transaction_Num在用户指定列表中")

if __name__ == "__main__":
    main()
