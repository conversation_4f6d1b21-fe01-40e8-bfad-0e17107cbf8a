# -*- coding: utf-8 -*-
"""
批量导入优化脚本
支持多文件一次性导入，智能重复检测，用户交互选择
解决数据库锁定问题，提供完整的错误恢复机制
"""

import os
import sys
import argparse
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import tkinter as tk
from tkinter import messagebox, filedialog
import threading
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.connection_pool import get_connection, reinitialize_connection_pool
from scripts.data_import_optimized import DataImportProcessor
from utils.exceptions import DatabaseError, ConnectionPoolError
from utils.logger import get_logger


class BatchImportOptimized:
    """批量导入优化器"""
    
    def __init__(self, db_path: str = None):
        """
        初始化批量导入器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path or r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
        self.logger = get_logger(__name__)
        self.import_results = {}
        self.failed_files = []
        self.successful_files = []
        
        # 初始化数据导入器
        self.data_importer = DataImportProcessor()
        
        # 重新初始化连接池，确保使用正确的数据库路径
        try:
            reinitialize_connection_pool(self.db_path)
            self.logger.info(f"✅ 批量导入器初始化成功，数据库: {self.db_path}")
        except Exception as e:
            self.logger.error(f"❌ 初始化连接池失败: {e}")
            raise
    
    def select_files_gui(self) -> List[str]:
        """
        使用GUI选择多个文件
        
        Returns:
            选中的文件路径列表
        """
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        file_paths = filedialog.askopenfilenames(
            title="选择要批量导入的Excel文件",
            filetypes=[
                ("Excel files", "*.xlsx *.xls"),
                ("All files", "*.*")
            ],
            multiple=True
        )
        
        root.destroy()
        return list(file_paths) if file_paths else []
    
    def validate_files(self, file_paths: List[str]) -> Tuple[bool, str]:
        """
        验证文件列表
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            (是否有效, 错误信息)
        """
        if not file_paths:
            return False, "未选择任何文件"
        
        invalid_files = []
        for file_path in file_paths:
            if not os.path.exists(file_path):
                invalid_files.append(f"文件不存在: {file_path}")
            elif not file_path.lower().endswith(('.xlsx', '.xls')):
                invalid_files.append(f"不支持的文件格式: {file_path}")
        
        if invalid_files:
            return False, "\n".join(invalid_files)
        
        return True, ""
    
    def analyze_files_data(self, file_paths: List[str], platform: str) -> Dict[str, Any]:
        """
        分析多个文件的数据情况
        
        Args:
            file_paths: 文件路径列表
            platform: 平台类型
            
        Returns:
            分析结果字典
        """
        analysis_result = {
            'total_files': len(file_paths),
            'total_records': 0,
            'date_range': {'start': None, 'end': None},
            'files_info': [],
            'potential_duplicates': 0,
            'tables_affected': set()
        }
        
        self.logger.info(f"🔍 开始分析 {len(file_paths)} 个文件...")
        
        for file_path in file_paths:
            try:
                # 读取文件基本信息
                df = pd.read_excel(file_path)
                file_info = {
                    'path': file_path,
                    'name': os.path.basename(file_path),
                    'records': len(df),
                    'date_range': None,
                    'status': 'valid'
                }
                
                # 提取日期范围
                if 'Order_time' in df.columns:
                    dates = pd.to_datetime(df['Order_time'], errors='coerce').dropna()
                    if not dates.empty:
                        file_info['date_range'] = {
                            'start': dates.min().strftime('%Y-%m-%d'),
                            'end': dates.max().strftime('%Y-%m-%d')
                        }
                        
                        # 更新总体日期范围
                        if analysis_result['date_range']['start'] is None:
                            analysis_result['date_range']['start'] = file_info['date_range']['start']
                            analysis_result['date_range']['end'] = file_info['date_range']['end']
                        else:
                            if file_info['date_range']['start'] < analysis_result['date_range']['start']:
                                analysis_result['date_range']['start'] = file_info['date_range']['start']
                            if file_info['date_range']['end'] > analysis_result['date_range']['end']:
                                analysis_result['date_range']['end'] = file_info['date_range']['end']
                
                # 分析目标表
                if 'Order_status' in df.columns:
                    status_values = df['Order_status'].unique()
                    for status in status_values:
                        if status == 'Close':
                            analysis_result['tables_affected'].add(f'{platform}_Sales_Close')
                        elif status == 'Refunding':
                            analysis_result['tables_affected'].add(f'{platform}_Sales_Refunding')
                        else:
                            analysis_result['tables_affected'].add(f'{platform}_Sales')
                
                analysis_result['total_records'] += len(df)
                analysis_result['files_info'].append(file_info)
                
            except Exception as e:
                self.logger.error(f"分析文件失败 {file_path}: {e}")
                file_info = {
                    'path': file_path,
                    'name': os.path.basename(file_path),
                    'records': 0,
                    'date_range': None,
                    'status': 'error',
                    'error': str(e)
                }
                analysis_result['files_info'].append(file_info)
        
        # 转换set为list以便JSON序列化
        analysis_result['tables_affected'] = list(analysis_result['tables_affected'])
        
        self.logger.info(f"📊 文件分析完成: {analysis_result['total_records']} 条记录，"
                        f"日期范围: {analysis_result['date_range']['start']} 到 {analysis_result['date_range']['end']}")
        
        return analysis_result
    
    def show_analysis_dialog(self, analysis_result: Dict[str, Any]) -> str:
        """
        显示分析结果对话框，让用户选择处理策略
        
        Args:
            analysis_result: 分析结果
            
        Returns:
            用户选择的策略 ('continue', 'skip_duplicates', 'cancel')
        """
        root = tk.Tk()
        root.title("批量导入分析结果")
        root.geometry("600x500")
        
        # 创建主框架
        main_frame = tk.Frame(root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(main_frame, text="📊 批量导入分析结果", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 总体信息
        info_text = f"""
📁 文件总数: {analysis_result['total_files']} 个
📊 记录总数: {analysis_result['total_records']} 条
📅 日期范围: {analysis_result['date_range']['start']} 到 {analysis_result['date_range']['end']}
🎯 影响表: {', '.join(analysis_result['tables_affected'])}
        """
        
        info_label = tk.Label(main_frame, text=info_text, justify=tk.LEFT, 
                             font=("Arial", 10))
        info_label.pack(pady=(0, 20))
        
        # 文件详情
        details_label = tk.Label(main_frame, text="📋 文件详情:", 
                                font=("Arial", 12, "bold"))
        details_label.pack(anchor=tk.W)
        
        # 创建滚动文本框显示文件详情
        text_frame = tk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 20))
        
        text_widget = tk.Text(text_frame, height=10, wrap=tk.WORD)
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        for file_info in analysis_result['files_info']:
            status_icon = "✅" if file_info['status'] == 'valid' else "❌"
            date_info = f"({file_info['date_range']['start']} 到 {file_info['date_range']['end']})" if file_info['date_range'] else "(无日期信息)"
            text_widget.insert(tk.END, f"{status_icon} {file_info['name']}: {file_info['records']} 条记录 {date_info}\n")
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        text_widget.config(state=tk.DISABLED)
        
        # 选择变量
        choice = tk.StringVar(value="continue")
        
        # 选项框架
        options_frame = tk.Frame(main_frame)
        options_frame.pack(pady=20)
        
        tk.Radiobutton(options_frame, text="🚀 继续导入（如有重复数据会提示选择）", 
                      variable=choice, value="continue").pack(anchor=tk.W)
        tk.Radiobutton(options_frame, text="⏭️ 跳过重复数据，只导入新数据", 
                      variable=choice, value="skip_duplicates").pack(anchor=tk.W)
        tk.Radiobutton(options_frame, text="❌ 取消导入", 
                      variable=choice, value="cancel").pack(anchor=tk.W)
        
        # 按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(pady=20)
        
        result = {"choice": "cancel"}
        
        def on_confirm():
            result["choice"] = choice.get()
            root.quit()
        
        def on_cancel():
            result["choice"] = "cancel"
            root.quit()
        
        tk.Button(button_frame, text="确认", command=on_confirm, 
                 bg="#4CAF50", fg="white", padx=20).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="取消", command=on_cancel, 
                 bg="#f44336", fg="white", padx=20).pack(side=tk.LEFT, padx=10)
        
        # 运行对话框
        root.mainloop()
        root.destroy()
        
        return result["choice"]

    def batch_import_files(self, file_paths: List[str], platform: str,
                          strategy: str = "continue") -> Dict[str, Any]:
        """
        批量导入多个文件

        Args:
            file_paths: 文件路径列表
            platform: 平台类型
            strategy: 处理策略 ('continue', 'skip_duplicates', 'cancel')

        Returns:
            导入结果字典
        """
        if strategy == "cancel":
            return {"success": False, "message": "用户取消导入"}

        self.logger.info(f"🚀 开始批量导入 {len(file_paths)} 个文件，策略: {strategy}")

        # 创建数据库备份
        backup_path = self._create_batch_backup()
        if not backup_path:
            return {"success": False, "message": "创建备份失败"}

        batch_result = {
            "success": True,
            "total_files": len(file_paths),
            "successful_files": 0,
            "failed_files": 0,
            "total_records": 0,
            "backup_path": backup_path,
            "file_results": [],
            "errors": []
        }

        try:
            for i, file_path in enumerate(file_paths, 1):
                self.logger.info(f"📁 处理文件 {i}/{len(file_paths)}: {os.path.basename(file_path)}")

                try:
                    # 设置导入策略
                    if strategy == "skip_duplicates":
                        # 设置环境变量，让导入脚本知道要跳过重复数据
                        os.environ['BATCH_IMPORT_SKIP_DUPLICATES'] = '1'

                    # 调用单文件导入
                    result = self.data_importer.process_file(
                        file_path=file_path,
                        platform=platform,
                        order_type='智能识别导入'
                    )

                    if result.get('success', False):
                        batch_result["successful_files"] += 1
                        batch_result["total_records"] += result.get('inserted_rows', 0)
                        self.successful_files.append(file_path)

                        file_result = {
                            "file": os.path.basename(file_path),
                            "status": "success",
                            "records": result.get('inserted_rows', 0),
                            "tables": result.get('table_distribution', {})
                        }
                    else:
                        batch_result["failed_files"] += 1
                        self.failed_files.append(file_path)

                        file_result = {
                            "file": os.path.basename(file_path),
                            "status": "failed",
                            "error": result.get('errors', ['未知错误'])
                        }
                        batch_result["errors"].extend(result.get('errors', []))

                    batch_result["file_results"].append(file_result)

                except Exception as e:
                    self.logger.error(f"❌ 处理文件失败 {file_path}: {e}")
                    batch_result["failed_files"] += 1
                    self.failed_files.append(file_path)

                    file_result = {
                        "file": os.path.basename(file_path),
                        "status": "failed",
                        "error": str(e)
                    }
                    batch_result["file_results"].append(file_result)
                    batch_result["errors"].append(str(e))

                finally:
                    # 清理环境变量
                    if 'BATCH_IMPORT_SKIP_DUPLICATES' in os.environ:
                        del os.environ['BATCH_IMPORT_SKIP_DUPLICATES']

            # 判断整体成功状态
            if batch_result["failed_files"] == 0:
                batch_result["message"] = f"✅ 所有文件导入成功！共导入 {batch_result['total_records']} 条记录"
            elif batch_result["successful_files"] == 0:
                batch_result["success"] = False
                batch_result["message"] = f"❌ 所有文件导入失败！"
            else:
                batch_result["message"] = f"⚠️ 部分文件导入成功：{batch_result['successful_files']}/{batch_result['total_files']}"

            self.logger.info(batch_result["message"])
            return batch_result

        except Exception as e:
            self.logger.error(f"❌ 批量导入过程出错: {e}")
            batch_result["success"] = False
            batch_result["message"] = f"批量导入过程出错: {e}"

            # 询问是否恢复备份
            self._handle_batch_failure(backup_path, str(e))

            return batch_result

    def _create_batch_backup(self) -> Optional[str]:
        """
        创建批量导入前的数据库备份

        Returns:
            备份文件路径，失败返回None
        """
        try:
            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            os.makedirs(backup_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"batch_import_backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)

            # 复制数据库文件
            import shutil
            shutil.copy2(self.db_path, backup_path)

            self.logger.info(f"✅ 批量导入备份创建成功: {backup_path}")
            return backup_path

        except Exception as e:
            self.logger.error(f"❌ 创建批量导入备份失败: {e}")
            return None

    def _handle_batch_failure(self, backup_path: str, error_msg: str):
        """
        处理批量导入失败，询问是否恢复备份

        Args:
            backup_path: 备份文件路径
            error_msg: 错误信息
        """
        if not backup_path or not os.path.exists(backup_path):
            return

        root = tk.Tk()
        root.withdraw()

        response = messagebox.askyesno(
            "批量导入失败",
            f"批量导入过程中出现错误：\n{error_msg}\n\n是否恢复到导入前的状态？",
            icon="error"
        )

        root.destroy()

        if response:
            try:
                import shutil
                shutil.copy2(backup_path, self.db_path)
                self.logger.info(f"✅ 数据库已恢复到导入前状态")
                messagebox.showinfo("恢复成功", "数据库已恢复到导入前状态")
            except Exception as e:
                self.logger.error(f"❌ 恢复备份失败: {e}")
                messagebox.showerror("恢复失败", f"恢复备份失败: {e}")

    def show_batch_result_dialog(self, result: Dict[str, Any]):
        """
        显示批量导入结果对话框

        Args:
            result: 导入结果字典
        """
        root = tk.Tk()
        root.title("批量导入结果")
        root.geometry("700x600")

        # 创建主框架
        main_frame = tk.Frame(root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_text = "✅ 批量导入完成" if result["success"] else "❌ 批量导入失败"
        title_label = tk.Label(main_frame, text=title_text,
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # 总体结果
        summary_text = f"""
📊 导入统计:
• 总文件数: {result['total_files']}
• 成功文件: {result['successful_files']}
• 失败文件: {result['failed_files']}
• 导入记录: {result['total_records']} 条

💾 备份文件: {os.path.basename(result.get('backup_path', '无'))}
        """

        summary_label = tk.Label(main_frame, text=summary_text, justify=tk.LEFT,
                                font=("Arial", 10))
        summary_label.pack(pady=(0, 20))

        # 详细结果
        details_label = tk.Label(main_frame, text="📋 详细结果:",
                                font=("Arial", 12, "bold"))
        details_label.pack(anchor=tk.W)

        # 创建滚动文本框
        text_frame = tk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 20))

        text_widget = tk.Text(text_frame, height=15, wrap=tk.WORD)
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        for file_result in result.get('file_results', []):
            if file_result['status'] == 'success':
                status_icon = "✅"
                detail = f"导入 {file_result['records']} 条记录"
                if file_result.get('tables'):
                    detail += f" 到表: {', '.join(file_result['tables'].keys())}"
            else:
                status_icon = "❌"
                detail = f"错误: {file_result.get('error', '未知错误')}"

            text_widget.insert(tk.END, f"{status_icon} {file_result['file']}: {detail}\n")

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        text_widget.config(state=tk.DISABLED)

        # 关闭按钮
        tk.Button(main_frame, text="关闭", command=root.destroy,
                 bg="#2196F3", fg="white", padx=30, pady=10).pack(pady=20)

        root.mainloop()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量导入Excel文件到数据库')
    parser.add_argument('--files', nargs='+', help='要导入的Excel文件路径列表')
    parser.add_argument('--platform', required=True, choices=['IOT', 'ZERO'],
                       help='平台类型 (IOT 或 ZERO)')
    parser.add_argument('--db_path', help='数据库路径')
    parser.add_argument('--strategy', choices=['continue', 'skip_duplicates'],
                       default='continue', help='处理策略')
    parser.add_argument('--gui', action='store_true', help='使用图形界面选择文件')

    args = parser.parse_args()

    try:
        # 初始化批量导入器
        batch_importer = BatchImportOptimized(db_path=args.db_path)

        # 获取文件列表
        if args.gui or not args.files:
            print("🔍 请选择要导入的Excel文件...")
            file_paths = batch_importer.select_files_gui()
            if not file_paths:
                print("❌ 未选择任何文件，退出")
                return 1
        else:
            file_paths = args.files

        # 验证文件
        valid, error_msg = batch_importer.validate_files(file_paths)
        if not valid:
            print(f"❌ 文件验证失败: {error_msg}")
            return 1

        print(f"📁 准备导入 {len(file_paths)} 个文件到 {args.platform} 平台")

        # 分析文件
        analysis_result = batch_importer.analyze_files_data(file_paths, args.platform)

        # 显示分析结果并获取用户选择
        if args.gui:
            strategy = batch_importer.show_analysis_dialog(analysis_result)
        else:
            strategy = args.strategy
            print(f"📊 分析结果: {analysis_result['total_records']} 条记录，"
                  f"日期范围: {analysis_result['date_range']['start']} 到 {analysis_result['date_range']['end']}")

        if strategy == "cancel":
            print("❌ 用户取消导入")
            return 1

        # 执行批量导入
        print(f"🚀 开始批量导入，策略: {strategy}")
        result = batch_importer.batch_import_files(file_paths, args.platform, strategy)

        # 显示结果
        if args.gui:
            batch_importer.show_batch_result_dialog(result)
        else:
            print(f"\n{result['message']}")
            if result.get('errors'):
                print("❌ 错误详情:")
                for error in result['errors']:
                    print(f"  • {error}")

        return 0 if result['success'] else 1

    except Exception as e:
        print(f"❌ 批量导入失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
