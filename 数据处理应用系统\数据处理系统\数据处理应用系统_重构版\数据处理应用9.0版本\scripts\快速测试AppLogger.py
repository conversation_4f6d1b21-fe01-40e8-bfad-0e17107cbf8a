#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试AppLogger修复
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("🔧 快速测试AppLogger修复")
    print("=" * 50)
    
    try:
        # 测试1：导入AppLogger
        print("1. 测试AppLogger导入...")
        from utils.logger import get_logger
        logger = get_logger('test')
        print(f"   ✅ 成功，类型: {type(logger)}")
        
        # 测试2：检查addHandler方法
        print("2. 测试addHandler方法...")
        has_addhandler = hasattr(logger, 'addHandler')
        print(f"   ✅ addHandler方法存在: {has_addhandler}")
        
        # 测试3：测试addHandler调用
        if has_addhandler:
            print("3. 测试addHandler调用...")
            import logging
            test_handler = logging.StreamHandler()
            logger.addHandler(test_handler)
            print("   ✅ addHandler调用成功")
        
        # 测试4：测试DataImportProcessor
        print("4. 测试DataImportProcessor...")
        from data_import_optimized import DataImportProcessor
        processor = DataImportProcessor()
        print("   ✅ DataImportProcessor创建成功")
        
        print("\n🎉 所有测试通过！AppLogger错误已修复")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n结果: {'成功' if success else '失败'}")
