#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复数据对话框修复脚本
修复数据重复提示框缺少确认和取消按键的问题
"""

import tkinter as tk
from tkinter import ttk
import os

class DuplicateDataDialog:
    """自定义重复数据处理对话框"""
    
    def __init__(self, parent=None):
        self.result = None
        self.root = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_dialog()
    
    def setup_dialog(self):
        """设置对话框"""
        # 窗口基本设置
        self.root.title("数据重复处理")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # 居中显示
        self.center_window()
        
        # 设置为模态对话框
        self.root.transient()
        self.root.grab_set()
        self.root.focus_set()
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="🔍 检测到重复数据", 
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 15))
        
        # 信息显示区域
        info_frame = ttk.LabelFrame(main_frame, text="数据分析", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 数据统计信息
        stats_text = """• 数据库总记录: 294335 条
• 相关日期记录: 
  - IOT_Sales: 3702 条
• 重复数据分析:
  - 完全重复: 3852 条 (与数据库中现有记录完全相同)
  - 部分重复: 0 条 (部分字段不同，需要更新)
  - 新数据: 726 条 (数据库中不存在的新记录)
  - 总计: 3852 条 (需要处理的重复数据总数)"""
        
        stats_label = ttk.Label(info_frame, text=stats_text, justify=tk.LEFT)
        stats_label.pack(anchor=tk.W)
        
        # 处理选项
        options_frame = ttk.LabelFrame(main_frame, text="请选择处理方式", padding="10")
        options_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.choice_var = tk.StringVar(value="skip")
        
        # 选项按钮
        options = [
            ("skip", "🔄 跳过重复数据 - 只导入新数据，保持现有数据不变"),
            ("overwrite", "📝 覆盖更新 - 用新数据完全覆盖现有重复数据"),
            ("incremental", "🔧 增量更新 - 只更新不同的字段，保留相同字段"),
            ("refresh_daily", "🗑️ 重新更新 - 删除当天数据后重新导入"),
            ("cancel", "❌ 取消导入 - 停止导入操作，不对数据库进行任何修改")
        ]
        
        for value, text in options:
            rb = ttk.Radiobutton(
                options_frame,
                text=text,
                variable=self.choice_var,
                value=value
            )
            rb.pack(anchor=tk.W, pady=2)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))
        
        # 确认按钮
        confirm_btn = ttk.Button(
            button_frame,
            text="✅ 确认",
            command=self.confirm_choice,
            style="Accent.TButton"
        )
        confirm_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 取消按钮
        cancel_btn = ttk.Button(
            button_frame,
            text="❌ 取消",
            command=self.cancel_choice
        )
        cancel_btn.pack(side=tk.RIGHT)
        
        # 绑定键盘事件
        self.root.bind('<Return>', lambda e: self.confirm_choice())
        self.root.bind('<Escape>', lambda e: self.cancel_choice())
        
        # 设置默认焦点
        confirm_btn.focus_set()
    
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def confirm_choice(self):
        """确认选择"""
        self.result = self.choice_var.get()
        self.root.destroy()
    
    def cancel_choice(self):
        """取消选择"""
        self.result = "cancel"
        self.root.destroy()
    
    def show(self):
        """显示对话框并返回结果"""
        self.root.wait_window()
        return self.result

def create_duplicate_dialog_patch():
    """创建重复数据对话框的补丁代码"""
    
    patch_code = '''
def show_duplicate_data_dialog(fully_duplicate_count=0, partial_different_count=0, new_data_count=0):
    """显示重复数据处理对话框 - 修复版"""
    import tkinter as tk
    from tkinter import ttk
    
    class DuplicateDataDialog:
        def __init__(self):
            self.result = None
            self.root = tk.Tk()
            self.setup_dialog()
        
        def setup_dialog(self):
            # 窗口基本设置
            self.root.title("数据重复处理")
            self.root.geometry("500x400")
            self.root.resizable(False, False)
            
            # 居中显示
            self.center_window()
            
            # 设置为置顶
            self.root.attributes('-topmost', True)
            self.root.focus_force()
            
            # 创建主框架
            main_frame = ttk.Frame(self.root, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 标题
            title_label = ttk.Label(
                main_frame, 
                text="🔍 检测到重复数据", 
                font=("Arial", 14, "bold")
            )
            title_label.pack(pady=(0, 15))
            
            # 信息显示
            info_text = f"""检测到重复数据情况：
            
• 完全重复: {fully_duplicate_count} 条
• 部分重复: {partial_different_count} 条  
• 新数据: {new_data_count} 条

请选择处理方式："""
            
            info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
            info_label.pack(pady=(0, 15))
            
            # 选项
            self.choice_var = tk.StringVar(value="skip")
            
            options = [
                ("skip", "跳过重复数据 - 只导入新数据"),
                ("overwrite", "覆盖更新 - 用新数据覆盖现有数据"),
                ("incremental", "增量更新 - 只更新不同字段"),
                ("refresh_daily", "重新更新 - 删除当天数据后重新导入"),
                ("cancel", "取消导入 - 停止操作")
            ]
            
            for value, text in options:
                rb = ttk.Radiobutton(
                    main_frame,
                    text=text,
                    variable=self.choice_var,
                    value=value
                )
                rb.pack(anchor=tk.W, pady=2)
            
            # 按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(20, 0))
            
            # 确认按钮
            confirm_btn = ttk.Button(
                button_frame,
                text="确认",
                command=self.confirm_choice
            )
            confirm_btn.pack(side=tk.RIGHT, padx=(5, 0))
            
            # 取消按钮
            cancel_btn = ttk.Button(
                button_frame,
                text="取消",
                command=self.cancel_choice
            )
            cancel_btn.pack(side=tk.RIGHT)
        
        def center_window(self):
            self.root.update_idletasks()
            width = 500
            height = 400
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f"{width}x{height}+{x}+{y}")
        
        def confirm_choice(self):
            self.result = self.choice_var.get()
            self.root.destroy()
        
        def cancel_choice(self):
            self.result = "cancel"
            self.root.destroy()
        
        def show(self):
            self.root.mainloop()
            return self.result
    
    dialog = DuplicateDataDialog()
    return dialog.show()
'''
    
    return patch_code

def test_dialog():
    """测试对话框"""
    print("🧪 测试重复数据对话框...")
    
    try:
        dialog = DuplicateDataDialog()
        result = dialog.show()
        print(f"✅ 对话框测试成功，用户选择: {result}")
        return True
    except Exception as e:
        print(f"❌ 对话框测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 重复数据对话框修复工具")
    print("="*50)
    
    print("\n📋 问题分析:")
    print("• 原始对话框使用 simpledialog.askstring()")
    print("• 缺少明显的确认和取消按钮")
    print("• 用户体验不佳")
    
    print("\n🛠️ 修复方案:")
    print("• 创建自定义对话框类")
    print("• 添加明确的确认和取消按钮")
    print("• 改善布局和用户体验")
    
    print("\n🧪 开始测试修复后的对话框...")
    
    if test_dialog():
        print("\n✅ 修复成功！")
        print("\n💡 使用建议:")
        print("1. 将修复代码集成到数据导入脚本中")
        print("2. 替换原有的 simpledialog.askstring() 调用")
        print("3. 测试各种场景下的对话框显示")
        
        # 生成补丁代码
        patch_code = create_duplicate_dialog_patch()
        
        with open("重复数据对话框补丁.py", "w", encoding="utf-8") as f:
            f.write(patch_code)
        
        print("\n📁 已生成补丁文件: 重复数据对话框补丁.py")
        
    else:
        print("\n❌ 测试失败，请检查tkinter环境")
    
    return True

if __name__ == "__main__":
    main()
