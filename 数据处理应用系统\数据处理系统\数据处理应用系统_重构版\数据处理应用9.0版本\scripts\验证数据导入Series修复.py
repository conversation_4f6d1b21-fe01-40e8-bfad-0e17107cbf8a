#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据导入Series错误修复
测试数据导入脚本中的Series错误修复是否有效
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_series_empty_fix():
    """测试Series .empty修复"""
    print("🔧 1. 测试Series .empty修复")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        test_df = pd.DataFrame({
            'Order_time': ['2025-01-01 10:00:00', '2025-01-02 11:00:00'],
            'Order_No': ['ORD001', 'ORD002'],
            'Order_price': [100.0, 200.0]
        })
        
        print(f"📊 测试数据: {len(test_df)} 条记录")
        
        # 测试extract_date_from_data方法
        try:
            result_date = processor.extract_date_from_data(test_df, "test_file.xlsx")
            print(f"✅ extract_date_from_data 执行成功: {result_date}")
        except Exception as e:
            print(f"❌ extract_date_from_data 执行失败: {e}")
            return False
        
        # 测试空DataFrame的情况
        empty_df = pd.DataFrame(columns=['Order_time', 'Order_No', 'Order_price'])
        try:
            result_date = processor.extract_date_from_data(empty_df, "test_file.xlsx")
            print(f"✅ 空DataFrame处理成功: {result_date}")
        except Exception as e:
            print(f"❌ 空DataFrame处理失败: {e}")
            return False
        
        # 测试没有Order_time列的情况
        no_time_df = pd.DataFrame({
            'Order_No': ['ORD001', 'ORD002'],
            'Order_price': [100.0, 200.0]
        })
        try:
            result_date = processor.extract_date_from_data(no_time_df, "test_file.xlsx")
            print(f"✅ 无Order_time列处理成功: {result_date}")
        except Exception as e:
            print(f"❌ 无Order_time列处理失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Series .empty修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_duplicate_detection():
    """测试增强的重复检测"""
    print("\n🔧 2. 测试增强的重复检测")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        df = pd.DataFrame({
            'Transaction_Num': ['TXN001', 'TXN002'],
            'Order_time': ['2025-01-01 10:00:00', '2025-01-02 11:00:00'],
            'Order_price': [100.0, 200.0]
        })
        
        existing_df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_time': ['2025-01-01 10:00:00'],
            'Order_price': [100.0]
        })
        
        print(f"📊 新数据: {len(df)} 条记录")
        print(f"📊 现有数据: {len(existing_df)} 条记录")
        
        # 执行重复检测
        try:
            result = processor._enhanced_duplicate_detection(df, existing_df)
            fully_dup, partial_dup, new_data = result
            print(f"✅ 重复检测成功:")
            print(f"  完全重复: {len(fully_dup)} 条")
            print(f"  部分重复: {len(partial_dup)} 条")
            print(f"  新数据: {len(new_data)} 条")
            return True
        except Exception as e:
            print(f"❌ 重复检测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 增强重复检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_dataframe_handling():
    """测试空DataFrame处理"""
    print("\n🔧 3. 测试空DataFrame处理")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建空DataFrame
        empty_df = pd.DataFrame(columns=['Transaction_Num', 'Order_time', 'Order_price'])
        normal_df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_time': ['2025-01-01 10:00:00'],
            'Order_price': [100.0]
        })
        
        print("📊 测试场景1: 两个空DataFrame")
        try:
            result = processor._enhanced_duplicate_detection(empty_df, empty_df)
            print("✅ 空DataFrame处理成功")
        except Exception as e:
            print(f"❌ 空DataFrame处理失败: {e}")
            return False
        
        print("📊 测试场景2: 新数据为空，现有数据不为空")
        try:
            result = processor._enhanced_duplicate_detection(empty_df, normal_df)
            print("✅ 新数据为空处理成功")
        except Exception as e:
            print(f"❌ 新数据为空处理失败: {e}")
            return False
        
        print("📊 测试场景3: 新数据不为空，现有数据为空")
        try:
            result = processor._enhanced_duplicate_detection(normal_df, empty_df)
            print("✅ 现有数据为空处理成功")
        except Exception as e:
            print(f"❌ 现有数据为空处理失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 空DataFrame处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_apply_operations_safety():
    """测试apply操作安全性"""
    print("\n🔧 4. 测试apply操作安全性")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_time': ['2025-01-01 10:00:00'],
            'Order_price': [100.0]
        })
        
        existing_df = pd.DataFrame({
            'Transaction_Num': ['TXN001'],
            'Order_time': ['2025-01-01 10:00:00'],
            'Order_price': [100.0]
        })
        
        print("📊 测试正常apply操作")
        try:
            result = processor._detect_by_transaction_num(df, existing_df)
            print("✅ apply操作安全性测试通过")
            return True
        except Exception as e:
            print(f"❌ apply操作安全性测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ apply操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smart_incremental_duplicate_check():
    """测试智能增量重复检测"""
    print("\n🔧 5. 测试智能增量重复检测")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        test_df = pd.DataFrame({
            'Transaction_Num': ['TXN001', 'TXN002'],
            'Order_No': ['ORD001', 'ORD002'],
            'Order_time': ['2025-01-01 10:00:00', '2025-01-02 11:00:00'],
            'Order_price': [100.0, 200.0],
            'Order_status': ['Finished', 'Finished'],
            'Equipment_ID': ['EQ001', 'EQ002']
        })
        
        print(f"📊 测试数据: {len(test_df)} 条记录")
        
        # 执行智能增量重复检测
        try:
            fully_dup, partial_dup, new_data = processor.smart_incremental_duplicate_check(test_df, 'IOT')
            print(f"✅ 智能增量检测成功:")
            print(f"  完全重复: {len(fully_dup)} 条")
            print(f"  部分重复: {len(partial_dup)} 条")
            print(f"  新数据: {len(new_data)} 条")
            return True
        except Exception as e:
            print(f"❌ 智能增量检测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 智能增量重复检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 数据导入Series错误修复验证")
    print("=" * 80)
    
    # 设置非交互模式
    os.environ['NON_INTERACTIVE'] = '1'
    os.environ['AUTO_DUPLICATE_HANDLING'] = 'overwrite'
    os.environ['AUTO_MISSING_HANDLING'] = 'ignore'
    
    tests = [
        ("Series .empty修复", test_series_empty_fix),
        ("增强重复检测", test_enhanced_duplicate_detection),
        ("空DataFrame处理", test_empty_dataframe_handling),
        ("apply操作安全性", test_apply_operations_safety),
        ("智能增量重复检测", test_smart_incremental_duplicate_check)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 数据导入Series错误修复验证结果")
    print("=" * 80)
    
    print(f"📊 通过测试: {passed}/{total}")
    success_rate = (passed / total) * 100
    print(f"📊 成功率: {success_rate:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过")
        print("✅ Series错误已修复")
        print("✅ 数据导入逻辑正常工作")
        print("✅ 空DataFrame处理安全")
        print("✅ apply操作不再出错")
        print("\n💡 修复内容:")
        print("  - 修复了Series .empty错误用法")
        print("  - 增强了空DataFrame处理")
        print("  - 优化了apply操作安全性")
        print("  - 改善了错误处理机制")
    elif passed >= total * 0.75:
        print("✅ 大部分测试通过")
        print("⚠️ 少量问题可能仍存在")
    else:
        print("❌ 多个测试失败")
        print("🔧 Series错误可能仍然存在")
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 修复验证{'成功' if success else '需要改进'}")
    input("按回车键退出...")
