# -*- coding: utf-8 -*-
"""
验证Transaction Num数据分布
检查具体的数据情况和分布
"""

import sqlite3
import pandas as pd
from datetime import datetime, timed<PERSON>ta

def verify_transaction_num_distribution():
    """验证Transaction Num数据分布"""
    print("🔍 Transaction Num数据分布验证")
    print("=" * 80)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        with sqlite3.connect(db_path) as conn:
            # 1. 总体统计
            print("📊 总体统计:")
            
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM ZERO_Sales")
            total = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM ZERO_Sales WHERE Transaction_Num IS NOT NULL AND Transaction_Num != ''")
            with_transaction_num = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM ZERO_Sales WHERE Transaction_Num IS NULL OR Transaction_Num = ''")
            without_transaction_num = cursor.fetchone()[0]
            
            print(f"  总记录数: {total}")
            print(f"  有Transaction_Num: {with_transaction_num} ({with_transaction_num/total*100:.1f}%)")
            print(f"  无Transaction_Num: {without_transaction_num} ({without_transaction_num/total*100:.1f}%)")
            
            # 2. 按日期分布
            print(f"\n📅 按导入日期分布:")
            
            cursor.execute("""
                SELECT 
                    Import_Date,
                    COUNT(*) as total_records,
                    SUM(CASE WHEN Transaction_Num IS NOT NULL AND Transaction_Num != '' THEN 1 ELSE 0 END) as with_transaction_num
                FROM ZERO_Sales 
                WHERE Import_Date IS NOT NULL
                GROUP BY Import_Date 
                ORDER BY Import_Date DESC 
                LIMIT 10
            """)
            
            date_stats = cursor.fetchall()
            
            for import_date, total_records, with_trans_num in date_stats:
                fill_rate = (with_trans_num / total_records * 100) if total_records > 0 else 0
                print(f"  {import_date}: {with_trans_num}/{total_records} ({fill_rate:.1f}%)")
            
            # 3. 按订单类型分布
            print(f"\n📋 按订单类型分布:")
            
            cursor.execute("""
                SELECT 
                    Order_types,
                    COUNT(*) as total_records,
                    SUM(CASE WHEN Transaction_Num IS NOT NULL AND Transaction_Num != '' THEN 1 ELSE 0 END) as with_transaction_num
                FROM ZERO_Sales 
                GROUP BY Order_types 
                ORDER BY total_records DESC
            """)
            
            type_stats = cursor.fetchall()
            
            for order_type, total_records, with_trans_num in type_stats:
                fill_rate = (with_trans_num / total_records * 100) if total_records > 0 else 0
                print(f"  {order_type or 'NULL'}: {with_trans_num}/{total_records} ({fill_rate:.1f}%)")
            
            # 4. 最近的记录样本
            print(f"\n📋 最近10条记录的Transaction_Num情况:")
            
            cursor.execute("""
                SELECT 
                    Order_No, 
                    Transaction_Num, 
                    Order_types, 
                    Import_Date,
                    CASE WHEN Transaction_Num IS NOT NULL AND Transaction_Num != '' THEN '有数据' ELSE '无数据' END as status
                FROM ZERO_Sales 
                ORDER BY rowid DESC 
                LIMIT 10
            """)
            
            recent_records = cursor.fetchall()
            
            for i, (order_no, trans_num, order_type, import_date, status) in enumerate(recent_records, 1):
                print(f"  {i:2d}. {order_no} | {trans_num or 'NULL'} | {order_type} | {import_date} | {status}")
            
            # 5. 有Transaction_Num的最新记录
            print(f"\n📋 最近10条有Transaction_Num的记录:")
            
            cursor.execute("""
                SELECT 
                    Order_No, 
                    Transaction_Num, 
                    Order_types, 
                    Import_Date
                FROM ZERO_Sales 
                WHERE Transaction_Num IS NOT NULL AND Transaction_Num != ''
                ORDER BY rowid DESC 
                LIMIT 10
            """)
            
            with_trans_records = cursor.fetchall()
            
            for i, (order_no, trans_num, order_type, import_date) in enumerate(with_trans_records, 1):
                print(f"  {i:2d}. {order_no} | {trans_num} | {order_type} | {import_date}")
            
            # 6. 检查特定日期范围
            print(f"\n📅 检查最近7天的数据:")
            
            cursor.execute("""
                SELECT 
                    DATE(Import_Date) as import_day,
                    COUNT(*) as total_records,
                    SUM(CASE WHEN Transaction_Num IS NOT NULL AND Transaction_Num != '' THEN 1 ELSE 0 END) as with_transaction_num
                FROM ZERO_Sales 
                WHERE Import_Date >= date('now', '-7 days')
                GROUP BY DATE(Import_Date)
                ORDER BY import_day DESC
            """)
            
            recent_days = cursor.fetchall()
            
            if recent_days:
                for day, total_records, with_trans_num in recent_days:
                    fill_rate = (with_trans_num / total_records * 100) if total_records > 0 else 0
                    print(f"  {day}: {with_trans_num}/{total_records} ({fill_rate:.1f}%)")
            else:
                print(f"  最近7天无数据")
            
            # 7. Transaction_Num格式分析
            print(f"\n🔍 Transaction_Num格式分析:")
            
            cursor.execute("""
                SELECT 
                    LENGTH(Transaction_Num) as length,
                    COUNT(*) as count,
                    MIN(Transaction_Num) as min_value,
                    MAX(Transaction_Num) as max_value
                FROM ZERO_Sales 
                WHERE Transaction_Num IS NOT NULL AND Transaction_Num != ''
                GROUP BY LENGTH(Transaction_Num)
                ORDER BY count DESC
            """)
            
            length_stats = cursor.fetchall()
            
            for length, count, min_val, max_val in length_stats:
                print(f"  长度{length}: {count}条记录 (范围: {min_val} - {max_val})")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return False

def main():
    """主函数"""
    success = verify_transaction_num_distribution()
    
    print(f"\n📄 验证完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print(f"\n🎯 结论:")
        print(f"  Transaction_Num数据确实存在于数据库中")
        print(f"  填充率较低可能是正常现象")
        print(f"  建议检查具体的查询条件和显示逻辑")
    else:
        print(f"\n❌ 验证过程出现问题")

if __name__ == "__main__":
    main()
