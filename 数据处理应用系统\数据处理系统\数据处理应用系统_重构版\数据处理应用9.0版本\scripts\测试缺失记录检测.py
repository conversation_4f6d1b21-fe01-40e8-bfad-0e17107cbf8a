#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试缺失记录检测功能 - 验证"数据库有但文件没有"的处理逻辑
"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_missing_records_detection():
    """测试缺失记录检测功能"""
    print("🔍 测试缺失记录检测功能")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        print("✅ DataImportProcessor 创建成功")
        
        # 创建模拟文件数据（只有部分记录）
        file_data = pd.DataFrame({
            'Transaction_Num': ['TXN001', 'TXN003', 'TXN005'],
            'Order_No': ['ORD001', 'ORD003', 'ORD005'],
            'Order_time': [
                '2025-01-01 10:00:00',
                '2025-01-03 12:00:00',
                '2025-01-05 14:00:00'
            ],
            'Order_price': [100.0, 300.0, 500.0],
            'Order_status': ['Finished', 'Finished', 'Finished'],
            'Equipment_ID': ['EQ001', 'EQ003', 'EQ005']
        })
        
        print(f"📊 模拟文件数据: {len(file_data)} 条记录")
        print("📋 文件包含记录: TXN001, TXN003, TXN005")
        
        # 执行缺失记录检测
        missing_report = processor._detect_missing_records(file_data, 'IOT')
        
        print(f"\n📋 缺失记录检测结果:")
        print(f"  是否有缺失: {missing_report['has_missing']}")
        print(f"  缺失记录数: {missing_report['missing_count']}")
        print(f"  数据库总记录: {missing_report['total_db_records']}")
        print(f"  文件记录数: {missing_report['file_records']}")
        print(f"  检查的表: {missing_report['tables_checked']}")
        
        if missing_report['has_missing']:
            print(f"\n📋 缺失记录详情:")
            missing_df = missing_report['missing_records']
            for i, (_, row) in enumerate(missing_df.head(5).iterrows()):
                print(f"  {i+1}. {row['Transaction_Num']} | {row['Order_time']} | {row['source_table']}")
        
        print("✅ 缺失记录检测功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 缺失记录检测功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_missing_records_backup():
    """测试缺失记录备份功能"""
    print("\n💾 测试缺失记录备份功能")
    print("-" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建模拟缺失记录
        missing_data = pd.DataFrame({
            'Transaction_Num': ['TXN002', 'TXN004'],
            'Order_No': ['ORD002', 'ORD004'],
            'Order_time': ['2025-01-02 11:00:00', '2025-01-04 13:00:00'],
            'Order_price': [200.0, 400.0],
            'Equipment_ID': ['EQ002', 'EQ004'],
            'source_table': ['IOT_Sales', 'IOT_Sales']
        })
        
        print(f"📊 模拟缺失记录: {len(missing_data)} 条")
        
        # 创建临时文件路径
        test_file_path = "C:/Users/<USER>/Desktop/test_missing_records.xlsx"
        
        # 执行备份
        backup_file = processor._create_missing_records_backup(missing_data, test_file_path)
        
        if backup_file and os.path.exists(backup_file):
            print(f"✅ 备份文件创建成功: {backup_file}")
            
            # 验证备份文件内容
            backup_df = pd.read_excel(backup_file, sheet_name='缺失记录')
            print(f"📊 备份文件记录数: {len(backup_df)}")
            
            # 清理测试文件
            try:
                os.remove(backup_file)
                print("🧹 测试文件已清理")
            except:
                pass
        else:
            print("❌ 备份文件创建失败")
            return False
        
        print("✅ 缺失记录备份功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 缺失记录备份功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_missing_records_scenarios():
    """测试不同的缺失记录场景"""
    print("\n🎭 测试不同的缺失记录场景")
    print("-" * 60)
    
    scenarios = [
        {
            "name": "完全匹配场景",
            "description": "文件和数据库记录完全一致",
            "file_records": ['TXN001', 'TXN002', 'TXN003'],
            "expected_missing": 0
        },
        {
            "name": "部分缺失场景", 
            "description": "文件缺少部分记录",
            "file_records": ['TXN001', 'TXN003'],
            "expected_missing": "可能有缺失"
        },
        {
            "name": "全新数据场景",
            "description": "文件包含全新记录",
            "file_records": ['TXN101', 'TXN102', 'TXN103'],
            "expected_missing": "可能有缺失"
        },
        {
            "name": "空文件场景",
            "description": "文件为空",
            "file_records": [],
            "expected_missing": "全部缺失"
        }
    ]
    
    try:
        from data_import_optimized import DataImportProcessor
        processor = DataImportProcessor()
        
        for scenario in scenarios:
            print(f"\n📋 场景: {scenario['name']}")
            print(f"   描述: {scenario['description']}")
            
            # 创建场景数据
            if scenario['file_records']:
                file_data = pd.DataFrame({
                    'Transaction_Num': scenario['file_records'],
                    'Order_No': [f'ORD{i:03d}' for i in range(len(scenario['file_records']))],
                    'Order_time': [f'2025-01-{i+1:02d} 10:00:00' for i in range(len(scenario['file_records']))],
                    'Order_price': [100.0 * (i+1) for i in range(len(scenario['file_records']))],
                    'Order_status': ['Finished'] * len(scenario['file_records']),
                    'Equipment_ID': [f'EQ{i:03d}' for i in range(len(scenario['file_records']))]
                })
            else:
                # 空文件场景
                file_data = pd.DataFrame(columns=['Transaction_Num', 'Order_No', 'Order_time', 'Order_price', 'Order_status', 'Equipment_ID'])
            
            # 执行检测
            missing_report = processor._detect_missing_records(file_data, 'IOT')
            
            print(f"   文件记录: {len(file_data)} 条")
            print(f"   数据库记录: {missing_report['total_db_records']} 条")
            print(f"   缺失记录: {missing_report['missing_count']} 条")
            print(f"   检测结果: {'✅ 有缺失' if missing_report['has_missing'] else '✅ 无缺失'}")
        
        print("\n✅ 缺失记录场景测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 缺失记录场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 缺失记录检测功能测试工具")
    print("=" * 80)
    
    tests = [
        ("缺失记录检测功能", test_missing_records_detection),
        ("缺失记录备份功能", test_missing_records_backup),
        ("缺失记录场景测试", test_missing_records_scenarios)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"\n✅ {test_name} 测试通过")
            else:
                print(f"\n❌ {test_name} 测试失败")
        except Exception as e:
            print(f"\n❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 80)
    print("🎯 缺失记录检测测试结果")
    print("=" * 80)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if i < passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过")
        print("✅ 缺失记录检测功能完善")
        print("✅ 数据一致性验证增强")
        print("✅ 增量导入逻辑更加健全")
        print("\n💡 功能特点:")
        print("  - 自动检测数据库中存在但文件中缺失的记录")
        print("  - 提供多种处理策略：忽略、标记删除、备份")
        print("  - 生成详细的缺失记录报告")
        print("  - 支持用户交互选择处理方式")
    elif passed >= total * 0.75:
        print("✅ 大部分测试通过")
        print("⚠️ 少量功能需要调整")
    else:
        print("❌ 多个测试失败")
        print("🔧 需要进一步检查")
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 测试{'成功' if success else '需要改进'}")
    input("按回车键退出...")
