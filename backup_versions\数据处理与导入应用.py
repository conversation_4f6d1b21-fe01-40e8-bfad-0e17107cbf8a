# coding: utf-8
import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import subprocess
import configparser
from datetime import datetime
import logging
import sqlite3
import shutil
import re
import pandas as pd
import traceback

# 配置日志
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"data_app_{datetime.now().strftime('%Y%m%d')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger()

# 从配置文件获取数据库路径或使用默认路径
def get_db_path_from_config():
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")
    default_db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db")
    
    if os.path.exists(config_path):
        config.read(config_path, encoding='utf-8')
        if 'Database' in config and 'db_path' in config['Database']:
            return config['Database']['db_path']
    
    return default_db_path

class iPhoneStyleApp(tk.Tk):
    # 类变量，存储数据库路径
    db_path = get_db_path_from_config()
    
    def __init__(self):
        # 初始化基类
        super().__init__()
        
        self.title("数据处理与导入应用")
        self.geometry("900x700")
        self.resizable(True, True)
        
        # 设置应用图标
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "kibdh-cj84b-001.ico")
        if os.path.exists(icon_path):
            self.iconbitmap(icon_path)
        
        # 读取配置文件
        self.config = configparser.ConfigParser()
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")
        if os.path.exists(config_path):
            self.config.read(config_path, encoding='utf-8')
            
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 创建主框架
        self.main_frame = ttk.Frame(self, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题栏
        self.create_title_bar()
        
        # 创建选项卡控件
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建数据处理选项卡
        self.processing_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.processing_tab, text="数据处理")
        self.create_processing_tab()
        
        # 创建数据导入选项卡
        self.import_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.import_tab, text="数据导入")
        self.create_import_tab()
        
        # 创建退款处理选项卡
        self.refund_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.refund_tab, text="退款处理")
        self.create_refund_tab()
        
        # 创建数据库设置选项卡
        self.settings_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_tab, text="数据库设置")
        self.create_settings_tab()
        
        # 创建状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 设置iPhone风格
        self.set_iphone_style()
        
        # 初始化变量
        self.processing_thread = None
        self.import_thread = None
        
        logger.info("数据处理与导入应用已启动")
    
    def create_title_bar(self):
        """创建标题栏"""
        title_frame = ttk.Frame(self.main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 应用标题
        title_label = ttk.Label(title_frame, text="数据处理与导入应用", font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT, padx=10)
        
        # 当前日期
        date_label = ttk.Label(title_frame, text=datetime.now().strftime("%Y-%m-%d"))
        date_label.pack(side=tk.RIGHT, padx=10)
    
    def set_iphone_style(self):
        """设置iPhone风格的UI"""
        self.style = ttk.Style()
        
        # 设置整体主题
        if "clam" in self.style.theme_names():
            self.style.theme_use("clam")
        
        # 设置颜色
        bg_color = "#f5f5f7"  # 浅灰色背景
        accent_color = "#0071e3"  # 蓝色强调色
        text_color = "#1d1d1f"  # 深灰色文本
        
        # 配置各种元素样式
        self.style.configure("TFrame", background=bg_color)
        self.style.configure("TLabel", background=bg_color, foreground=text_color)
        self.style.configure("TButton", background=accent_color, foreground="white", padding=6)
        self.style.map("TButton", background=[("active", "#0077ed")])
        self.style.configure("TNotebook", background=bg_color, tabmargins=[2, 5, 2, 0])
        self.style.configure("TNotebook.Tab", background=bg_color, padding=[10, 5], font=("Arial", 10))
        self.style.map("TNotebook.Tab", background=[("selected", accent_color)], foreground=[("selected", "white")])
        
        # 设置窗口背景色
        self.configure(background=bg_color)
        self.main_frame.configure(style="TFrame")
    
    def create_processing_tab(self):
        """创建数据处理选项卡内容"""
        # 主框架
        frame = ttk.Frame(self.processing_tab, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(frame, text="文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=10)
        
        # 第一文件（SETTLEMENT文件）
        ttk.Label(file_frame, text="第一文件 (SETTLEMENT):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.file1_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file1_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_file1).grid(row=0, column=2, padx=5, pady=5)
        
        # 第一文件Sheet名称
        ttk.Label(file_frame, text="第一文件Sheet名称:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.sheet_name_var = tk.StringVar(value="TRANSACTION_LIST")
        ttk.Entry(file_frame, textvariable=self.sheet_name_var, width=50).grid(row=1, column=1, padx=5, pady=5)
        
        # 第二文件（CHINA文件）
        ttk.Label(file_frame, text="第二文件 (CHINA):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.file2_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file2_var, width=50).grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_file2).grid(row=2, column=2, padx=5, pady=5)
        
        # 脚本选择区域
        script_frame = ttk.LabelFrame(frame, text="处理脚本选择", padding="10")
        script_frame.pack(fill=tk.X, pady=10)
        
        self.script_var = tk.StringVar(value="report 脚本 3.0.py")
        scripts = ["report 脚本 3.0.py", "report 三天报告 3.0.py"]
        
        for i, script in enumerate(scripts):
            ttk.Radiobutton(script_frame, text=script, value=script, variable=self.script_var).grid(row=0, column=i, padx=20, pady=5, sticky=tk.W)
        
        # 操作按钮区域
        button_frame = ttk.Frame(frame, padding="10")
        button_frame.pack(fill=tk.X, pady=10)
        
        # 处理按钮
        self.process_button = ttk.Button(button_frame, text="开始处理", command=self.process_files)
        self.process_button.pack(side=tk.LEFT, padx=5)
        
        # 清空按钮
        ttk.Button(button_frame, text="清空选择", command=self.clear_processing_selection).pack(side=tk.LEFT, padx=5)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(frame, text="处理日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建文本框和滚动条
        self.processing_log_text = tk.Text(log_frame, wrap=tk.WORD, width=80, height=15)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.processing_log_text.yview)
        self.processing_log_text.configure(yscrollcommand=scrollbar.set)
        
        self.processing_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置只读
        self.processing_log_text.config(state=tk.DISABLED)
        
        # 显示初始提示信息
        self.log_processing_message("请使用浏览按钮选择文件")
    
    def create_import_tab(self):
        """创建数据导入选项卡内容"""
        # 主框架
        frame = ttk.Frame(self.import_tab, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(frame, text="导入文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(file_frame, text="导入文件(可多选):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.import_files_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.import_files_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_import_file).grid(row=0, column=2, padx=5, pady=5)
        
        # 已选择文件列表框
        ttk.Label(file_frame, text="已选择的文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.import_selected_files_text = tk.Text(file_frame, wrap=tk.WORD, width=50, height=5)
        self.import_selected_files_text.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W+tk.E)
        scrollbar = ttk.Scrollbar(file_frame, orient="vertical", command=self.import_selected_files_text.yview)
        scrollbar.grid(row=1, column=2, sticky=tk.N+tk.S)
        self.import_selected_files_text.configure(yscrollcommand=scrollbar.set)
        self.import_selected_files_text.config(state=tk.DISABLED)
        
        # 平台类型选择
        platform_frame = ttk.LabelFrame(frame, text="平台类型", padding="10")
        platform_frame.pack(fill=tk.X, pady=10)
        
        self.platform_var = tk.StringVar(value="IOT")
        ttk.Radiobutton(platform_frame, text="IOT", value="IOT", variable=self.platform_var).grid(row=0, column=0, padx=20, pady=5, sticky=tk.W)
        ttk.Radiobutton(platform_frame, text="ZERO", value="ZERO", variable=self.platform_var).grid(row=0, column=1, padx=20, pady=5, sticky=tk.W)
        
        # 订单类型选择
        order_type_frame = ttk.LabelFrame(frame, text="订单类型", padding="10")
        order_type_frame.pack(fill=tk.X, pady=10)
        
        self.order_type_var = tk.StringVar(value="all")
        ttk.Radiobutton(order_type_frame, text="所有订单", value="all", variable=self.order_type_var).grid(row=0, column=0, padx=20, pady=5, sticky=tk.W)
        ttk.Radiobutton(order_type_frame, text="仅API订单", value="api", variable=self.order_type_var).grid(row=0, column=1, padx=20, pady=5, sticky=tk.W)
        ttk.Radiobutton(order_type_frame, text="仅普通订单", value="normal", variable=self.order_type_var).grid(row=0, column=2, padx=20, pady=5, sticky=tk.W)
        
        # 操作按钮区域
        button_frame = ttk.Frame(frame, padding="10")
        button_frame.pack(fill=tk.X, pady=10)
        
        # 导入按钮
        self.import_button = ttk.Button(button_frame, text="开始导入", command=self.import_data)
        self.import_button.pack(side=tk.LEFT, padx=5)
        
        # 备份数据库按钮
        ttk.Button(button_frame, text="备份数据库", command=self.backup_database).pack(side=tk.LEFT, padx=5)
        
        # 恢复备份按钮
        ttk.Button(button_frame, text="恢复备份", command=self.restore_backup_import).pack(side=tk.LEFT, padx=5)
        
        # 清空按钮
        ttk.Button(button_frame, text="清空选择", command=self.clear_import_selection).pack(side=tk.LEFT, padx=5)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(frame, text="导入日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建文本框和滚动条
        self.import_log_text = tk.Text(log_frame, wrap=tk.WORD, width=80, height=15)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.import_log_text.yview)
        self.import_log_text.configure(yscrollcommand=scrollbar.set)
        
        self.import_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置只读
        self.import_log_text.config(state=tk.DISABLED)
        
        # 显示初始提示信息
        self.log_import_message("请使用浏览按钮选择文件")
    
    def browse_file1(self):
        """浏览选择第一文件"""
        filename = filedialog.askopenfilename(
            title="选择第一文件 (SETTLEMENT)",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.file1_var.set(filename)
            # 只显示文件名，不显示完整路径
            basename = os.path.basename(filename)
            self.log_processing_message(f"已选择第一文件: {basename}")
    
    def browse_file2(self):
        """浏览选择第二文件"""
        filename = filedialog.askopenfilename(
            title="选择第二文件 (CHINA)",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.file2_var.set(filename)
            # 只显示文件名，不显示完整路径
            basename = os.path.basename(filename)
            self.log_processing_message(f"已选择第二文件: {basename}")
    
    def browse_import_file(self):
        """浏览选择导入文件(可多选)"""
        filenames = filedialog.askopenfilenames(
            title="选择导入文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filenames:
            # 获取当前已选择的文件（如果有）
            current_files = self.import_files_var.get().split(";;")
            current_files = [f for f in current_files if f.strip()]
            
            # 合并当前文件和新选择的文件
            all_files = current_files + list(filenames)
            
            # 将多个文件路径用分号连接
            self.import_files_var.set(";;" .join(all_files))
            
            # 只显示文件名，不显示完整路径
            basenames = [os.path.basename(f) for f in filenames]
            self.log_import_message(f"已选择{len(filenames)}个导入文件: {', '.join(basenames)}")
            
            # 更新显示的文件名列表
            self.update_import_selected_files_display(all_files)
            
            # 尝试自动识别平台类型（基于第一个文件）
            if filenames:
                first_file = filenames[0]
                if "IOT" in first_file.upper():
                    self.platform_var.set("IOT")
                    self.log_import_message("自动识别为IOT平台")
                elif "ZERO" in first_file.upper():
                    self.platform_var.set("ZERO")
                    self.log_import_message("自动识别为ZERO平台")
    
    # 拖放功能已移除，使用标准文件对话框代替
    
    def clear_processing_selection(self):
        """清空处理选项卡的文件选择"""
        self.file1_var.set("")
        self.file2_var.set("")
        self.log_processing_message("已清空文件选择")
    
    def clear_import_selection(self):
        """清空导入选项卡的文件选择"""
        self.import_files_var.set("")
        self.update_import_selected_files_display([])
        self.log_import_message("已清空文件选择")
        
    def update_import_selected_files_display(self, file_paths):
        """更新导入选项卡已选择文件的显示"""
        if hasattr(self, 'import_selected_files_text'):
            self.import_selected_files_text.config(state=tk.NORMAL)
            self.import_selected_files_text.delete(1.0, tk.END)
            for file_path in file_paths:
                self.import_selected_files_text.insert(tk.END, f"{os.path.basename(file_path)}\n")
            self.import_selected_files_text.config(state=tk.DISABLED)
    
    def log_processing_message(self, message):
        """记录处理日志消息"""
        self.processing_log_text.config(state=tk.NORMAL)
        self.processing_log_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
        self.processing_log_text.see(tk.END)
        self.processing_log_text.config(state=tk.DISABLED)
        logger.info(message)
    
    def log_import_message(self, message):
        """记录导入日志消息"""
        self.import_log_text.config(state=tk.NORMAL)
        self.import_log_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
        self.import_log_text.see(tk.END)
        self.import_log_text.config(state=tk.DISABLED)
        logger.info(message)
    
    def process_files(self):
        """处理文件"""
        # 获取文件路径
        file1_path = self.file1_var.get()
        file2_path = self.file2_var.get()
        script = self.script_var.get()
        
        # 检查文件是否存在
        if not file1_path or not os.path.exists(file1_path):
            messagebox.showerror("错误", "请选择有效的第一文件 (SETTLEMENT)")
            return
        
        if not file2_path or not os.path.exists(file2_path):
            messagebox.showerror("错误", "请选择有效的第二文件 (CHINA)")
            return
        
        # 禁用处理按钮，防止重复点击
        self.process_button.config(state=tk.DISABLED)
        self.status_var.set("处理中...")
        
        # 在新线程中处理文件，避免界面卡死
        self.processing_thread = threading.Thread(target=self.run_processing, args=(file1_path, file2_path, script))
        self.processing_thread.daemon = True
        self.processing_thread.start()
    
    def run_processing(self, file1_path, file2_path, script):
        """在新线程中运行处理脚本"""
        try:
            self.log_processing_message("开始处理文件...")
            
            # 获取sheet名称
            sheet_name = self.sheet_name_var.get().strip()
            
            # 构建命令
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), script)
            cmd = [sys.executable, script_path, "--file1", file1_path, "--file2", file2_path]
            
            # 如果指定了sheet名称，添加到命令参数中
            if sheet_name:
                cmd.extend(["--sheet_name", sheet_name])
                self.log_processing_message(f"使用指定的Sheet名称: {sheet_name}")
            
            # 执行命令
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                universal_newlines=True,
                encoding='utf-8',
                bufsize=1  # 行缓冲，确保及时获取输出
            )
            
            # 创建线程实时读取输出
            def read_output(pipe, is_error=False):
                for line in pipe:
                    line = line.strip()
                    if line:
                        if is_error:
                            self.log_processing_message(f"错误: {line}")
                        else:
                            # 检查是否是进度条信息
                            if "[" in line and "]" in line and "%" in line:
                                # 更新状态栏显示进度
                                self.status_var.set(line)
                            self.log_processing_message(line)
            
            # 创建并启动读取线程
            stdout_thread = threading.Thread(target=read_output, args=(process.stdout,))
            stderr_thread = threading.Thread(target=read_output, args=(process.stderr, True))
            stdout_thread.daemon = True
            stderr_thread.daemon = True
            stdout_thread.start()
            stderr_thread.start()
            
            # 等待处理完成
            process.wait()
            stdout_thread.join()
            stderr_thread.join()
            
            if process.returncode == 0:
                self.log_processing_message("文件处理成功完成")
                self.status_var.set("处理完成")
            else:
                self.log_processing_message(f"文件处理失败，返回码: {process.returncode}")
                self.status_var.set("处理失败")
        
        except Exception as e:
            self.log_processing_message(f"处理文件时出错: {str(e)}")
            self.status_var.set("处理出错")
        
        finally:
            # 恢复处理按钮
            self.process_button.config(state=tk.NORMAL)
    
    def import_data(self):
        """导入数据到数据库"""
        # 获取文件路径列表和选项
        files_str = self.import_files_var.get()
        if not files_str:
            messagebox.showerror("错误", "请选择至少一个导入文件")
            return
        
        file_paths = files_str.split(";;")
        if not file_paths:
            messagebox.showerror("错误", "请选择至少一个导入文件")
            return
        
        platform_type = self.platform_var.get()
        order_type = self.order_type_var.get()
        
        # 检查文件是否存在
        for file_path in file_paths:
            if not os.path.exists(file_path):
                messagebox.showerror("错误", f"文件不存在: {file_path}")
                return
        
        # 禁用导入按钮，防止重复点击
        self.import_button.config(state=tk.DISABLED)
        self.status_var.set("导入中...")
        
        # 在新线程中导入数据，避免界面卡死
        self.import_thread = threading.Thread(target=self.run_import, args=(file_paths, platform_type, order_type))
        self.import_thread.daemon = True
        self.import_thread.start()
    
    def run_import(self, file_paths, platform_type, order_type):
        """在新线程中运行导入过程，支持多个文件"""
        try:
            self.log_import_message(f"开始导入数据，平台类型: {platform_type}, 订单类型: {order_type}")
            
            # 备份数据库
            backup_file = self.backup_database()
            if not backup_file:
                self.log_import_message("警告: 数据库备份失败，但将继续导入")
            else:
                self.log_import_message(f"数据库已备份到: {backup_file}")
            
            # 导入前确保数据库目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # 获取数据导入脚本的路径
            script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "数据导入脚本.py")
            
            # 检查脚本是否存在
            if not os.path.exists(script_path):
                raise FileNotFoundError(f"数据导入脚本不存在: {script_path}")
            
            # 处理每个文件
            all_success = True
            for file_path in file_paths:
                try:
                    self.log_import_message(f"处理文件: {os.path.basename(file_path)}")
                    
                    # 使用subprocess调用脚本，以便捕获输出
                    self.log_import_message(f"正在导入{platform_type}平台数据...")
                    
                    # 构建命令
                    cmd = [
                        sys.executable, 
                        script_path, 
                        "--file", file_path, 
                        "--platform", platform_type, 
                        "--db_path", self.db_path
                    ]
                    
                    # 如果指定了订单类型，添加到命令参数中
                    if order_type != "all":
                        cmd.extend(["--order_type", order_type])
                    
                    # 执行命令
                    process = subprocess.Popen(
                        cmd, 
                        stdout=subprocess.PIPE, 
                        stderr=subprocess.PIPE,
                        universal_newlines=True,
                        encoding='utf-8',
                        bufsize=1  # 行缓冲，确保及时获取输出
                    )
                    
                    # 创建线程实时读取输出
                    def read_output(pipe, is_error=False):
                        for line in pipe:
                            line = line.strip()
                            if line:
                                if is_error:
                                    self.log_import_message(f"错误: {line}")
                                else:
                                    # 检查是否是进度条信息
                                    if "[" in line and "]" in line and "%" in line:
                                        # 更新状态栏显示进度
                                        self.status_var.set(line)
                                    self.log_import_message(line)
                    
                    # 创建并启动读取线程
                    stdout_thread = threading.Thread(target=read_output, args=(process.stdout,))
                    stderr_thread = threading.Thread(target=read_output, args=(process.stderr, True))
                    stdout_thread.daemon = True
                    stderr_thread.daemon = True
                    stdout_thread.start()
                    stderr_thread.start()
                    
                    # 等待处理完成
                    process.wait()
                    stdout_thread.join()
                    stderr_thread.join()
                    
                    # 根据返回码判断是否成功
                    if process.returncode == 0:
                        self.log_import_message(f"文件 {os.path.basename(file_path)} 导入成功")
                    else:
                        self.log_import_message(f"文件 {os.path.basename(file_path)} 导入失败，返回码: {process.returncode}")
                        all_success = False
                
                except Exception as e:
                    self.log_import_message(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
                    all_success = False
            
            # 根据所有文件的处理结果显示最终消息
            if all_success:
                self.log_import_message("所有文件导入成功")
                self.status_var.set("导入完成")
                messagebox.showinfo("成功", "所有文件导入成功")
            else:
                self.log_import_message("部分或全部文件导入失败")
                self.status_var.set("导入部分失败")
                messagebox.showerror("错误", "部分或全部文件导入失败，请查看日志了解详情")
                
                # 如果导入失败且有备份，询问是否恢复
                if backup_file and messagebox.askyesno("恢复备份", "导入失败，是否恢复数据库备份?"):
                    try:
                        # 复制备份文件到数据库位置
                        shutil.copy2(backup_file, self.db_path)
                        self.log_import_message("数据库已恢复到备份状态")
                        messagebox.showinfo("恢复成功", "数据库已恢复到备份状态")
                    except Exception as e:
                        self.log_import_message(f"数据库恢复失败: {str(e)}")
                        messagebox.showerror("恢复失败", f"数据库恢复失败: {str(e)}")
        
        except Exception as e:
            self.log_import_message(f"导入数据时出错: {str(e)}")
            self.status_var.set("导入出错")
            messagebox.showerror("错误", f"导入数据时出错: {str(e)}")
        
        finally:
            # 恢复导入按钮
            self.import_button.config(state=tk.NORMAL)
    
    def create_refund_tab(self):
        """创建退款处理选项卡内容"""
        # 主框架
        frame = ttk.Frame(self.refund_tab, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(frame, text="退款文件选择", padding="10")
        file_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(file_frame, text="退款文件(可多选):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.refund_files_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.refund_files_var, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览...", command=self.browse_refund_files).grid(row=0, column=2, padx=5, pady=5)
        
        # 已选择文件列表框
        ttk.Label(file_frame, text="已选择的文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.selected_files_text = tk.Text(file_frame, wrap=tk.WORD, width=50, height=5)
        self.selected_files_text.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W+tk.E)
        scrollbar = ttk.Scrollbar(file_frame, orient="vertical", command=self.selected_files_text.yview)
        scrollbar.grid(row=1, column=2, sticky=tk.N+tk.S)
        self.selected_files_text.configure(yscrollcommand=scrollbar.set)
        self.selected_files_text.config(state=tk.DISABLED)
        
        # 操作按钮区域
        button_frame = ttk.Frame(frame, padding="10")
        button_frame.pack(fill=tk.X, pady=10)
        
        # 处理按钮
        self.refund_button = ttk.Button(button_frame, text="开始处理退款", command=self.process_refunds)
        self.refund_button.pack(side=tk.LEFT, padx=5)
        
        # 备份数据库按钮
        ttk.Button(button_frame, text="备份数据库", command=self.backup_database).pack(side=tk.LEFT, padx=5)
        
        # 清空按钮
        ttk.Button(button_frame, text="清空选择", command=self.clear_refund_selection).pack(side=tk.LEFT, padx=5)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(frame, text="退款处理日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建文本框和滚动条
        self.refund_log_text = tk.Text(log_frame, wrap=tk.WORD, width=80, height=15)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.refund_log_text.yview)
        self.refund_log_text.configure(yscrollcommand=scrollbar.set)
        
        self.refund_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置只读
        self.refund_log_text.config(state=tk.DISABLED)
        
        # 显示初始提示信息
        self.log_refund_message("请选择包含REFUND_LIST工作表的Excel文件进行退款处理")
    
    def create_settings_tab(self):
        """创建数据库设置选项卡内容"""
        # 主框架
        frame = ttk.Frame(self.settings_tab, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 数据库路径设置区域
        path_frame = ttk.LabelFrame(frame, text="数据库路径设置", padding="10")
        path_frame.pack(fill=tk.X, pady=10)
        
        # 显示当前数据库路径
        ttk.Label(path_frame, text="当前数据库路径:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.db_path_var = tk.StringVar(value=self.db_path)
        ttk.Entry(path_frame, textvariable=self.db_path_var, width=50, state="readonly").grid(row=0, column=1, padx=5, pady=5)
        
        # 新数据库路径设置
        ttk.Label(path_frame, text="新数据库路径:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.new_db_path_var = tk.StringVar()
        ttk.Entry(path_frame, textvariable=self.new_db_path_var, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(path_frame, text="浏览...", command=self.browse_db_path).grid(row=1, column=2, padx=5, pady=5)
        
        # 应用和重置按钮
        button_frame = ttk.Frame(path_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=10)
        ttk.Button(button_frame, text="应用设置", command=self.apply_db_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置为默认", command=self.reset_db_settings).pack(side=tk.LEFT, padx=5)
        
        # 备份与恢复区域
        backup_frame = ttk.LabelFrame(frame, text="数据库备份与恢复", padding="10")
        backup_frame.pack(fill=tk.X, pady=10)
        
        # 备份按钮
        ttk.Button(backup_frame, text="备份数据库", command=self.backup_database).grid(row=0, column=0, padx=5, pady=5)
        
        # 恢复按钮
        ttk.Button(backup_frame, text="恢复备份", command=self.restore_backup).grid(row=0, column=1, padx=5, pady=5)
        
        # 显示备份列表
        ttk.Label(backup_frame, text="可用备份:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.backup_listbox = tk.Listbox(backup_frame, width=70, height=5)
        self.backup_listbox.grid(row=2, column=0, columnspan=3, padx=5, pady=5, sticky=tk.W+tk.E)
        scrollbar = ttk.Scrollbar(backup_frame, orient="vertical", command=self.backup_listbox.yview)
        scrollbar.grid(row=2, column=3, sticky=tk.N+tk.S)
        self.backup_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 刷新备份列表按钮
        ttk.Button(backup_frame, text="刷新备份列表", command=self.refresh_backup_list).grid(row=3, column=0, padx=5, pady=5)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(frame, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建文本框和滚动条
        self.settings_log_text = tk.Text(log_frame, wrap=tk.WORD, width=80, height=15)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.settings_log_text.yview)
        self.settings_log_text.configure(yscrollcommand=scrollbar.set)
        
        self.settings_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置只读
        self.settings_log_text.config(state=tk.DISABLED)
        
        # 显示初始提示信息
        self.log_settings_message("数据库设置就绪")
    
    def browse_db_path(self):
        """浏览选择数据库文件路径"""
        filename = filedialog.asksaveasfilename(
            title="选择数据库文件路径",
            filetypes=[("数据库文件", "*.db"), ("所有文件", "*.*")],
            defaultextension=".db",
            initialfile="sales_reports.db"
        )
        if filename:
            self.new_db_path_var.set(filename)
            self.log_settings_message(f"已选择新数据库路径: {filename}")
    
    def apply_db_settings(self):
        """应用数据库设置"""
        new_db_path = self.new_db_path_var.get()
        if not new_db_path:
            messagebox.showwarning("警告", "请先选择新的数据库路径")
            return
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(new_db_path), exist_ok=True)
            
            # 如果当前数据库存在且新路径不同，则复制数据库文件
            if os.path.exists(self.db_path) and os.path.normpath(self.db_path) != os.path.normpath(new_db_path):
                if messagebox.askyesno("确认", "是否将当前数据库内容复制到新位置？"):
                    shutil.copy2(self.db_path, new_db_path)
                    self.log_settings_message(f"已将数据库内容复制到新位置: {new_db_path}")
            
            # 更新配置文件
            config = configparser.ConfigParser()
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")
            
            if os.path.exists(config_path):
                config.read(config_path, encoding='utf-8')
            
            if 'Database' not in config:
                config['Database'] = {}
            
            config['Database']['db_path'] = new_db_path
            
            with open(config_path, 'w', encoding='utf-8') as f:
                config.write(f)
            
            # 更新类变量和实例变量
            self.__class__.db_path = new_db_path
            self.db_path = new_db_path
            
            # 更新显示
            self.db_path_var.set(new_db_path)
            self.new_db_path_var.set("")
            
            # 刷新备份列表，确保使用新的数据库路径
            self.refresh_backup_list()
            
            self.log_settings_message("数据库路径设置已更新并保存到配置文件")
            messagebox.showinfo("成功", "数据库路径设置已更新并立即生效")
            
        except Exception as e:
            self.log_settings_message(f"应用数据库设置时出错: {str(e)}")
            messagebox.showerror("错误", f"应用数据库设置时出错: {str(e)}")
    
    def reset_db_settings(self):
        """重置数据库设置为默认"""
        default_db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db")
        
        if messagebox.askyesno("确认", "是否将数据库路径重置为默认值？"):
            try:
                # 更新配置文件
                config = configparser.ConfigParser()
                config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")
                
                if os.path.exists(config_path):
                    config.read(config_path, encoding='utf-8')
                
                if 'Database' not in config:
                    config['Database'] = {}
                
                config['Database']['db_path'] = default_db_path
                
                with open(config_path, 'w', encoding='utf-8') as f:
                    config.write(f)
                
                # 更新类变量和实例变量
                self.__class__.db_path = default_db_path
                self.db_path = default_db_path
                
                # 更新显示
                self.db_path_var.set(default_db_path)
                self.new_db_path_var.set("")
                
                # 刷新备份列表，确保使用新的数据库路径
                self.refresh_backup_list()
                
                self.log_settings_message("数据库路径已重置为默认值")
                messagebox.showinfo("成功", "数据库路径已重置为默认值并立即生效")
                
            except Exception as e:
                self.log_settings_message(f"重置数据库设置时出错: {str(e)}")
                messagebox.showerror("错误", f"重置数据库设置时出错: {str(e)}")
    
    def log_settings_message(self, message):
        """记录设置日志消息"""
        self.settings_log_text.config(state=tk.NORMAL)
        self.settings_log_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
        self.settings_log_text.see(tk.END)
        self.settings_log_text.config(state=tk.DISABLED)
        logger.info(message)
    
    def backup_database(self):
        """备份数据库"""
        try:
            # 确保数据库目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # 检查数据库文件是否存在
            if not os.path.exists(self.db_path):
                messagebox.showwarning("警告", "数据库文件不存在，无法备份")
                return None
            
            # 创建备份目录
            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 创建备份文件名
            backup_file = os.path.join(backup_dir, f"sales_reports_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
            
            # 复制数据库文件
            shutil.copy2(self.db_path, backup_file)
            
            # 显示成功消息
            message = f"数据库已备份到: {backup_file}"
            self.log_import_message(message)
            if hasattr(self, 'settings_log_text'):
                self.log_settings_message(message)
            messagebox.showinfo("备份成功", f"数据库已备份到:\n{backup_file}")
            
            return backup_file
        except Exception as e:
            error_msg = f"备份数据库时出错: {str(e)}"
            self.log_import_message(error_msg)
            if hasattr(self, 'settings_log_text'):
                self.log_settings_message(error_msg)
            messagebox.showerror("备份失败", error_msg)
            return None
            
    def restore_backup(self):
        """从备份恢复数据库（数据库设置选项卡使用）"""
        try:
            # 检查备份目录是否存在
            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            if not os.path.exists(backup_dir):
                messagebox.showwarning("警告", "备份目录不存在，无法恢复")
                return False
            
            # 确保备份文件列表已加载
            if not hasattr(self, 'backup_files') or not self.backup_files:
                # 刷新备份列表
                self.refresh_backup_list()
                if not hasattr(self, 'backup_files') or not self.backup_files:
                    messagebox.showwarning("警告", "没有找到备份文件，无法恢复")
                    return False
            
            # 创建选择备份文件的对话框
            dialog = tk.Toplevel(self)
            dialog.title("选择要恢复的备份")
            dialog.geometry("600x400")
            dialog.transient(self)  # 设置为应用程序的子窗口
            dialog.grab_set()  # 模态对话框
            
            # 创建列表框
            frame = ttk.Frame(dialog, padding="10")
            frame.pack(fill=tk.BOTH, expand=True)
            
            ttk.Label(frame, text="请选择要恢复的备份文件:").pack(anchor=tk.W, pady=(0, 5))
            
            # 创建列表框和滚动条
            listbox_frame = ttk.Frame(frame)
            listbox_frame.pack(fill=tk.BOTH, expand=True, pady=5)
            
            listbox = tk.Listbox(listbox_frame, width=70, height=15)
            scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=listbox.yview)
            listbox.configure(yscrollcommand=scrollbar.set)
            
            listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # 填充备份文件列表
            for backup_file in self.backup_files:
                try:
                    # 从文件名提取日期时间
                    date_str = backup_file.replace("sales_reports_backup_", "").replace(".db", "")
                    date_obj = datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                    formatted_date = date_obj.strftime("%Y-%m-%d %H:%M:%S")
                    
                    # 获取文件大小
                    file_path = os.path.join(backup_dir, backup_file)
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    
                    # 添加到列表
                    listbox.insert(tk.END, f"{formatted_date} - {file_size:.2f} KB")
                except Exception:
                    listbox.insert(tk.END, backup_file)
            
            # 如果有备份文件，默认选择第一个（最新的）
            if self.backup_files:
                listbox.selection_set(0)
            
            # 按钮区域
            button_frame = ttk.Frame(frame)
            button_frame.pack(fill=tk.X, pady=10)
            
            # 取消按钮
            ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)
            
            # 确定按钮
            def on_confirm():
                # 获取选中的备份文件
                selection = listbox.curselection()
                if not selection:
                    messagebox.showwarning("警告", "请选择一个备份文件")
                    return
                
                selected_index = selection[0]
                selected_backup = self.backup_files[selected_index]
                backup_path = os.path.join(backup_dir, selected_backup)
                
                # 确认恢复
                if messagebox.askyesno("确认恢复", f"确定要从以下备份恢复数据库吗？\n{selected_backup}\n\n这将覆盖当前的数据库！"):
                    # 先备份当前数据库
                    current_backup = None
                    if os.path.exists(self.db_path):
                        current_backup = self.backup_database()
                    
                    try:
                        # 复制备份文件到数据库位置
                        shutil.copy2(backup_path, self.db_path)
                        
                        message = f"数据库已从备份恢复: {selected_backup}"
                        self.log_settings_message(message)
                        messagebox.showinfo("恢复成功", message)
                        
                        dialog.destroy()
                    except Exception as e:
                        error_msg = f"恢复数据库时出错: {str(e)}"
                        self.log_settings_message(error_msg)
                        messagebox.showerror("恢复失败", error_msg)
                        
                        # 如果恢复失败且有当前备份，则恢复到恢复前的状态
                        if current_backup and os.path.exists(current_backup):
                            try:
                                shutil.copy2(current_backup, self.db_path)
                                self.log_settings_message("已恢复到操作前的状态")
                            except Exception:
                                self.log_settings_message("警告: 无法恢复到操作前的状态")
            
            ttk.Button(button_frame, text="恢复", command=on_confirm).pack(side=tk.RIGHT, padx=5)
            
            # 等待对话框关闭
            self.wait_window(dialog)
            return True
            
        except Exception as e:
            error_msg = f"恢复备份过程中出错: {str(e)}"
            self.log_settings_message(error_msg)
            messagebox.showerror("恢复失败", error_msg)
            return False
    
    def restore_backup_import(self):
        """从备份恢复数据库（数据导入选项卡使用）"""
        try:
            # 检查备份目录是否存在
            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            if not os.path.exists(backup_dir):
                messagebox.showwarning("警告", "备份目录不存在，无法恢复")
                return False
            
            # 确保备份文件列表已加载
            if not hasattr(self, 'backup_files') or not self.backup_files:
                # 刷新备份列表
                self.refresh_backup_list_import()
                if not hasattr(self, 'backup_files') or not self.backup_files:
                    messagebox.showwarning("警告", "没有找到备份文件，无法恢复")
                    return False
            
            # 如果没有选择，则使用列表中的备份文件
            selection = self.backup_listbox.curselection()
            if not selection:
                # 如果没有选择，提示用户选择
                messagebox.showinfo("提示", "请在备份列表中选择一个备份文件，然后再点击恢复备份按钮")
                return False
            
            selected_index = selection[0]
            selected_backup = self.backup_files[selected_index]
            backup_path = os.path.join(backup_dir, selected_backup)
            
            # 确认恢复
            if messagebox.askyesno("确认恢复", f"确定要从以下备份恢复数据库吗？\n{selected_backup}\n\n这将覆盖当前的数据库！"):
                # 先备份当前数据库
                current_backup = None
                if os.path.exists(self.db_path):
                    current_backup = self.backup_database()
                
                try:
                    # 复制备份文件到数据库位置
                    shutil.copy2(backup_path, self.db_path)
                    
                    message = f"数据库已从备份恢复: {selected_backup}"
                    self.log_import_message(message)
                    messagebox.showinfo("恢复成功", message)
                    
                except Exception as e:
                    error_msg = f"恢复数据库时出错: {str(e)}"
                    self.log_import_message(error_msg)
                    messagebox.showerror("恢复失败", error_msg)
                    
                    # 如果恢复失败且有当前备份，则恢复到恢复前的状态
                    if current_backup and os.path.exists(current_backup):
                        try:
                            shutil.copy2(current_backup, self.db_path)
                            self.log_import_message("已恢复到操作前的状态")
                        except Exception:
                            self.log_import_message("警告: 无法恢复到操作前的状态")
            
            return True
            
        except Exception as e:
            error_msg = f"恢复备份过程中出错: {str(e)}"
            self.log_import_message(error_msg)
            messagebox.showerror("恢复失败", error_msg)
            return False
    
    def refresh_backup_list_import(self):
        """刷新导入选项卡中的备份列表"""
        try:
            # 清空列表
            self.backup_listbox.delete(0, tk.END)
            
            # 检查备份目录是否存在
            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            if not os.path.exists(backup_dir):
                self.log_import_message("备份目录不存在")
                return
            
            # 获取所有备份文件
            backup_files = [f for f in os.listdir(backup_dir) if f.startswith("sales_reports_backup_") and f.endswith(".db")]
            if not backup_files:
                self.log_import_message("没有找到备份文件")
                return
            
            # 按时间排序，最新的在前面
            backup_files.sort(reverse=True)
            
            # 保存备份文件列表供恢复功能使用
            self.backup_files = backup_files
            
            # 填充备份文件列表
            for backup_file in self.backup_files:
                try:
                    # 从文件名提取日期时间
                    date_str = backup_file.replace("sales_reports_backup_", "").replace(".db", "")
                    date_obj = datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                    formatted_date = date_obj.strftime("%Y-%m-%d %H:%M:%S")
                    
                    # 获取文件大小
                    file_path = os.path.join(backup_dir, backup_file)
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    
                    # 添加到列表
                    self.backup_listbox.insert(tk.END, f"{formatted_date} - {file_size:.2f} KB")
                except Exception:
                    self.backup_listbox.insert(tk.END, backup_file)
            
            # 如果有备份文件，默认选择第一个（最新的）
            if self.backup_files:
                self.backup_listbox.selection_set(0)
                self.log_import_message(f"已刷新备份列表，共{len(self.backup_files)}个备份文件")
            
        except Exception as e:
            self.log_import_message(f"刷新备份列表时出错: {str(e)}")
    
    def refresh_backup_list(self):
        """刷新数据库设置选项卡中的备份列表"""
        try:
            # 清空列表
            self.backup_listbox.delete(0, tk.END)
            
            # 检查备份目录是否存在
            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            if not os.path.exists(backup_dir):
                self.log_settings_message("备份目录不存在")
                return
            
            # 获取所有备份文件
            backup_files = [f for f in os.listdir(backup_dir) if f.startswith("sales_reports_backup_") and f.endswith(".db")]
            if not backup_files:
                self.log_settings_message("没有找到备份文件")
                return
            
            # 按时间排序，最新的在前面
            backup_files.sort(reverse=True)
            
            # 保存备份文件列表供恢复功能使用
            self.backup_files = backup_files
            
            # 填充备份文件列表
            for backup_file in self.backup_files:
                try:
                    # 从文件名提取日期时间
                    date_str = backup_file.replace("sales_reports_backup_", "").replace(".db", "")
                    date_obj = datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                    formatted_date = date_obj.strftime("%Y-%m-%d %H:%M:%S")
                    
                    # 获取文件大小
                    file_path = os.path.join(backup_dir, backup_file)
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    
                    # 添加到列表
                    self.backup_listbox.insert(tk.END, f"{formatted_date} - {file_size:.2f} KB")
                except Exception:
                    self.backup_listbox.insert(tk.END, backup_file)
            
            # 如果有备份文件，默认选择第一个（最新的）
            if backup_files:
                self.backup_listbox.selection_set(0)
                self.log_settings_message(f"已刷新备份列表，共{len(backup_files)}个备份文件")
            
        except Exception as e:
            self.log_settings_message(f"刷新备份列表时出错: {str(e)}")

    
    def log_settings_message(self, message):
        """记录设置选项卡日志消息"""
        if hasattr(self, 'settings_log_text'):
            self.settings_log_text.config(state=tk.NORMAL)
            self.settings_log_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
            self.settings_log_text.see(tk.END)
            self.settings_log_text.config(state=tk.DISABLED)
            logger.info(message)
    
    def browse_refund_files(self):
        """浏览选择退款文件(可多选)"""
        filenames = filedialog.askopenfilenames(
            title="选择退款文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filenames:
            # 获取当前已选择的文件（如果有）
            current_files = self.refund_files_var.get().split(";;") 
            current_files = [f for f in current_files if f.strip()]
            
            # 合并当前文件和新选择的文件
            all_files = current_files + list(filenames)
            
            # 将多个文件路径用分号连接
            self.refund_files_var.set(";;".join(all_files))
            
            # 只显示文件名，不显示完整路径
            basenames = [os.path.basename(f) for f in filenames]
            self.log_refund_message(f"已选择{len(filenames)}个退款文件: {', '.join(basenames)}")
            
            # 更新显示的文件名列表
            self.update_selected_files_display(all_files)
    
    def clear_refund_selection(self):
        """清空退款选项卡的文件选择"""
        self.refund_files_var.set("")
        self.update_selected_files_display([])
        self.log_refund_message("已清空文件选择")
        
    def update_selected_files_display(self, file_paths):
        """更新已选择文件的显示"""
        if hasattr(self, 'selected_files_text'):
            self.selected_files_text.config(state=tk.NORMAL)
            self.selected_files_text.delete(1.0, tk.END)
            for file_path in file_paths:
                self.selected_files_text.insert(tk.END, f"{os.path.basename(file_path)}\n")
            self.selected_files_text.config(state=tk.DISABLED)
    
    def log_refund_message(self, message):
        """记录退款处理日志消息"""
        self.refund_log_text.config(state=tk.NORMAL)
        self.refund_log_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
        self.refund_log_text.see(tk.END)
        self.refund_log_text.config(state=tk.DISABLED)
        logger.info(message)
    
    def process_refunds(self):
        """处理退款文件"""
        # 获取文件路径列表
        files_str = self.refund_files_var.get()
        if not files_str:
            messagebox.showerror("错误", "请选择至少一个退款文件")
            return
        
        file_paths = files_str.split(";;") if files_str else []
        if not file_paths:
            messagebox.showerror("错误", "请选择至少一个退款文件")
            return
        
        # 检查文件是否存在
        for file_path in file_paths:
            if not os.path.exists(file_path):
                messagebox.showerror("错误", f"文件不存在: {file_path}")
                return
        
        # 禁用处理按钮，防止重复点击
        self.refund_button.config(state=tk.DISABLED)
        self.status_var.set("退款处理中...")
        
        # 在新线程中处理文件，避免界面卡死
        self.refund_thread = threading.Thread(target=self.run_refund_processing, args=(file_paths,))
        self.refund_thread.daemon = True
        self.refund_thread.start()
    
    def run_refund_processing(self, file_paths):
        """在新线程中运行退款处理"""
        try:
            self.log_refund_message("开始处理退款文件...")
            
            # 备份数据库
            backup_file = self.backup_database()
            if not backup_file:
                self.log_refund_message("警告: 数据库备份失败，但将继续处理")
            else:
                self.log_refund_message(f"数据库已备份到: {backup_file}")
            
            import pandas as pd
            import openpyxl
            import sqlite3
            import traceback
            
            # 处理每个文件
            for file_path in file_paths:
                try:
                    self.log_refund_message(f"处理文件: {os.path.basename(file_path)}")
                    
                    # 检查文件是否包含REFUND_LIST工作表
                    wb = openpyxl.load_workbook(file_path, read_only=True)
                    if 'REFUND_LIST' not in wb.sheetnames:
                        self.log_refund_message(f"警告: {os.path.basename(file_path)} 不包含REFUND_LIST工作表，跳过")
                        continue
                    
                    # 读取REFUND_LIST工作表
                    df = pd.read_excel(file_path, sheet_name='REFUND_LIST', engine='openpyxl')
                    
                    # 检查是否为空
                    if df.empty:
                        self.log_refund_message(f"警告: {os.path.basename(file_path)} 的REFUND_LIST工作表为空，跳过")
                        continue
                    
                    # 处理Reference列拆分
                    if 'Reference' in df.columns and 'Reference1' not in df.columns:
                        df['Reference1'] = df['Reference']
                        df['Reference2'] = ''
                        self.log_refund_message(f"已将Reference列拆分为Reference1和Reference2")
                    
                    # 判断是IOT还是ZERO类型
                    platform_type = "IOT"
                    if 'Merchant Ref ID' in df.columns:
                        # 检查第一个非空的Merchant Ref ID值
                        merchant_refs = df['Merchant Ref ID'].dropna()
                        if not merchant_refs.empty:
                            first_ref = str(merchant_refs.iloc[0]).upper()
                            if 'ZERO' in first_ref and 'IOT' not in first_ref:
                                platform_type = "ZERO"
                    
                    self.log_refund_message(f"检测到平台类型: {platform_type}")
                    
                    # 构建命令
                    script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Refund_process_修复版.py")
                    
                    # 创建临时目录存放处理文件
                    temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_refund_data")
                    os.makedirs(temp_dir, exist_ok=True)
                    
                    # 保存处理后的文件
                    temp_file = os.path.join(temp_dir, f"temp_refund_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx")
                    df.to_excel(temp_file, sheet_name='REFUND_LIST', index=False)
                    
                    # 执行命令
                    cmd = [sys.executable, script_path, "--file", temp_file, "--platform", platform_type, "--db_path", self.db_path]
                    
                    process = subprocess.Popen(
                        cmd, 
                        stdout=subprocess.PIPE, 
                        stderr=subprocess.PIPE,
                        universal_newlines=True,
                        encoding='utf-8',
                        bufsize=1  # 行缓冲，确保及时获取输出
                    )
                    
                    # 实时读取输出并显示
                    self.log_refund_message(f"开始处理文件 {os.path.basename(file_path)}...")
                    
                    # 创建线程实时读取输出
                    def read_output(pipe, is_error=False):
                        for line in pipe:
                            line = line.strip()
                            if line:
                                if is_error:
                                    self.log_refund_message(f"错误: {line}")
                                else:
                                    # 检查是否是进度条信息
                                    if "[" in line and "]" in line and "%" in line:
                                        # 更新状态栏显示进度
                                        self.status_var.set(line)
                                    self.log_refund_message(line)
                    
                    # 创建并启动读取线程
                    import threading
                    stdout_thread = threading.Thread(target=read_output, args=(process.stdout,))
                    stderr_thread = threading.Thread(target=read_output, args=(process.stderr, True))
                    stdout_thread.daemon = True
                    stderr_thread.daemon = True
                    stdout_thread.start()
                    stderr_thread.start()
                    
                    # 等待处理完成
                    process.wait()
                    stdout_thread.join()
                    stderr_thread.join()
                    
                    if process.returncode == 0:
                        self.log_refund_message(f"文件 {os.path.basename(file_path)} 处理成功")
                    else:
                        self.log_refund_message(f"文件 {os.path.basename(file_path)} 处理失败，返回码: {process.returncode}")
                    
                    # 清理临时文件
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                    except Exception as e:
                        self.log_refund_message(f"清理临时文件时出错: {str(e)}")
                
                except Exception as e:
                    self.log_refund_message(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
                    self.log_refund_message(traceback.format_exc())
            
            self.log_refund_message("退款处理完成")
            self.status_var.set("退款处理完成")
        
        except Exception as e:
            self.log_refund_message(f"退款处理过程中出错: {str(e)}")
            self.log_refund_message(traceback.format_exc())
            self.status_var.set("退款处理出错")
        
        finally:
            # 恢复处理按钮
            self.refund_button.config(state=tk.NORMAL)

# 主程序入口
if __name__ == "__main__":
    try:
        # 创建应用程序实例
        app = iPhoneStyleApp()
        app.mainloop()
    except Exception as e:
        logger.error(f"应用程序启动失败: {str(e)}")
        print(f"错误: {str(e)}")
        traceback.print_exc()