#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试导入状态脚本 - 检查导入过程的状态
"""

import os
import sys
import subprocess
import time

def check_import_script_status():
    """检查导入脚本状态"""
    print("🔧 检查导入脚本状态")
    print("=" * 50)
    
    # 检查脚本文件是否存在
    script_path = "data_import_optimized.py"
    if os.path.exists(script_path):
        print(f"✅ 导入脚本存在: {script_path}")
    else:
        print(f"❌ 导入脚本不存在: {script_path}")
        return False
    
    # 检查脚本是否可以正常导入
    try:
        from data_import_optimized import DataImportProcessor
        print("✅ 导入脚本可以正常导入")
        
        # 检查关键方法是否存在
        processor = DataImportProcessor()
        required_methods = [
            'process_file',
            '_get_data_for_table',
            '_is_gui_environment',
            'handle_duplicate_data_interaction'
        ]
        
        for method in required_methods:
            if hasattr(processor, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法缺失")
                return False
                
    except Exception as e:
        print(f"❌ 导入脚本导入失败: {e}")
        return False
    
    return True

def check_environment_variables():
    """检查环境变量设置"""
    print("\n🔧 检查环境变量设置")
    print("=" * 50)
    
    # 检查关键环境变量
    env_vars = {
        'FORCE_CONSOLE': '1',
        'AUTO_DUPLICATE_HANDLING': 'skip',
        'PYTHONIOENCODING': 'utf-8'
    }
    
    for var_name, expected_value in env_vars.items():
        actual_value = os.environ.get(var_name)
        if actual_value == expected_value:
            print(f"✅ {var_name} = {actual_value}")
        else:
            print(f"⚠️ {var_name} = {actual_value} (期望: {expected_value})")
    
    return True

def simulate_import_command():
    """模拟导入命令执行"""
    print("\n🔧 模拟导入命令执行")
    print("=" * 50)
    
    # 构建导入命令
    script_path = "data_import_optimized.py"
    file_path = "030725 CHINA IOT.xlsx"  # 假设文件存在
    platform = "IOT"
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    cmd = [
        sys.executable,
        script_path,
        '--file', file_path,
        '--platform', platform,
        '--db_path', db_path
    ]
    
    print("构建的命令:")
    print(f"  {' '.join(cmd)}")
    
    # 设置环境变量
    env = os.environ.copy()
    env['FORCE_CONSOLE'] = '1'
    env['AUTO_DUPLICATE_HANDLING'] = 'skip'
    env['PYTHONIOENCODING'] = 'utf-8'
    
    print("\n设置的环境变量:")
    print(f"  FORCE_CONSOLE = {env.get('FORCE_CONSOLE')}")
    print(f"  AUTO_DUPLICATE_HANDLING = {env.get('AUTO_DUPLICATE_HANDLING')}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"⚠️ 测试文件不存在: {file_path}")
        print("这是正常的，因为我们只是在模拟")
        return True
    
    return True

def check_database_connection():
    """检查数据库连接"""
    print("\n🔧 检查数据库连接")
    print("=" * 50)
    
    try:
        # 尝试导入数据库模块
        sys.path.append('..')
        from database.connection_pool import get_connection
        
        print("✅ 数据库模块导入成功")
        
        # 尝试连接数据库
        try:
            with get_connection() as conn:
                print("✅ 数据库连接成功")
                return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 数据库模块导入失败: {e}")
        return False

def show_troubleshooting_tips():
    """显示故障排除提示"""
    print("\n" + "=" * 60)
    print("🔧 故障排除提示")
    print("=" * 60)
    
    print("\n如果导入仍然卡住，请尝试以下方法：")
    print("")
    print("1. 🚨 手动设置环境变量:")
    print("   在命令行中执行:")
    print("   set FORCE_CONSOLE=1")
    print("   set AUTO_DUPLICATE_HANDLING=skip")
    print("")
    print("2. 🔧 直接运行导入脚本:")
    print("   python data_import_optimized.py --file \"030725 CHINA IOT.xlsx\" --platform IOT")
    print("")
    print("3. 📋 检查文件路径:")
    print("   确保文件路径正确，没有特殊字符")
    print("")
    print("4. 🗂️ 检查数据库:")
    print("   确保数据库文件存在且可访问")
    print("")
    print("5. 📊 查看详细日志:")
    print("   检查应用程序的日志输出，寻找错误信息")
    print("")
    print("6. 🔄 重启应用:")
    print("   关闭应用程序，重新启动后再试")

def main():
    """主函数"""
    print("🔧 调试导入状态")
    print("=" * 60)
    
    # 执行所有检查
    checks = [
        ("导入脚本状态", check_import_script_status),
        ("环境变量设置", check_environment_variables),
        ("导入命令模拟", simulate_import_command),
        ("数据库连接", check_database_connection),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            print(f"\n正在执行: {check_name}")
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {e}")
            results.append((check_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 检查结果总结:")
    
    passed = 0
    for check_name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"   {check_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n总体状态: {passed}/{total} 检查通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有检查都通过！")
        print("导入脚本应该能够正常运行。")
        print("如果仍然有问题，可能是文件特定的问题。")
    else:
        print("⚠️ 发现一些问题，请查看上面的详细信息")
    
    # 显示故障排除提示
    show_troubleshooting_tips()

if __name__ == "__main__":
    main()
