#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试重构模块导入
"""

print("🔧 [TEST] 开始测试重构模块导入", flush=True)

import os
import sys

# 添加父目录到路径
print("🔧 [TEST] 设置sys.path", flush=True)
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print("🔧 [TEST] sys.path设置完成", flush=True)

# 测试各个模块导入
modules_to_test = [
    ("utils.logger", "get_logger"),
    ("utils.exceptions", "*"),
    ("utils.validators", "input_validator"),
    ("database.connection_pool", "get_connection"),
    ("database.models", "TABLE_SCHEMAS"),
    ("database.smart_backup_manager", "get_smart_backup_manager")
]

for module_name, import_item in modules_to_test:
    try:
        print(f"🔧 [TEST] 测试导入 {module_name}", flush=True)
        if import_item == "*":
            exec(f"from {module_name} import *")
        else:
            exec(f"from {module_name} import {import_item}")
        print(f"🔧 [TEST] ✅ {module_name} 导入成功", flush=True)
    except Exception as e:
        print(f"🔧 [TEST] ❌ {module_name} 导入失败: {e}", flush=True)

print("🔧 [TEST] 重构模块测试完成", flush=True)
