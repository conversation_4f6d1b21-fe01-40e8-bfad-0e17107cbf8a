# -*- coding: utf-8 -*-
"""
PostgreSQL数据库适配器
提供与SQLite兼容的接口，支持无缝切换
"""

import psycopg2
import psycopg2.extras
import sqlite3
import pandas as pd
import os
from typing import Optional, Dict, Any, List, Union
from contextlib import contextmanager
import configparser
import logging

logger = logging.getLogger(__name__)

class DatabaseAdapter:
    """数据库适配器 - 支持SQLite和PostgreSQL"""
    
    def __init__(self, config_file: str = "config.ini"):
        self.config_file = config_file
        self.db_type = None
        self.connection_params = {}
        self._load_config()
    
    def _load_config(self):
        """加载数据库配置"""
        if not os.path.exists(self.config_file):
            # 默认使用SQLite
            self.db_type = "sqlite"
            self.connection_params = {
                'database': os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                       "..", "database", "sales_reports.db")
            }
            return
        
        config = configparser.ConfigParser()
        config.read(self.config_file, encoding='utf-8')
        
        # 检查是否配置了PostgreSQL
        if config.has_option('Database', 'db_type'):
            self.db_type = config.get('Database', 'db_type', fallback='sqlite')
        else:
            self.db_type = "sqlite"
        
        if self.db_type == "postgresql":
            self.connection_params = {
                'host': config.get('Database', 'db_host', fallback='localhost'),
                'port': config.get('Database', 'db_port', fallback='5432'),
                'database': config.get('Database', 'db_name'),
                'user': config.get('Database', 'db_user'),
                'password': config.get('Database', 'db_password')
            }
        else:
            # SQLite配置
            db_path = config.get('Database', 'db_path', fallback='')
            if not db_path:
                db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                     "..", "database", "sales_reports.db")
            self.connection_params = {'database': db_path}
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            if self.db_type == "postgresql":
                conn = psycopg2.connect(**self.connection_params)
                conn.autocommit = True
            else:
                conn = sqlite3.connect(self.connection_params['database'])
                conn.row_factory = sqlite3.Row  # 支持字典式访问
            
            yield DatabaseConnection(conn, self.db_type)
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_connection() as db_conn:
                if self.db_type == "postgresql":
                    db_conn.execute("SELECT 1")
                else:
                    db_conn.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        info = {
            'type': self.db_type,
            'connection_params': self.connection_params.copy()
        }
        
        # 隐藏密码
        if 'password' in info['connection_params']:
            info['connection_params']['password'] = '***'
        
        return info

class DatabaseConnection:
    """数据库连接包装器"""
    
    def __init__(self, connection, db_type: str):
        self.connection = connection
        self.db_type = db_type
    
    def execute(self, query: str, params: tuple = None) -> Any:
        """执行SQL查询"""
        cursor = self.connection.cursor()
        
        if params:
            if self.db_type == "postgresql":
                # PostgreSQL使用%s占位符
                query = self._convert_placeholders(query)
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        return cursor
    
    def executemany(self, query: str, params_list: List[tuple]) -> Any:
        """批量执行SQL查询"""
        cursor = self.connection.cursor()
        
        if self.db_type == "postgresql":
            query = self._convert_placeholders(query)
        
        cursor.executemany(query, params_list)
        return cursor
    
    def fetchall(self, query: str, params: tuple = None) -> List[Any]:
        """查询所有结果"""
        cursor = self.execute(query, params)
        return cursor.fetchall()
    
    def fetchone(self, query: str, params: tuple = None) -> Any:
        """查询单个结果"""
        cursor = self.execute(query, params)
        return cursor.fetchone()
    
    def read_sql(self, query: str, params: tuple = None) -> pd.DataFrame:
        """读取SQL查询结果为DataFrame"""
        if self.db_type == "postgresql":
            query = self._convert_placeholders(query)
            return pd.read_sql(query, self.connection, params=params)
        else:
            return pd.read_sql(query, self.connection, params=params)
    
    def to_sql(self, df: pd.DataFrame, table_name: str, 
              if_exists: str = 'append', index: bool = False) -> None:
        """将DataFrame写入数据库"""
        if self.db_type == "postgresql":
            # PostgreSQL需要特殊处理
            df.to_sql(table_name, self.connection, if_exists=if_exists, 
                     index=index, method='multi')
        else:
            df.to_sql(table_name, self.connection, if_exists=if_exists, index=index)
    
    def _convert_placeholders(self, query: str) -> str:
        """转换占位符格式"""
        if self.db_type == "postgresql":
            # 将?占位符转换为%s
            return query.replace('?', '%s')
        return query
    
    def get_table_names(self) -> List[str]:
        """获取所有表名"""
        if self.db_type == "postgresql":
            query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            """
        else:
            query = "SELECT name FROM sqlite_master WHERE type='table'"
        
        cursor = self.execute(query)
        return [row[0] for row in cursor.fetchall()]
    
    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表结构"""
        if self.db_type == "postgresql":
            query = """
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = %s
                ORDER BY ordinal_position
            """
            cursor = self.execute(query, (table_name,))
            return [
                {
                    'name': row[0],
                    'type': row[1],
                    'nullable': row[2] == 'YES',
                    'default': row[3]
                }
                for row in cursor.fetchall()
            ]
        else:
            query = f"PRAGMA table_info({table_name})"
            cursor = self.execute(query)
            return [
                {
                    'name': row[1],
                    'type': row[2],
                    'nullable': not bool(row[3]),
                    'default': row[4]
                }
                for row in cursor.fetchall()
            ]
    
    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        if self.db_type == "postgresql":
            query = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = %s
                )
            """
            cursor = self.execute(query, (table_name,))
            return cursor.fetchone()[0]
        else:
            query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
            cursor = self.execute(query, (table_name,))
            return cursor.fetchone() is not None
    
    def get_row_count(self, table_name: str) -> int:
        """获取表行数"""
        query = f"SELECT COUNT(*) FROM {table_name}"
        cursor = self.execute(query)
        return cursor.fetchone()[0]

# 全局数据库适配器实例
_db_adapter = None

def get_database_adapter() -> DatabaseAdapter:
    """获取全局数据库适配器实例"""
    global _db_adapter
    if _db_adapter is None:
        _db_adapter = DatabaseAdapter()
    return _db_adapter

def get_connection():
    """获取数据库连接（兼容原有接口）"""
    adapter = get_database_adapter()
    return adapter.get_connection()

# 兼容性函数
def test_database_connection() -> bool:
    """测试数据库连接"""
    adapter = get_database_adapter()
    return adapter.test_connection()

def get_database_info() -> Dict[str, Any]:
    """获取数据库信息"""
    adapter = get_database_adapter()
    return adapter.get_database_info()

def switch_to_postgresql(host: str, port: str, database: str, 
                        user: str, password: str) -> bool:
    """切换到PostgreSQL数据库"""
    try:
        # 更新配置文件
        config = configparser.ConfigParser()
        config_file = "config.ini"
        
        if os.path.exists(config_file):
            config.read(config_file, encoding='utf-8')
        
        if 'Database' not in config:
            config['Database'] = {}
        
        config['Database']['db_type'] = 'postgresql'
        config['Database']['db_host'] = host
        config['Database']['db_port'] = port
        config['Database']['db_name'] = database
        config['Database']['db_user'] = user
        config['Database']['db_password'] = password
        
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
        
        # 重新加载适配器
        global _db_adapter
        _db_adapter = None
        
        # 测试连接
        return test_database_connection()
        
    except Exception as e:
        logger.error(f"切换到PostgreSQL失败: {e}")
        return False

def switch_to_sqlite(db_path: str) -> bool:
    """切换到SQLite数据库"""
    try:
        # 更新配置文件
        config = configparser.ConfigParser()
        config_file = "config.ini"
        
        if os.path.exists(config_file):
            config.read(config_file, encoding='utf-8')
        
        if 'Database' not in config:
            config['Database'] = {}
        
        config['Database']['db_type'] = 'sqlite'
        config['Database']['db_path'] = db_path
        
        # 移除PostgreSQL配置
        for key in ['db_host', 'db_port', 'db_name', 'db_user', 'db_password']:
            if key in config['Database']:
                del config['Database'][key]
        
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
        
        # 重新加载适配器
        global _db_adapter
        _db_adapter = None
        
        # 测试连接
        return test_database_connection()
        
    except Exception as e:
        logger.error(f"切换到SQLite失败: {e}")
        return False
