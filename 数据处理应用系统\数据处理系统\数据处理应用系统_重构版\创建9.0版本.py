#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建数据处理应用9.0版本
完整复制并修改所有配置和脚本，确保独立运行
"""

import os
import shutil
import sys
from pathlib import Path
import configparser
import re

def create_version_9():
    """创建9.0版本的完整副本"""
    
    # 获取当前脚本所在目录（重构版目录）
    base_dir = Path(__file__).parent
    
    # 源目录和目标目录
    source_dir = base_dir
    target_dir = base_dir / "数据处理应用9.0版本"
    
    print(f"🚀 开始创建数据处理应用9.0版本...")
    print(f"📁 源目录: {source_dir}")
    print(f"📁 目标目录: {target_dir}")
    
    # 确保目标目录存在
    target_dir.mkdir(exist_ok=True)
    
    # 需要复制的文件和文件夹列表
    items_to_copy = [
        # 主程序文件
        ("01_主程序/数据处理与导入应用_完整版.py", "数据处理应用9.0版本.py"),
        
        # 依赖的Python模块
        ("01_主程序/unified_processing_tab.py", "unified_processing_tab.py"),
        
        # 配置文件夹
        ("03_配置文件", "config"),
        ("config", "config_backup"),  # 如果存在的话
        
        # 脚本文件夹
        ("scripts", "scripts"),
        
        # 数据库文件夹
        ("database", "database"),
        
        # 工具类文件夹
        ("utils", "utils"),
        
        # UI组件文件夹
        ("ui", "ui"),
        
        # 其他重要文件
        ("01_主程序/Refund_process_修复版.py", "Refund_process_修复版.py"),
    ]
    
    # 需要创建的空文件夹
    folders_to_create = [
        "logs",
        "backups", 
        "temp_data",
        "PostgreSQL database",
    ]
    
    print("\n📋 开始复制文件和文件夹...")
    
    # 复制文件和文件夹
    for source_item, target_item in items_to_copy:
        source_path = source_dir / source_item
        target_path = target_dir / target_item
        
        if source_path.exists():
            try:
                if source_path.is_file():
                    # 确保目标目录存在
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(source_path, target_path)
                    print(f"✅ 复制文件: {source_item} -> {target_item}")
                elif source_path.is_dir():
                    if target_path.exists():
                        shutil.rmtree(target_path)
                    shutil.copytree(source_path, target_path)
                    print(f"✅ 复制文件夹: {source_item} -> {target_item}")
            except Exception as e:
                print(f"❌ 复制失败 {source_item}: {e}")
        else:
            print(f"⚠️ 源文件不存在: {source_item}")
    
    # 创建空文件夹
    print("\n📁 创建必要的空文件夹...")
    for folder in folders_to_create:
        folder_path = target_dir / folder
        folder_path.mkdir(exist_ok=True)
        print(f"✅ 创建文件夹: {folder}")
    
    print("\n🔧 开始修改配置和路径...")
    
    # 修改主程序文件
    modify_main_program(target_dir)
    
    # 修改配置文件
    modify_config_files(target_dir)
    
    # 修改脚本文件
    modify_scripts(target_dir)
    
    print("\n🎉 数据处理应用9.0版本创建完成！")
    print(f"📍 位置: {target_dir}")
    print(f"🚀 主程序: {target_dir / '数据处理应用9.0版本.py'}")

def modify_main_program(target_dir):
    """修改主程序文件"""
    main_file = target_dir / "数据处理应用9.0版本.py"
    
    if not main_file.exists():
        print("❌ 主程序文件不存在")
        return
    
    print("🔧 修改主程序文件...")
    
    try:
        # 读取文件内容
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修改应用标题和版本信息
        content = re.sub(
            r'数据处理与导入应用 - 完整版',
            '数据处理应用9.0版本',
            content
        )
        
        content = re.sub(
            r'版本: 2\.0',
            '版本: 9.0',
            content
        )
        
        # 修改窗口标题
        content = re.sub(
            r'self\.title\(["\'].*?["\']\)',
            'self.title("数据处理应用9.0版本")',
            content
        )
        
        # 修改配置文件路径 - 确保使用当前目录下的配置
        content = re.sub(
            r'parent_dir = os\.path\.dirname\(os\.path\.dirname\(os\.path\.abspath\(__file__\)\)\)',
            'parent_dir = os.path.dirname(os.path.abspath(__file__))',
            content
        )
        
        # 修改数据库路径为相对路径
        content = re.sub(
            r'os\.path\.join\(os\.path\.dirname\(os\.path\.abspath\(__file__\)\), "database", "sales_reports\.db"\)',
            'os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db")',
            content
        )
        
        # 写回文件
        with open(main_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 主程序文件修改完成")
        
    except Exception as e:
        print(f"❌ 修改主程序文件失败: {e}")

def modify_config_files(target_dir):
    """修改配置文件"""
    config_dirs = [target_dir / "config", target_dir / "03_配置文件"]
    
    for config_dir in config_dirs:
        if not config_dir.exists():
            continue
            
        config_file = config_dir / "config.ini"
        if config_file.exists():
            print(f"🔧 修改配置文件: {config_file}")
            modify_config_ini(config_file, target_dir)

def modify_config_ini(config_file, target_dir):
    """修改config.ini文件"""
    try:
        config = configparser.ConfigParser()
        config.read(config_file, encoding='utf-8')
        
        # 修改数据库路径
        if 'Database' in config:
            config['Database']['db_path'] = str(target_dir / "database" / "sales_reports.db")
        
        # 修改脚本路径
        if 'Scripts' in config:
            for key in config['Scripts']:
                old_path = config['Scripts'][key]
                # 将所有脚本路径改为相对于9.0版本目录的路径
                if 'scripts/' in old_path:
                    config['Scripts'][key] = old_path.split('scripts/')[-1]
                    config['Scripts'][key] = f"scripts/{config['Scripts'][key]}"
                elif old_path.endswith('.py'):
                    config['Scripts'][key] = old_path.split('/')[-1]
        
        # 修改备份目录
        if 'Files' in config:
            config['Files']['backup_dir'] = 'backups'
            config['Files']['temp_dir'] = 'temp_data'
        
        # 保存配置
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
        
        print(f"✅ 配置文件修改完成: {config_file}")
        
    except Exception as e:
        print(f"❌ 修改配置文件失败: {e}")

def modify_scripts(target_dir):
    """修改脚本文件中的路径"""
    scripts_dir = target_dir / "scripts"
    
    if not scripts_dir.exists():
        print("⚠️ scripts目录不存在")
        return
    
    print("🔧 修改脚本文件...")
    
    # 需要修改的脚本文件
    script_files = list(scripts_dir.glob("*.py"))
    
    for script_file in script_files:
        try:
            with open(script_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修改数据库路径引用
            content = re.sub(
                r'os\.path\.join\([^)]*"database"[^)]*\)',
                'os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "database", "sales_reports.db")',
                content
            )
            
            # 修改日志路径
            content = re.sub(
                r'logs/',
                '../logs/',
                content
            )
            
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 修改脚本: {script_file.name}")
            
        except Exception as e:
            print(f"❌ 修改脚本失败 {script_file.name}: {e}")

if __name__ == "__main__":
    create_version_9()
