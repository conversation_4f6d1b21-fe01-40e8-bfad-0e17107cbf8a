#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速数据库表数量检查
"""

import sqlite3
import os

def quick_check():
    db_path = "database/sales_reports.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 获取所有对象统计
            cursor.execute("SELECT type, COUNT(*) FROM sqlite_master GROUP BY type ORDER BY type")
            stats = cursor.fetchall()
            
            print("🔍 数据库对象统计:")
            for obj_type, count in stats:
                print(f"  {obj_type}: {count} 个")
            
            # 获取表列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
            tables = cursor.fetchall()
            
            print(f"\n📊 表详细列表 (共 {len(tables)} 个):")
            for i, (table_name,) in enumerate(tables, 1):
                print(f"  {i:2d}. {table_name}")
            
            # 获取视图列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='view' ORDER BY name")
            views = cursor.fetchall()
            
            print(f"\n👁️ 视图列表 (共 {len(views)} 个):")
            for i, (view_name,) in enumerate(views, 1):
                print(f"  {i:2d}. {view_name}")
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    quick_check()
