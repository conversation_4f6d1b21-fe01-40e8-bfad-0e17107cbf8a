# -*- coding: utf-8 -*-
"""
配置服务 - 架构优化步骤2
实现缓存配置管理、预加载和文件监控

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import os
import time
import json
import threading
import configparser
from typing import Any, Dict, List, Optional, Callable, Set
from dataclasses import dataclass, field
from pathlib import Path
import hashlib
import weakref


@dataclass
class ConfigCacheEntry:
    """配置缓存条目"""
    value: Any
    timestamp: float
    file_mtime: float
    access_count: int = 0
    last_access: float = field(default_factory=time.time)


class FileWatcher:
    """文件变更监控器"""
    
    def __init__(self, check_interval: float = 1.0):
        self.check_interval = check_interval
        self.watched_files: Dict[str, float] = {}  # file_path -> last_mtime
        self.callbacks: Dict[str, List[Callable[[str], None]]] = {}
        self.running = False
        self.worker_thread: Optional[threading.Thread] = None
        self._lock = threading.RLock()
        
    def watch_file(self, file_path: str, callback: Callable[[str], None]):
        """监控文件变更"""
        with self._lock:
            abs_path = os.path.abspath(file_path)
            
            if abs_path not in self.callbacks:
                self.callbacks[abs_path] = []
                
            self.callbacks[abs_path].append(callback)
            
            # 记录初始修改时间
            if os.path.exists(abs_path):
                self.watched_files[abs_path] = os.path.getmtime(abs_path)
            else:
                self.watched_files[abs_path] = 0
                
            # 启动监控线程
            if not self.running:
                self.start()
                
    def unwatch_file(self, file_path: str, callback: Optional[Callable[[str], None]] = None):
        """停止监控文件"""
        with self._lock:
            abs_path = os.path.abspath(file_path)
            
            if abs_path in self.callbacks:
                if callback:
                    if callback in self.callbacks[abs_path]:
                        self.callbacks[abs_path].remove(callback)
                else:
                    self.callbacks[abs_path].clear()
                    
                # 如果没有回调了，移除文件监控
                if not self.callbacks[abs_path]:
                    del self.callbacks[abs_path]
                    if abs_path in self.watched_files:
                        del self.watched_files[abs_path]
                        
    def start(self):
        """启动文件监控"""
        if self.running:
            return
            
        self.running = True
        self.worker_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.worker_thread.start()
        
    def stop(self):
        """停止文件监控"""
        self.running = False
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=2)
            if self.worker_thread.is_alive():
                print("⚠️ [CONFIG] 文件监控线程未能在2秒内停止")
        self.worker_thread = None
            
    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                with self._lock:
                    files_to_check = list(self.watched_files.keys())
                    
                for file_path in files_to_check:
                    try:
                        if os.path.exists(file_path):
                            current_mtime = os.path.getmtime(file_path)
                            last_mtime = self.watched_files.get(file_path, 0)
                            
                            if current_mtime > last_mtime:
                                # 文件已变更
                                self.watched_files[file_path] = current_mtime
                                
                                # 调用回调函数
                                with self._lock:
                                    callbacks = self.callbacks.get(file_path, []).copy()
                                    
                                for callback in callbacks:
                                    try:
                                        callback(file_path)
                                    except Exception as e:
                                        print(f"❌ File watcher callback error: {e}")
                                        
                    except Exception as e:
                        print(f"❌ File monitoring error for {file_path}: {e}")
                        
                time.sleep(self.check_interval)
                
            except Exception as e:
                print(f"❌ File monitor loop error: {e}")
                time.sleep(self.check_interval)


class CachedConfigManager:
    """
    缓存配置管理器
    
    功能：
    - 配置缓存和预加载
    - 文件变更自动检测
    - 性能优化
    - API兼容性
    """
    
    def __init__(self, config_file: str = "config.ini", cache_ttl: float = 300):
        self.config_file = os.path.abspath(config_file)
        self.cache_ttl = cache_ttl  # 缓存生存时间（秒）
        
        # 缓存系统
        self.cache: Dict[str, ConfigCacheEntry] = {}
        self.cache_lock = threading.RLock()
        
        # 配置解析器
        self.config = configparser.ConfigParser()
        self.config_lock = threading.RLock()
        
        # 文件监控
        self.file_watcher = FileWatcher(check_interval=1.0)
        self.file_watcher.watch_file(self.config_file, self._on_config_file_changed)
        
        # 性能统计
        self.stats = {
            "cache_hits": 0,
            "cache_misses": 0,
            "file_reloads": 0,
            "total_requests": 0,
            "last_reload_time": 0.0
        }
        
        # 预加载配置
        self.critical_configs: Set[str] = set()
        
        # 初始加载
        self._load_config_file()
        
    def get(self, section: str, key: str, default: Any = None, 
           fallback: Any = None) -> Any:
        """
        获取配置值（带缓存）
        
        Args:
            section: 配置节
            key: 配置键
            default: 默认值
            fallback: 回退值
            
        Returns:
            Any: 配置值
        """
        cache_key = f"{section}.{key}"
        
        with self.cache_lock:
            self.stats["total_requests"] += 1
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                entry = self.cache[cache_key]
                entry.access_count += 1
                entry.last_access = time.time()
                self.stats["cache_hits"] += 1
                return entry.value
                
            # 缓存未命中，从文件读取
            self.stats["cache_misses"] += 1
            
        # 从配置文件读取
        value = self._read_config_value(section, key, default, fallback)
        
        # 更新缓存
        with self.cache_lock:
            file_mtime = os.path.getmtime(self.config_file) if os.path.exists(self.config_file) else 0
            self.cache[cache_key] = ConfigCacheEntry(
                value=value,
                timestamp=time.time(),
                file_mtime=file_mtime
            )
            
        return value
        
    def set(self, section: str, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            section: 配置节
            key: 配置键
            value: 配置值
            
        Returns:
            bool: 是否设置成功
        """
        try:
            with self.config_lock:
                # 确保节存在
                if not self.config.has_section(section):
                    self.config.add_section(section)
                    
                # 设置值
                self.config.set(section, key, str(value))
                
                # 保存到文件
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    self.config.write(f)
                    
            # 清除相关缓存
            cache_key = f"{section}.{key}"
            with self.cache_lock:
                if cache_key in self.cache:
                    del self.cache[cache_key]
                    
            return True
            
        except Exception as e:
            print(f"❌ Failed to set config {section}.{key}: {e}")
            return False
            
    def has_option(self, section: str, key: str) -> bool:
        """检查配置项是否存在"""
        with self.config_lock:
            return self.config.has_option(section, key)
            
    def has_section(self, section: str) -> bool:
        """检查配置节是否存在"""
        with self.config_lock:
            return self.config.has_section(section)
            
    def sections(self) -> List[str]:
        """获取所有配置节"""
        with self.config_lock:
            return self.config.sections()
            
    def options(self, section: str) -> List[str]:
        """获取配置节的所有选项"""
        with self.config_lock:
            if self.config.has_section(section):
                return self.config.options(section)
            return []
            
    def add_critical_config(self, section: str, key: str):
        """添加关键配置（会被预加载）"""
        cache_key = f"{section}.{key}"
        self.critical_configs.add(cache_key)
        
        # 立即预加载
        self.get(section, key)
        
    def preload_critical_configs(self):
        """预加载关键配置"""
        print("🔧 [CONFIG] 预加载关键配置...")
        
        for cache_key in self.critical_configs:
            try:
                section, key = cache_key.split('.', 1)
                self.get(section, key)
            except Exception as e:
                print(f"❌ Failed to preload config {cache_key}: {e}")
                
        print(f"✅ [CONFIG] 预加载完成，共 {len(self.critical_configs)} 个配置项")
        
    def clear_cache(self):
        """清空缓存"""
        with self.cache_lock:
            self.cache.clear()
            
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.cache_lock:
            total_requests = self.stats["total_requests"]
            hit_rate = (self.stats["cache_hits"] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                "cache_size": len(self.cache),
                "cache_hits": self.stats["cache_hits"],
                "cache_misses": self.stats["cache_misses"],
                "hit_rate": f"{hit_rate:.1f}%",
                "file_reloads": self.stats["file_reloads"],
                "total_requests": total_requests,
                "last_reload_time": self.stats["last_reload_time"],
                "critical_configs": len(self.critical_configs)
            }
            
    def reload_config(self) -> bool:
        """重新加载配置文件"""
        try:
            self._load_config_file()
            self.clear_cache()
            return True
        except Exception as e:
            print(f"❌ Failed to reload config: {e}")
            return False
            
    def shutdown(self):
        """关闭配置管理器"""
        try:
            if self.file_watcher:
                self.file_watcher.stop()
                self.file_watcher = None

            # 清空缓存
            with self.cache_lock:
                self.cache.clear()

            print("✅ [CONFIG] 缓存配置管理器已关闭")
        except Exception as e:
            print(f"❌ [CONFIG] 关闭配置管理器时出错: {e}")
        
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.cache:
            return False
            
        entry = self.cache[cache_key]
        
        # 检查时间过期
        if time.time() - entry.timestamp > self.cache_ttl:
            return False
            
        # 检查文件修改时间
        if os.path.exists(self.config_file):
            current_mtime = os.path.getmtime(self.config_file)
            if current_mtime > entry.file_mtime:
                return False
                
        return True
        
    def _read_config_value(self, section: str, key: str, default: Any, fallback: Any) -> Any:
        """从配置文件读取值"""
        with self.config_lock:
            try:
                if self.config.has_option(section, key):
                    value = self.config.get(section, key)
                    
                    # 尝试转换类型
                    return self._convert_value(value)
                else:
                    return default if default is not None else fallback
                    
            except Exception as e:
                print(f"❌ Failed to read config {section}.{key}: {e}")
                return default if default is not None else fallback
                
    def _convert_value(self, value: str) -> Any:
        """转换配置值类型"""
        if not isinstance(value, str):
            return value
            
        # 布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
            
        # 数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
            
        # 字符串
        return value
        
    def _load_config_file(self):
        """加载配置文件"""
        try:
            with self.config_lock:
                if os.path.exists(self.config_file):
                    self.config.read(self.config_file, encoding='utf-8')
                    self.stats["file_reloads"] += 1
                    self.stats["last_reload_time"] = time.time()
                    print(f"✅ [CONFIG] 配置文件加载成功: {self.config_file}")
                else:
                    print(f"⚠️ [CONFIG] 配置文件不存在: {self.config_file}")
                    
        except Exception as e:
            print(f"❌ [CONFIG] 配置文件加载失败: {e}")
            
    def _on_config_file_changed(self, file_path: str):
        """配置文件变更回调"""
        print(f"🔄 [CONFIG] 检测到配置文件变更: {file_path}")
        
        # 重新加载配置
        self._load_config_file()
        
        # 清空缓存
        self.clear_cache()
        
        # 重新预加载关键配置
        if self.critical_configs:
            self.preload_critical_configs()


class ConfigPreloader:
    """配置预加载器"""
    
    def __init__(self, config_manager: CachedConfigManager):
        self.config_manager = config_manager
        
        # 定义关键配置项
        self.critical_configs = [
            ("Database", "db_path"),
            ("Database", "backup_dir"),
            ("UI", "window_width"),
            ("UI", "window_height"),
            ("UI", "theme"),
            ("Backup", "auto_backup"),
            ("Backup", "backup_interval"),
            ("Processing", "max_workers"),
            ("Processing", "timeout"),
            ("Logging", "level"),
            ("Logging", "file_path")
        ]
        
    def preload_all(self):
        """预加载所有关键配置"""
        print("🚀 [CONFIG] 开始预加载关键配置...")
        
        for section, key in self.critical_configs:
            try:
                self.config_manager.add_critical_config(section, key)
            except Exception as e:
                print(f"❌ Failed to preload {section}.{key}: {e}")
                
        print("✅ [CONFIG] 关键配置预加载完成")
        
    def get_preload_stats(self) -> Dict[str, Any]:
        """获取预加载统计"""
        return {
            "critical_configs_count": len(self.critical_configs),
            "cache_stats": self.config_manager.get_cache_stats()
        }


class ConfigServiceFactory:
    """配置服务工厂"""

    @staticmethod
    def create_config_service(config_file: str = "config.ini") -> CachedConfigManager:
        """创建配置服务实例"""
        print(f"🏗️ [CONFIG] 创建配置服务: {config_file}")

        # 创建缓存配置管理器
        config_manager = CachedConfigManager(config_file, cache_ttl=300)

        # 创建预加载器并预加载关键配置
        preloader = ConfigPreloader(config_manager)
        preloader.preload_all()

        print("✅ [CONFIG] 配置服务创建完成")
        return config_manager

    @staticmethod
    def create_legacy_compatible_service(config_file: str = "config.ini"):
        """创建兼容旧系统的配置服务"""
        config_manager = ConfigServiceFactory.create_config_service(config_file)

        # 添加兼容性方法
        def get_db_path():
            return config_manager.get("Database", "db_path", "data.db")

        def get_backup_dir():
            return config_manager.get("Database", "backup_dir", "backups")

        def get_window_size():
            width = config_manager.get("UI", "window_width", 1200)
            height = config_manager.get("UI", "window_height", 800)
            return width, height

        def get_theme():
            return config_manager.get("UI", "theme", "default")

        # 动态添加兼容性方法
        config_manager.get_db_path = get_db_path
        config_manager.get_backup_dir = get_backup_dir
        config_manager.get_window_size = get_window_size
        config_manager.get_theme = get_theme

        return config_manager


# 全局配置服务实例
_global_config_service: Optional[CachedConfigManager] = None


def get_config_service() -> CachedConfigManager:
    """获取全局配置服务实例"""
    global _global_config_service
    if _global_config_service is None:
        _global_config_service = ConfigServiceFactory.create_config_service()
    return _global_config_service


def set_config_service(config_service: CachedConfigManager):
    """设置全局配置服务实例"""
    global _global_config_service
    _global_config_service = config_service
