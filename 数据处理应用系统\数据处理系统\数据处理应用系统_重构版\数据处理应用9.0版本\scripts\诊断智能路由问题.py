#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断智能路由问题脚本 - 检查为什么只导入到主表
"""

import os
import sys
import sqlite3
import pandas as pd

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def check_database_tables():
    """检查数据库中的表"""
    print("🔍 检查数据库表结构")
    print("=" * 50)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"数据库中的表:")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  {table_name}: {count} 条记录")
        
        # 检查是否存在 Refunding 和 Close 表
        required_tables = [
            'IOT_Sales_Refunding', 'IOT_Sales_Close',
            'ZERO_Sales_Refunding', 'ZERO_Sales_Close',
            'APP_Sales_Refunding', 'APP_Sales_Close'
        ]
        
        existing_table_names = [table[0] for table in tables]
        missing_tables = [table for table in required_tables if table not in existing_table_names]
        
        if missing_tables:
            print(f"\n❌ 缺失的表: {missing_tables}")
            return False
        else:
            print(f"\n✅ 所有必需的表都存在")
            return True
            
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_status_detection():
    """测试状态检测逻辑"""
    print("\n🔍 测试状态检测逻辑")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 测试各种状态
        test_cases = [
            ("Finished", "IOT"),
            ("Refunding", "IOT"),
            ("Close", "IOT"),
            ("Refunded", "ZERO"),
            ("Closed", "ZERO"),
            ("Complete", "IOT"),
            ("已完成", "ZERO"),
            ("退款中", "IOT"),
            ("关闭", "ZERO"),
            ("", "IOT"),  # 空状态
            ("Unknown", "ZERO"),  # 未知状态
        ]
        
        print("状态检测结果:")
        for status, platform in test_cases:
            target_table = processor._determine_target_table(platform, status)
            print(f"  '{status}' ({platform}) → {target_table}")
        
        return True
        
    except Exception as e:
        print(f"❌ 状态检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_file_order_status():
    """检查文件中的订单状态"""
    print("\n🔍 检查文件中的订单状态")
    print("=" * 50)
    
    file_path = "030725 CHINA ZERO.xlsx"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 读取文件
        df = pd.read_excel(file_path, engine='openpyxl')
        
        print(f"文件列名: {list(df.columns)}")
        
        # 查找状态列
        status_columns = [col for col in df.columns if 'status' in col.lower() or '状态' in col]
        
        if status_columns:
            print(f"找到状态列: {status_columns}")
            
            for col in status_columns:
                unique_values = df[col].value_counts()
                print(f"\n{col} 列的唯一值:")
                for value, count in unique_values.items():
                    print(f"  '{value}': {count} 条")
        else:
            print("❌ 没有找到状态列")
            
        return True
        
    except Exception as e:
        print(f"❌ 检查文件失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smart_routing_with_sample_data():
    """使用示例数据测试智能路由"""
    print("\n🔍 测试智能路由功能")
    print("=" * 50)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        processor = DataImportProcessor()
        
        # 创建测试数据
        test_data = pd.DataFrame([
            {
                'Copartner_name': '测试合作伙伴1',
                'Order_No': 'TEST001',
                'Order_types': 'Normal',
                'Order_status': 'Finished',  # 应该路由到主表
                'Order_price': '10.00',
                'Payment': '10.00',
                'Order_time': '2025-01-01 10:00:00',
                'Equipment_ID': 'E001',
                'Equipment_name': '测试设备1',
                'Branch_name': '测试分店1',
                'Payment_date': '2025-01-01',
                'User_name': '测试用户1',
                'Time': '10:00:00',
                'Matched_Order_ID': '',
                'OrderTime_dt': '2025-01-01 10:00:00',
                'Transaction_Num': 'TXN001',
                'Import_Date': '2025-01-01'
            },
            {
                'Copartner_name': '测试合作伙伴2',
                'Order_No': 'TEST002',
                'Order_types': 'Normal',
                'Order_status': 'Refunding',  # 应该路由到退款表
                'Order_price': '20.00',
                'Payment': '20.00',
                'Order_time': '2025-01-01 11:00:00',
                'Equipment_ID': 'E002',
                'Equipment_name': '测试设备2',
                'Branch_name': '测试分店2',
                'Payment_date': '2025-01-01',
                'User_name': '测试用户2',
                'Time': '11:00:00',
                'Matched_Order_ID': '',
                'OrderTime_dt': '2025-01-01 11:00:00',
                'Transaction_Num': 'TXN002',
                'Import_Date': '2025-01-01'
            },
            {
                'Copartner_name': '测试合作伙伴3',
                'Order_No': 'TEST003',
                'Order_types': 'Normal',
                'Order_status': 'Close',  # 应该路由到关闭表
                'Order_price': '30.00',
                'Payment': '30.00',
                'Order_time': '2025-01-01 12:00:00',
                'Equipment_ID': 'E003',
                'Equipment_name': '测试设备3',
                'Branch_name': '测试分店3',
                'Payment_date': '2025-01-01',
                'User_name': '测试用户3',
                'Time': '12:00:00',
                'Matched_Order_ID': '',
                'OrderTime_dt': '2025-01-01 12:00:00',
                'Transaction_Num': 'TXN003',
                'Import_Date': '2025-01-01'
            }
        ])
        
        print("测试数据:")
        for _, row in test_data.iterrows():
            status = row['Order_status']
            target_table = processor._determine_target_table('IOT', status)
            print(f"  订单 {row['Order_No']}: '{status}' → {target_table}")
        
        # 测试数据分布分析
        distribution = processor._analyze_data_distribution(test_data, 'IOT')
        print(f"\n数据分布分析结果: {distribution}")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能路由测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 诊断智能路由问题")
    print("=" * 60)
    
    # 检查1: 数据库表
    test1_result = check_database_tables()
    
    # 检查2: 状态检测逻辑
    test2_result = test_status_detection()
    
    # 检查3: 文件中的订单状态
    test3_result = check_file_order_status()
    
    # 检查4: 智能路由功能
    test4_result = test_smart_routing_with_sample_data()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 诊断结果总结:")
    print(f"   数据库表检查: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   状态检测逻辑: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"   文件状态检查: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"   智能路由测试: {'✅ 通过' if test4_result else '❌ 失败'}")
    
    if not test1_result:
        print("\n🔧 建议：需要创建 Refunding 和 Close 表")
        print("   运行: python scripts/create_refunding_close_tables.py")
    
    if test1_result and test2_result and not test3_result:
        print("\n🔧 建议：检查文件中的订单状态列名和值")
    
    if test1_result and test2_result and test3_result and not test4_result:
        print("\n🔧 建议：检查智能路由代码逻辑")

if __name__ == "__main__":
    main()
