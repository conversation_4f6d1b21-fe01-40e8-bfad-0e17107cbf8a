#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据排序 - 检查数据库中的数据是否按Order_time正确排序
"""

import sqlite3
import os
import sys
from datetime import datetime

def check_data_order():
    """检查数据库中数据的排序"""
    print("🔍 检查数据库中数据的Order_time排序")
    print("=" * 60)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查的表
        tables_to_check = [
            'IOT_Sales', 'IOT_Sales_Refunding', 'IOT_Sales_Close',
            'ZERO_Sales', 'ZERO_Sales_Refunding', 'ZERO_Sales_Close'
        ]
        
        for table_name in tables_to_check:
            try:
                print(f"\n📊 检查表: {table_name}")
                
                # 检查表是否存在
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                if not cursor.fetchone():
                    print(f"   ⚠️ 表不存在，跳过")
                    continue
                
                # 检查是否有Order_time列
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in cursor.fetchall()]
                
                if 'Order_time' not in columns:
                    print(f"   ⚠️ 表中没有Order_time列")
                    continue
                
                # 获取总记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                total_count = cursor.fetchone()[0]
                
                if total_count == 0:
                    print(f"   ⚠️ 表为空")
                    continue
                
                print(f"   📊 总记录数: {total_count:,}")
                
                # 检查最早和最晚的记录
                cursor.execute(f"""
                    SELECT Order_time, Order_No, Order_status 
                    FROM {table_name} 
                    WHERE Order_time IS NOT NULL 
                    ORDER BY Order_time ASC 
                    LIMIT 1
                """)
                earliest = cursor.fetchone()
                
                cursor.execute(f"""
                    SELECT Order_time, Order_No, Order_status 
                    FROM {table_name} 
                    WHERE Order_time IS NOT NULL 
                    ORDER BY Order_time DESC 
                    LIMIT 1
                """)
                latest = cursor.fetchone()
                
                if earliest and latest:
                    print(f"   📅 最早记录: {earliest[0]} (订单: {earliest[1]}, 状态: {earliest[2]})")
                    print(f"   📅 最晚记录: {latest[0]} (订单: {latest[1]}, 状态: {latest[2]})")
                
                # 检查最后插入的5条记录（按rowid排序）
                cursor.execute(f"""
                    SELECT Order_time, Order_No, Order_status, rowid 
                    FROM {table_name} 
                    ORDER BY rowid DESC 
                    LIMIT 5
                """)
                last_inserted = cursor.fetchall()
                
                if last_inserted:
                    print(f"   📋 最后插入的5条记录 (按插入顺序):")
                    for i, (order_time, order_no, status, rowid) in enumerate(last_inserted, 1):
                        print(f"      {i}. {order_time} | {order_no} | {status} (rowid: {rowid})")
                
                # 检查Order_time排序是否正确
                cursor.execute(f"""
                    SELECT COUNT(*) FROM (
                        SELECT Order_time, 
                               LAG(Order_time) OVER (ORDER BY rowid) as prev_time
                        FROM {table_name}
                        WHERE Order_time IS NOT NULL
                    ) 
                    WHERE Order_time < prev_time
                """)
                disorder_count = cursor.fetchone()[0]
                
                if disorder_count == 0:
                    print(f"   ✅ 数据按Order_time正确排序")
                else:
                    print(f"   ⚠️ 发现 {disorder_count} 条记录的Order_time顺序不正确")
                
            except Exception as e:
                print(f"   ❌ 检查表 {table_name} 失败: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def check_recent_refunded_close_data():
    """专门检查最近的Refunded/Close数据"""
    print("\n🔍 检查最近的Refunded/Close数据")
    print("=" * 60)
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查Refunding表
        refunding_tables = ['IOT_Sales_Refunding', 'ZERO_Sales_Refunding']
        close_tables = ['IOT_Sales_Close', 'ZERO_Sales_Close']
        
        for table_name in refunding_tables + close_tables:
            try:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                if not cursor.fetchone():
                    continue
                
                print(f"\n📊 {table_name}:")
                
                # 获取总数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                total = cursor.fetchone()[0]
                print(f"   📊 总记录数: {total:,}")
                
                if total == 0:
                    print(f"   ⚠️ 表为空")
                    continue
                
                # 获取最近的记录
                cursor.execute(f"""
                    SELECT Order_time, Order_No, Order_status, Payment, Equipment_name
                    FROM {table_name} 
                    ORDER BY Order_time DESC 
                    LIMIT 3
                """)
                recent_records = cursor.fetchall()
                
                print(f"   📋 最近的3条记录:")
                for i, (order_time, order_no, status, payment, equipment) in enumerate(recent_records, 1):
                    print(f"      {i}. {order_time} | {order_no} | {status} | {payment} | {equipment}")
                
                # 检查今天的数据
                cursor.execute(f"""
                    SELECT COUNT(*) FROM {table_name} 
                    WHERE DATE(Order_time) = DATE('now')
                """)
                today_count = cursor.fetchone()[0]
                print(f"   📅 今天的记录数: {today_count}")
                
                # 检查最近7天的数据
                cursor.execute(f"""
                    SELECT COUNT(*) FROM {table_name} 
                    WHERE DATE(Order_time) >= DATE('now', '-7 days')
                """)
                week_count = cursor.fetchone()[0]
                print(f"   📅 最近7天的记录数: {week_count}")
                
            except Exception as e:
                print(f"   ❌ 检查 {table_name} 失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def suggest_query_commands():
    """提供查询建议"""
    print("\n💡 查询最新数据的建议命令")
    print("=" * 60)
    
    print("📋 查看最新的Refunded数据:")
    print("   SELECT * FROM IOT_Sales_Refunding ORDER BY Order_time DESC LIMIT 10;")
    print("   SELECT * FROM ZERO_Sales_Refunding ORDER BY Order_time DESC LIMIT 10;")
    
    print("\n📋 查看最新的Close数据:")
    print("   SELECT * FROM IOT_Sales_Close ORDER BY Order_time DESC LIMIT 10;")
    print("   SELECT * FROM ZERO_Sales_Close ORDER BY Order_time DESC LIMIT 10;")
    
    print("\n📋 查看今天导入的数据:")
    print("   SELECT * FROM IOT_Sales_Refunding WHERE DATE(Order_time) = DATE('now') ORDER BY Order_time DESC;")
    print("   SELECT * FROM IOT_Sales_Close WHERE DATE(Order_time) = DATE('now') ORDER BY Order_time DESC;")
    
    print("\n📋 查看特定状态的数据:")
    print("   SELECT * FROM IOT_Sales_Refunding WHERE Order_status = 'Refunded' ORDER BY Order_time DESC LIMIT 10;")
    print("   SELECT * FROM IOT_Sales_Close WHERE Order_status = 'Close' ORDER BY Order_time DESC LIMIT 10;")

def main():
    """主函数"""
    print("🔧 验证数据排序工具")
    print("=" * 60)
    
    try:
        # 1. 检查数据排序
        check_data_order()
        
        # 2. 检查最近的Refunded/Close数据
        check_recent_refunded_close_data()
        
        # 3. 提供查询建议
        suggest_query_commands()
        
        print("\n" + "=" * 60)
        print("✅ 数据排序验证完成！")
        print("\n🎯 总结:")
        print("1. ✅ 已修复数据插入排序问题")
        print("2. 📊 数据现在按Order_time升序插入")
        print("3. 🔍 最新数据应该在表的最后面")
        print("4. 💡 使用 ORDER BY Order_time DESC 查看最新数据")
        
        return 0
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
