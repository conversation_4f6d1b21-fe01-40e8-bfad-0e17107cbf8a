# -*- coding: utf-8 -*-
"""
日志级别验证工具

用于检查代码中的日志使用是否符合标准化的日志分类规范
"""

import re
import os
from typing import List, Dict, Any
from pathlib import Path


class LogLevelValidator:
    """日志级别验证器"""
    
    def __init__(self):
        # 定义日志级别的使用规范
        self.level_patterns = {
            'info': {
                'description': '用户需要的重要业务信息（UI显示）',
                'keywords': ['开始', '完成', '成功', '策略', '结果', '金额', '处理', '评估'],
                'avoid_keywords': ['详细', '技术', '调试', '内部', '具体']
            },
            'warning': {
                'description': '需要用户关注的问题（UI显示）',
                'keywords': ['警告', '注意', '失败', '问题', '建议', '检查'],
                'avoid_keywords': []
            },
            'error': {
                'description': '严重错误，需要用户处理（UI显示）',
                'keywords': ['错误', '失败', '异常', '无法', '不能'],
                'avoid_keywords': []
            },
            'debug': {
                'description': '技术实现细节（仅文件记录）',
                'keywords': ['详细', '技术', '调试', '匹配条件', '影响行数', '表名', '操作'],
                'avoid_keywords': []
            }
        }
    
    def validate_file(self, file_path: str) -> Dict[str, Any]:
        """
        验证文件中的日志级别使用
        
        Args:
            file_path: 要验证的文件路径
            
        Returns:
            验证结果字典
        """
        result = {
            'file_path': file_path,
            'total_logs': 0,
            'level_counts': {'info': 0, 'warning': 0, 'error': 0, 'debug': 0},
            'issues': [],
            'suggestions': []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 查找所有日志调用
            log_pattern = r'self\.logger\.(info|warning|error|debug)\s*\(\s*f?["\']([^"\']*)["\']'
            matches = re.finditer(log_pattern, content, re.MULTILINE)
            
            for match in matches:
                result['total_logs'] += 1
                level = match.group(1)
                message = match.group(2)
                line_num = content[:match.start()].count('\n') + 1
                
                result['level_counts'][level] += 1
                
                # 检查日志级别使用是否合适
                issues = self._check_log_level_usage(level, message, line_num)
                result['issues'].extend(issues)
            
            # 生成建议
            result['suggestions'] = self._generate_suggestions(result)
            
        except Exception as e:
            result['issues'].append(f"文件读取失败: {e}")
        
        return result
    
    def _check_log_level_usage(self, level: str, message: str, line_num: int) -> List[str]:
        """检查单个日志的级别使用是否合适"""
        issues = []
        
        # 检查INFO级别的使用
        if level == 'info':
            # 检查是否包含过多技术细节
            tech_keywords = ['表名', '匹配条件', '影响行数', 'SQL', '数据库操作']
            for keyword in tech_keywords:
                if keyword in message:
                    issues.append(f"行{line_num}: INFO级别包含技术细节'{keyword}'，建议改为DEBUG级别")
        
        # 检查DEBUG级别的使用
        elif level == 'debug':
            # 检查是否包含用户关心的业务信息
            business_keywords = ['处理完成', '成功', '失败', '金额', '订单']
            for keyword in business_keywords:
                if keyword in message and '详细' not in message:
                    issues.append(f"行{line_num}: DEBUG级别包含业务信息'{keyword}'，可能应该使用INFO级别")
        
        # 检查WARNING级别的使用
        elif level == 'warning':
            # 检查是否真的需要用户关注
            if '调试' in message or '内部' in message:
                issues.append(f"行{line_num}: WARNING级别包含调试信息，建议改为DEBUG级别")
        
        return issues
    
    def _generate_suggestions(self, result: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        total_logs = result['total_logs']
        if total_logs == 0:
            return suggestions
        
        # 检查日志级别分布
        info_ratio = result['level_counts']['info'] / total_logs
        debug_ratio = result['level_counts']['debug'] / total_logs
        
        if info_ratio > 0.6:
            suggestions.append("INFO级别日志占比过高，考虑将技术细节改为DEBUG级别")
        
        if debug_ratio < 0.2:
            suggestions.append("DEBUG级别日志较少，考虑增加技术细节的调试信息")
        
        if result['level_counts']['warning'] == 0 and result['level_counts']['error'] == 0:
            suggestions.append("缺少WARNING和ERROR级别日志，考虑添加错误处理日志")
        
        return suggestions
    
    def validate_directory(self, directory: str, pattern: str = "*.py") -> Dict[str, Any]:
        """
        验证目录中所有匹配文件的日志级别使用
        
        Args:
            directory: 要验证的目录路径
            pattern: 文件匹配模式
            
        Returns:
            验证结果汇总
        """
        summary = {
            'directory': directory,
            'files_checked': 0,
            'total_logs': 0,
            'total_issues': 0,
            'file_results': [],
            'overall_suggestions': []
        }
        
        try:
            directory_path = Path(directory)
            files = list(directory_path.glob(pattern))
            
            for file_path in files:
                if file_path.is_file():
                    result = self.validate_file(str(file_path))
                    summary['files_checked'] += 1
                    summary['total_logs'] += result['total_logs']
                    summary['total_issues'] += len(result['issues'])
                    summary['file_results'].append(result)
            
            # 生成整体建议
            if summary['total_issues'] == 0:
                summary['overall_suggestions'].append("✅ 所有文件的日志级别使用都符合规范")
            else:
                summary['overall_suggestions'].append(f"⚠️ 发现 {summary['total_issues']} 个日志级别使用问题")
                summary['overall_suggestions'].append("建议根据具体问题进行调整")
        
        except Exception as e:
            summary['overall_suggestions'].append(f"目录验证失败: {e}")
        
        return summary
    
    def print_validation_report(self, result: Dict[str, Any]):
        """打印验证报告"""
        if 'directory' in result:
            # 目录验证报告
            print(f"\n📋 日志级别验证报告 - {result['directory']}")
            print(f"检查文件数: {result['files_checked']}")
            print(f"总日志数: {result['total_logs']}")
            print(f"发现问题: {result['total_issues']}")
            
            print("\n📊 整体建议:")
            for suggestion in result['overall_suggestions']:
                print(f"  {suggestion}")
            
            if result['total_issues'] > 0:
                print("\n⚠️ 具体问题:")
                for file_result in result['file_results']:
                    if file_result['issues']:
                        print(f"\n文件: {file_result['file_path']}")
                        for issue in file_result['issues']:
                            print(f"  {issue}")
        else:
            # 单文件验证报告
            print(f"\n📋 日志级别验证报告 - {result['file_path']}")
            print(f"总日志数: {result['total_logs']}")
            print(f"级别分布: INFO({result['level_counts']['info']}) WARNING({result['level_counts']['warning']}) ERROR({result['level_counts']['error']}) DEBUG({result['level_counts']['debug']})")
            
            if result['issues']:
                print(f"\n⚠️ 发现问题 ({len(result['issues'])}个):")
                for issue in result['issues']:
                    print(f"  {issue}")
            
            if result['suggestions']:
                print(f"\n💡 优化建议:")
                for suggestion in result['suggestions']:
                    print(f"  {suggestion}")
            
            if not result['issues'] and not result['suggestions']:
                print("✅ 日志级别使用符合规范")


def main():
    """主函数 - 命令行工具"""
    import argparse
    
    parser = argparse.ArgumentParser(description='日志级别验证工具')
    parser.add_argument('path', help='要验证的文件或目录路径')
    parser.add_argument('--pattern', default='*.py', help='文件匹配模式（仅目录验证时使用）')
    
    args = parser.parse_args()
    
    validator = LogLevelValidator()
    
    if os.path.isfile(args.path):
        result = validator.validate_file(args.path)
    elif os.path.isdir(args.path):
        result = validator.validate_directory(args.path, args.pattern)
    else:
        print(f"错误: 路径不存在 - {args.path}")
        return
    
    validator.print_validation_report(result)


if __name__ == '__main__':
    main()
