#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复制infrastructure模块到9.0版本
"""

import os
import shutil
from pathlib import Path

def main():
    """复制infrastructure模块"""
    
    # 源目录和目标目录
    source_dir = Path("数据处理应用系统/数据处理系统/数据处理应用系统_重构版/01_主程序/infrastructure")
    target_dir = Path("数据处理应用系统/数据处理系统/数据处理应用系统_重构版/数据处理应用9.0版本/infrastructure")
    
    print(f"🔄 复制infrastructure模块...")
    print(f"📁 源目录: {source_dir}")
    print(f"📁 目标目录: {target_dir}")
    
    try:
        if source_dir.exists():
            if target_dir.exists():
                shutil.rmtree(target_dir)
            shutil.copytree(source_dir, target_dir)
            print("✅ infrastructure模块复制成功")
        else:
            print("❌ 源目录不存在")
            
    except Exception as e:
        print(f"❌ 复制失败: {e}")

if __name__ == "__main__":
    main()
