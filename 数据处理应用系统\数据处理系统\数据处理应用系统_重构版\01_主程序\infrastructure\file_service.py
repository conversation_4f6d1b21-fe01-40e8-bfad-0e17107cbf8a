# -*- coding: utf-8 -*-
"""
文件服务 - 架构优化步骤4
实现异步文件处理、验证缓存和状态监控

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import os
import time
import threading
import hashlib
from typing import Any, Dict, List, Optional, Tuple, Callable
try:
    from typing import Future
except ImportError:
    from concurrent.futures import Future
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback
from pathlib import Path


class FileStatus(Enum):
    """文件状态枚举"""
    UNKNOWN = "unknown"
    EXISTS = "exists"
    NOT_EXISTS = "not_exists"
    ACCESSIBLE = "accessible"
    NOT_ACCESSIBLE = "not_accessible"
    VALID = "valid"
    INVALID = "invalid"


class FileType(Enum):
    """文件类型枚举"""
    UNKNOWN = "unknown"
    EXCEL = "excel"
    CSV = "csv"
    TEXT = "text"
    DATABASE = "database"
    BACKUP = "backup"
    SCRIPT = "script"
    CONFIG = "config"


@dataclass
class FileInfo:
    """文件信息数据结构"""
    path: str
    status: FileStatus = FileStatus.UNKNOWN
    file_type: FileType = FileType.UNKNOWN
    size: int = 0
    modified_time: float = 0
    accessible: bool = False
    exists: bool = False
    validation_result: Optional[str] = None
    last_checked: float = field(default_factory=time.time)
    check_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "path": self.path,
            "status": self.status.value,
            "file_type": self.file_type.value,
            "size": self.size,
            "modified_time": self.modified_time,
            "accessible": self.accessible,
            "exists": self.exists,
            "validation_result": self.validation_result,
            "last_checked": self.last_checked,
            "check_count": self.check_count
        }


@dataclass
class ValidationCacheEntry:
    """验证缓存条目"""
    result: Tuple[bool, str]
    file_hash: str
    timestamp: float
    access_count: int = 0
    
    def is_valid(self, current_hash: str, ttl: float = 300) -> bool:
        """检查缓存是否有效"""
        return (
            self.file_hash == current_hash and
            time.time() - self.timestamp < ttl
        )


class FileService:
    """
    文件服务
    
    功能：
    - 异步文件操作
    - 文件验证缓存
    - 文件状态监控
    - 性能优化
    """
    
    def __init__(self, config_service=None, logging_service=None, 
                 max_workers: int = 4, cache_ttl: float = 300):
        self.config_service = config_service
        self.logging_service = logging_service
        self.max_workers = max_workers
        self.cache_ttl = cache_ttl
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 验证缓存
        self.validation_cache: Dict[str, ValidationCacheEntry] = {}
        self.cache_lock = threading.RLock()
        
        # 文件信息缓存
        self.file_info_cache: Dict[str, FileInfo] = {}
        self.info_cache_lock = threading.RLock()
        
        # 性能统计
        self.stats = {
            "validations_performed": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "async_operations": 0,
            "total_validation_time": 0.0,
            "average_validation_time": 0.0,
            "files_monitored": 0
        }
        
        # 支持的文件类型
        self.file_type_patterns = {
            FileType.EXCEL: ['.xlsx', '.xls'],
            FileType.CSV: ['.csv'],
            FileType.TEXT: ['.txt', '.log'],
            FileType.DATABASE: ['.db', '.sqlite', '.sqlite3'],
            FileType.BACKUP: ['.bak', '.backup'],
            FileType.SCRIPT: ['.py', '.bat', '.sh'],
            FileType.CONFIG: ['.ini', '.conf', '.cfg', '.json', '.yaml', '.yml']
        }
        
        self._log_info("文件服务已初始化")
        
    def validate_files_cached(self, file_paths: List[str]) -> Tuple[bool, str]:
        """
        带缓存的文件验证
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            Tuple[bool, str]: (是否有效, 验证消息)
        """
        start_time = time.perf_counter()
        
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(file_paths)
            
            # 检查缓存
            with self.cache_lock:
                if cache_key in self.validation_cache:
                    entry = self.validation_cache[cache_key]
                    current_hash = self._calculate_files_hash(file_paths)
                    
                    if entry.is_valid(current_hash, self.cache_ttl):
                        entry.access_count += 1
                        self.stats["cache_hits"] += 1
                        self._log_info(f"文件验证缓存命中: {len(file_paths)}个文件")
                        return entry.result
                        
            # 缓存未命中，执行验证
            self.stats["cache_misses"] += 1
            result = self._validate_files_impl(file_paths)
            
            # 更新缓存
            with self.cache_lock:
                current_hash = self._calculate_files_hash(file_paths)
                self.validation_cache[cache_key] = ValidationCacheEntry(
                    result=result,
                    file_hash=current_hash,
                    timestamp=time.time()
                )
                
            # 更新统计
            validation_time = time.perf_counter() - start_time
            self.stats["validations_performed"] += 1
            self.stats["total_validation_time"] += validation_time
            self.stats["average_validation_time"] = (
                self.stats["total_validation_time"] / self.stats["validations_performed"]
            )
            
            self._log_info(f"文件验证完成: {len(file_paths)}个文件, 耗时: {validation_time:.3f}秒")
            return result
            
        except Exception as e:
            self._log_error(f"文件验证失败: {e}")
            return False, f"验证过程出错: {str(e)}"
            
    def get_file_info_async(self, file_path: str) -> Future[FileInfo]:
        """
        异步获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Future[FileInfo]: 文件信息的Future对象
        """
        self.stats["async_operations"] += 1
        return self.executor.submit(self._get_file_info_impl, file_path)
        
    def get_file_info(self, file_path: str, use_cache: bool = True) -> FileInfo:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            use_cache: 是否使用缓存
            
        Returns:
            FileInfo: 文件信息
        """
        abs_path = os.path.abspath(file_path)
        
        # 检查缓存
        if use_cache:
            with self.info_cache_lock:
                if abs_path in self.file_info_cache:
                    cached_info = self.file_info_cache[abs_path]
                    # 检查文件是否有变化
                    if os.path.exists(abs_path):
                        current_mtime = os.path.getmtime(abs_path)
                        if current_mtime == cached_info.modified_time:
                            cached_info.check_count += 1
                            return cached_info
                            
        # 获取新的文件信息
        file_info = self._get_file_info_impl(abs_path)
        
        # 更新缓存
        if use_cache:
            with self.info_cache_lock:
                self.file_info_cache[abs_path] = file_info
                
        return file_info
        
    def batch_validate_async(self, file_paths: List[str]) -> Future[List[Tuple[str, bool, str]]]:
        """
        批量异步验证文件
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            Future[List[Tuple[str, bool, str]]]: 验证结果的Future对象
        """
        self.stats["async_operations"] += 1
        return self.executor.submit(self._batch_validate_impl, file_paths)
        
    def monitor_files(self, file_paths: List[str], callback: Callable[[str, FileInfo], None]):
        """
        监控文件状态变化
        
        Args:
            file_paths: 要监控的文件路径列表
            callback: 状态变化回调函数
        """
        def monitor_worker():
            """监控工作线程"""
            last_states = {}
            
            while True:
                try:
                    for file_path in file_paths:
                        current_info = self.get_file_info(file_path, use_cache=False)
                        
                        if file_path in last_states:
                            last_info = last_states[file_path]
                            
                            # 检查是否有变化
                            if (current_info.exists != last_info.exists or
                                current_info.modified_time != last_info.modified_time or
                                current_info.size != last_info.size):
                                
                                try:
                                    callback(file_path, current_info)
                                except Exception as e:
                                    self._log_error(f"文件监控回调错误: {e}")
                                    
                        last_states[file_path] = current_info
                        
                    time.sleep(1)  # 每秒检查一次
                    
                except Exception as e:
                    self._log_error(f"文件监控错误: {e}")
                    time.sleep(5)  # 错误时等待5秒
                    
        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()
        
        self.stats["files_monitored"] += len(file_paths)
        self._log_info(f"开始监控 {len(file_paths)} 个文件")
        
    def clear_cache(self):
        """清空缓存"""
        with self.cache_lock:
            self.validation_cache.clear()
            
        with self.info_cache_lock:
            self.file_info_cache.clear()
            
        self._log_info("文件服务缓存已清空")
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.cache_lock:
            cache_size = len(self.validation_cache)
            
        with self.info_cache_lock:
            info_cache_size = len(self.file_info_cache)
            
        total_requests = self.stats["cache_hits"] + self.stats["cache_misses"]
        hit_rate = (self.stats["cache_hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.stats,
            "validation_cache_size": cache_size,
            "info_cache_size": info_cache_size,
            "cache_hit_rate": f"{hit_rate:.1f}%",
            "executor_active_threads": self.executor._threads.__len__() if hasattr(self.executor, '_threads') else 0
        }
        
    def shutdown(self):
        """关闭文件服务"""
        self._log_info("关闭文件服务...")

        try:
            # 关闭线程池
            if self.executor:
                self.executor.shutdown(wait=True)
                self.executor = None

            # 清空缓存
            self.clear_cache()

            self._log_info("文件服务已关闭")

        except Exception as e:
            self._log_error(f"关闭文件服务时出错: {e}")
        
    def _validate_files_impl(self, file_paths: List[str]) -> Tuple[bool, str]:
        """实际的文件验证实现"""
        try:
            invalid_files = []
            
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    invalid_files.append(f"{file_path} (不存在)")
                elif not os.path.isfile(file_path):
                    invalid_files.append(f"{file_path} (不是文件)")
                elif not os.access(file_path, os.R_OK):
                    invalid_files.append(f"{file_path} (无读取权限)")
                    
            if invalid_files:
                return False, f"以下文件无效: {', '.join(invalid_files)}"
            else:
                return True, f"所有 {len(file_paths)} 个文件验证通过"
                
        except Exception as e:
            return False, f"验证过程出错: {str(e)}"
            
    def _get_file_info_impl(self, file_path: str) -> FileInfo:
        """实际的文件信息获取实现"""
        try:
            abs_path = os.path.abspath(file_path)
            file_info = FileInfo(path=abs_path)
            
            # 检查文件是否存在
            if os.path.exists(abs_path):
                file_info.exists = True
                file_info.status = FileStatus.EXISTS
                
                # 获取文件信息
                stat = os.stat(abs_path)
                file_info.size = stat.st_size
                file_info.modified_time = stat.st_mtime
                
                # 检查访问权限
                if os.access(abs_path, os.R_OK):
                    file_info.accessible = True
                    file_info.status = FileStatus.ACCESSIBLE
                else:
                    file_info.status = FileStatus.NOT_ACCESSIBLE
                    
                # 确定文件类型
                file_info.file_type = self._determine_file_type(abs_path)
                
                # 验证文件
                if file_info.accessible:
                    is_valid, message = self._validate_single_file(abs_path)
                    if is_valid:
                        file_info.status = FileStatus.VALID
                    else:
                        file_info.status = FileStatus.INVALID
                        file_info.validation_result = message
                        
            else:
                file_info.exists = False
                file_info.status = FileStatus.NOT_EXISTS
                
            file_info.check_count = 1
            return file_info
            
        except Exception as e:
            self._log_error(f"获取文件信息失败 {file_path}: {e}")
            return FileInfo(path=file_path, status=FileStatus.UNKNOWN)
            
    def _batch_validate_impl(self, file_paths: List[str]) -> List[Tuple[str, bool, str]]:
        """批量验证实现"""
        results = []
        
        for file_path in file_paths:
            try:
                is_valid, message = self._validate_single_file(file_path)
                results.append((file_path, is_valid, message))
            except Exception as e:
                results.append((file_path, False, f"验证出错: {str(e)}"))
                
        return results
        
    def _validate_single_file(self, file_path: str) -> Tuple[bool, str]:
        """验证单个文件"""
        try:
            if not os.path.exists(file_path):
                return False, "文件不存在"
                
            if not os.path.isfile(file_path):
                return False, "不是文件"
                
            if not os.access(file_path, os.R_OK):
                return False, "无读取权限"
                
            # 根据文件类型进行特定验证
            file_type = self._determine_file_type(file_path)
            
            if file_type == FileType.EXCEL:
                return self._validate_excel_file(file_path)
            elif file_type == FileType.CSV:
                return self._validate_csv_file(file_path)
            elif file_type == FileType.DATABASE:
                return self._validate_database_file(file_path)
            else:
                return True, "文件基本验证通过"
                
        except Exception as e:
            return False, f"验证出错: {str(e)}"
            
    def _determine_file_type(self, file_path: str) -> FileType:
        """确定文件类型"""
        ext = Path(file_path).suffix.lower()
        
        for file_type, extensions in self.file_type_patterns.items():
            if ext in extensions:
                return file_type
                
        return FileType.UNKNOWN
        
    def _validate_excel_file(self, file_path: str) -> Tuple[bool, str]:
        """验证Excel文件"""
        try:
            # 这里可以添加更详细的Excel文件验证
            # 例如检查文件是否损坏、是否可以打开等
            return True, "Excel文件验证通过"
        except Exception as e:
            return False, f"Excel文件验证失败: {str(e)}"
            
    def _validate_csv_file(self, file_path: str) -> Tuple[bool, str]:
        """验证CSV文件"""
        try:
            # 这里可以添加CSV文件格式验证
            return True, "CSV文件验证通过"
        except Exception as e:
            return False, f"CSV文件验证失败: {str(e)}"
            
    def _validate_database_file(self, file_path: str) -> Tuple[bool, str]:
        """验证数据库文件"""
        try:
            # 这里可以添加数据库文件完整性检查
            return True, "数据库文件验证通过"
        except Exception as e:
            return False, f"数据库文件验证失败: {str(e)}"
            
    def _generate_cache_key(self, file_paths: List[str]) -> str:
        """生成缓存键"""
        sorted_paths = sorted(file_paths)
        content = "|".join(sorted_paths)
        return hashlib.md5(content.encode()).hexdigest()
        
    def _calculate_files_hash(self, file_paths: List[str]) -> str:
        """计算文件哈希"""
        try:
            hash_content = []
            for file_path in sorted(file_paths):
                if os.path.exists(file_path):
                    stat = os.stat(file_path)
                    hash_content.append(f"{file_path}:{stat.st_size}:{stat.st_mtime}")
                else:
                    hash_content.append(f"{file_path}:not_exists")
                    
            content = "|".join(hash_content)
            return hashlib.md5(content.encode()).hexdigest()
        except Exception:
            return str(time.time())  # 回退到时间戳
            
    def _log_info(self, message: str):
        """记录信息日志"""
        if self.logging_service:
            self.logging_service.log_info(message, source="FileService")
        else:
            print(f"[FILE] {message}")
            
    def _log_error(self, message: str):
        """记录错误日志"""
        if self.logging_service:
            self.logging_service.log_error(message, source="FileService")
        else:
            print(f"[FILE ERROR] {message}")


class FileServiceFactory:
    """文件服务工厂"""
    
    @staticmethod
    def create_file_service(config_service=None, logging_service=None,
                          config: Optional[Dict[str, Any]] = None, max_workers: int = 4) -> FileService:
        """创建文件服务实例"""
        print("🏗️ [FILE] 创建文件服务...")
        
        # 默认配置
        default_config = {
            "max_workers": max_workers,
            "cache_ttl": 300
        }

        if config:
            default_config.update(config)

        # 创建文件服务
        file_service = FileService(
            config_service=config_service,
            logging_service=logging_service,
            max_workers=default_config["max_workers"],
            cache_ttl=default_config["cache_ttl"]
        )
        
        print("✅ [FILE] 文件服务创建完成")
        return file_service
        
    @staticmethod
    def create_legacy_compatible_service(config_service=None, logging_service=None):
        """创建兼容旧系统的文件服务"""
        file_service = FileServiceFactory.create_file_service(config_service, logging_service)
        
        # 添加兼容性方法
        def validate_files(file_paths):
            """兼容性方法：验证文件"""
            if isinstance(file_paths, str):
                file_paths = [file_paths]
            return file_service.validate_files_cached(file_paths)
            
        def get_file_info_compat(file_path):
            """兼容性方法：获取文件信息"""
            info = file_service.get_file_info(file_path)
            return info.to_dict()
            
        # 动态添加兼容性方法
        file_service.validate_files = validate_files
        file_service.get_file_info_compat = get_file_info_compat
        
        return file_service


# 全局文件服务实例
_global_file_service: Optional[FileService] = None


def get_file_service() -> Optional[FileService]:
    """获取全局文件服务实例"""
    return _global_file_service


def set_file_service(file_service: FileService):
    """设置全局文件服务实例"""
    global _global_file_service
    _global_file_service = file_service
