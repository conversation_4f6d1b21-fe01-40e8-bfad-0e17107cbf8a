#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接检查数据库 - 不依赖其他模块
"""

import sqlite3
import os

def main():
    """主函数"""
    print("🔧 直接检查数据库表状态")
    print("=" * 60)
    
    # 数据库路径
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        print("📋 数据库中的所有表:")
        refunding_tables = []
        close_tables = []
        main_tables = []
        
        for table in tables:
            table_name = table[0]
            print(f"   📊 {table_name}")
            
            if 'Refunding' in table_name:
                refunding_tables.append(table_name)
            elif 'Close' in table_name:
                close_tables.append(table_name)
            elif any(platform in table_name for platform in ['IOT_Sales', 'ZERO_Sales', 'APP_Sales']):
                if table_name in ['IOT_Sales', 'ZERO_Sales', 'APP_Sales']:
                    main_tables.append(table_name)
        
        print(f"\n📊 表分类:")
        print(f"   主表: {main_tables}")
        print(f"   退款表: {refunding_tables}")
        print(f"   关闭表: {close_tables}")
        
        # 检查各表数据数量
        print(f"\n📊 各表数据数量:")
        
        all_tables = main_tables + refunding_tables + close_tables
        for table_name in all_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   {table_name}: {count:,} 条记录")
            except Exception as e:
                print(f"   {table_name}: 查询失败 - {e}")
        
        # 检查主表中的订单状态分布
        print(f"\n📊 主表订单状态分布:")
        for table_name in main_tables:
            try:
                print(f"\n   {table_name}:")
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in cursor.fetchall()]
                
                if 'Order_status' in columns:
                    cursor.execute(f"""
                        SELECT Order_status, COUNT(*) as count 
                        FROM {table_name} 
                        WHERE Order_status IS NOT NULL AND Order_status != ''
                        GROUP BY Order_status 
                        ORDER BY count DESC
                        LIMIT 10
                    """)
                    
                    status_distribution = cursor.fetchall()
                    for status, count in status_distribution:
                        if any(keyword in status.lower() for keyword in ['refund', 'close', 'cancel']):
                            print(f"     🚨 {status}: {count:,} 条 (可能需要迁移)")
                        else:
                            print(f"     ✅ {status}: {count:,} 条")
                else:
                    print(f"     ⚠️ 没有Order_status列")
                    
            except Exception as e:
                print(f"     ❌ 查询失败: {e}")
        
        conn.close()
        
        # 诊断结论
        print(f"\n🎯 诊断结论:")
        if not refunding_tables:
            print("❌ 没有发现Refunding表，需要创建")
        else:
            print(f"✅ 发现 {len(refunding_tables)} 个Refunding表")
            
        if not close_tables:
            print("❌ 没有发现Close表，需要创建")
        else:
            print(f"✅ 发现 {len(close_tables)} 个Close表")
        
        print(f"\n🔧 建议:")
        if not refunding_tables or not close_tables:
            print("1. 运行创建表脚本: python scripts/create_refunding_close_tables.py")
        print("2. 检查数据导入时是否选择了正确的导入模式")
        print("3. 确认Excel文件中Order_status列的内容格式")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    main()
