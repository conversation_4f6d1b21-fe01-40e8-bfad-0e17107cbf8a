#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单数据库连接测试
"""

import sqlite3
import os
import time

def test_direct_sqlite():
    """直接测试SQLite连接"""
    print("🔧 直接测试SQLite连接...")
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        print(f"数据库路径: {db_path}")
        print(f"文件存在: {os.path.exists(db_path)}")
        
        if os.path.exists(db_path):
            file_size = os.path.getsize(db_path)
            print(f"文件大小: {file_size:,} 字节")
        
        print("尝试连接数据库...")
        start_time = time.time()
        
        # 使用短超时测试
        conn = sqlite3.connect(db_path, timeout=5.0)
        print(f"✅ 连接成功，耗时: {time.time() - start_time:.2f}秒")
        
        # 测试查询
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        print(f"✅ 查询成功: {result}")
        
        # 测试表查询
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 3")
        tables = cursor.fetchall()
        print(f"✅ 表查询成功，找到 {len(tables)} 个表")
        
        conn.close()
        print("✅ 连接已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接SQLite连接失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_timeout():
    """测试带超时的连接"""
    print("\n🔧 测试带超时的连接...")
    
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    try:
        print("尝试带超时的连接...")
        
        # 使用更短的超时
        conn = sqlite3.connect(db_path, timeout=1.0)
        print("✅ 短超时连接成功")
        
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        count = cursor.fetchone()[0]
        print(f"✅ 表数量: {count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 超时连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 简单数据库连接测试")
    print("=" * 40)
    
    tests = [
        test_direct_sqlite,
        test_with_timeout,
    ]
    
    for i, test_func in enumerate(tests, 1):
        print(f"\n📋 测试 {i}: {test_func.__name__}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ 测试 {i} 通过")
            else:
                print(f"❌ 测试 {i} 失败")
        except Exception as e:
            print(f"❌ 测试 {i} 异常: {e}")
    
    print("\n" + "=" * 40)
    print("测试完成")

if __name__ == "__main__":
    main()
