#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单诊断 - 快速检查导入脚本的问题
"""

import sys
import os

def test_basic_imports():
    """测试基本导入"""
    print("🔧 测试基本导入")
    try:
        print("1. 导入基础模块...")
        import pandas as pd
        import sqlite3
        from datetime import datetime
        print("✅ 基础模块导入成功")
        
        print("2. 测试pandas基本操作...")
        df = pd.DataFrame({'test': [1, 2, 3]})
        print(f"✅ pandas测试成功: {len(df)} 行")
        
        print("3. 测试sqlite3连接...")
        conn = sqlite3.connect(':memory:')
        conn.close()
        print("✅ sqlite3测试成功")
        
        return True
    except Exception as e:
        print(f"❌ 基本导入失败: {e}")
        return False

def test_utils_imports():
    """测试utils模块导入"""
    print("\n🔧 测试utils模块导入")
    try:
        print("1. 导入logger...")
        from utils.logger import get_logger
        print("✅ logger导入成功")
        
        print("2. 测试logger...")
        logger = get_logger('test')
        logger.info("测试日志")
        print("✅ logger测试成功")
        
        print("3. 导入exceptions...")
        from utils.exceptions import *
        print("✅ exceptions导入成功")
        
        print("4. 导入validators...")
        from utils.validators import input_validator
        print("✅ validators导入成功")
        
        return True
    except Exception as e:
        print(f"❌ utils模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_imports():
    """测试数据库模块导入"""
    print("\n🔧 测试数据库模块导入")
    try:
        print("1. 导入connection_pool...")
        from database.connection_pool import get_connection, reinitialize_connection_pool
        print("✅ connection_pool导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 数据库模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_import_class():
    """测试DataImportProcessor类"""
    print("\n🔧 测试DataImportProcessor类")
    try:
        print("1. 导入DataImportProcessor...")
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # 设置环境变量
        os.environ['NON_INTERACTIVE'] = '1'
        os.environ['AUTO_DUPLICATE_HANDLING'] = 'overwrite'
        os.environ['AUTO_MISSING_HANDLING'] = 'ignore'
        
        from data_import_optimized import DataImportProcessor
        print("✅ DataImportProcessor导入成功")
        
        print("2. 创建DataImportProcessor实例...")
        processor = DataImportProcessor()
        print("✅ DataImportProcessor实例创建成功")
        
        print("3. 检查processor属性...")
        print(f"   db_path: {processor.db_path}")
        print(f"   batch_size: {processor.batch_size}")
        print("✅ processor属性检查成功")
        
        return True
    except Exception as e:
        print(f"❌ DataImportProcessor测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 简单诊断：检查导入脚本问题")
    print("=" * 60)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("utils模块导入", test_utils_imports),
        ("数据库模块导入", test_database_imports),
        ("DataImportProcessor类", test_data_import_class),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                break  # 如果某个测试失败，停止后续测试
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            break
    
    print("\n" + "=" * 60)
    print("🎯 诊断结果")
    print("=" * 60)
    
    print(f"📊 通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过")
        print("✅ 导入脚本基础功能正常")
        print("💡 问题可能出现在具体的文件处理逻辑中")
    else:
        print(f"❌ 在第{passed+1}个测试时失败")
        print("🔧 需要修复基础模块问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 诊断{'成功' if success else '失败'}")
    print("按回车键退出...")
    input()
