# -*- coding: utf-8 -*-
"""
双数据库管理器
支持SQLite和PostgreSQL同时使用，可选择导入目标数据库
"""

import sqlite3
import psycopg2
import pandas as pd
import os
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import configparser
import logging

logger = logging.getLogger(__name__)

class DualDatabaseManager:
    """双数据库管理器"""
    
    def __init__(self, config_file: str = "config.ini"):
        # 如果是相对路径，则在03_配置文件目录中查找
        if not os.path.isabs(config_file):
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            self.config_file = os.path.join(base_dir, "03_配置文件", config_file)
        else:
            self.config_file = config_file
        self.sqlite_config = {}
        self.postgresql_config = {}
        self.load_config()
    
    def load_config(self):
        """加载数据库配置"""
        config = configparser.ConfigParser()
        
        if os.path.exists(self.config_file):
            config.read(self.config_file, encoding='utf-8')
        
        # SQLite配置 - 优先使用Database配置，然后是SQLite配置
        default_db_path = r'C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db'

        # 尝试从Database配置读取
        if config.has_section('Database') and config.has_option('Database', 'db_path'):
            db_path = config.get('Database', 'db_path', fallback=default_db_path)
        else:
            db_path = config.get('SQLite', 'db_path', fallback=default_db_path)

        self.sqlite_config = {
            'db_path': db_path,
            'enabled': config.getboolean('SQLite', 'enabled', fallback=True)
        }
        
        # PostgreSQL配置
        self.postgresql_config = {
            'host': config.get('PostgreSQL', 'host', fallback='localhost'),
            'port': config.get('PostgreSQL', 'port', fallback='5432'),
            'database': config.get('PostgreSQL', 'database', fallback='postgres'),
            'user': config.get('PostgreSQL', 'user', fallback='postgres'),
            'password': config.get('PostgreSQL', 'password', fallback=''),
            'enabled': config.getboolean('PostgreSQL', 'enabled', fallback=False)
        }
    
    def save_config(self):
        """保存配置到文件"""
        config = configparser.ConfigParser()
        
        # 读取现有配置
        if os.path.exists(self.config_file):
            config.read(self.config_file, encoding='utf-8')
        
        # 更新SQLite配置
        if 'SQLite' not in config:
            config['SQLite'] = {}
        config['SQLite']['db_path'] = self.sqlite_config['db_path']
        config['SQLite']['enabled'] = str(self.sqlite_config['enabled'])
        
        # 更新PostgreSQL配置
        if 'PostgreSQL' not in config:
            config['PostgreSQL'] = {}
        config['PostgreSQL']['host'] = self.postgresql_config['host']
        config['PostgreSQL']['port'] = self.postgresql_config['port']
        config['PostgreSQL']['database'] = self.postgresql_config['database']
        config['PostgreSQL']['user'] = self.postgresql_config['user']
        config['PostgreSQL']['password'] = self.postgresql_config['password']
        config['PostgreSQL']['enabled'] = str(self.postgresql_config['enabled'])
        
        # 保存配置
        with open(self.config_file, 'w', encoding='utf-8') as f:
            config.write(f)
    
    def test_sqlite_connection(self) -> bool:
        """测试SQLite连接"""
        try:
            if not self.sqlite_config['enabled']:
                return False
            
            conn = sqlite3.connect(self.sqlite_config['db_path'])
            conn.execute("SELECT 1")
            conn.close()
            return True
        except Exception as e:
            logger.error(f"SQLite连接测试失败: {e}")
            return False
    
    def test_postgresql_connection(self) -> bool:
        """测试PostgreSQL连接"""
        try:
            if not self.postgresql_config['enabled']:
                return False
            
            conn = psycopg2.connect(
                host=self.postgresql_config['host'],
                port=self.postgresql_config['port'],
                database=self.postgresql_config['database'],
                user=self.postgresql_config['user'],
                password=self.postgresql_config['password']
            )
            conn.close()
            return True
        except Exception as e:
            logger.error(f"PostgreSQL连接测试失败: {e}")
            return False
    
    def get_available_databases(self) -> List[str]:
        """获取可用的数据库列表"""
        available = []
        
        if self.sqlite_config['enabled'] and self.test_sqlite_connection():
            available.append('SQLite')
        
        if self.postgresql_config['enabled'] and self.test_postgresql_connection():
            available.append('PostgreSQL')
        
        return available
    
    def get_sqlite_connection(self):
        """获取SQLite连接"""
        if not self.sqlite_config['enabled']:
            raise Exception("SQLite未启用")
        
        return sqlite3.connect(self.sqlite_config['db_path'])
    
    def get_postgresql_connection(self):
        """获取PostgreSQL连接"""
        if not self.postgresql_config['enabled']:
            raise Exception("PostgreSQL未启用")
        
        return psycopg2.connect(
            host=self.postgresql_config['host'],
            port=self.postgresql_config['port'],
            database=self.postgresql_config['database'],
            user=self.postgresql_config['user'],
            password=self.postgresql_config['password']
        )
    
    def backup_database(self, db_type: str) -> str:
        """备份指定数据库"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if db_type == 'SQLite':
            if not self.sqlite_config['enabled']:
                raise Exception("SQLite未启用")
            
            backup_path = f"sqlite_backup_{timestamp}.db"
            shutil.copy2(self.sqlite_config['db_path'], backup_path)
            return backup_path
        
        elif db_type == 'PostgreSQL':
            if not self.postgresql_config['enabled']:
                raise Exception("PostgreSQL未启用")
            
            backup_path = f"postgresql_backup_{timestamp}.sql"
            
            # 使用pg_dump备份PostgreSQL
            import subprocess
            cmd = [
                'pg_dump',
                '-h', self.postgresql_config['host'],
                '-p', self.postgresql_config['port'],
                '-U', self.postgresql_config['user'],
                '-d', self.postgresql_config['database'],
                '-f', backup_path
            ]
            
            env = os.environ.copy()
            env['PGPASSWORD'] = self.postgresql_config['password']
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode != 0:
                raise Exception(f"PostgreSQL备份失败: {result.stderr}")
            
            return backup_path
        
        else:
            raise Exception(f"不支持的数据库类型: {db_type}")

    def backup_databases(self, db_types: List[str]) -> Dict[str, str]:
        """批量备份多个数据库"""
        backup_files = {}

        for db_type in db_types:
            try:
                backup_path = self.backup_database(db_type)
                backup_files[db_type] = backup_path
                logger.info(f"{db_type}数据库备份成功: {backup_path}")
            except Exception as e:
                logger.error(f"备份{db_type}数据库失败: {e}")
                # 继续备份其他数据库，不中断整个过程

        return backup_files

    def import_data_to_database(self, df: pd.DataFrame, table_name: str,
                               db_type: str, if_exists: str = 'append') -> bool:
        """导入数据到指定数据库"""
        try:
            if db_type == 'SQLite':
                conn = self.get_sqlite_connection()
                df.to_sql(table_name, conn, if_exists=if_exists, index=False)
                conn.close()
                return True
            
            elif db_type == 'PostgreSQL':
                conn = self.get_postgresql_connection()
                cursor = conn.cursor()

                # 获取列名
                columns = list(df.columns)
                quoted_columns = [f'"{col}"' for col in columns]

                # 处理if_exists参数
                if if_exists == 'replace':
                    cursor.execute(f'DROP TABLE IF EXISTS "{table_name}" CASCADE')
                    # 创建表结构（所有字段为TEXT类型）
                    columns_ddl = [f'"{col}" TEXT' for col in columns]
                    create_sql = f'CREATE TABLE "{table_name}" ({", ".join(columns_ddl)})'
                    cursor.execute(create_sql)
                elif if_exists == 'fail':
                    # 检查表是否存在
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = 'public'
                            AND table_name = %s
                        )
                    """, (table_name,))
                    if cursor.fetchone()[0]:
                        raise Exception(f"表 {table_name} 已存在")

                # 确保表存在（对于append模式）
                if if_exists == 'append':
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = 'public'
                            AND table_name = %s
                        )
                    """, (table_name,))
                    if not cursor.fetchone()[0]:
                        # 创建表
                        columns_ddl = [f'"{col}" TEXT' for col in columns]
                        create_sql = f'CREATE TABLE "{table_name}" ({", ".join(columns_ddl)})'
                        cursor.execute(create_sql)

                # 批量插入数据
                placeholders = ', '.join(['%s'] * len(columns))
                insert_sql = f'INSERT INTO "{table_name}" ({", ".join(quoted_columns)}) VALUES ({placeholders})'

                # 准备数据
                data_rows = []
                for _, row in df.iterrows():
                    row_data = []
                    for col in columns:
                        value = row[col]
                        if pd.isna(value):
                            value = None
                        else:
                            value = str(value)
                        row_data.append(value)
                    data_rows.append(tuple(row_data))

                # 执行批量插入
                cursor.executemany(insert_sql, data_rows)
                conn.commit()
                conn.close()
                return True
            
            else:
                raise Exception(f"不支持的数据库类型: {db_type}")
        
        except Exception as e:
            logger.error(f"导入数据到{db_type}失败: {e}")
            return False
    
    def import_data_to_multiple_databases(self, df: pd.DataFrame, table_name: str, 
                                        target_databases: List[str], 
                                        if_exists: str = 'append') -> Dict[str, bool]:
        """导入数据到多个数据库"""
        results = {}
        
        for db_type in target_databases:
            try:
                # 先备份数据库
                backup_path = self.backup_database(db_type)
                logger.info(f"{db_type}数据库已备份到: {backup_path}")
                
                # 导入数据
                success = self.import_data_to_database(df, table_name, db_type, if_exists)
                results[db_type] = success
                
                if success:
                    logger.info(f"数据成功导入到{db_type}")
                else:
                    logger.error(f"数据导入到{db_type}失败")
                
            except Exception as e:
                logger.error(f"处理{db_type}数据库时出错: {e}")
                results[db_type] = False
        
        return results
    
    def get_table_info(self, db_type: str) -> List[Dict[str, Any]]:
        """获取数据库表信息"""
        try:
            if db_type == 'SQLite':
                conn = self.get_sqlite_connection()
                cursor = conn.cursor()
                
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                table_info = []
                for (table_name,) in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    count = cursor.fetchone()[0]
                    table_info.append({
                        'name': table_name,
                        'rows': count,
                        'database': 'SQLite'
                    })
                
                conn.close()
                return table_info
            
            elif db_type == 'PostgreSQL':
                conn = self.get_postgresql_connection()
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_type = 'BASE TABLE'
                """)
                tables = cursor.fetchall()
                
                table_info = []
                for (table_name,) in tables:
                    cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                    count = cursor.fetchone()[0]
                    table_info.append({
                        'name': table_name,
                        'rows': count,
                        'database': 'PostgreSQL'
                    })
                
                conn.close()
                return table_info
            
            else:
                raise Exception(f"不支持的数据库类型: {db_type}")
        
        except Exception as e:
            logger.error(f"获取{db_type}表信息失败: {e}")
            return []
    
    def sync_data_between_databases(self, table_name: str, 
                                  source_db: str, target_db: str) -> bool:
        """在数据库之间同步数据"""
        try:
            # 从源数据库读取数据
            if source_db == 'SQLite':
                conn = self.get_sqlite_connection()
                df = pd.read_sql_query(f"SELECT * FROM `{table_name}`", conn)
                conn.close()
            elif source_db == 'PostgreSQL':
                conn = self.get_postgresql_connection()
                df = pd.read_sql_query(f'SELECT * FROM "{table_name}"', conn)
                conn.close()
            else:
                raise Exception(f"不支持的源数据库类型: {source_db}")
            
            # 导入到目标数据库
            return self.import_data_to_database(df, table_name, target_db, 'replace')
        
        except Exception as e:
            logger.error(f"数据同步失败: {e}")
            return False
    
    def get_database_status(self) -> Dict[str, Any]:
        """获取数据库状态"""
        status = {
            'SQLite': {
                'enabled': self.sqlite_config['enabled'],
                'connected': False,
                'path': self.sqlite_config['db_path'],
                'tables': []
            },
            'PostgreSQL': {
                'enabled': self.postgresql_config['enabled'],
                'connected': False,
                'config': {k: v for k, v in self.postgresql_config.items() if k != 'password'},
                'tables': []
            }
        }
        
        # 测试连接和获取表信息
        if self.sqlite_config['enabled']:
            status['SQLite']['connected'] = self.test_sqlite_connection()
            if status['SQLite']['connected']:
                status['SQLite']['tables'] = self.get_table_info('SQLite')
        
        if self.postgresql_config['enabled']:
            status['PostgreSQL']['connected'] = self.test_postgresql_connection()
            if status['PostgreSQL']['connected']:
                status['PostgreSQL']['tables'] = self.get_table_info('PostgreSQL')
        
        return status

# 全局实例
_dual_db_manager = None

def get_dual_database_manager() -> DualDatabaseManager:
    """获取全局双数据库管理器实例"""
    global _dual_db_manager
    if _dual_db_manager is None:
        _dual_db_manager = DualDatabaseManager()
    return _dual_db_manager

def configure_databases(sqlite_path: str = None, sqlite_enabled: bool = True,
                       pg_host: str = None, pg_port: str = None, 
                       pg_database: str = None, pg_user: str = None, 
                       pg_password: str = None, pg_enabled: bool = False):
    """配置数据库设置"""
    manager = get_dual_database_manager()
    
    if sqlite_path:
        manager.sqlite_config['db_path'] = sqlite_path
    manager.sqlite_config['enabled'] = sqlite_enabled
    
    if pg_host:
        manager.postgresql_config['host'] = pg_host
    if pg_port:
        manager.postgresql_config['port'] = pg_port
    if pg_database:
        manager.postgresql_config['database'] = pg_database
    if pg_user:
        manager.postgresql_config['user'] = pg_user
    if pg_password:
        manager.postgresql_config['password'] = pg_password
    manager.postgresql_config['enabled'] = pg_enabled
    
    manager.save_config()

def get_available_databases() -> List[str]:
    """获取可用的数据库列表"""
    manager = get_dual_database_manager()
    return manager.get_available_databases()

def import_to_databases(df: pd.DataFrame, table_name: str, 
                       databases: List[str]) -> Dict[str, bool]:
    """导入数据到指定的数据库"""
    manager = get_dual_database_manager()
    return manager.import_data_to_multiple_databases(df, table_name, databases)

def get_database_status() -> Dict[str, Any]:
    """获取数据库状态"""
    manager = get_dual_database_manager()
    return manager.get_database_status()
