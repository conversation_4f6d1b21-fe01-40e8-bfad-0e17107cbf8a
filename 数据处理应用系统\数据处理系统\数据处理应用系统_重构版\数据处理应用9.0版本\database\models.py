# -*- coding: utf-8 -*-
"""
数据库模型定义
定义数据库表结构和数据模型
"""

from dataclasses import dataclass
from typing import Optional
from datetime import datetime


@dataclass
class SalesRecord:
    """销售记录模型"""
    ID: Optional[int] = None
    Copartner_name: Optional[str] = None
    Order_No: Optional[str] = None
    Order_types: Optional[str] = None
    Order_status: Optional[str] = None
    Order_price: Optional[str] = None
    Payment: Optional[str] = None
    Order_time: Optional[str] = None
    Equipment_ID: Optional[str] = None
    Equipment_name: Optional[str] = None
    Branch_name: Optional[str] = None
    Payment_date: Optional[str] = None
    User_name: Optional[str] = None
    Time: Optional[str] = None
    Matched_Order_ID: Optional[str] = None
    OrderTime_dt: Optional[str] = None
    Transaction_Num: Optional[str] = None
    Import_Date: Optional[str] = None
    Import_Timestamp: Optional[datetime] = None


@dataclass
class RefundRecord:
    """退款记录模型"""
    Order_No: str
    Equipment_ID: str
    Order_price: str
    Refund_Date: Optional[str] = None
    Original_Order_Status: Optional[str] = None


# 表结构定义 - 基础表结构
BASE_TABLE_SCHEMA = """
    ID INTEGER PRIMARY KEY AUTOINCREMENT,
    Copartner_name TEXT,
    Order_No TEXT,
    Order_types TEXT,
    Order_status TEXT,
    Order_price TEXT,
    Payment TEXT,
    Order_time TEXT,
    Equipment_ID TEXT,
    Equipment_name TEXT,
    Branch_name TEXT,
    Payment_date TEXT,
    User_name TEXT,
    Time TEXT,
    Matched_Order_ID TEXT,
    OrderTime_dt TEXT,
    Transaction_Num TEXT,
    Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
"""

# 完整的表结构定义 - 包含所有状态表
TABLE_SCHEMAS = {
    # IOT平台表
    'IOT_Sales': f"CREATE TABLE IF NOT EXISTS IOT_Sales ({BASE_TABLE_SCHEMA})",
    'IOT_Sales_Refunding': f"CREATE TABLE IF NOT EXISTS IOT_Sales_Refunding ({BASE_TABLE_SCHEMA})",
    'IOT_Sales_Close': f"CREATE TABLE IF NOT EXISTS IOT_Sales_Close ({BASE_TABLE_SCHEMA})",

    # ZERO平台表
    'ZERO_Sales': f"CREATE TABLE IF NOT EXISTS ZERO_Sales ({BASE_TABLE_SCHEMA})",
    'ZERO_Sales_Refunding': f"CREATE TABLE IF NOT EXISTS ZERO_Sales_Refunding ({BASE_TABLE_SCHEMA})",
    'ZERO_Sales_Close': f"CREATE TABLE IF NOT EXISTS ZERO_Sales_Close ({BASE_TABLE_SCHEMA})",

    # APP平台表
    'APP_Sales': f"CREATE TABLE IF NOT EXISTS APP_Sales ({BASE_TABLE_SCHEMA})",
    'APP_Sales_Refunding': f"CREATE TABLE IF NOT EXISTS APP_Sales_Refunding ({BASE_TABLE_SCHEMA})",
    'APP_Sales_Close': f"CREATE TABLE IF NOT EXISTS APP_Sales_Close ({BASE_TABLE_SCHEMA})"
}

# 智能状态检测规则 - 支持模糊匹配和多语言
SMART_STATUS_PATTERNS = {
    'FINISHED': {
        'keywords': ['finish', 'finished', 'complete', 'completed', 'success', 'successful', 'done', 'paid', 'payment', '完成', '已完成', '成功', '支付'],
        'table_suffix': ''  # 主表
    },
    'REFUNDING': {
        'keywords': ['refund', 'refunded', 'refunding', 'return', 'returned', 'returning', 'cancel', 'cancelled', 'canceling', '退款', '已退款', '退款中', '取消', '已取消'],
        'table_suffix': '_Refunding'
    },
    'CLOSED': {
        'keywords': ['close', 'closed', 'closing', 'end', 'ended', 'ending', 'stop', 'stopped', 'stopping', '关闭', '已关闭', '结束', '已结束', '停止'],
        'table_suffix': '_Close'
    }
}

# 状态到表的映射（保持向后兼容）
STATUS_TABLE_MAPPING = {
    'IOT': {
        'Finish': 'IOT_Sales',
        'Finished': 'IOT_Sales',
        'Refunded': 'IOT_Sales_Refunding',
        'Refunding': 'IOT_Sales_Refunding',
        'Close': 'IOT_Sales_Close',
        'Closed': 'IOT_Sales_Close'
    },
    'ZERO': {
        'Finish': 'ZERO_Sales',
        'Finished': 'ZERO_Sales',
        'Refunded': 'ZERO_Sales_Refunding',
        'Refunding': 'ZERO_Sales_Refunding',
        'Close': 'ZERO_Sales_Close',
        'Closed': 'ZERO_Sales_Close'
    },
    'APP': {
        'Finish': 'APP_Sales',
        'Finished': 'APP_Sales',
        'Refunded': 'APP_Sales_Refunding',
        'Refunding': 'APP_Sales_Refunding',
        'Close': 'APP_Sales_Close',
        'Closed': 'APP_Sales_Close'
    }
}
