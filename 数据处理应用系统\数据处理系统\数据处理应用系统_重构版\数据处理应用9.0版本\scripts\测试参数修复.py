#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试参数修复脚本 - 验证命令行参数处理是否正常
"""

import subprocess
import sys
import os

def test_argument_parsing():
    """测试命令行参数解析"""
    print("🔧 测试命令行参数解析")
    print("=" * 50)
    
    script_path = "data_import_optimized.py"
    
    # 测试用例
    test_cases = [
        {
            "name": "位置参数测试",
            "args": ["python", script_path, "test.xlsx", "ZERO"],
            "should_work": True
        },
        {
            "name": "命名参数测试",
            "args": ["python", script_path, "--file-path", "test.xlsx", "--platform-type", "ZERO"],
            "should_work": True
        },
        {
            "name": "缺少文件参数",
            "args": ["python", script_path, "ZERO"],
            "should_work": False
        },
        {
            "name": "缺少平台参数",
            "args": ["python", script_path, "test.xlsx"],
            "should_work": False
        },
        {
            "name": "帮助信息",
            "args": ["python", script_path, "--help"],
            "should_work": True
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"   命令: {' '.join(test_case['args'])}")
        
        try:
            # 运行命令
            result = subprocess.run(
                test_case['args'],
                capture_output=True,
                text=True,
                timeout=10,
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
            
            print(f"   返回码: {result.returncode}")
            
            # 检查输出
            if result.stdout:
                print(f"   输出: {result.stdout[:200]}...")
            
            if result.stderr:
                print(f"   错误: {result.stderr[:200]}...")
            
            # 判断结果
            if test_case['should_work']:
                if result.returncode == 0 or "帮助" in test_case['name']:
                    print("   ✅ 测试通过")
                    results.append(True)
                else:
                    print("   ❌ 测试失败（应该成功但失败了）")
                    results.append(False)
            else:
                if result.returncode != 0:
                    print("   ✅ 测试通过（预期的失败）")
                    results.append(True)
                else:
                    print("   ❌ 测试失败（应该失败但成功了）")
                    results.append(False)
                    
        except subprocess.TimeoutExpired:
            print("   ⏰ 测试超时")
            results.append(False)
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            results.append(False)
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    print(f"\n" + "=" * 50)
    print(f"📊 测试结果总结:")
    print(f"   通过: {passed}/{total}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！参数解析修复成功")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能")
    print("=" * 50)
    
    try:
        # 测试导入脚本
        import data_import_optimized
        print("✅ 脚本导入成功")
        
        # 测试类实例化
        processor = data_import_optimized.DataImportProcessor()
        print("✅ 处理器实例化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 测试参数修复效果")
    print("=" * 60)
    
    # 测试1: 基本功能
    test1_result = test_basic_functionality()
    
    # 测试2: 参数解析
    test2_result = test_argument_parsing()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 总体测试结果:")
    print(f"   基本功能: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   参数解析: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    overall_success = test1_result and test2_result
    print(f"   总体状态: {'✅ 修复成功' if overall_success else '❌ 需要进一步修复'}")
    
    if overall_success:
        print("\n🎉 参数修复效果良好！")
        print("现在可以使用以下方式调用脚本：")
        print("1. 位置参数: python data_import_optimized.py '030725 CHINA ZERO.xlsx' ZERO")
        print("2. 命名参数: python data_import_optimized.py --file-path '030725 CHINA ZERO.xlsx' --platform-type ZERO")

if __name__ == "__main__":
    main()
