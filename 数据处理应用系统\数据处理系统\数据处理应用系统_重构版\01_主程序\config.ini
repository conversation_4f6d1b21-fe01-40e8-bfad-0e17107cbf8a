[Database]
db_path = C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db

[Scripts]
intelligent_processor = c:\Users\<USER>\Desktop\Day Report\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 脚本 3.0.py
modular_processor_9_0 = c:\Users\<USER>\Desktop\Day Report\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 9.0.py
modular_processor_8_0 = c:\Users\<USER>\Desktop\Day Report\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 8.0.py
modular_processor = c:\Users\<USER>\Desktop\Day Report\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\report 模块化设计 7.0.py
refund_script = c:\Users\<USER>\Desktop\Day Report\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\Refund_process_修复版.py
refund_script_optimized = c:\Users\<USER>\Desktop\Day Report\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\../scripts/refund_process_optimized.py
data_import_script = c:\Users\<USER>\Desktop\Day Report\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\数据导入脚本.py
data_import_script_optimized = c:\Users\<USER>\Desktop\Day Report\数据处理应用系统\数据处理系统\数据处理应用系统_重构版\01_主程序\../scripts/data_import_optimized.py

[UI]
window_width = 900
window_height = 700
theme = iphone_style

[Files]
file_separator = ||
temp_dir = temp_data
backup_dir = backups

[Backup]
auto_backup = true
backup_before_import = true
backup_before_refund = true
max_backup_files = 10

[PostgreSQL]
host = localhost
port = 5432
database = postgres
user = postgres
password = zerochon

