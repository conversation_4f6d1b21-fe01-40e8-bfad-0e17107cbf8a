#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动改进版数据处理与导入应用
"""

import os
import sys
import traceback

def main():
    """主函数"""
    try:
        print("🚀 启动改进版数据处理与导入应用...")
        
        # 检查依赖
        print("📦 检查依赖...")
        required_modules = ['tkinter', 'pandas', 'openpyxl', 'configparser']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"  ✅ {module}")
            except ImportError:
                missing_modules.append(module)
                print(f"  ❌ {module} - 缺失")
        
        if missing_modules:
            print(f"\n❌ 缺少必要的模块: {', '.join(missing_modules)}")
            print("请安装缺失的模块后重试。")
            return False
        
        # 导入改进版应用
        print("\n📱 加载应用程序...")
        from 数据处理与导入应用_改进版 import ImprovedDataApp
        
        # 创建并启动应用
        print("🎨 创建用户界面...")
        app = ImprovedDataApp()
        
        print("✨ 应用程序启动成功！")
        print("\n📋 功能说明:")
        print("  • 数据处理: 处理SETTLEMENT和CHINA文件")
        print("  • 数据导入: 导入IOT/ZERO平台数据到数据库")
        print("  • 退款处理: 处理退款文件")
        print("  • 线程安全: 所有操作都是线程安全的")
        print("  • 配置管理: 支持配置文件自定义")
        
        print("\n🎯 改进亮点:")
        print("  ✅ 线程安全 - 避免界面卡死")
        print("  ✅ 代码复用 - 消除重复代码")
        print("  ✅ 模块化设计 - 易于维护和扩展")
        print("  ✅ 配置文件 - 灵活的参数管理")
        print("  ✅ 完整测试 - 保证代码质量")
        
        print("\n🎉 应用程序已就绪，请在GUI界面中操作！")
        
        # 启动主循环
        app.mainloop()
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保改进版应用文件存在且可访问。")
        return False
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
        return False

def show_help():
    """显示帮助信息"""
    print("📖 改进版数据处理与导入应用 - 使用说明")
    print("=" * 50)
    print()
    print("🎯 主要改进:")
    print("  • 线程安全: 解决了原版的线程安全问题")
    print("  • 代码复用: 消除了重复的日志记录代码")
    print("  • 模块化: 将巨大的类拆分为多个专门的类")
    print("  • 配置管理: 使用配置文件替代硬编码")
    print("  • 测试覆盖: 包含完整的单元测试")
    print()
    print("🚀 启动方式:")
    print("  python 启动改进版应用.py")
    print()
    print("📁 文件结构:")
    print("  • 数据处理与导入应用_改进版.py - 主应用程序")
    print("  • 测试改进版应用.py - 单元测试")
    print("  • 改进对比报告.md - 详细的改进说明")
    print("  • config.ini - 配置文件（自动生成）")
    print()
    print("🔧 配置文件说明:")
    print("  [Database]")
    print("    db_path = 数据库文件路径")
    print("  [Scripts]")
    print("    report_script = 报告脚本名称")
    print("    refund_script = 退款脚本名称")
    print("  [UI]")
    print("    window_width = 窗口宽度")
    print("    window_height = 窗口高度")
    print()
    print("🧪 运行测试:")
    print("  python 测试改进版应用.py")
    print()

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help', 'help']:
            show_help()
            sys.exit(0)
        elif sys.argv[1] in ['-t', '--test', 'test']:
            print("🧪 运行测试...")
            os.system("python 测试改进版应用.py")
            sys.exit(0)
    
    # 启动应用
    success = main()
    
    if not success:
        print("\n💡 提示:")
        print("  • 使用 'python 启动改进版应用.py --help' 查看帮助")
        print("  • 使用 'python 启动改进版应用.py --test' 运行测试")
        print("  • 检查是否安装了所有必要的依赖")
        
        sys.exit(1)
    
    print("\n👋 应用程序已关闭，感谢使用！")
