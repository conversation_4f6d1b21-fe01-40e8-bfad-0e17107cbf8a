# -*- coding: utf-8 -*-
"""
统一日志系统
提供结构化日志记录、日志轮转、多目标输出等功能
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import json

from .exceptions import ConfigurationError


class AppLogger:
    """应用程序日志管理器"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化日志管理器

        Args:
            name: 日志器名称
            config: 日志配置，如果为None则使用默认配置
        """
        self.name = name
        self.config = config or self._get_default_config()
        self.logger = logging.getLogger(name)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_logger()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认日志配置"""
        return {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'log_dir': 'logs',
            'max_size': 10485760,  # 10MB
            'backup_count': 5
        }
    
    def _setup_logger(self):
        """
        设置日志器

        🔧 日志级别优化说明：
        - 控制台输出：只显示INFO及以上级别（UI友好）
        - 文件记录：记录所有级别包括DEBUG（调试完整）
        - 这确保UI显示简洁，同时保持完整的调试信息
        """
        # 设置日志级别
        level = getattr(logging, self.config.get('level', 'INFO').upper())
        self.logger.setLevel(level)
        
        # 创建格式化器
        formatter = logging.Formatter(
            self.config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 🔧 日志级别优化：添加控制台处理器，但只显示INFO及以上级别
        # 这确保debug级别的日志不会显示在UI中，只保存到文件
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)  # 只显示INFO及以上级别
        self.logger.addHandler(console_handler)
        
        # 添加文件处理器
        self._add_file_handler(formatter)
        
        # 添加错误文件处理器
        self._add_error_file_handler(formatter)
    
    def _add_file_handler(self, formatter: logging.Formatter):
        """添加文件处理器"""
        try:
            # 创建日志目录
            log_dir = Path(self.config.get('log_dir', 'logs'))
            log_dir.mkdir(exist_ok=True)
            
            # 创建日志文件路径
            log_file = log_dir / f"{self.name}.log"
            
            # 创建轮转文件处理器
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=self.config.get('max_size', 10485760),  # 10MB
                backupCount=self.config.get('backup_count', 5),
                encoding='utf-8'
            )
            
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.DEBUG)  # 🔧 文件记录所有级别（包括debug）
            self.logger.addHandler(file_handler)
            
        except Exception as e:
            raise ConfigurationError(f"Failed to create log file handler: {e}")
    
    def _add_error_file_handler(self, formatter: logging.Formatter):
        """添加错误文件处理器（只记录ERROR和CRITICAL）"""
        try:
            log_dir = Path(self.config.get('log_dir', 'logs'))
            error_log_file = log_dir / f"{self.name}_error.log"
            
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=self.config.get('max_size', 10485760),
                backupCount=self.config.get('backup_count', 5),
                encoding='utf-8'
            )
            
            error_handler.setFormatter(formatter)
            error_handler.setLevel(logging.ERROR)
            self.logger.addHandler(error_handler)
            
        except Exception as e:
            # 错误处理器失败不应该阻止应用程序运行
            # print(f"警告: 创建错误日志处理器失败: {e}")  # 避免Unicode编码问题
            pass
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录调试信息"""
        self._log(logging.DEBUG, message, extra)
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录信息"""
        self._log(logging.INFO, message, extra)
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录警告"""
        self._log(logging.WARNING, message, extra)
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """记录错误"""
        self._log(logging.ERROR, message, extra, exc_info=exc_info)
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """记录严重错误"""
        self._log(logging.CRITICAL, message, extra, exc_info=exc_info)
    
    def _log(self, level: int, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """内部日志记录方法"""
        # 如果有额外信息，格式化消息
        if extra:
            try:
                formatted_extra = json.dumps(extra, ensure_ascii=True, indent=2)
                message = f"{message}\nExtra info: {formatted_extra}"
            except (TypeError, ValueError):
                # 如果无法序列化，转换为字符串
                message = f"{message}\nExtra info: {str(extra)}"
        
        self.logger.log(level, message, exc_info=exc_info)
    
    def log_exception(self, exception: Exception, operation: str = "operation"):
        """记录异常信息"""
        from .exceptions import AppException

        if isinstance(exception, AppException):
            # 自定义异常，记录详细信息
            self.error(
                f"{operation} failed: {exception.message}",
                extra=exception.to_dict(),
                exc_info=True
            )
        else:
            # 标准异常
            self.error(
                f"Unknown error during {operation}: {str(exception)}",
                exc_info=True
            )
    
    def log_performance(self, operation: str, duration: float, extra: Optional[Dict[str, Any]] = None):
        """记录性能信息"""
        perf_info = {
            'operation': operation,
            'duration_seconds': duration,
            'timestamp': datetime.now().isoformat()
        }
        
        if extra:
            perf_info.update(extra)
        
        self.info(f"Performance - {operation}: {duration:.2f}s", extra=perf_info)
    
    def log_data_processing(self, stage: str, records_processed: int, 
                          records_matched: int = None, records_inserted: int = None,
                          extra: Optional[Dict[str, Any]] = None):
        """记录数据处理信息"""
        processing_info = {
            'stage': stage,
            'records_processed': records_processed,
            'timestamp': datetime.now().isoformat()
        }
        
        if records_matched is not None:
            processing_info['records_matched'] = records_matched
        if records_inserted is not None:
            processing_info['records_inserted'] = records_inserted
        if extra:
            processing_info.update(extra)
        
        message = f"Data processing - {stage}: processed {records_processed} records"
        if records_matched is not None:
            message += f", matched {records_matched}"
        if records_inserted is not None:
            message += f", inserted {records_inserted}"
        
        self.info(message, extra=processing_info)
    
    def log_file_operation(self, operation: str, file_path: str, 
                          success: bool = True, extra: Optional[Dict[str, Any]] = None):
        """记录文件操作"""
        file_info = {
            'operation': operation,
            'file_path': file_path,
            'success': success,
            'timestamp': datetime.now().isoformat()
        }
        
        if extra:
            file_info.update(extra)
        
        message = f"File operation - {operation}: {file_path}"
        
        if success:
            self.info(message, extra=file_info)
        else:
            self.error(message, extra=file_info)

    def addHandler(self, handler):
        """🔧 修复：添加处理器方法，委托给内部logger"""
        if hasattr(self.logger, 'addHandler'):
            self.logger.addHandler(handler)
        else:
            raise AttributeError(f"Logger {self.name} does not support addHandler")

    def removeHandler(self, handler):
        """移除处理器方法，委托给内部logger"""
        if hasattr(self.logger, 'removeHandler'):
            self.logger.removeHandler(handler)
        else:
            raise AttributeError(f"Logger {self.name} does not support removeHandler")

    def setLevel(self, level):
        """设置日志级别，委托给内部logger"""
        if hasattr(self.logger, 'setLevel'):
            self.logger.setLevel(level)
        else:
            raise AttributeError(f"Logger {self.name} does not support setLevel")

    def getEffectiveLevel(self):
        """获取有效日志级别，委托给内部logger"""
        if hasattr(self.logger, 'getEffectiveLevel'):
            return self.logger.getEffectiveLevel()
        else:
            return logging.INFO

    def isEnabledFor(self, level):
        """检查是否启用指定级别，委托给内部logger"""
        if hasattr(self.logger, 'isEnabledFor'):
            return self.logger.isEnabledFor(level)
        else:
            return True

    def getChild(self, suffix):
        """获取子logger，委托给内部logger"""
        if hasattr(self.logger, 'getChild'):
            return self.logger.getChild(suffix)
        else:
            raise AttributeError(f"Logger {self.name} does not support getChild")

    def hasHandlers(self):
        """检查是否有处理器，委托给内部logger"""
        if hasattr(self.logger, 'hasHandlers'):
            return self.logger.hasHandlers()
        else:
            return len(getattr(self.logger, 'handlers', [])) > 0


class LoggerFactory:
    """日志器工厂类"""
    
    _loggers: Dict[str, AppLogger] = {}
    
    @classmethod
    def get_logger(cls, name: str, config: Optional[Dict[str, Any]] = None) -> AppLogger:
        """
        获取日志器实例（单例模式）
        
        Args:
            name: 日志器名称
            config: 日志配置
            
        Returns:
            AppLogger实例
        """
        if name not in cls._loggers:
            cls._loggers[name] = AppLogger(name, config)
        return cls._loggers[name]
    
    @classmethod
    def get_app_logger(cls) -> AppLogger:
        """获取应用程序主日志器"""
        return cls.get_logger('app')
    
    @classmethod
    def get_data_processor_logger(cls) -> AppLogger:
        """获取数据处理日志器"""
        return cls.get_logger('data_processor')
    
    @classmethod
    def get_database_logger(cls) -> AppLogger:
        """获取数据库日志器"""
        return cls.get_logger('database')
    
    @classmethod
    def get_ui_logger(cls) -> AppLogger:
        """获取UI日志器"""
        return cls.get_logger('ui')


# 便捷函数
def get_logger(name: str = 'app') -> AppLogger:
    """获取日志器的便捷函数"""
    return LoggerFactory.get_logger(name)


# 全局日志器实例
app_logger = LoggerFactory.get_app_logger()
data_processor_logger = LoggerFactory.get_data_processor_logger()
database_logger = LoggerFactory.get_database_logger()
ui_logger = LoggerFactory.get_ui_logger()
