# coding: utf-8
"""
简单测试 - 验证基本功能
"""

import os
import sys

def test_basic():
    """基本测试"""
    print("开始基本测试...")
    
    # 1. 测试配置文件
    config_path = "config.ini"
    if os.path.exists(config_path):
        print("✅ config.ini 存在")
        
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if "数据处理系统/数据处理脚本.py" in content:
                print("✅ 配置文件已更新为新脚本路径")
            else:
                print("❌ 配置文件未更新")
    else:
        print("❌ config.ini 不存在")
    
    # 2. 测试脚本文件存在性
    scripts = [
        "数据处理系统/数据处理脚本.py",
        "数据处理系统/三天报告脚本.py",
        "数据处理系统/数据验证器.py",
        "数据处理系统/数据匹配器.py"
    ]
    
    for script in scripts:
        if os.path.exists(script):
            print(f"✅ {script} 存在")
        else:
            print(f"❌ {script} 不存在")
    
    # 3. 测试主应用文件
    main_app = "数据处理与导入应用_完整版.py"
    if os.path.exists(main_app):
        print(f"✅ {main_app} 存在")
        
        # 检查主应用中的配置
        with open(main_app, 'r', encoding='utf-8') as f:
            content = f.read()
            if "数据处理系统/数据处理脚本.py" in content:
                print("✅ 主应用已更新为新脚本路径")
            else:
                print("❌ 主应用未更新")
    else:
        print(f"❌ {main_app} 不存在")
    
    # 4. 测试Python语法
    try:
        import py_compile
        
        for script in scripts:
            if os.path.exists(script):
                try:
                    py_compile.compile(script, doraise=True)
                    print(f"✅ {script} 语法正确")
                except Exception as e:
                    print(f"❌ {script} 语法错误: {e}")
    except ImportError:
        print("⚠️ 无法导入py_compile，跳过语法检查")
    
    print("基本测试完成")

if __name__ == "__main__":
    test_basic()
