#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
退款处理脚本
处理IOT和ZERO平台的退款数据
"""

import os
import shutil
import sqlite3
import datetime
import pandas as pd
import traceback
from glob import glob
import configparser
import argparse

# 动态获取数据库路径
def get_db_path():
    """从配置文件获取数据库路径"""
    import os

    # 尝试从配置文件读取
    config_file = "config.ini"
    if os.path.exists(config_file):
        try:
            config = configparser.ConfigParser()
            config.read(config_file, encoding='utf-8')
            if config.has_section('Database'):
                db_path = config.get('Database', 'db_path', fallback=None)
                if db_path:
                    return db_path
        except Exception as e:
            print(f"⚠️ 读取配置文件失败: {e}")

    # 使用默认路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(script_dir, "database", "sales_reports.db")

# 全局变量，支持命令行参数覆盖
DB_PATH = None  # 将在main函数中设置

# 动态获取项目根目录
script_dir = os.path.dirname(os.path.abspath(__file__))
REFUND_DIRS = {
    "IOT": os.path.join(script_dir, "Refunding", "IOT"),
    "ZERO": os.path.join(script_dir, "Refunding", "ZERO")
}
BACKUP_DIR = os.path.join(script_dir, "Refunding", "Refunding backup")
SUCCESS_LOG = os.path.join(BACKUP_DIR, "refund_success.log")
FAIL_LOG = os.path.join(BACKUP_DIR, "refund_fail.csv")
NOTE_LOG = os.path.join(BACKUP_DIR, "refund_note.log")

REFUND_LIST_COLUMNS = [
    'Transaction Date','Settlement Date','Refund Date','Merchant Ref ID','Transaction ID','Channel','Order ID','Currency','Billing','Actual','Refund','MDR','GST','Status','Refund Fee','Quantity','Reference1','Reference2','PROCESS'
]


def move_file(self, src, dst):
    # 确保目标目录存在
    os.makedirs(os.path.dirname(dst), exist_ok=True)
    
    # 尝试多次移动文件
    max_attempts = 3
    attempt = 0
    success = False
    
    while attempt < max_attempts and not success:
        try:
            # 如果目标文件已存在，先尝试删除
            if os.path.exists(dst):
                try:
                    os.remove(dst)
                except PermissionError:
                    # 如果无法删除，使用随机后缀创建新文件名
                    import random
                    base, ext = os.path.splitext(dst)
                    dst = f"{base}_{random.randint(1000, 9999)}{ext}"
            
            # 尝试使用shutil.copy2代替move，然后手动删除源文件
            shutil.copy2(src, dst)
            success = True
            
            # 尝试删除源文件，但如果失败也不阻止程序继续
            try:
                os.unlink(src)
            except PermissionError:
                self.log_mgr.log_note(f"警告：无法删除源文件 {src}，但已成功复制到 {dst}")
                
        except PermissionError as e:
            attempt += 1
            self.log_mgr.log_note(f"移动文件尝试 {attempt}/{max_attempts} 失败: {e}")
            # 等待一段时间再重试
            import time
            time.sleep(2)
    
    if not success:
        # 所有尝试都失败，记录错误但不中断程序
        self.log_mgr.log_note(f"警告：无法移动文件 {src} 到 {dst}，将继续处理其他文件")
        return False
    return True
class LogManager:
    def __init__(self, success_log, fail_log, note_log):
        self.success_log = success_log
        self.fail_log = fail_log
        self.note_log = note_log
        # 初始化csv失败日志
        if not os.path.exists(self.fail_log):
            with open(self.fail_log, 'w', encoding='utf-8') as f:
                f.write('Transaction Date,Order ID,Refund,报错原因\n')
    def log_success(self, msg):
        with open(self.success_log, "a", encoding="utf-8") as f:
            f.write(f"{datetime.datetime.now()} {msg}\n")
    def log_fail(self, row, reason):
        with open(self.fail_log, "a", encoding="utf-8") as f:
            f.write(f"{row.get('Transaction Date','')},{row.get('Order ID','')},{row.get('Refund','')},{reason}\n")
    def log_note(self, msg):
        with open(self.note_log, "a", encoding="utf-8") as f:
            f.write(f"{datetime.datetime.now()} {msg}\n")
    def clear_logs(self):
        for log in [self.success_log, self.fail_log, self.note_log]:
            if os.path.exists(log):
                os.remove(log)
    def archive_logs(self):
        today = datetime.datetime.now().strftime('%Y%m')
        for log in [self.success_log, self.fail_log, self.note_log]:
            if os.path.exists(log):
                base, ext = os.path.splitext(log)
                archive_name = f"{base}_{today}{ext}"
                shutil.move(log, archive_name)

class BackupManager:
    def __init__(self, backup_dir):
        self.backup_dir = backup_dir
        os.makedirs(self.backup_dir, exist_ok=True)
    def backup_table(self, table_name):
        backup_file = os.path.join(self.backup_dir, f"{table_name}_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        with sqlite3.connect(DB_PATH) as conn:
            df = pd.read_sql(f"SELECT * FROM {table_name}", conn)
            df.to_excel(backup_file, index=False)
        return backup_file
    def restore_table_from_backup(self, table_name):
        files = [f for f in os.listdir(self.backup_dir) if f.startswith(table_name+"_backup") and f.endswith(".xlsx")]
        if not files:
            print(f"未找到{table_name}的备份文件")
            return
        latest = max(files, key=lambda x: os.path.getmtime(os.path.join(self.backup_dir, x)))
        backup_file = os.path.join(self.backup_dir, latest)
        df = pd.read_excel(backup_file, engine='openpyxl')
        with sqlite3.connect(DB_PATH) as conn:
            conn.execute(f"DELETE FROM {table_name}")
            df.to_sql(table_name, conn, if_exists='append', index=False)
        print(f"{table_name}已恢复自{latest}")
    def archive_db(self):
        today = datetime.datetime.now().strftime('%Y%m')
        db_archive = os.path.join(self.backup_dir, f"sales_reports_{today}.db")
        if os.path.exists(DB_PATH):
            shutil.copy(DB_PATH, db_archive)

class RefundProcessor:
    def __init__(self, log_mgr, backup_mgr):
        self.log_mgr = log_mgr
        self.backup_mgr = backup_mgr
        
    def log_message(self, message, level='info'):
        """根据日志级别将消息转发到适当的LogManager方法"""
        if level == 'info' or level == 'note':
            self.log_mgr.log_note(message)
        elif level == 'warning' or level == 'warn':
            self.log_mgr.log_note(f"警告: {message}")
        elif level == 'error' or level == 'err':
            self.log_mgr.log_note(f"错误: {message}")
        elif level == 'popup_request':
            self.log_mgr.log_note(f"弹窗请求: {message}")
        else:
            self.log_mgr.log_note(message)
    def ensure_folder(self, folder):
        os.makedirs(folder, exist_ok=True)
    def create_refund_list_table(self):
        with sqlite3.connect(DB_PATH) as conn:
            cur = conn.cursor()
            cur.execute('''CREATE TABLE IF NOT EXISTS REFUND_LIST (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                [Transaction Date] TEXT,
                [Settlement Date] TEXT,
                [Refund Date] TEXT,
                [Merchant Ref ID] TEXT,
                [Transaction ID] TEXT,
                [Channel] TEXT,
                [Order ID] TEXT,
                [Currency] TEXT,
                [Billing] REAL,
                [Actual] REAL,
                [Refund] REAL,
                [MDR] REAL,
                [GST] REAL,
                [Status] TEXT,
                [Refund Fee] REAL,
                [Quantity] INTEGER,
                [Reference1] TEXT,
                [Reference2] TEXT,
                [PROCESS] TEXT
            )''')
            cur.close()
            conn.commit()
    def standardize_date(self, date_str):
        if pd.isna(date_str) or not str(date_str).strip():
            return None
        s = str(date_str).strip()
        try:
            return pd.to_datetime(s).strftime("%Y-%m-%d")
        except Exception:
            return s
    def detect_sheet(self, file_path, platform):
        try:
            xl = pd.ExcelFile(file_path, engine='openpyxl')
            if platform in xl.sheet_names:
                return platform
            # 自动检测第一个sheet
            return xl.sheet_names[0]
        except Exception as e:
            return None
    def process_refund_file(self, platform, file_path):
        table_name = f"{platform}_Sales"
        filename = os.path.basename(file_path)
        sheet_name = self.detect_sheet(file_path, platform)
        if not sheet_name:
            self.log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, f"{filename} 无法检测sheet名")
            return False
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
        except Exception as e:
            self.log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, f"{filename} 读取失败: {e}\n{traceback.format_exc()}")
            return False
        df = df.rename(columns=lambda x: x.strip())
        df['PROCESS'] = ''
        
        # 处理Reference1和Reference2字段
        if 'Reference1' not in df.columns:
            # 如果没有Reference1字段，但有Reference字段，则拆分Reference字段
            if 'Reference' in df.columns:
                # 尝试拆分Reference字段为Reference1和Reference2
                df['Reference1'] = df['Reference']
                df['Reference2'] = ''
            else:
                # 如果没有任何Reference相关字段，添加空的Reference1和Reference2字段
                df['Reference1'] = ''
                df['Reference2'] = ''
        
        if 'Reference2' not in df.columns:
            # 如果没有Reference2字段，添加空的Reference2字段
            df['Reference2'] = ''
        with sqlite3.connect(DB_PATH) as conn:
            db = conn.cursor()
            for idx, row in df.iterrows():
                try:
                    refund_date = self.standardize_date(row.get('Transaction Date',''))
                    order_id = str(row.get('Order ID','')).strip()
                    try:
                        refund_amt = float(row.get('Refund',0)) if str(row.get('Refund','')).strip() else 0
                    except Exception:
                        refund_amt = 0
                    db.execute(f"SELECT * FROM {table_name} WHERE (Order_time=? OR substr(Order_time,1,10)=?)", (refund_date, refund_date))
                    candidates = db.fetchall()
                    found = False
                    for c in candidates:
                        col_names = [d[0] for d in db.description]
                        db_id = str(c[col_names.index('Equipment_ID')]) if 'Equipment_ID' in col_names else ''
                        db_no = str(c[col_names.index('Order_No')]) if 'Order_No' in col_names else ''
                        if (len(order_id) <= 9 and order_id == db_id) or (len(order_id) > 9 and order_id == db_no):
                            found = True
                            orig_amt = float(c[col_names.index('Order_price')]) if 'Order_price' in col_names else 0
                            new_amt = orig_amt - refund_amt
                            
                            if new_amt <= 0:
                                db.execute(f"DELETE FROM {table_name} WHERE rowid=?", (c[0],))
                                process_status = '已删除'
                            else:
                                db.execute(f"UPDATE {table_name} SET Order_price=? WHERE rowid=?", (new_amt, c[0]))
                                process_status = '已退款'
                            row['PROCESS'] = process_status
                            self.log_mgr.log_success(f"{filename} 第{idx+1}行 退款成功，状态:{process_status}")

                            break
                    if not found:
                        row['PROCESS'] = '未找到'
                        self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行 未找到匹配数据")
                    insert_row = [row.get(col, '') for col in REFUND_LIST_COLUMNS]
                    db.execute(f"INSERT INTO REFUND_LIST ({','.join(['['+col+']' for col in REFUND_LIST_COLUMNS])}) VALUES ({','.join(['?']*len(REFUND_LIST_COLUMNS))})", insert_row)
                except Exception as e:
                    row['PROCESS'] = '失败'
                    self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行 处理异常: {e}\n{traceback.format_exc()}")
            conn.commit()
            db.close()
        return True
    def move_file(self, src, dst):
        # 确保目标目录存在
        os.makedirs(os.path.dirname(dst), exist_ok=True)
        
        # 尝试多次移动文件
        max_attempts = 3
        attempt = 0
        success = False
        
        while attempt < max_attempts and not success:
            try:
                # 如果目标文件已存在，先尝试删除
                if os.path.exists(dst):
                    try:
                        os.remove(dst)
                    except PermissionError:
                        # 如果无法删除，使用随机后缀创建新文件名
                        import random
                        base, ext = os.path.splitext(dst)
                        dst = f"{base}_{random.randint(1000, 9999)}{ext}"
                
                # 尝试使用shutil.copy2代替move，然后手动删除源文件
                shutil.copy2(src, dst)
                success = True
                
                # 尝试删除源文件，但如果失败也不阻止程序继续
                try:
                    os.unlink(src)
                except PermissionError:
                    self.log_mgr.log_note(f"警告：无法删除源文件 {src}，但已成功复制到 {dst}")
                    
            except PermissionError as e:
                attempt += 1
                self.log_mgr.log_note(f"移动文件尝试 {attempt}/{max_attempts} 失败: {e}")
                # 等待一段时间再重试
                import time
                time.sleep(2)
        
        if not success:
            # 所有尝试都失败，记录错误但不中断程序
            self.log_mgr.log_note(f"警告：无法移动文件 {src} 到 {dst}，将继续处理其他文件")
            return False
        return True
    def clean_old_logs(self):
        self.log_mgr.clear_logs()
    def archive_logs_and_db(self):
        self.log_mgr.archive_logs()
        self.backup_mgr.archive_db()
    def compress_and_delete_old_refund_list(self):
        # 按月份导出REFUND_LIST并删除历史数据
        with sqlite3.connect(DB_PATH) as conn:
            now = datetime.datetime.now()
            month_str = now.strftime('%Y-%m')
            df = pd.read_sql(f"SELECT * FROM REFUND_LIST WHERE substr([Transaction Date],1,7)=?", conn, params=(month_str,))
            if not df.empty:
                export_path = os.path.join(BACKUP_DIR, f"REFUND_LIST_{month_str}.xlsx")
                df.to_excel(export_path, index=False)
                conn.execute(f"DELETE FROM REFUND_LIST WHERE substr([Transaction Date],1,7)=?", (month_str,))
                conn.commit()
                self.log_mgr.log_note(f"已归档并删除{month_str}的REFUND_LIST数据")

def main(db_path=None, mode=None):
    global DB_PATH

    # 设置数据库路径
    if db_path:
        DB_PATH = db_path
    else:
        DB_PATH = get_db_path()

    print(f"使用数据库路径: {DB_PATH}")

    log_mgr = LogManager(SUCCESS_LOG, FAIL_LOG, NOTE_LOG)
    backup_mgr = BackupManager(BACKUP_DIR)
    processor = RefundProcessor(log_mgr, backup_mgr)

    # 如果没有指定模式，则交互式选择
    if mode is None:
        try:
            print("请选择操作模式：1-退款处理  2-恢复数据库备份  3-清理日志  4-归档日志和数据库  5-按月归档REFUND_LIST")
            mode = input("输入1/2/3/4/5：").strip()
        except Exception as e:
            print(f"输入异常: {e}")
            return
    try:
        if mode == '2':
            for plat in REFUND_DIRS:
                backup_mgr.restore_table_from_backup(f"{plat}_Sales")
            backup_mgr.restore_table_from_backup("REFUND_LIST")
            return
        elif mode == '3':
            processor.clean_old_logs()
            print("日志已清理")
            return
        elif mode == '4':
            processor.archive_logs_and_db()
            print("日志和数据库已归档")
            return
        elif mode == '5':
            processor.compress_and_delete_old_refund_list()
            print("REFUND_LIST已按月归档并删除")
            return
        elif mode != '1':
            print("无效输入")
            return
        processor.create_refund_list_table()
        for plat, folder in REFUND_DIRS.items():
            table_name = f"{plat}_Sales"
            backup_mgr.backup_table(table_name)
            processor.ensure_folder(os.path.join(folder, "已处理"))
            processor.ensure_folder(os.path.join(folder, "需人工检查"))
            files = [f for f in os.listdir(folder) if f.lower().endswith(('.xls','.xlsx'))]
            total = len(files)  # 添加总文件数变量
            for idx, fname in enumerate(files):
                fpath = os.path.join(folder, fname)
                # 在main函数中修改相关代码段（第255行附近）
                try:
                    ok = processor.process_refund_file(plat, fpath)
                    target_dir = "已处理" if ok else "需人工检查"
                    dst = os.path.join(folder, target_dir, fname)
                    move_success = processor.move_file(fpath, dst)
                    if not move_success:
                        # 即使移动失败，也标记为需要人工检查，但继续处理其他文件
                        log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, 
                                        f"{fname} 文件移动失败，可能被其他程序占用，请手动移动到{target_dir}文件夹")
                    print(f"{plat}：正在处理第{idx+1}/{total}个文件：{fname}")
                except Exception as e:
                    log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, f"{fname} 主流程异常: {e}\n{traceback.format_exc()}")
                    try:
                        dst = os.path.join(folder, "需人工检查", fname)
                        processor.move_file(fpath, dst)
                    except Exception:
                        pass
        backup_mgr.backup_table("REFUND_LIST")
        print("全部处理完成！")  # 添加完成提示
    except Exception as e:
        log_mgr.log_note(f"主流程异常: {e}\n{traceback.format_exc()}")
        print(f"主流程异常: {e}")

if __name__ == "__main__":
    # 支持命令行参数
    parser = argparse.ArgumentParser(description='退款处理脚本')
    parser.add_argument('--db_path', help='数据库路径')
    parser.add_argument('--mode', help='操作模式 (1-5)', choices=['1', '2', '3', '4', '5'])

    args = parser.parse_args()
    main(db_path=args.db_path, mode=args.mode)