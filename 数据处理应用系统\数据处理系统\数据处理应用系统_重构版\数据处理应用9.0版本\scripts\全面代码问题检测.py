#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面代码问题检测 - 检查所有代码，识别和解决潜在问题
"""

import sys
import os
import ast
import importlib.util
from pathlib import Path
from typing import List, Dict, Any

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class CodeAnalyzer:
    """代码分析器"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.suggestions = []
        
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """分析单个文件"""
        result = {
            'file': file_path,
            'syntax_errors': [],
            'import_issues': [],
            'exception_issues': [],
            'potential_bugs': [],
            'performance_issues': [],
            'security_issues': []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 语法检查
            result['syntax_errors'] = self._check_syntax(content, file_path)
            
            # 导入检查
            result['import_issues'] = self._check_imports(content, file_path)
            
            # 异常处理检查
            result['exception_issues'] = self._check_exception_handling(content)
            
            # 潜在bug检查
            result['potential_bugs'] = self._check_potential_bugs(content)
            
            # 性能问题检查
            result['performance_issues'] = self._check_performance_issues(content)
            
            # 安全问题检查
            result['security_issues'] = self._check_security_issues(content)
            
        except Exception as e:
            result['analysis_error'] = str(e)
        
        return result
    
    def _check_syntax(self, content: str, file_path: str) -> List[str]:
        """检查语法错误"""
        errors = []
        try:
            ast.parse(content)
        except SyntaxError as e:
            errors.append(f"语法错误 第{e.lineno}行: {e.msg}")
        except Exception as e:
            errors.append(f"解析错误: {e}")
        return errors
    
    def _check_imports(self, content: str, file_path: str) -> List[str]:
        """检查导入问题"""
        issues = []
        lines = content.split('\n')
        
        # 检查循环导入
        imports = []
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('import ') or line.startswith('from '):
                imports.append((i, line))
        
        # 检查未使用的导入
        for line_num, import_line in imports:
            if 'import' in import_line:
                try:
                    # 提取模块名
                    if import_line.startswith('from '):
                        parts = import_line.split()
                        if 'import' in parts:
                            idx = parts.index('import')
                            modules = ' '.join(parts[idx+1:]).split(',')
                        else:
                            continue
                    else:
                        modules = [import_line.replace('import ', '').split(' as ')[0]]
                    
                    for module in modules:
                        module = module.strip()
                        if module and module not in content.replace(import_line, ''):
                            issues.append(f"第{line_num}行: 可能未使用的导入 '{module}'")
                except:
                    pass
        
        # 检查条件导入的异常处理
        in_try_block = False
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('try:'):
                in_try_block = True
            elif line.startswith('except'):
                in_try_block = False
            elif in_try_block and ('import' in line):
                # 检查是否有对应的except处理
                found_except = False
                for j in range(i, min(i+10, len(lines))):
                    if lines[j].strip().startswith('except'):
                        found_except = True
                        break
                if not found_except:
                    issues.append(f"第{i}行: try块中的导入缺少异常处理")
        
        return issues
    
    def _check_exception_handling(self, content: str) -> List[str]:
        """检查异常处理问题"""
        issues = []
        lines = content.split('\n')
        
        # 检查裸露的except
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line == 'except:' or line.startswith('except:'):
                issues.append(f"第{i}行: 使用了裸露的except，应该指定异常类型")
        
        # 检查空的except块
        for i, line in enumerate(lines, 1):
            if line.strip().startswith('except'):
                # 检查接下来几行是否只有pass
                next_lines = []
                for j in range(i, min(i+5, len(lines))):
                    next_line = lines[j].strip()
                    if next_line and not next_line.startswith('#'):
                        next_lines.append(next_line)
                
                if len(next_lines) == 2 and next_lines[1] == 'pass':
                    issues.append(f"第{i}行: 空的异常处理块，应该添加日志记录")
        
        # 检查异常重新抛出
        for i, line in enumerate(lines, 1):
            if 'raise' in line and line.strip() == 'raise':
                # 检查是否在except块中
                in_except = False
                for j in range(max(0, i-10), i):
                    if lines[j].strip().startswith('except'):
                        in_except = True
                        break
                if not in_except:
                    issues.append(f"第{i}行: 裸露的raise语句，可能导致未定义的异常")
        
        return issues
    
    def _check_potential_bugs(self, content: str) -> List[str]:
        """检查潜在bug"""
        bugs = []
        lines = content.split('\n')
        
        # 检查可能的空指针访问
        for i, line in enumerate(lines, 1):
            if '.get(' in line and 'if' not in line and 'assert' not in line:
                if 'pd.notna(' not in line and 'is not None' not in line:
                    bugs.append(f"第{i}行: 使用.get()但未检查None值")
        
        # 检查DataFrame操作
        for i, line in enumerate(lines, 1):
            if 'df[' in line and 'if' not in line and 'try' not in line:
                if 'in df.columns' not in lines[max(0, i-3):i]:
                    bugs.append(f"第{i}行: DataFrame列访问未检查列是否存在")
        
        # 检查文件操作
        for i, line in enumerate(lines, 1):
            if 'open(' in line and 'with' not in line:
                bugs.append(f"第{i}行: 文件操作未使用with语句，可能导致资源泄露")
        
        # 检查SQL注入风险
        for i, line in enumerate(lines, 1):
            if 'execute(' in line and 'f"' in line:
                bugs.append(f"第{i}行: SQL查询使用f-string，存在注入风险")
        
        return bugs
    
    def _check_performance_issues(self, content: str) -> List[str]:
        """检查性能问题"""
        issues = []
        lines = content.split('\n')
        
        # 检查循环中的DataFrame操作
        in_loop = False
        for i, line in enumerate(lines, 1):
            if 'for ' in line and ' in ' in line:
                in_loop = True
            elif line.strip() == '' or not line.startswith(' '):
                in_loop = False
            elif in_loop and 'pd.concat(' in line:
                issues.append(f"第{i}行: 循环中使用pd.concat，考虑使用列表收集后一次性合并")
        
        # 检查重复的数据库连接
        db_connections = []
        for i, line in enumerate(lines, 1):
            if 'get_connection()' in line:
                db_connections.append(i)
        
        if len(db_connections) > 5:
            issues.append(f"文件中有{len(db_connections)}个数据库连接，考虑连接复用")
        
        # 检查大数据集操作
        for i, line in enumerate(lines, 1):
            if 'read_sql(' in line and 'SELECT *' in line:
                issues.append(f"第{i}行: 使用SELECT *可能导致内存问题，建议指定列名")
        
        return issues
    
    def _check_security_issues(self, content: str) -> List[str]:
        """检查安全问题"""
        issues = []
        lines = content.split('\n')
        
        # 检查硬编码密码
        for i, line in enumerate(lines, 1):
            if any(keyword in line.lower() for keyword in ['password', 'passwd', 'pwd']):
                if '=' in line and '"' in line:
                    issues.append(f"第{i}行: 可能包含硬编码密码")
        
        # 检查eval/exec使用
        for i, line in enumerate(lines, 1):
            if 'eval(' in line or 'exec(' in line:
                issues.append(f"第{i}行: 使用eval/exec存在安全风险")
        
        # 检查shell命令执行
        for i, line in enumerate(lines, 1):
            if 'os.system(' in line or 'subprocess.call(' in line:
                if 'shell=True' in line:
                    issues.append(f"第{i}行: shell命令执行存在注入风险")
        
        return issues

def analyze_project():
    """分析整个项目"""
    print("🔍 开始全面代码问题检测")
    print("=" * 80)
    
    analyzer = CodeAnalyzer()
    
    # 要分析的文件
    files_to_analyze = [
        "scripts/data_import_optimized.py",
        "scripts/refund_process_optimized.py", 
        "01_主程序/数据处理与导入应用_完整版.py",
        "01_主程序/unified_processing_tab.py",
        "utils/logger.py",
        "database/connection_pool.py",
        "database/models.py"
    ]
    
    total_issues = 0
    critical_issues = 0
    
    for file_path in files_to_analyze:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"\n📁 分析文件: {file_path}")
            print("-" * 60)
            
            result = analyzer.analyze_file(str(full_path))
            
            # 统计问题
            file_issues = 0
            
            if result['syntax_errors']:
                print("🚨 语法错误:")
                for error in result['syntax_errors']:
                    print(f"  ❌ {error}")
                    file_issues += 1
                    critical_issues += 1
            
            if result['import_issues']:
                print("⚠️ 导入问题:")
                for issue in result['import_issues']:
                    print(f"  ⚠️ {issue}")
                    file_issues += 1
            
            if result['exception_issues']:
                print("🔧 异常处理问题:")
                for issue in result['exception_issues']:
                    print(f"  🔧 {issue}")
                    file_issues += 1
            
            if result['potential_bugs']:
                print("🐛 潜在bug:")
                for bug in result['potential_bugs']:
                    print(f"  🐛 {bug}")
                    file_issues += 1
            
            if result['performance_issues']:
                print("⚡ 性能问题:")
                for issue in result['performance_issues']:
                    print(f"  ⚡ {issue}")
                    file_issues += 1
            
            if result['security_issues']:
                print("🔒 安全问题:")
                for issue in result['security_issues']:
                    print(f"  🔒 {issue}")
                    file_issues += 1
                    critical_issues += 1
            
            if file_issues == 0:
                print("✅ 未发现问题")
            else:
                print(f"📊 发现 {file_issues} 个问题")
            
            total_issues += file_issues
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    # 总结报告
    print("\n" + "=" * 80)
    print("🎯 代码问题检测总结")
    print("=" * 80)
    
    print(f"📊 总问题数: {total_issues}")
    print(f"🚨 关键问题: {critical_issues}")
    print(f"⚠️ 一般问题: {total_issues - critical_issues}")
    
    if critical_issues == 0:
        print("🎉 未发现关键问题")
    else:
        print(f"⚠️ 发现 {critical_issues} 个关键问题需要立即修复")
    
    if total_issues == 0:
        print("✅ 代码质量良好，未发现明显问题")
    elif total_issues < 10:
        print("✅ 代码质量较好，少量问题可以优化")
    elif total_issues < 30:
        print("⚠️ 代码质量一般，建议修复主要问题")
    else:
        print("❌ 代码质量需要改进，建议全面重构")
    
    return total_issues, critical_issues

def main():
    """主函数"""
    try:
        total_issues, critical_issues = analyze_project()
        
        print("\n💡 修复建议:")
        print("1. 优先修复语法错误和安全问题")
        print("2. 改进异常处理，添加具体异常类型")
        print("3. 优化性能问题，特别是数据库操作")
        print("4. 清理未使用的导入")
        print("5. 添加输入验证和边界检查")
        
        return 0 if critical_issues == 0 else 1
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n🎯 分析{'完成' if exit_code == 0 else '失败'}")
    input("按回车键退出...")
    sys.exit(exit_code)
