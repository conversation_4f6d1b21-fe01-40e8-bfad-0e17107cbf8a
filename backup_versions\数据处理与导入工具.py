# coding: utf-8
import os
import re
import pandas as pd
import numpy as np
import logging
import sqlite3
import shutil
import traceback
import configparser
import openpyxl
from datetime import datetime
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter
import warnings
import argparse

# 抑制 openpyxl 警告
warnings.simplefilter("ignore", category=UserWarning)

# 读取配置文件
config = configparser.ConfigParser()
config.read(os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini"), encoding='utf-8')

# 配置日志
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"data_processing_{datetime.now().strftime('%Y%m%d')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger()

# 从配置文件获取数据库路径或使用默认路径
def get_db_path_from_config():
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")
    default_db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "database", "sales_reports.db")
    
    if os.path.exists(config_path):
        config.read(config_path, encoding='utf-8')
        if 'Database' in config and 'db_path' in config['Database']:
            return config['Database']['db_path']
    
    return default_db_path

# 数据库路径
DB_PATH = get_db_path_from_config()

# 确保数据库目录存在
os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

def backup_database():
    """🔧 备份安全修复：增强的数据库文件备份"""
    import threading
    import tempfile

    # 🔧 备份并发保护：使用锁防止并发备份冲突
    backup_lock = getattr(backup_database, '_lock', None)
    if backup_lock is None:
        backup_lock = threading.Lock()
        backup_database._lock = backup_lock

    with backup_lock:
        try:
            db_path = get_db_path_from_config()
            logger.info(f"使用数据库路径: {db_path}")

            if not os.path.exists(db_path):
                logger.warning("数据库文件不存在，无法备份")
                return None

            backup_dir = os.path.join(os.path.dirname(db_path), "backups")
            os.makedirs(backup_dir, exist_ok=True)

            backup_file = os.path.join(backup_dir, f"sales_reports_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")

            # 🔧 备份原子性修复：先备份到临时文件，再移动到最终位置
            with tempfile.NamedTemporaryFile(delete=False, suffix='.db.tmp') as temp_file:
                temp_backup_path = temp_file.name

            try:
                # 复制到临时文件
                shutil.copy2(db_path, temp_backup_path)

                # 🔧 备份验证修复：验证备份文件完整性
                if not _verify_backup_file(temp_backup_path, db_path):
                    raise Exception("备份文件验证失败")

                # 原子性移动到最终位置
                shutil.move(temp_backup_path, backup_file)

                logger.info(f"数据库已安全备份到: {backup_file}")
                return backup_file

            except Exception as backup_error:
                # 🔧 备份错误处理修复：清理失败的备份文件
                for cleanup_file in [temp_backup_path, backup_file]:
                    if os.path.exists(cleanup_file):
                        try:
                            os.remove(cleanup_file)
                        except:
                            pass
                raise backup_error

        except Exception as e:
            logger.error(f"备份数据库时出错: {str(e)}")
            return None

def _verify_backup_file(backup_path, original_path):
    """🔧 备份验证修复：验证备份文件完整性"""
    try:
        # 检查文件大小
        backup_size = os.path.getsize(backup_path)
        original_size = os.path.getsize(original_path)

        if backup_size != original_size:
            logger.error(f"备份文件大小不匹配: {backup_size} vs {original_size}")
            return False

        if backup_size < 1024:  # 小于1KB肯定有问题
            logger.error(f"备份文件太小: {backup_size} bytes")
            return False

        # 简单的数据库连接测试
        import sqlite3
        try:
            with sqlite3.connect(backup_path, timeout=10) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 1")
                result = cursor.fetchone()
                if not result:
                    logger.error("备份数据库没有表")
                    return False
        except Exception as db_error:
            logger.error(f"备份数据库连接测试失败: {db_error}")
            return False

        return True

    except Exception as e:
        logger.error(f"备份验证失败: {e}")
        return False

def restore_database(backup_file):
    """🔧 恢复安全修复：增强的数据库恢复功能"""
    import tempfile

    try:
        db_path = get_db_path_from_config()
        logger.info(f"使用数据库路径: {db_path}")

        # 🔧 恢复验证修复：验证备份文件
        if not backup_file or not os.path.exists(backup_file):
            logger.warning("备份文件不存在，无法恢复")
            return False

        # 验证备份文件完整性
        if not _verify_restore_file(backup_file):
            logger.error("备份文件验证失败，无法恢复")
            return False

        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # 🔧 恢复安全修复：备份原数据库（如果存在）
        original_backup = None
        if os.path.exists(db_path):
            try:
                original_backup = f"{db_path}.restore_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(db_path, original_backup)
                logger.info(f"原数据库已备份到: {original_backup}")
            except Exception as backup_error:
                logger.error(f"备份原数据库失败: {backup_error}")
                return False

        # 🔧 恢复原子性修复：使用临时文件进行原子性恢复
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix='.db.tmp') as temp_file:
                temp_restore_path = temp_file.name

            # 复制备份到临时文件
            shutil.copy2(backup_file, temp_restore_path)

            # 🔧 恢复验证修复：验证恢复后的数据库
            if not _verify_restore_file(temp_restore_path):
                raise Exception("恢复文件验证失败")

            # 原子性移动到最终位置
            if os.path.exists(db_path):
                os.remove(db_path)
            shutil.move(temp_restore_path, db_path)

            # 🔧 恢复后验证修复：验证恢复后的数据库可用性
            if not _verify_database_functionality(db_path):
                raise Exception("恢复后数据库功能验证失败")

            logger.info(f"数据库已安全恢复: {backup_file}")

            # 清理原数据库备份（恢复成功后）
            if original_backup and os.path.exists(original_backup):
                try:
                    os.remove(original_backup)
                    logger.info("原数据库备份已清理")
                except:
                    logger.warning("清理原数据库备份失败，但不影响恢复")

            return True

        except Exception as restore_error:
            # 🔧 恢复错误处理修复：恢复失败时回滚到原数据库
            logger.error(f"数据库恢复失败: {restore_error}")

            # 清理临时文件
            if os.path.exists(temp_restore_path):
                try:
                    os.remove(temp_restore_path)
                except:
                    pass

            # 如果有原数据库备份，尝试恢复
            if original_backup and os.path.exists(original_backup):
                try:
                    if os.path.exists(db_path):
                        os.remove(db_path)
                    shutil.move(original_backup, db_path)
                    logger.info("已回滚到原数据库")
                except Exception as rollback_error:
                    logger.error(f"回滚到原数据库失败: {rollback_error}")

            return False

    except Exception as e:
        logger.error(f"恢复数据库时出错: {str(e)}")
        return False

def _verify_restore_file(file_path):
    """🔧 恢复验证修复：验证恢复文件的完整性"""
    try:
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size < 1024:  # 小于1KB肯定有问题
            logger.error(f"恢复文件太小: {file_size} bytes")
            return False

        # 数据库连接测试
        import sqlite3
        try:
            with sqlite3.connect(file_path, timeout=10) as conn:
                cursor = conn.cursor()
                # 检查数据库结构
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                if not tables:
                    logger.error("恢复文件没有表结构")
                    return False

                # 检查关键表是否存在
                table_names = [table[0] for table in tables]
                required_tables = ['IOT_Sales', 'ZERO_Sales']  # 根据实际需要调整
                for required_table in required_tables:
                    if required_table in table_names:
                        # 检查表是否有数据结构
                        cursor.execute(f"PRAGMA table_info({required_table})")
                        columns = cursor.fetchall()
                        if not columns:
                            logger.error(f"表 {required_table} 没有列结构")
                            return False

        except Exception as db_error:
            logger.error(f"恢复文件数据库测试失败: {db_error}")
            return False

        return True

    except Exception as e:
        logger.error(f"恢复文件验证失败: {e}")
        return False

def _verify_database_functionality(db_path):
    """🔧 恢复后验证修复：验证数据库功能性"""
    try:
        import sqlite3
        with sqlite3.connect(db_path, timeout=10) as conn:
            cursor = conn.cursor()

            # 测试基本查询功能
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]

            if table_count == 0:
                logger.error("恢复后数据库没有表")
                return False

            # 测试写入功能
            cursor.execute("CREATE TEMPORARY TABLE test_table (id INTEGER)")
            cursor.execute("INSERT INTO test_table (id) VALUES (1)")
            cursor.execute("SELECT id FROM test_table WHERE id = 1")
            result = cursor.fetchone()

            if not result or result[0] != 1:
                logger.error("恢复后数据库写入测试失败")
                return False

            return True

    except Exception as e:
        logger.error(f"数据库功能验证失败: {e}")
        return False

def create_tables():
    """创建数据库表结构"""
    try:
        db_path = get_db_path_from_config()
        logger.info(f"使用数据库路径: {db_path}")
        with sqlite3.connect(db_path) as conn:
            cur = conn.cursor()
            
            # 创建IOT_Sales表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS IOT_Sales (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Copartner_name TEXT,
                Order_No TEXT,
                Order_types TEXT,
                Order_status TEXT,
                Order_price TEXT,
                Payment TEXT,
                Order_time TEXT,
                Equipment_ID TEXT,
                Equipment_name TEXT,
                Branch_name TEXT,
                Payment_date TEXT,
                User_name TEXT,
                Time TEXT,
                Matched_Order_ID TEXT,
                OrderTime_dt TEXT,
                Transaction_Num TEXT,
                Import_Date TEXT,
                Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 创建ZERO_Sales表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS ZERO_Sales (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Copartner_name TEXT,
                Order_No TEXT,
                Order_types TEXT,
                Order_status TEXT,
                Order_price TEXT,
                Payment TEXT,
                Order_time TEXT,
                Equipment_ID TEXT,
                Equipment_name TEXT,
                Branch_name TEXT,
                Payment_date TEXT,
                User_name TEXT,
                Time TEXT,
                Matched_Order_ID TEXT,
                OrderTime_dt TEXT,
                Transaction_Num TEXT,
                Import_Date TEXT,
                Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 创建APP_Sales表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS APP_Sales (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Copartner_name TEXT,
                Order_No TEXT,
                Order_types TEXT,
                Order_status TEXT,
                Order_price TEXT,
                Payment TEXT,
                Order_time TEXT,
                Equipment_ID TEXT,
                Equipment_name TEXT,
                Branch_name TEXT,
                Payment_date TEXT,
                User_name TEXT,
                Time TEXT,
                Matched_Order_ID TEXT,
                OrderTime_dt TEXT,
                Transaction_Num TEXT,
                Import_Date TEXT,
                Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 创建导入日志表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS Import_Logs (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Platform TEXT,
                Filename TEXT,
                Sheet_Name TEXT,
                Import_Date TEXT,
                Status TEXT,
                Message TEXT,
                Log_Time TEXT
            )
            """)
            
            conn.commit()
            logger.info("数据库表结构已创建或已存在")
    except Exception as e:
        logger.error(f"创建数据库表结构时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise

def extract_date_from_filename(filename):
    """从文件名中提取日期信息"""
    try:
        # 尝试从文件名中提取6位数字作为日期
        match = re.search(r'(\d{6})', filename)
        if match:
            date_str = match.group(1)
            # 假设格式为YYMMDD
            year = int(date_str[:2])
            month = int(date_str[2:4])
            day = int(date_str[4:6])
            
            # 处理年份，假设20xx年
            if year < 50:  # 假设50以下是2000年以后
                year += 2000
            else:
                year += 1900
                
            return f"{year}-{month:02d}-{day:02d}"
    except Exception as e:
        logger.warning(f"从文件名提取日期失败: {filename}, 错误: {str(e)}")
    
    return None

def verify_file_totals(file1_path, file2_path, tolerance=0.01):
    """验证第一文件和第二文件的总金额是否一致"""
    try:
        logger.info(f"开始验证文件总金额: {file1_path} 和 {file2_path}")
        
        # 检查文件是否存在
        if not os.path.exists(file1_path):
            logger.error(f"第一文件不存在: {file1_path}")
            return False, None, None
        
        if not os.path.exists(file2_path):
            logger.error(f"第二文件不存在: {file2_path}")
            return False, None, None
        
        # 确定平台类型（IOT或ZERO）
        platform_type = None
        if "IOT" in file2_path.upper():
            platform_type = "IOT"
        elif "ZERO" in file2_path.upper():
            platform_type = "ZERO"
        else:
            logger.error(f"无法确定文件的平台类型: {file2_path}")
            return False, None, None
        
        # 检查第二文件是否包含DATA表
        try:
            xl = pd.ExcelFile(file2_path)
            if "DATA" not in xl.sheet_names:
                logger.error(f"第二文件中不存在DATA表，可能尚未处理: {file2_path}")
                return False, None, platform_type
        except Exception as e:
            logger.error(f"检查第二文件sheet时出错: {str(e)}")
            return False, None, platform_type
        
        # 计算第一文件的总金额
        try:
            # 🔧 资源管理修复：使用with语句确保ExcelFile正确关闭
            with pd.ExcelFile(file1_path) as xl1:
                sheet_name = None

                # 尝试找到合适的sheet
                if "TRANSACTION_LIST" in xl1.sheet_names:
                    sheet_name = "TRANSACTION_LIST"
                else:
                    # 查找数字格式的sheet名
                    for sheet in xl1.sheet_names:
                        if sheet.isdigit():
                            sheet_name = sheet
                            break

                    # 如果没找到，使用第一个sheet
                    if sheet_name is None and xl1.sheet_names:
                        sheet_name = xl1.sheet_names[0]

                if sheet_name is None:
                    logger.error(f"无法确定第一文件的sheet名: {file1_path}")
                    return False, None, platform_type

                # 读取第一文件数据
                df1 = pd.read_excel(file1_path, sheet_name=sheet_name)
            
            # 标准化列名
            df1.columns = [str(col).strip() for col in df1.columns]
            
            # 查找金额列
            amount_col = None
            for col in df1.columns:
                if "bill amt" in col.lower() or "actual amt" in col.lower():
                    amount_col = col
                    break
            
            if amount_col is None:
                logger.error(f"无法在第一文件中找到金额列: {file1_path}")
                return False, None, platform_type
            
            # 查找状态列
            status_col = None
            for col in df1.columns:
                if "status" in col.lower():
                    status_col = col
                    break
            
            if status_col is None:
                logger.warning(f"无法在第一文件中找到状态列，将使用所有记录计算总金额: {file1_path}")
                df1_filtered = df1
            else:
                # 筛选状态为settled的记录
                df1_filtered = df1[df1[status_col].astype(str).str.strip().str.lower().str.contains("settled")]
            
            # 计算总金额
            df1_filtered[amount_col] = pd.to_numeric(df1_filtered[amount_col], errors="coerce")
            total_amount1 = df1_filtered[amount_col].sum()
            
            logger.info(f"第一文件总金额: {total_amount1}")
            
            # 统计第一文件中各价格的数量
            price_counts1 = df1_filtered[amount_col].value_counts().to_dict()
            price_stats = {float(price): int(count) for price, count in price_counts1.items()}
            
        except Exception as e:
            logger.error(f"计算第一文件总金额时出错: {str(e)}")
            return False, None, platform_type
        
        # 计算第二文件DATA表的总金额
        try:
            # 读取第二文件DATA表
            df2 = pd.read_excel(file2_path, sheet_name="DATA")
            
            # 标准化列名
            df2.columns = [str(col).strip() for col in df2.columns]
            
            # 查找金额列
            amount_col2 = None
            for col in df2.columns:
                if "order price" in col.lower() or "price" in col.lower():
                    amount_col2 = col
                    break
            
            if amount_col2 is None:
                logger.error(f"无法在第二文件DATA表中找到金额列: {file2_path}")
                return False, None, platform_type
            
            # 查找状态列
            status_col2 = None
            for col in df2.columns:
                if "order status" in col.lower() or "status" in col.lower():
                    status_col2 = col
                    break
            
            if status_col2 is None:
                logger.warning(f"无法在第二文件DATA表中找到状态列，将使用所有记录计算总金额: {file2_path}")
                df2_filtered = df2
            else:
                # 筛选状态为finish的记录
                df2_filtered = df2[df2[status_col2].astype(str).str.strip().str.lower() == "finish"]
            
            # 计算总金额
            df2_filtered[amount_col2] = pd.to_numeric(df2_filtered[amount_col2], errors="coerce")
            total_amount2 = df2_filtered[amount_col2].sum()
            
            logger.info(f"第二文件DATA表总金额: {total_amount2}")
            
            # 统计第二文件中各价格的数量
            price_counts2 = df2_filtered[amount_col2].value_counts().to_dict()
            second_price_stats = {float(price): int(count) for price, count in price_counts2.items()}
            
        except Exception as e:
            logger.error(f"计算第二文件DATA表总金额时出错: {str(e)}")
            return False, None, platform_type
        
        # 比较两个文件的总金额
        if total_amount1 == 0 or total_amount2 == 0:
            logger.error("总金额为0，请检查数据是否正确")
            return False, None, platform_type
        
        difference = abs(total_amount1 - total_amount2)
        difference_percentage = difference / max(total_amount1, total_amount2)
        
        # 收集金额统计信息
        stats_info = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "platform": platform_type,
            "first_file_total": total_amount1,
            "second_file_total": total_amount2,
            "verification_passed": difference_percentage <= tolerance,
            "price_counts": price_stats,
            "second_file_price_counts": second_price_stats
        }
        
        if difference_percentage > tolerance:
            logger.error(f"总金额不一致！第一文件: {total_amount1}, 第二文件: {total_amount2}, 差异: {difference} ({difference_percentage:.2%})")
            return False, stats_info, platform_type
        else:
            logger.info(f"总金额验证通过，差异在允许范围内: {difference_percentage:.2%}")
            return True, stats_info, platform_type
        
    except Exception as e:
        logger.error(f"验证文件总金额时出错: {str(e)}")
        return False, None, platform_type

def process_second_file(file_path, stats_info):
    """处理第二文件，筛选数据并更新LOG表"""
    try:
        logger.info(f"开始处理第二文件: {file_path}")
        platform_type = stats_info["platform"]
        
        # 读取第二文件DATA表
        df = pd.read_excel(file_path, sheet_name="DATA")
        
        # 标准化列名
        df.columns = [str(col).strip() for col in df.columns]
        
        # 查找状态列
        status_col = None
        for col in df.columns:
            if "order status" in col.lower() or "status" in col.lower():
                status_col = col
                break
        
        if status_col is None:
            logger.error(f"无法在第二文件DATA表中找到Order status列，无法进行筛选: {file_path}")
            return False
        
        # 记录原始记录数
        original_count = len(df)
        
        # 筛选出不是Close和Refunding的记录
        df_filtered = df[~df[status_col].astype(str).str.strip().str.lower().isin(["close", "refunding"])]
        
        # 记录删除的记录数
        removed_count = original_count - len(df_filtered)
        logger.info(f"已从DATA表中删除 {removed_count} 条Close和Refunding状态的记录")
        
        # 保留指定列
        required_columns = [
            'Copartner name', 'Order No.', 'Order types', 'Order status', 
            'Order price', 'Payment', 'Order time', 'Equipment ID', 
            'Equipment name', 'Branch name', 'Payment date', 'User name', 
            'Time', 'Matched Order ID', 'OrderTime_dt'
        ]
        
        # 标准化列名
        df_columns = [col.strip() for col in df_filtered.columns]
        columns_to_keep = []
        
        for req_col in required_columns:
            found = False
            for col in df_columns:
                if req_col.lower() == col.lower():
                    columns_to_keep.append(col)
                    found = True
                    break
            if not found:
                logger.warning(f"在DATA表中未找到列: {req_col}")
        
        # 如果找不到任何指定列，保留所有列
        if not columns_to_keep:
            logger.warning("未找到任何指定列，将保留所有列")
            columns_to_keep = df_filtered.columns.tolist()
        
        df_filtered = df_filtered[columns_to_keep]
        
        # 提取日期信息用于sheet名
        date_str = extract_date_from_filename(os.path.basename(file_path))
        if not date_str:
            date_str = datetime.now().strftime("%y%m%d")
        else:
            # 转换为YYMMDD格式
            try:
                dt = datetime.strptime(date_str, "%Y-%m-%d")
                date_str = dt.strftime("%y%m%d")
            except:
                pass
        
        # 新的sheet名
        new_sheet_name = f"{platform_type}{date_str}"
        
        # 将处理后的数据写回Excel文件
        with pd.ExcelWriter(file_path, engine="openpyxl", mode="a", if_sheet_exists="replace") as writer:
            df_filtered.to_excel(writer, sheet_name="DATA", index=False)
        
        # 重命名DATA表为新的sheet名并处理LOG表
        wb = openpyxl.load_workbook(file_path)
        
        # 重命名DATA表
        if "DATA" in wb.sheetnames:
            ws = wb["DATA"]
            ws.title = new_sheet_name
            logger.info(f"已将DATA表重命名为: {new_sheet_name}")
        
        # 处理LOG表
        if "LOG" in wb.sheetnames:
            update_log_sheet(wb, stats_info)
        
        # 保存工作簿
        wb.save(file_path)
        logger.info(f"已成功更新第二文件: {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"处理第二文件时出错: {str(e)}")
        return False

def update_log_sheet(workbook, stats_info):
    """更新LOG表中的金额统计数据"""
    try:
        ws = workbook["LOG"]
        
        # 查找最后一列
        last_col = 1
        for col in range(1, 100):  # 假设不超过100列
            if ws.cell(row=1, column=col).value is None:
                last_col = col
                break
        
        # 设置标题
        date_str = datetime.now().strftime("%Y-%m-%d")
        ws.cell(row=1, column=last_col).value = f"{date_str} {stats_info['platform']}"
        ws.cell(row=1, column=last_col).font = Font(bold=True)
        
        # 设置RAZER金额
        ws.cell(row=2, column=last_col).value = f"RAZER : RM{stats_info['first_file_total']:.2f}"
        
        # 设置各价格的数量
        row = 3
        for price, count in sorted(stats_info['price_counts'].items()):
            ws.cell(row=row, column=last_col).value = f"RM{price:.2f} x {count}"
            row += 1
        
        # 设置列宽
        ws.column_dimensions[get_column_letter(last_col)].width = 30
        
        logger.info("已成功更新LOG表中的金额统计数据")
    except Exception as e:
        logger.error(f"更新LOG表中的金额统计数据时出错: {str(e)}")

def import_to_database(file_path, platform_type):
    """将处理后的数据导入数据库"""
    try:
        logger.info(f"开始导入数据到数据库: {file_path}")
        
        # 确保数据库目录存在
        db_path = get_db_path_from_config()
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 备份数据库
        backup_file = backup_database()
        
        # 创建数据库表结构
        create_tables()
        
        # 提取日期信息
        import_date = extract_date_from_filename(os.path.basename(file_path))
        if not import_date:
            import_date = datetime.now().strftime("%Y-%m-%d")
        
        # 确定sheet名
        wb = openpyxl.load_workbook(file_path, read_only=True)
        sheet_name = None
        
        # 查找以平台类型开头的sheet
        for sheet in wb.sheetnames:
            if sheet.startswith(platform_type):
                sheet_name = sheet
                break
        
        if not sheet_name:
            logger.error(f"在文件中未找到以{platform_type}开头的sheet: {file_path}")
            return False
        
        # 读取数据
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        
        # 标准化列名
        column_map = {
            'Copartner name': 'Copartner_name',
            'Order No.': 'Order_No',
            'Order types': 'Order_types',
            'Order status': 'Order_status',
            'Order price': 'Order_price',
            'Payment': 'Payment',
            'Order time': 'Order_time',
            'Equipment ID': 'Equipment_ID',
            'Equipment name': 'Equipment_name',
            'Branch name': 'Branch_name',
            'Payment date': 'Payment_date',
            'User name': 'User_name',
            'Time': 'Time',
            'Matched Order ID': 'Matched_Order_ID',
            'OrderTime_dt': 'OrderTime_dt'
        }
        
        # 重命名列
        df = df.rename(columns={col: column_map.get(col, col) for col in df.columns})
        
        # 添加导入日期
        df['Import_Date'] = import_date
        
        # 连接数据库并开始事务
        conn = sqlite3.connect(DB_PATH)
        try:
            # 检查是否已存在相同日期的数据
            table_name = f"{platform_type}_Sales"
            cursor = conn.cursor()
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE Import_Date = ?", (import_date,))
            count = cursor.fetchone()[0]
            
            if count > 0:
                # 备份现有数据
                backup_table_name = f"{table_name}_Backup_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                cursor.execute(f"CREATE TABLE {backup_table_name} AS SELECT * FROM {table_name} WHERE Import_Date = ?", (import_date,))
                conn.commit()
                logger.info(f"已备份现有数据到表: {backup_table_name}")
                
                # 删除现有数据
                cursor.execute(f"DELETE FROM {table_name} WHERE Import_Date = ?", (import_date,))
                conn.commit()
                logger.info(f"已删除现有数据，准备导入新数据")
            
            # 导入数据
            try:
                total_rows = len(df)
                df.to_sql(table_name, conn, if_exists='append', index=False)
                
                # 记录导入日志
                cursor.execute("""
                INSERT INTO Import_Logs (Platform, Filename, Sheet_Name, Import_Date, Status, Message, Log_Time)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    platform_type,
                    os.path.basename(file_path),
                    sheet_name,
                    import_date,
                    "Success",
                    f"成功导入 {total_rows} 条记录",
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ))
                
                conn.commit()
                logger.info(f"已成功导入 {total_rows} 条记录到数据库表: {table_name}")
                return True
            except Exception as e:
                logger.error(f"导入数据时出错: {str(e)}")
                if backup_file:
                    restore_database(backup_file)
                    logger.info("已从备份恢复数据库")
                return False
            
        except Exception as e:
            # 回滚事务
            conn.rollback()
            logger.error(f"导入数据到数据库时出错，已回滚: {str(e)}")
            
            # 记录导入日志
            try:
                cursor.execute("""
                INSERT INTO Import_Logs (File_Name, Platform, Import_Date, Status, Message, Total_Records, Imported_Records)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (os.path.basename(file_path), platform_type, import_date, "ERROR", str(e), 0, 0))
                conn.commit()
            except:
                pass
            
            return False
            
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"导入数据到数据库时出错: {str(e)}")
        logger.error(traceback.format_exc())
        
        # 记录错误到Import_Logs表
        try:
            db_path = get_db_path_from_config()
            with sqlite3.connect(db_path) as conn:
                log_sql = "INSERT INTO Import_Logs (Platform, Filename, Sheet_Name, Import_Date, Status, Message, Log_Time) VALUES (?, ?, ?, ?, ?, ?, ?)"
                conn.execute(log_sql, (
                    platform_type,
                    os.path.basename(file_path),
                    sheet_name if 'sheet_name' in locals() else "",
                    import_date if 'import_date' in locals() else datetime.now().strftime("%Y-%m-%d"),
                    "Error",
                    f"导入数据到数据库时出错: {str(e)}",
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ))
                conn.commit()
        except Exception as log_err:
            logger.error(f"记录导入日志时出错: {str(log_err)}")
        
        # 恢复数据库
        if 'backup_file' in locals() and backup_file:
            restore_database(backup_file)
            logger.info("已从备份恢复数据库")
            
        return False

def process_file_pair(file1_path, file2_path):
    """处理一对文件（第一文件和第二文件）"""
    try:
        # 验证文件总金额
        success, stats_info, platform_type = verify_file_totals(file1_path, file2_path)
        
        if not success:
            logger.error(f"验证文件总金额失败: {file1_path} 和 {file2_path}")
            return False
        
        # 处理第二文件
        if not process_second_file(file2_path, stats_info):
            logger.error(f"处理第二文件失败: {file2_path}")
            return False
        
        # 导入数据到数据库
        if import_to_database(file2_path, platform_type):
            logger.info(f"已成功处理并导入数据: {file2_path}")
            print(f"已成功处理并导入数据: {file2_path}")
            return True
        else:
            logger.error(f"导入数据到数据库失败: {file2_path}")
            return False
        
    except Exception as e:
        logger.error(f"处理文件对时出错: {str(e)}")
        return False

def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = argparse.ArgumentParser(description="数据处理与导入工具")
        parser.add_argument("--file1", help="第一文件路径（SETTLEMENT文件）")
        parser.add_argument("--file2", help="第二文件路径（CHINA文件）")
        args = parser.parse_args()
        
        if args.file1 and args.file2:
            # 处理指定的文件对
            if process_file_pair(args.file1, args.file2):
                print("文件处理和导入成功！")
            else:
                print("文件处理或导入失败，请查看日志了解详情。")
        else:
            print("请提供第一文件和第二文件的路径。")
            print("示例: python 数据处理与导入工具.py --file1 SETTLEMENT_REPORT.xlsx --file2 110525_CHINA_IOT.xlsx")
    
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        print(f"程序执行出错: {str(e)}")

if __name__ == "__main__":
    main()