#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
潜在问题检查脚本
深入检查可能影响数据库连接和应用稳定性的潜在问题
"""

import os
import sys
import sqlite3
import configparser
import tempfile
import threading
import time
from pathlib import Path
from datetime import datetime

def check_path_separator_issues():
    """检查路径分隔符问题"""
    print("🔍 检查路径分隔符问题...")
    
    issues = []
    
    # 检查主要Python文件中的硬编码反斜杠
    python_files = [
        "main_app.py",
        "main_workflow.py", 
        "数据导入脚本_完整版.py",
        "Refund_process 脚本.py"
    ]
    
    for file_path in python_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查硬编码的反斜杠路径
                lines = content.split('\n')
                for line_num, line in enumerate(lines, 1):
                    if '\\\\' in line and ('C:' in line or 'Users' in line):
                        issues.append({
                            'file': file_path,
                            'line': line_num,
                            'issue': '硬编码反斜杠路径',
                            'content': line.strip()
                        })
                        
            except Exception as e:
                print(f"  ⚠️ 无法检查文件 {file_path}: {e}")
    
    if issues:
        print(f"  ❌ 发现 {len(issues)} 个路径分隔符问题:")
        for issue in issues[:5]:  # 显示前5个
            print(f"    📄 {issue['file']}:{issue['line']} - {issue['issue']}")
        if len(issues) > 5:
            print(f"    ... 还有 {len(issues) - 5} 个问题")
    else:
        print(f"  ✅ 未发现路径分隔符问题")
    
    return len(issues) == 0

def check_file_permissions():
    """检查文件和目录权限"""
    print("\n🔐 检查文件和目录权限...")
    
    critical_paths = [
        "config.ini",
        "database",
        "database/sales_reports.db",
        "logs",
        "temp_data",
        "IOT",
        "ZERO",
        "已处理",
        "需人工检查"
    ]
    
    permission_issues = []
    
    for path in critical_paths:
        if os.path.exists(path):
            try:
                # 检查读权限
                if not os.access(path, os.R_OK):
                    permission_issues.append(f"{path}: 缺少读权限")
                
                # 检查写权限（对于目录和可写文件）
                if os.path.isdir(path) or path.endswith(('.db', '.ini', '.log')):
                    if not os.access(path, os.W_OK):
                        permission_issues.append(f"{path}: 缺少写权限")
                
                # 检查目录的执行权限
                if os.path.isdir(path):
                    if not os.access(path, os.X_OK):
                        permission_issues.append(f"{path}: 缺少执行权限")
                        
            except Exception as e:
                permission_issues.append(f"{path}: 权限检查失败 - {e}")
        else:
            print(f"  ℹ️ 路径不存在: {path}")
    
    if permission_issues:
        print(f"  ❌ 发现 {len(permission_issues)} 个权限问题:")
        for issue in permission_issues:
            print(f"    🔒 {issue}")
    else:
        print(f"  ✅ 所有关键路径权限正常")
    
    return len(permission_issues) == 0

def check_database_concurrent_access():
    """检查数据库并发访问安全性"""
    print("\n🔄 检查数据库并发访问安全性...")
    
    # 获取数据库路径
    config = configparser.ConfigParser()
    config.read("config.ini", encoding='utf-8')
    
    if not config.has_section('Database'):
        print("  ❌ 配置文件中没有Database段")
        return False
    
    db_path = config.get('Database', 'db_path', fallback='')
    if not db_path or not os.path.exists(db_path):
        print(f"  ❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 测试并发连接
        connections = []
        errors = []
        
        def create_connection(conn_id):
            try:
                conn = sqlite3.connect(db_path, timeout=5)
                conn.execute("PRAGMA journal_mode=WAL")  # 确保WAL模式
                connections.append((conn_id, conn))
                
                # 简单查询测试
                cursor = conn.cursor()
                cursor.execute("SELECT sqlite_version()")
                version = cursor.fetchone()[0]
                
            except Exception as e:
                errors.append(f"连接{conn_id}: {e}")
        
        # 创建多个并发连接
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_connection, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=10)
        
        # 关闭所有连接
        for conn_id, conn in connections:
            try:
                conn.close()
            except:
                pass
        
        if errors:
            print(f"  ⚠️ 并发连接测试发现问题:")
            for error in errors:
                print(f"    🔄 {error}")
            return False
        else:
            print(f"  ✅ 并发连接测试通过 ({len(connections)}/5 连接成功)")
            return True
            
    except Exception as e:
        print(f"  ❌ 并发测试失败: {e}")
        return False

def check_config_file_robustness():
    """检查配置文件的健壮性"""
    print("\n📄 检查配置文件健壮性...")
    
    config_files = ["config.ini", "数据处理应用系统/config.ini"]
    issues = []
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                # 测试配置文件解析
                config = configparser.ConfigParser()
                config.read(config_file, encoding='utf-8')
                
                # 检查必要的段
                required_sections = ['Database']
                for section in required_sections:
                    if not config.has_section(section):
                        issues.append(f"{config_file}: 缺少必要段 [{section}]")
                
                # 检查数据库路径
                if config.has_section('Database'):
                    db_path = config.get('Database', 'db_path', fallback='')
                    if not db_path:
                        issues.append(f"{config_file}: 数据库路径为空")
                    elif not os.path.exists(os.path.dirname(db_path)):
                        issues.append(f"{config_file}: 数据库目录不存在")
                
                # 测试配置文件写入
                try:
                    with open(config_file, 'a', encoding='utf-8') as f:
                        pass  # 只是测试写入权限
                except Exception as e:
                    issues.append(f"{config_file}: 无法写入 - {e}")
                    
            except Exception as e:
                issues.append(f"{config_file}: 解析失败 - {e}")
    
    if issues:
        print(f"  ❌ 发现 {len(issues)} 个配置文件问题:")
        for issue in issues:
            print(f"    📄 {issue}")
    else:
        print(f"  ✅ 配置文件健壮性检查通过")
    
    return len(issues) == 0

def check_temp_file_cleanup():
    """检查临时文件清理机制"""
    print("\n🗑️ 检查临时文件清理机制...")
    
    temp_dirs = [
        "temp_data",
        "temp_refund_data",
        os.path.join("数据处理应用系统", "temp_data")
    ]
    
    issues = []
    total_temp_files = 0
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            try:
                files = os.listdir(temp_dir)
                temp_files = [f for f in files if f.startswith('temp_') or f.endswith('.tmp')]
                total_temp_files += len(temp_files)
                
                # 检查是否有过期的临时文件
                current_time = time.time()
                for file in temp_files:
                    file_path = os.path.join(temp_dir, file)
                    try:
                        file_age = current_time - os.path.getmtime(file_path)
                        if file_age > 86400:  # 超过24小时
                            issues.append(f"过期临时文件: {file_path} (已存在 {file_age/3600:.1f} 小时)")
                    except Exception as e:
                        issues.append(f"无法检查文件时间: {file_path} - {e}")
                        
            except Exception as e:
                issues.append(f"无法访问临时目录: {temp_dir} - {e}")
    
    print(f"  📊 总计临时文件: {total_temp_files} 个")
    
    if issues:
        print(f"  ⚠️ 发现 {len(issues)} 个临时文件问题:")
        for issue in issues[:5]:  # 显示前5个
            print(f"    🗑️ {issue}")
        if len(issues) > 5:
            print(f"    ... 还有 {len(issues) - 5} 个问题")
    else:
        print(f"  ✅ 临时文件清理机制正常")
    
    return len(issues) == 0

def check_encoding_consistency():
    """检查编码处理一致性"""
    print("\n🔤 检查编码处理一致性...")
    
    python_files = [
        "main_app.py",
        "数据导入脚本_完整版.py", 
        "Refund_process 脚本.py"
    ]
    
    encoding_issues = []
    
    for file_path in python_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查文件开头的编码声明
                lines = content.split('\n')
                has_encoding_declaration = False
                
                for line in lines[:3]:  # 检查前3行
                    if 'coding' in line or 'encoding' in line:
                        has_encoding_declaration = True
                        if 'utf-8' not in line.lower():
                            encoding_issues.append(f"{file_path}: 编码声明不是UTF-8")
                        break
                
                if not has_encoding_declaration:
                    encoding_issues.append(f"{file_path}: 缺少编码声明")
                
                # 检查文件打开操作是否指定编码
                if 'open(' in content:
                    open_calls = []
                    for line_num, line in enumerate(lines, 1):
                        if 'open(' in line and 'encoding=' not in line and 'rb' not in line and 'wb' not in line and 'subprocess' not in line:
                            open_calls.append(line_num)
                    
                    if open_calls:
                        encoding_issues.append(f"{file_path}: {len(open_calls)} 个文件打开操作未指定编码")
                        
            except UnicodeDecodeError:
                encoding_issues.append(f"{file_path}: 文件编码不是UTF-8")
            except Exception as e:
                encoding_issues.append(f"{file_path}: 检查失败 - {e}")
    
    if encoding_issues:
        print(f"  ❌ 发现 {len(encoding_issues)} 个编码问题:")
        for issue in encoding_issues:
            print(f"    🔤 {issue}")
    else:
        print(f"  ✅ 编码处理一致性检查通过")
    
    return len(encoding_issues) == 0

def generate_potential_issues_report(results):
    """生成潜在问题报告"""
    print("\n" + "="*60)
    print("📊 潜在问题检查报告")
    print("="*60)
    
    checks = [
        ("路径分隔符", results.get('path_separator', False)),
        ("文件权限", results.get('file_permissions', False)),
        ("数据库并发", results.get('db_concurrent', False)),
        ("配置文件健壮性", results.get('config_robustness', False)),
        ("临时文件清理", results.get('temp_cleanup', False)),
        ("编码一致性", results.get('encoding_consistency', False))
    ]
    
    passed = sum(1 for _, result in checks if result)
    total = len(checks)
    
    print(f"\n📋 检查结果:")
    for check_name, result in checks:
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
    
    success_rate = (passed / total) * 100
    print(f"\n🎯 总体评估:")
    print(f"  📊 通过率: {success_rate:.1f}% ({passed}/{total})")
    
    if success_rate >= 90:
        print(f"  🎉 优秀！系统非常稳定，潜在问题很少")
    elif success_rate >= 70:
        print(f"  ✅ 良好！系统基本稳定，有少量需要关注的问题")
    else:
        print(f"  ⚠️ 需要改进！发现多个潜在问题，建议及时修复")
    
    return success_rate >= 80

def main():
    """主函数"""
    print("🚀 开始潜在问题深度检查...")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = {}
    
    # 1. 检查路径分隔符问题
    results['path_separator'] = check_path_separator_issues()
    
    # 2. 检查文件权限
    results['file_permissions'] = check_file_permissions()
    
    # 3. 检查数据库并发访问
    results['db_concurrent'] = check_database_concurrent_access()
    
    # 4. 检查配置文件健壮性
    results['config_robustness'] = check_config_file_robustness()
    
    # 5. 检查临时文件清理
    results['temp_cleanup'] = check_temp_file_cleanup()
    
    # 6. 检查编码一致性
    results['encoding_consistency'] = check_encoding_consistency()
    
    # 7. 生成报告
    all_ok = generate_potential_issues_report(results)
    
    if all_ok:
        print("\n🎊 恭喜！系统非常健壮，潜在问题很少！")
    else:
        print("\n🔧 请根据上述报告修复发现的潜在问题")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
