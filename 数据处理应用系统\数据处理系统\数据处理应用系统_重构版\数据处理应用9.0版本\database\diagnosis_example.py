#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 数据库诊断功能使用示例

展示如何使用新增的数据库诊断功能：
1. 基本数据库诊断
2. 健康检查
3. 生成健康报告
4. 备份前后诊断
5. 恢复前后诊断

作者: Claude 4.0 sonnet
创建时间: 2025-01-22
"""

import os
import sys
import sqlite3
import tempfile
from pathlib import Path

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟依赖
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")
    def critical(self, msg): print(f"CRITICAL: {msg}")
    def debug(self, msg): print(f"DEBUG: {msg}")

class DatabaseError(Exception): pass
class BackupError(Exception): pass

def get_logger(name): return MockLogger()

# 模拟导入
sys.modules['utils.exceptions'] = type(sys)('utils.exceptions')
sys.modules['utils.exceptions'].DatabaseError = DatabaseError
sys.modules['utils.exceptions'].BackupError = BackupError
sys.modules['utils.logger'] = type(sys)('utils.logger')
sys.modules['utils.logger'].get_logger = get_logger

from backup_manager import DatabaseBackupManager


def create_sample_database(db_path):
    """创建示例数据库"""
    print("📝 创建示例数据库...")
    
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        
        # 创建用户表
        cursor.execute("""
            CREATE TABLE users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建订单表
        cursor.execute("""
            CREATE TABLE orders (
                id INTEGER PRIMARY KEY,
                user_id INTEGER,
                product_name TEXT NOT NULL,
                amount DECIMAL(10,2),
                order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        """)
        
        # 创建索引
        cursor.execute("CREATE INDEX idx_users_username ON users(username)")
        cursor.execute("CREATE INDEX idx_orders_user_id ON orders(user_id)")
        
        # 插入示例数据
        users = [
            ("alice", "<EMAIL>"),
            ("bob", "<EMAIL>"),
            ("charlie", "<EMAIL>")
        ]
        cursor.executemany("INSERT INTO users (username, email) VALUES (?, ?)", users)
        
        orders = [
            (1, "笔记本电脑", 5999.99),
            (1, "鼠标", 99.99),
            (2, "键盘", 299.99),
            (3, "显示器", 1999.99)
        ]
        cursor.executemany("INSERT INTO orders (user_id, product_name, amount) VALUES (?, ?, ?)", orders)
        
        conn.commit()
    
    print("✅ 示例数据库创建完成")


def example_basic_diagnosis():
    """示例1：基本数据库诊断"""
    print("\n" + "="*60)
    print("📋 示例1：基本数据库诊断")
    print("="*60)
    
    # 创建临时数据库
    temp_dir = Path(tempfile.mkdtemp(prefix="diagnosis_example_"))
    db_path = temp_dir / "example.db"
    
    try:
        # 创建示例数据库
        create_sample_database(db_path)
        
        # 初始化备份管理器
        backup_manager = DatabaseBackupManager(str(db_path))
        
        # 执行不同级别的诊断
        print("\n🔍 执行快速诊断...")
        quick_diagnosis = backup_manager.diagnose_database(db_path, "quick")
        print(f"快速诊断结果: {quick_diagnosis['overall_status']}")
        
        print("\n🔍 执行标准诊断...")
        standard_diagnosis = backup_manager.diagnose_database(db_path, "standard")
        print(f"标准诊断结果: {standard_diagnosis['overall_status']}")
        print(f"检查项数量: {len(standard_diagnosis.get('checks', {}))}")
        
        print("\n🔍 执行完整诊断...")
        full_diagnosis = backup_manager.diagnose_database(db_path, "full")
        print(f"完整诊断结果: {full_diagnosis['overall_status']}")
        print(f"检查项数量: {len(full_diagnosis.get('checks', {}))}")
        
        # 显示详细信息
        if full_diagnosis.get("checks", {}).get("schema"):
            schema_info = full_diagnosis["checks"]["schema"]["schema"]
            print(f"数据库包含: {schema_info['table_count']} 个表, {schema_info['index_count']} 个索引")
        
    finally:
        # 清理
        import shutil
        shutil.rmtree(temp_dir)


def example_health_check_and_report():
    """示例2：健康检查和报告生成"""
    print("\n" + "="*60)
    print("📊 示例2：健康检查和报告生成")
    print("="*60)
    
    # 创建临时数据库
    temp_dir = Path(tempfile.mkdtemp(prefix="health_example_"))
    db_path = temp_dir / "example.db"
    
    try:
        # 创建示例数据库
        create_sample_database(db_path)
        
        # 初始化备份管理器
        backup_manager = DatabaseBackupManager(str(db_path))
        
        # 执行健康检查
        print("\n🏥 执行数据库健康检查...")
        health_result = backup_manager.run_database_health_check("full")
        
        print(f"健康检查状态: {health_result['overall_status']}")
        
        # 生成健康报告
        print("\n📄 生成健康报告...")
        report = backup_manager.generate_health_report("full", save_to_file=True)
        
        # 显示报告摘要
        lines = report.split('\n')
        for i, line in enumerate(lines):
            if i < 20:  # 只显示前20行
                print(line)
            elif i == 20:
                print("... (报告内容较长，已截断)")
                break
        
        print(f"\n✅ 完整报告已保存到备份目录")
        
    finally:
        # 清理
        import shutil
        shutil.rmtree(temp_dir)


def example_backup_with_diagnosis():
    """示例3：带诊断的备份操作"""
    print("\n" + "="*60)
    print("💾 示例3：带诊断的备份操作")
    print("="*60)
    
    # 创建临时数据库
    temp_dir = Path(tempfile.mkdtemp(prefix="backup_example_"))
    db_path = temp_dir / "example.db"
    
    try:
        # 创建示例数据库
        create_sample_database(db_path)
        
        # 初始化备份管理器
        backup_manager = DatabaseBackupManager(str(db_path))
        
        print("\n💾 执行带诊断的备份...")
        print("注意：备份过程中会自动执行备份前和备份后诊断")
        
        # 执行备份（会自动包含诊断）
        backup_file = backup_manager.create_backup("示例备份")
        
        if backup_file:
            print(f"✅ 备份成功: {os.path.basename(backup_file)}")
            
            # 手动验证备份文件
            print("\n🔍 手动验证备份文件...")
            backup_diagnosis = backup_manager.diagnose_database(Path(backup_file), "standard")
            print(f"备份文件诊断结果: {backup_diagnosis['overall_status']}")
        else:
            print("❌ 备份失败")
        
    finally:
        # 清理
        import shutil
        shutil.rmtree(temp_dir)


def example_restore_with_diagnosis():
    """示例4：带诊断的恢复操作"""
    print("\n" + "="*60)
    print("🔄 示例4：带诊断的恢复操作")
    print("="*60)
    
    # 创建临时数据库
    temp_dir = Path(tempfile.mkdtemp(prefix="restore_example_"))
    db_path = temp_dir / "example.db"
    
    try:
        # 创建示例数据库
        create_sample_database(db_path)
        
        # 初始化备份管理器
        backup_manager = DatabaseBackupManager(str(db_path))
        
        # 创建备份
        print("\n💾 创建初始备份...")
        backup_file = backup_manager.create_backup("恢复示例备份")
        
        if backup_file:
            print(f"✅ 备份创建成功: {os.path.basename(backup_file)}")
            
            # 修改数据库
            print("\n✏️ 修改数据库数据...")
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("INSERT INTO users (username, email) VALUES (?, ?)", 
                             ("testuser", "<EMAIL>"))
                cursor.execute("DELETE FROM orders WHERE id = 1")
                conn.commit()
            
            print("✅ 数据库已修改")
            
            # 执行恢复（会自动包含诊断）
            print("\n🔄 执行带诊断的恢复...")
            print("注意：恢复过程中会自动执行恢复前和恢复后诊断")
            
            restore_result = backup_manager.restore_from_backup(backup_file, None)
            
            if restore_result:
                print("✅ 恢复成功")
                
                # 验证恢复结果
                with sqlite3.connect(db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'testuser'")
                    test_user_count = cursor.fetchone()[0]
                    cursor.execute("SELECT COUNT(*) FROM orders")
                    order_count = cursor.fetchone()[0]
                
                print(f"验证结果: 测试用户数量 = {test_user_count}, 订单数量 = {order_count}")
                
                if test_user_count == 0 and order_count == 4:
                    print("✅ 数据恢复正确")
                else:
                    print("❌ 数据恢复可能有问题")
            else:
                print("❌ 恢复失败")
        else:
            print("❌ 备份创建失败")
        
    finally:
        # 清理
        import shutil
        shutil.rmtree(temp_dir)


def main():
    """主函数"""
    print("🚀 数据库诊断功能使用示例")
    print("本示例展示如何使用新增的数据库诊断功能")
    
    # 运行各个示例
    example_basic_diagnosis()
    example_health_check_and_report()
    example_backup_with_diagnosis()
    example_restore_with_diagnosis()
    
    print("\n" + "="*60)
    print("🎉 所有示例运行完成！")
    print("="*60)
    print("\n📚 数据库诊断功能总结：")
    print("✅ diagnose_database() - 执行数据库诊断")
    print("✅ run_database_health_check() - 运行健康检查")
    print("✅ generate_health_report() - 生成健康报告")
    print("✅ 备份操作自动包含诊断")
    print("✅ 恢复操作自动包含诊断")
    print("\n💡 诊断级别：")
    print("  - quick: 快速检查")
    print("  - standard: 标准检查")
    print("  - full: 完整检查")
    print("  - deep: 深度检查")


if __name__ == "__main__":
    main()
