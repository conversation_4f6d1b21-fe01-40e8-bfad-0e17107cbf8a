#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 优化备份脚本测试工具

测试优化后的备份功能，验证问题修复效果：
1. 时间戳准确性测试
2. 文件命名人性化测试  
3. 恢复操作性能测试
4. 文件锁管理测试

作者: Claude 4.0 sonnet
创建时间: 2025-01-22
"""

import os
import sys
import time
import sqlite3
import tempfile
import threading
from datetime import datetime
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from optimized_backup_script import OptimizedBackupManager


class BackupTestSuite:
    """备份功能测试套件"""
    
    def __init__(self):
        self.test_results = []
        self.temp_dir = None
        self.test_db_path = None
        self.backup_manager = None
    
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 创建临时目录
        self.temp_dir = Path(tempfile.mkdtemp(prefix="backup_test_"))
        self.test_db_path = self.temp_dir / "test_database.db"
        
        # 创建测试数据库
        self._create_test_database()
        
        # 初始化备份管理器
        self.backup_manager = OptimizedBackupManager(str(self.test_db_path))
        
        print(f"✅ 测试环境已设置: {self.temp_dir}")
    
    def _create_test_database(self):
        """创建测试数据库"""
        with sqlite3.connect(self.test_db_path) as conn:
            cursor = conn.cursor()
            
            # 创建测试表
            cursor.execute("""
                CREATE TABLE test_data (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 插入测试数据
            test_data = [
                ("测试数据1",), ("测试数据2",), ("测试数据3",)
            ]
            cursor.executemany("INSERT INTO test_data (name) VALUES (?)", test_data)
            conn.commit()
    
    def test_timestamp_accuracy(self):
        """🔧 测试1：时间戳准确性"""
        print("\n📅 测试1：时间戳准确性")
        
        try:
            # 记录操作开始时间
            operation_start = datetime.now()
            
            # 创建备份
            operation_id = f"timestamp_test_{int(time.time())}"
            backup_file = self.backup_manager.create_backup("时间戳测试", operation_id)
            
            # 记录操作结束时间
            operation_end = datetime.now()
            
            if backup_file:
                # 从文件名提取时间戳
                filename = Path(backup_file).name
                # 格式：backup_时间戳测试_YYYYMMDD_HHMMSS.db
                timestamp_part = filename.split('_')[-2] + '_' + filename.split('_')[-1].replace('.db', '')
                
                try:
                    backup_time = datetime.strptime(timestamp_part, '%Y%m%d_%H%M%S')
                    
                    # 检查时间戳是否在操作时间范围内（允许1分钟误差）
                    time_diff = abs((backup_time - operation_start).total_seconds())
                    
                    if time_diff <= 60:  # 1分钟内
                        print(f"✅ 时间戳准确: {timestamp_part}")
                        print(f"   操作时间: {operation_start.strftime('%Y%m%d_%H%M%S')}")
                        print(f"   备份时间: {backup_time.strftime('%Y%m%d_%H%M%S')}")
                        print(f"   时间差: {time_diff:.1f}秒")
                        self.test_results.append(("时间戳准确性", True, f"时间差{time_diff:.1f}秒"))
                    else:
                        print(f"❌ 时间戳不准确: 时间差{time_diff:.1f}秒")
                        self.test_results.append(("时间戳准确性", False, f"时间差{time_diff:.1f}秒"))
                        
                except ValueError as e:
                    print(f"❌ 时间戳格式错误: {e}")
                    self.test_results.append(("时间戳准确性", False, f"格式错误: {e}"))
            else:
                print("❌ 备份创建失败")
                self.test_results.append(("时间戳准确性", False, "备份创建失败"))
                
        except Exception as e:
            print(f"❌ 时间戳测试失败: {e}")
            self.test_results.append(("时间戳准确性", False, str(e)))
    
    def test_filename_readability(self):
        """🔧 测试2：文件命名人性化"""
        print("\n📝 测试2：文件命名人性化")
        
        test_cases = [
            "手动备份",
            "导入前备份", 
            "退款处理前",
            "数据修复备份",
            "Test Backup with Spaces"
        ]
        
        try:
            for operation_name in test_cases:
                backup_file = self.backup_manager.create_backup(operation_name)
                
                if backup_file:
                    filename = Path(backup_file).name
                    print(f"✅ {operation_name} -> {filename}")
                    
                    # 检查文件名是否人性化
                    is_readable = self._check_filename_readability(filename)
                    self.test_results.append((f"文件命名-{operation_name}", is_readable, filename))
                else:
                    print(f"❌ {operation_name} -> 备份失败")
                    self.test_results.append((f"文件命名-{operation_name}", False, "备份失败"))
                    
        except Exception as e:
            print(f"❌ 文件命名测试失败: {e}")
            self.test_results.append(("文件命名人性化", False, str(e)))
    
    def _check_filename_readability(self, filename: str) -> bool:
        """检查文件名是否人性化"""
        # 检查是否包含不必要的技术细节
        technical_details = ['pid', '_r', 'random', 'process']
        has_technical = any(detail in filename.lower() for detail in technical_details)
        
        # 检查是否有合理的格式
        parts = filename.replace('.db', '').split('_')
        has_reasonable_format = len(parts) >= 3 and len(parts) <= 5
        
        return not has_technical and has_reasonable_format
    
    def test_restore_performance(self):
        """🔧 测试3：恢复操作性能"""
        print("\n⚡ 测试3：恢复操作性能")
        
        try:
            # 先创建一个备份
            backup_file = self.backup_manager.create_backup("性能测试备份")
            
            if not backup_file:
                print("❌ 无法创建测试备份")
                self.test_results.append(("恢复性能", False, "无法创建测试备份"))
                return
            
            # 测试恢复性能
            start_time = time.time()
            
            def auto_confirm(message):
                return True  # 自动确认恢复
            
            success = self.backup_manager.restore_backup(backup_file, auto_confirm)
            
            end_time = time.time()
            restore_time = end_time - start_time
            
            if success:
                print(f"✅ 恢复成功，耗时: {restore_time:.2f}秒")
                
                # 检查性能是否合理（应该在10秒内完成）
                is_fast = restore_time <= 10.0
                self.test_results.append(("恢复性能", is_fast, f"{restore_time:.2f}秒"))
                
                if not is_fast:
                    print(f"⚠️ 恢复时间较长: {restore_time:.2f}秒")
            else:
                print("❌ 恢复失败")
                self.test_results.append(("恢复性能", False, "恢复失败"))
                
        except Exception as e:
            print(f"❌ 恢复性能测试失败: {e}")
            self.test_results.append(("恢复性能", False, str(e)))
    
    def test_concurrent_backup(self):
        """🔧 测试4：并发备份（文件锁管理）"""
        print("\n🔒 测试4：并发备份测试")
        
        try:
            results = []
            threads = []
            
            def create_backup_thread(thread_id):
                try:
                    backup_file = self.backup_manager.create_backup(f"并发测试{thread_id}")
                    results.append((thread_id, backup_file is not None, backup_file))
                except Exception as e:
                    results.append((thread_id, False, str(e)))
            
            # 启动多个并发备份线程
            for i in range(3):
                thread = threading.Thread(target=create_backup_thread, args=(i+1,))
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=30)  # 30秒超时
            
            # 分析结果
            successful_backups = sum(1 for _, success, _ in results if success)
            
            print(f"✅ 并发备份完成: {successful_backups}/{len(results)} 成功")
            
            for thread_id, success, result in results:
                if success:
                    filename = Path(result).name if result else "未知"
                    print(f"   线程{thread_id}: ✅ {filename}")
                else:
                    print(f"   线程{thread_id}: ❌ {result}")
            
            # 所有备份都应该成功（锁机制正常工作）
            all_success = successful_backups == len(results)
            self.test_results.append(("并发备份", all_success, f"{successful_backups}/{len(results)}成功"))
            
        except Exception as e:
            print(f"❌ 并发备份测试失败: {e}")
            self.test_results.append(("并发备份", False, str(e)))
    
    def test_backup_list_functionality(self):
        """🔧 测试5：备份列表功能"""
        print("\n📋 测试5：备份列表功能")
        
        try:
            # 创建几个测试备份
            test_backups = ["列表测试1", "列表测试2", "列表测试3"]
            created_backups = []
            
            for backup_name in test_backups:
                backup_file = self.backup_manager.create_backup(backup_name)
                if backup_file:
                    created_backups.append(backup_file)
                time.sleep(1)  # 确保时间戳不同
            
            # 获取备份列表
            backup_list = self.backup_manager.list_backups()
            
            print(f"✅ 找到 {len(backup_list)} 个备份文件:")
            for backup in backup_list:
                size_mb = backup['size'] / (1024 * 1024)
                print(f"   {backup['filename']} ({size_mb:.1f} MB) - {backup['created']}")
            
            # 检查列表功能是否正常
            list_works = len(backup_list) >= len(created_backups)
            self.test_results.append(("备份列表", list_works, f"找到{len(backup_list)}个备份"))
            
        except Exception as e:
            print(f"❌ 备份列表测试失败: {e}")
            self.test_results.append(("备份列表", False, str(e)))
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        try:
            if self.temp_dir and self.temp_dir.exists():
                import shutil
                shutil.rmtree(self.temp_dir)
                print(f"✅ 已清理测试目录: {self.temp_dir}")
        except Exception as e:
            print(f"⚠️ 清理测试环境失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始备份功能优化测试")
        print("=" * 50)
        
        try:
            self.setup_test_environment()
            
            # 运行各项测试
            self.test_timestamp_accuracy()
            self.test_filename_readability()
            self.test_restore_performance()
            self.test_concurrent_backup()
            self.test_backup_list_functionality()
            
            # 显示测试结果
            self.show_test_results()
            
        finally:
            self.cleanup_test_environment()
    
    def show_test_results(self):
        """显示测试结果"""
        print("\n" + "=" * 50)
        print("📊 测试结果汇总")
        print("=" * 50)
        
        passed = 0
        failed = 0
        
        for test_name, success, details in self.test_results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{status} {test_name}: {details}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print("=" * 50)
        print(f"总计: {passed + failed} 项测试")
        print(f"✅ 通过: {passed} 项")
        print(f"❌ 失败: {failed} 项")
        
        if failed == 0:
            print("\n🎉 所有测试通过！备份功能优化成功！")
        else:
            print(f"\n⚠️ 有 {failed} 项测试失败，需要进一步优化")


def main():
    """主函数"""
    test_suite = BackupTestSuite()
    test_suite.run_all_tests()


if __name__ == "__main__":
    main()
