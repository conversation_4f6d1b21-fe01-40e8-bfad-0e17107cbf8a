#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码逻辑检查脚本 - 全面检查代码逻辑和调用的一致性
"""

import os
import re
import sys

def check_parameter_consistency():
    """检查参数一致性"""
    print("🔧 检查参数一致性")
    print("=" * 50)
    
    # 检查主应用程序调用导入脚本的参数
    main_app_path = "../01_主程序/数据处理与导入应用_完整版.py"
    import_script_path = "data_import_optimized.py"
    
    issues = []
    
    try:
        # 检查主应用程序中的参数使用
        with open(main_app_path, 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        # 查找命令构建部分
        cmd_patterns = [
            r"'--file',\s*file_path",
            r"'--platform',\s*platform_type",
            r"'--file1',\s*file1_path",
            r"'--file2',\s*file2_path",
        ]
        
        found_params = []
        for pattern in cmd_patterns:
            matches = re.findall(pattern, main_content)
            if matches:
                param_name = pattern.split("'")[1]
                found_params.append(param_name)
        
        print(f"主应用程序使用的参数: {found_params}")
        
        # 检查导入脚本中的参数定义
        with open(import_script_path, 'r', encoding='utf-8') as f:
            import_content = f.read()
        
        # 查找参数定义
        param_definitions = re.findall(r"parser\.add_argument\('([^']+)'", import_content)
        param_definitions.extend(re.findall(r"parser\.add_argument\('(--[^']+)'", import_content))
        
        print(f"导入脚本定义的参数: {param_definitions}")
        
        # 检查一致性
        for param in found_params:
            if param not in param_definitions:
                issues.append(f"参数不匹配: 主应用程序使用 '{param}' 但导入脚本未定义")
        
        if not issues:
            print("✅ 参数一致性检查通过")
        else:
            for issue in issues:
                print(f"❌ {issue}")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ 参数一致性检查失败: {e}")
        return False

def check_database_schema_consistency():
    """检查数据库表结构一致性"""
    print("\n🔧 检查数据库表结构一致性")
    print("=" * 50)
    
    models_path = "../database/models.py"
    import_script_path = "data_import_optimized.py"
    
    issues = []
    
    try:
        # 检查models.py中的表结构
        with open(models_path, 'r', encoding='utf-8') as f:
            models_content = f.read()
        
        # 提取BASE_TABLE_SCHEMA
        base_schema_match = re.search(r'BASE_TABLE_SCHEMA = """(.*?)"""', models_content, re.DOTALL)
        if base_schema_match:
            base_schema = base_schema_match.group(1)
            columns = re.findall(r'(\w+)\s+\w+', base_schema)
            print(f"数据库表结构定义的列: {columns}")
        else:
            issues.append("无法找到BASE_TABLE_SCHEMA定义")
        
        # 检查导入脚本中使用的列名
        with open(import_script_path, 'r', encoding='utf-8') as f:
            import_content = f.read()
        
        # 查找列名使用
        column_patterns = [
            r"'(\w+)':\s*row\[",
            r"df\['(\w+)'\]",
            r"'(\w+)'\s*in\s*df\.columns",
        ]
        
        used_columns = set()
        for pattern in column_patterns:
            matches = re.findall(pattern, import_content)
            used_columns.update(matches)
        
        print(f"导入脚本使用的列: {sorted(used_columns)}")
        
        # 检查一致性
        if base_schema_match:
            defined_columns = set(columns)
            undefined_columns = used_columns - defined_columns
            if undefined_columns:
                issues.append(f"使用了未定义的列: {undefined_columns}")
        
        if not issues:
            print("✅ 数据库表结构一致性检查通过")
        else:
            for issue in issues:
                print(f"❌ {issue}")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ 数据库表结构一致性检查失败: {e}")
        return False

def check_method_call_chains():
    """检查方法调用链的完整性"""
    print("\n🔧 检查方法调用链完整性")
    print("=" * 50)
    
    main_app_path = "../01_主程序/数据处理与导入应用_完整版.py"
    
    issues = []
    
    try:
        with open(main_app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键方法的定义和调用
        critical_methods = [
            '_register_components',
            '_run_processing_modern',
            '_on_success_modern',
            '_on_error_modern',
        ]
        
        for method in critical_methods:
            # 检查方法定义
            definition_pattern = rf"def {method}\("
            definitions = re.findall(definition_pattern, content)
            
            # 检查方法调用
            call_pattern = rf"{method}\("
            calls = re.findall(call_pattern, content)
            
            print(f"方法 {method}: 定义 {len(definitions)} 次, 调用 {len(calls)} 次")
            
            if len(definitions) == 0:
                issues.append(f"方法 {method} 未定义")
            elif len(definitions) > 1:
                issues.append(f"方法 {method} 重复定义 {len(definitions)} 次")
            
            if len(calls) == 0:
                issues.append(f"方法 {method} 定义但未被调用")
        
        if not issues:
            print("✅ 方法调用链检查通过")
        else:
            for issue in issues:
                print(f"❌ {issue}")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ 方法调用链检查失败: {e}")
        return False

def check_exception_handling():
    """检查异常处理的完整性"""
    print("\n🔧 检查异常处理完整性")
    print("=" * 50)
    
    import_script_path = "data_import_optimized.py"
    
    issues = []
    
    try:
        with open(import_script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查try-except块
        try_blocks = re.findall(r'try:', content)
        except_blocks = re.findall(r'except.*?:', content)
        
        print(f"Try块数量: {len(try_blocks)}")
        print(f"Except块数量: {len(except_blocks)}")
        
        if len(try_blocks) != len(except_blocks):
            issues.append(f"Try-Except块数量不匹配: {len(try_blocks)} try vs {len(except_blocks)} except")
        
        # 检查数据库操作的异常处理
        db_operations = re.findall(r'with get_connection\(\)', content)
        print(f"数据库操作数量: {len(db_operations)}")
        
        # 检查关键方法的异常处理
        critical_methods = ['process_file', '_insert_to_table', 'load_data']
        for method in critical_methods:
            method_content = re.search(rf'def {method}.*?(?=def|\Z)', content, re.DOTALL)
            if method_content:
                method_text = method_content.group(0)
                has_try = 'try:' in method_text
                has_except = 'except' in method_text
                
                if not (has_try and has_except):
                    issues.append(f"方法 {method} 缺少异常处理")
                else:
                    print(f"✅ 方法 {method} 有异常处理")
        
        if not issues:
            print("✅ 异常处理检查通过")
        else:
            for issue in issues:
                print(f"❌ {issue}")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ 异常处理检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 代码逻辑和调用一致性检查")
    print("=" * 60)
    
    # 执行所有检查
    checks = [
        ("参数一致性", check_parameter_consistency),
        ("数据库表结构一致性", check_database_schema_consistency),
        ("方法调用链完整性", check_method_call_chains),
        ("异常处理完整性", check_exception_handling),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} 检查失败: {e}")
            results.append((check_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 检查结果总结:")
    
    passed = 0
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {check_name}: {status}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n总体结果: {passed}/{total} 检查通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有代码逻辑检查通过！")
        print("\n✅ 代码质量评估:")
        print("- 参数调用一致性良好")
        print("- 数据库表结构统一")
        print("- 方法调用链完整")
        print("- 异常处理全面")
        print("- 代码逻辑健壮")
    else:
        print("⚠️ 发现一些代码逻辑问题需要修复")
        print("\n建议:")
        print("- 修复参数不匹配问题")
        print("- 统一数据库表结构")
        print("- 完善异常处理")
        print("- 检查方法调用关系")

if __name__ == "__main__":
    main()
