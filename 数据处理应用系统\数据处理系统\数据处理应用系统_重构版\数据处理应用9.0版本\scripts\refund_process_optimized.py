# -*- coding: utf-8 -*-
"""
优化版退款处理脚本
使用重构后的基础模块，提供更好的性能、错误处理和日志记录

🔧 修复内容：
1. 修正Transaction ID匹配逻辑 - 有Transaction ID时必须使用Transaction ID匹配
2. 区分"不需要退款"和"匹配失败"两种情况
3. 修正数据质量预检算法，提供更准确的成功率预测
4. 优化日志记录和用户建议系统
5. 增强错误处理和数据验证
6. 添加智能备份功能
7. 建立标准化的日志分类和级别控制体系

📋 日志分类规范：
- INFO级别：用户需要的重要业务信息（UI显示）
- WARNING级别：需要用户关注的问题（UI显示）
- ERROR级别：严重错误，需要用户处理（UI显示）
- DEBUG级别：技术实现细节（仅文件记录）

详细规范请参考：docs/日志分类规范.md
"""

import os
import sys
import argparse
import pandas as pd
import sqlite3
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# 添加父目录到路径，以便导入重构的模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger
from utils.exceptions import *
from utils.validators import input_validator
from database.connection_pool import get_connection, reinitialize_connection_pool

# 🔧 导入智能备份管理器
try:
    from database.smart_backup_manager import get_smart_backup_manager
except ImportError:
    # 创建空的备份管理器以保持兼容性
    class DummyBackupManager:
        def __init__(self, db_path):
            self.db_path = db_path
        def create_backup(self, operation_name):
            return None

    def get_smart_backup_manager(db_path):
        return DummyBackupManager(db_path)

# 🔒 导入数据库安全工具
try:
    from database.security_utils import (
        SQLSecurityValidator,
        SafeSQLBuilder,
        get_safe_sql_builder,
        DatabaseSecurityError,
        validate_database_path
    )
except ImportError:
    # 创建空的安全工具类以保持兼容性
    class SQLSecurityValidator:
        @classmethod
        def validate_table_name(cls, table_name): return True
        @classmethod
        def sanitize_table_name(cls, table_name): return f'"{table_name}"'

    class SafeSQLBuilder:
        def __init__(self): pass

    def get_safe_sql_builder(): return SafeSQLBuilder()

    class DatabaseSecurityError(Exception): pass

    def validate_database_path(path): return True


class RefundProcessor:
    """
    优化版退款处理器

    🔧 修复内容：
    1. 修正Transaction ID匹配逻辑
    2. 区分"不需要退款"和"匹配失败"
    3. 修正数据质量预检算法
    4. 优化日志记录和建议系统
    5. 增强错误处理和数据验证
    6. 添加智能备份功能
    7. 建立标准化的日志分类和级别控制体系

    📋 日志分类规范应用：
    - INFO级别：用户关心的业务信息（UI显示）
    - WARNING级别：需要用户注意的问题（UI显示）
    - ERROR级别：严重错误（UI显示）
    - DEBUG级别：技术实现细节（仅文件记录）
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        初始化退款处理器
        
        Args:
            db_path: 数据库路径，如果为None则使用配置中的路径
        """
        self.logger = get_logger('refund_process')

        # 设置数据库路径
        if db_path:
            self.db_path = db_path
        else:
            # 从配置管理器获取数据库路径
            try:
                from utils.config_manager import config_manager
                self.db_path = config_manager.get_db_path()
            except ImportError:
                # 如果配置管理器不可用，使用默认路径
                self.db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "database", "sales_reports.db")

        self.batch_size = 1000
        
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        # 确保连接池使用正确的数据库路径
        reinitialize_connection_pool(self.db_path)

        # 🔧 新增：初始化智能备份管理器
        try:
            self.backup_manager = get_smart_backup_manager(self.db_path)
            self.logger.info("✅ 智能备份管理器初始化成功")
        except Exception as e:
            self.logger.warning(f"⚠️ 备份管理器初始化失败: {e}")
            self.backup_manager = None

        self.logger.info(f"退款处理器已初始化，数据库路径: {self.db_path}")
    
    def validate_file(self, file_path: str) -> bool:
        """
        🔧 修复：验证文件 - 智能识别退款工作表

        Args:
            file_path: 文件路径

        Returns:
            是否验证通过
        """
        is_valid, error_msg = input_validator.validate_file_path(file_path)
        if not is_valid:
            raise FileValidationError(file_path, error_msg)

        # 🔧 修复：智能检测退款工作表，不再硬编码要求REFUND_LIST
        refund_sheet = self._detect_refund_sheet(file_path)
        if not refund_sheet:
            raise FileValidationError(file_path, "未找到包含退款数据的工作表")

        self.logger.info(f"检测到退款工作表: {refund_sheet}")
        return True

    def _detect_refund_sheet(self, file_path: str) -> str:
        """
        🔧 修复：智能检测退款工作表

        Args:
            file_path: Excel文件路径

        Returns:
            退款工作表名称，如果找不到则返回None
        """
        try:
            # 获取所有工作表名称
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names

            # 🔧 日志级别优化：工作表列表详情改为debug级别（技术细节）
            self.logger.debug(f"文件包含的工作表: {sheet_names}")

            # 退款工作表的可能名称（按优先级排序）
            refund_keywords = [
                'REFUND_LIST',      # 标准名称
                'REFUND LIST',      # 带空格
                'REFUNDLIST',       # 无分隔符
                'REFUND',           # 简化名称
                'SETTLEMENT_REPORT', # 结算报告
                'SETTLEMENT REPORT', # 带空格的结算报告
                'SETTLEMENT',       # 简化结算
                'REPORT',           # 报告
                'LIST',             # 列表
                'DATA',             # 数据
                'SHEET1',           # 默认工作表
                'SHEET 1'           # 带空格的默认工作表
            ]

            # 按优先级查找匹配的工作表
            for keyword in refund_keywords:
                for sheet_name in sheet_names:
                    if keyword.upper() in sheet_name.upper():
                        self.logger.info(f"找到匹配的退款工作表: {sheet_name} (匹配关键词: {keyword})")
                        return sheet_name

            # 如果没有找到匹配的关键词，使用第一个工作表
            if sheet_names:
                default_sheet = sheet_names[0]
                self.logger.warning(f"未找到明确的退款工作表，使用第一个工作表: {default_sheet}")
                return default_sheet

            # 如果没有工作表，返回None
            self.logger.error("文件中没有找到任何工作表")
            return None

        except Exception as e:
            self.logger.error(f"检测退款工作表时出错: {e}")
            return None
    
    def load_refund_data(self, file_path: str) -> pd.DataFrame:
        """
        加载退款数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            退款数据DataFrame
        """
        try:
            # 🔧 修复：使用智能检测的退款工作表
            refund_sheet = self._detect_refund_sheet(file_path)
            if not refund_sheet:
                raise DataProcessingError("未找到退款工作表", file_path=file_path, stage="退款数据加载")

            # 读取检测到的退款工作表
            df = pd.read_excel(file_path, sheet_name=refund_sheet, engine='openpyxl')
            self.logger.info(f"成功读取退款文件: {file_path}, 工作表: {refund_sheet}, 数据行数: {len(df)}")

            # 🔧 修复：检查退款文件列名，支持Transaction ID优先匹配
            required_columns = ['Transaction Date', 'Order ID', 'Refund']
            optional_columns = ['Transaction ID']  # Transaction ID是可选的，用于优先匹配

            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                self.logger.error(f"❌ 缺少必需的列: {missing_columns}")
                self.logger.error(f"📋 可用列: {list(df.columns)}")
                raise DataProcessingError(f"缺少必需的列: {missing_columns}", file_path=file_path, stage="退款数据验证")

            # 检查是否有Transaction ID列
            has_transaction_id = 'Transaction ID' in df.columns
            if has_transaction_id:
                self.logger.info("✅ 检测到Transaction ID列，将优先使用Transaction ID匹配")
            else:
                self.logger.info("⚠️ 未检测到Transaction ID列，将使用Order ID匹配")

            # 🔧 日志级别优化：技术细节改为debug级别
            self.logger.debug(f"✅ 找到所有必需列: {required_columns}")

            # 数据清洗
            df = self._clean_data(df)

            self.logger.info(f"退款数据验证和清洗完成，有效数据行数: {len(df)}")
            return df
            
        except Exception as e:
            if isinstance(e, (DataProcessingError, FileValidationError)):
                raise
            raise DataProcessingError(f"加载退款数据失败: {e}", file_path=file_path, stage="数据加载")

    # 🔧 修复：删除复杂的列名检测函数，使用简单直接的列名检查
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        🔧 增强：清洗和验证数据

        Args:
            df: 原始DataFrame

        Returns:
            清洗后的DataFrame

        Raises:
            DataProcessingError: 数据验证失败时抛出
        """
        original_count = len(df)
        self.logger.info(f"开始数据清洗，原始记录数: {original_count}")

        # 删除完全空白的行
        df = df.dropna(how='all')
        self.logger.debug(f"删除空白行后记录数: {len(df)}")

        # 🔧 增强：数据类型验证和转换
        try:
            # 验证和清理Transaction Date
            if 'Transaction Date' in df.columns:
                df['Transaction Date'] = pd.to_datetime(df['Transaction Date'], errors='coerce')
                invalid_dates = df['Transaction Date'].isna().sum()
                if invalid_dates > 0:
                    self.logger.warning(f"发现 {invalid_dates} 个无效的Transaction Date")

            # 验证和清理Refund金额
            if 'Refund' in df.columns:
                df['Refund'] = pd.to_numeric(df['Refund'], errors='coerce')
                invalid_amounts = df['Refund'].isna().sum()
                if invalid_amounts > 0:
                    self.logger.warning(f"发现 {invalid_amounts} 个无效的Refund金额")
                    # 移除无效金额的记录
                    df = df.dropna(subset=['Refund'])

                # 验证金额范围
                negative_amounts = (df['Refund'] < 0).sum()
                if negative_amounts > 0:
                    self.logger.warning(f"发现 {negative_amounts} 个负数退款金额，将被移除")
                    df = df[df['Refund'] >= 0]

                zero_amounts = (df['Refund'] == 0).sum()
                if zero_amounts > 0:
                    self.logger.warning(f"发现 {zero_amounts} 个零金额退款，将被移除")
                    df = df[df['Refund'] > 0]

        except Exception as e:
            raise DataProcessingError(f"数据类型验证失败: {e}", stage="数据清洗")

        # 清理字符串列的空白字符
        for col in df.select_dtypes(include=['object']).columns:
            df[col] = df[col].astype(str).str.strip()

        # 🔧 增强：Order ID验证
        if 'Order ID' in df.columns:
            # 移除Order ID为空的行
            empty_order_ids = (df['Order ID'] == '').sum()
            if empty_order_ids > 0:
                self.logger.warning(f"发现 {empty_order_ids} 个空的Order ID，将被移除")
                df = df[df['Order ID'] != '']

            # 验证Order ID格式（基本长度检查）
            short_order_ids = (df['Order ID'].str.len() < 3).sum()
            if short_order_ids > 0:
                self.logger.warning(f"发现 {short_order_ids} 个过短的Order ID（<3字符）")

        # 🔧 增强：Transaction ID验证（如果存在）
        if 'Transaction ID' in df.columns:
            # 清理Transaction ID
            df['Transaction ID'] = df['Transaction ID'].astype(str).str.strip()

            # 移除明显无效的Transaction ID
            invalid_transaction_ids = df['Transaction ID'].isin(['', 'nan', 'None', '0']).sum()
            if invalid_transaction_ids > 0:
                self.logger.info(f"发现 {invalid_transaction_ids} 个无效的Transaction ID，将保留但不用于匹配")

        # 最终验证
        final_count = len(df)
        removed_count = original_count - final_count

        if removed_count > 0:
            self.logger.info(f"数据清洗完成，移除 {removed_count} 条无效记录，剩余 {final_count} 条有效记录")
        else:
            self.logger.info(f"数据清洗完成，所有 {final_count} 条记录都有效")

        # 检查是否还有有效数据
        if final_count == 0:
            raise DataProcessingError("数据清洗后没有有效记录", stage="数据清洗")

        return df

    def _determine_matching_strategy(self, refund_df: pd.DataFrame, sales_df: pd.DataFrame) -> str:
        """
        🔧 修正：确定匹配策略 - 支持混合策略

        Args:
            refund_df: 退款数据
            sales_df: 销售数据

        Returns:
            匹配策略:
            - 'transaction_id': 所有记录都有有效Transaction ID
            - 'order_id': 所有记录都使用Order ID匹配
            - 'mixed': 部分记录有Transaction ID，部分记录使用Order ID
        """
        has_refund_transaction_id = 'Transaction ID' in refund_df.columns
        has_sales_transaction_num = 'Transaction_Num' in sales_df.columns

        # 🔧 修复：检查数据是否为空
        if refund_df.empty or sales_df.empty:
            self.logger.info("数据为空，使用Order ID匹配")
            return 'order_id'

        if has_refund_transaction_id and has_sales_transaction_num:
            # 检查Transaction ID的有效性
            valid_transaction_ids = refund_df['Transaction ID'].notna().sum()
            total_records = len(refund_df)

            if valid_transaction_ids > 0:
                # 🔧 日志级别优化：简化策略分析显示，保留核心信息
                invalid_transaction_ids = total_records - valid_transaction_ids
                # 详细分析改为debug级别
                self.logger.debug(f"📊 Transaction ID分析: {valid_transaction_ids}/{total_records} 条记录有有效Transaction ID")
                if invalid_transaction_ids > 0:
                    self.logger.info(f"🎯 匹配策略: 智能混合匹配 ({valid_transaction_ids}条精确匹配 + {invalid_transaction_ids}条订单号匹配)")
                    return 'mixed'  # 更准确的策略描述
                else:
                    self.logger.info("🎯 匹配策略: 精确匹配 (所有订单都有完整的交易信息)")
                    return 'transaction_id'
            else:
                self.logger.info("🎯 匹配策略: 订单号匹配 (使用订单编号进行匹配)")
                return 'order_id'
        else:
            missing_cols = []
            if not has_refund_transaction_id:
                missing_cols.append("退款文件缺少Transaction ID列")
            if not has_sales_transaction_num:
                missing_cols.append("销售数据缺少Transaction_Num列")

            # 🔧 日志修正：更清晰地说明为什么使用Order ID匹配
            self.logger.info(f"📊 使用Order ID匹配策略: {'; '.join(missing_cols)}")
            self.logger.info("📊 所有退款记录将基于Order ID进行匹配")
            return 'order_id'

    def _match_single_refund_record(self, refund_row: pd.Series, sales_df: pd.DataFrame, strategy: str) -> Dict[str, Any]:
        """
        🔧 修正：匹配单条退款记录 - 实现正确的业务逻辑

        业务逻辑修正：
        - 如果记录有有效Transaction ID，必须使用Transaction ID匹配
        - Transaction ID匹配失败时，该订单不需要退款（不回退到Order ID匹配）
        - 只有记录没有有效Transaction ID时，才使用Order ID匹配

        Args:
            refund_row: 退款记录
            sales_df: 销售数据
            strategy: 文件级别的匹配策略（用于参考）

        Returns:
            匹配结果字典
        """
        result = {
            'matched': False,
            'sales_records': [],
            'reason': '',
            'strategy_used': 'unknown'
        }

        try:
            # 🔧 业务逻辑修正：检查该记录是否有有效的Transaction ID
            transaction_id = refund_row.get('Transaction ID')
            has_valid_tid = pd.notna(transaction_id) and str(transaction_id).strip() != ''

            if has_valid_tid:
                # 有有效Transaction ID，必须使用Transaction ID匹配
                result = self._match_by_transaction_id(refund_row, sales_df)
                if not result['matched']:
                    # 🔧 关键修正：Transaction ID匹配失败，该订单不需要退款
                    clean_transaction_id = str(transaction_id).strip()
                    result['reason'] = f"Transaction ID {clean_transaction_id} 未找到匹配，该订单不需要退款"
                    result['skip_reason'] = 'no_refund_needed'
                    result['strategy_used'] = 'transaction_id'
                    self.logger.info(f"ℹ️ Transaction ID {clean_transaction_id} 不存在于数据库，该订单无需退款")
                return result
            else:
                # 没有有效Transaction ID，使用Order ID匹配
                result = self._match_by_order_id(refund_row, sales_df)
                result['strategy_used'] = 'order_id'
                return result

        except Exception as e:
            result['reason'] = f"匹配过程出错: {e}"
            result['strategy_used'] = 'error'
            return result

    def _match_by_transaction_id(self, refund_row: pd.Series, sales_df: pd.DataFrame) -> Dict[str, Any]:
        """
        🔧 新增：通过Transaction ID匹配
        """
        result = {
            'matched': False,
            'sales_records': [],
            'reason': '',
            'strategy_used': 'transaction_id'
        }

        transaction_id = refund_row.get('Transaction ID')

        # 验证Transaction ID
        if pd.isna(transaction_id) or str(transaction_id).strip() in ['', 'nan', 'None']:
            result['reason'] = "Transaction ID为空或无效"
            return result

        # 清理Transaction ID
        clean_transaction_id = str(transaction_id).strip()

        # 在销售数据中查找匹配
        if 'Transaction_Num' not in sales_df.columns:
            result['reason'] = "销售数据缺少Transaction_Num列"
            return result

        # 执行匹配
        matching_sales = sales_df[
            sales_df['Transaction_Num'].astype(str).str.strip() == clean_transaction_id
        ]

        if not matching_sales.empty:
            result['matched'] = True
            result['sales_records'] = [row for _, row in matching_sales.iterrows()]
            self.logger.debug(f"✅ Transaction ID匹配成功: {clean_transaction_id}")
        else:
            # 🔧 日志修正：更准确地描述Transaction ID匹配失败的含义
            result['reason'] = f"Transaction ID {clean_transaction_id} 在数据库中未找到匹配记录"
            self.logger.debug(f"ℹ️ Transaction ID不存在: {clean_transaction_id}，该订单无需退款（正常情况）")

        return result

    def _match_by_order_id(self, refund_row: pd.Series, sales_df: pd.DataFrame) -> Dict[str, Any]:
        """
        🔧 新增：通过Order ID匹配（传统方式）
        """
        result = {
            'matched': False,
            'sales_records': [],
            'reason': '',
            'strategy_used': 'order_id'
        }

        order_id = refund_row.get('Order ID')

        # 验证Order ID
        if pd.isna(order_id) or str(order_id).strip() == '':
            result['reason'] = "Order ID为空"
            return result

        # 🔧 退款逻辑修复：清理Order ID，确保数据类型一致性
        clean_order_id = str(order_id).strip()

        # 在销售数据中查找匹配
        if 'Order_No' not in sales_df.columns:
            result['reason'] = "销售数据缺少Order_No列"
            return result

        # 🔧 退款逻辑修复：改进匹配逻辑，统一数据类型和清理空格
        matching_sales = sales_df[
            sales_df['Order_No'].astype(str).str.strip() == clean_order_id
        ]

        if not matching_sales.empty:
            result['matched'] = True
            result['sales_records'] = [row for _, row in matching_sales.iterrows()]
            self.logger.debug(f"✅ Order ID匹配成功: {order_id}")
        else:
            result['reason'] = f"Order ID {order_id} 未找到匹配"
            self.logger.debug(result['reason'])

        return result

    def _create_matched_record(self, sales_row: pd.Series, refund_row: pd.Series, strategy: str = 'unknown') -> Dict[str, Any]:
        """
        🔧 新增：创建匹配记录（增强数据验证）
        """
        try:
            matched_record = sales_row.copy()

            # 🔧 退款逻辑修复：增强退款金额验证和处理
            refund_amount = refund_row.get('Refund', 0)

            # 验证和转换退款金额
            try:
                refund_amount = float(refund_amount)
                if refund_amount < 0:
                    self.logger.warning(f"退款金额为负数: {refund_amount}，取绝对值")
                    refund_amount = abs(refund_amount)
                elif refund_amount == 0:
                    self.logger.warning(f"退款金额为0: {refund_amount}")
            except (ValueError, TypeError) as e:
                self.logger.error(f"退款金额转换失败: {refund_amount}, 错误: {e}")
                raise DataProcessingError(f"退款金额无效: {refund_amount}")

            matched_record['Refund_Amount'] = refund_amount
            matched_record['Refund_Date'] = datetime.now().strftime("%Y-%m-%d")
            matched_record['Original_Order_Status'] = sales_row.get('Order_status', 'Unknown')

            # 🔧 退款逻辑修复：添加匹配验证信息（动态策略记录）
            matched_record['Match_Strategy'] = strategy  # 记录实际使用的匹配策略
            if strategy == 'transaction_id':
                matched_record['Transaction_ID_Used'] = refund_row.get('Transaction ID', 'Unknown')
                matched_record['Order_ID_Used'] = refund_row.get('Order ID', 'Unknown')  # 也记录Order ID作为参考
            else:
                matched_record['Order_ID_Used'] = refund_row.get('Order ID', 'Unknown')

            return matched_record

        except Exception as e:
            self.logger.error(f"创建匹配记录失败: {e}")
            raise DataProcessingError(f"创建匹配记录失败: {e}")

    def find_matching_records(self, refund_df: pd.DataFrame, platform: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        查找匹配的销售记录
        
        Args:
            refund_df: 退款数据DataFrame
            platform: 平台类型
            
        Returns:
            (匹配的记录, 未匹配的记录)
        """
        try:
            with get_connection() as conn:
                table_name = f"{platform}_Sales"
                
                # 查询销售数据
                try:
                    # 🔒 安全修复：使用安全的表名验证和SQL构建
                    safe_table = SQLSecurityValidator.sanitize_table_name(table_name)
                    sales_df = pd.read_sql(
                        f"SELECT * FROM {safe_table}",
                        conn.connection
                    )
                except (sqlite3.OperationalError, pd.errors.DatabaseError) as e:
                    self.logger.warning(f"表 {table_name} 不存在或查询失败: {e}")
                    return pd.DataFrame(), refund_df
                except Exception as e:
                    self.logger.error(f"查询销售数据时发生未预期错误: {e}")
                    raise DatabaseError(f"查询销售数据失败: {e}", operation="数据查询", table=table_name)
                
                if sales_df.empty:
                    self.logger.warning(f"销售表 {table_name} 为空")
                    return pd.DataFrame(), refund_df
                
                # 🔧 重构：简化Transaction ID匹配逻辑
                matched_records = []
                unmatched_records = []

                # 检查匹配能力
                matching_strategy = self._determine_matching_strategy(refund_df, sales_df)

                # 🔧 日志修正：更详细地说明匹配策略
                strategy_descriptions = {
                    'transaction_id': '纯Transaction ID匹配策略',
                    'order_id': '纯Order ID匹配策略',
                    'mixed': '混合匹配策略（Transaction ID优先，Order ID备用）'
                }
                strategy_desc = strategy_descriptions.get(matching_strategy, matching_strategy)
                self.logger.info(f"🎯 采用匹配策略: {strategy_desc}")

                # 🔧 用户友好信息：优化处理开始信息
                total_refunds = len(refund_df)
                self.logger.info(f"🚀 开始处理退款: 共 {total_refunds} 笔订单待处理")

                # 🔧 用户友好信息：简化业务逻辑说明
                if matching_strategy in ['transaction_id', 'mixed']:
                    self.logger.info("💡 处理说明: 部分订单可能无需退款，这是正常的业务情况")

                for index, refund_row in refund_df.iterrows():
                    try:
                        # 🔧 增强日志：记录每条记录的处理详情
                        transaction_id = refund_row.get('Transaction ID', 'N/A')
                        order_id = refund_row.get('Order ID', 'N/A')
                        refund_amount = refund_row.get('Refund', 0)

                        self.logger.debug(f"🔍 处理退款记录 {index+1}/{total_refunds}:")
                        self.logger.debug(f"  📋 Transaction ID: {transaction_id}")
                        self.logger.debug(f"  📋 Order ID: {order_id}")
                        self.logger.debug(f"  💰 退款金额: {refund_amount}")
                        self.logger.debug(f"  🎯 匹配策略: {matching_strategy}")

                        matching_result = self._match_single_refund_record(refund_row, sales_df, matching_strategy)

                        if matching_result['matched']:
                            # 🔒 安全修复：防止重复退款，只取第一个匹配的销售记录
                            if len(matching_result['sales_records']) > 1:
                                self.logger.warning(f"退款记录匹配到多个销售记录({len(matching_result['sales_records'])}个)，只处理第一个匹配项以防重复退款")

                            # 只处理第一个匹配的销售记录
                            sales_row = matching_result['sales_records'][0]
                            # 🔧 Transaction ID匹配修复：传递实际使用的匹配策略
                            matched_record = self._create_matched_record(sales_row, refund_row, matching_result['strategy_used'])
                            matched_records.append(matched_record)

                            # 🔧 日志级别优化：将匹配成功的详细信息改为debug级别（技术细节）
                            matched_order_no = sales_row.get('Order_No', 'N/A')
                            matched_equipment_id = sales_row.get('Equipment_ID', 'N/A')
                            self.logger.debug(f"✅ 匹配成功 {index+1}/{total_refunds}:")
                            self.logger.debug(f"  🎯 策略: {matching_result['strategy_used']}")
                            self.logger.debug(f"  📋 匹配到Order_No: {matched_order_no}")
                            self.logger.debug(f"  🔧 Equipment_ID: {matched_equipment_id}")
                            self.logger.debug(f"  💰 退款金额: {refund_amount}")
                        else:
                            # 🔧 业务逻辑修正：区分"不需要退款"和"匹配失败"
                            skip_reason = matching_result.get('skip_reason', '')

                            if skip_reason == 'no_refund_needed':
                                # 这是正常情况：Transaction ID不存在，订单不需要退款
                                # 将skip_reason添加到记录中以便后续统计
                                refund_row_with_skip = refund_row.copy()
                                refund_row_with_skip['skip_reason'] = skip_reason
                                unmatched_records.append(refund_row_with_skip)

                                # 🔧 日志级别优化：简化无需退款的显示，避免UI信息冗余
                                self.logger.info(f"ℹ️ 无需退款 {index+1}/{total_refunds}")
                                # 详细信息改为debug级别（技术细节）
                                self.logger.debug(f"  📋 Transaction ID: {transaction_id}")
                                self.logger.debug(f"  ✅ 原因: {matching_result['reason']}")
                                self.logger.debug(f"  💡 说明: 该Transaction ID在数据库中不存在，表示订单无需退款")
                            else:
                                # 这是真正的匹配失败（问题情况）
                                unmatched_records.append(refund_row)

                                # 🔧 用户友好信息：简化匹配失败的警告信息
                                self.logger.warning(f"❌ 订单处理失败 ({index+1}/{total_refunds}): {order_id}")
                                # 详细技术信息改为debug级别
                                self.logger.debug(f"  📋 Transaction ID: {transaction_id}")
                                self.logger.debug(f"  ❌ 失败原因: {matching_result['reason']}")
                                self.logger.warning(f"  💡 建议: {self._get_match_failure_suggestion(matching_result, refund_row)}")

                    except Exception as e:
                        # 🔧 安全修复：增强错误处理，提供更详细的错误信息
                        order_id = refund_row.get('Order ID', 'Unknown')
                        refund_amount = refund_row.get('Refund', 'Unknown')
                        error_details = f"Order ID: {order_id}, Refund Amount: {refund_amount}, Error: {str(e)}"
                        self.logger.error(f"处理退款记录时出错: {error_details}")

                        # 将详细错误信息添加到退款记录中
                        refund_row_with_error = refund_row.copy()
                        refund_row_with_error['Error_Details'] = str(e)
                        unmatched_records.append(refund_row_with_error)
                
                matched_df = pd.DataFrame(matched_records) if matched_records else pd.DataFrame()
                unmatched_df = pd.DataFrame(unmatched_records) if unmatched_records else pd.DataFrame()
                
                self.logger.info(f"记录匹配完成 - 匹配: {len(matched_df)}, 未匹配: {len(unmatched_df)}")
                return matched_df, unmatched_df
                
        except Exception as e:
            raise DatabaseError(f"查找匹配记录失败: {e}", operation="记录匹配", table=f"{platform}_Sales")
    
    def verify_update_operation(self, cursor, order_no: str, equipment_id: str, platform: str) -> bool:
        """
        验证更新操作是否成功（使用同一个事务的cursor）

        Args:
            cursor: 数据库游标（同一事务）
            order_no: 订单号
            equipment_id: 设备ID
            platform: 平台类型

        Returns:
            验证是否成功
        """
        try:
            table_name = f"{platform}_Sales"

            # 验证状态是否真的变成了Refund（在同一事务中查询）
            # 🔒 安全修复：使用安全的表名验证和SQL构建
            safe_table = SQLSecurityValidator.sanitize_table_name(table_name)
            cursor.execute(f"""
                SELECT Order_status, Order_types FROM {safe_table}
                WHERE Order_No = ? AND Equipment_ID = ?
            """, (order_no, equipment_id))

            result = cursor.fetchone()
            if result:
                status, types = result
                if status == 'Refund' and types == 'Refund':
                    self.logger.debug(f"✅ 验证成功: {order_no} 状态已更新为Refund")
                    return True
                else:
                    self.logger.error(f"❌ 验证失败: {order_no} 状态={status}, 类型={types}")
                    return False
            else:
                self.logger.error(f"❌ 验证失败: 找不到记录 {order_no}")
                return False

        except Exception as e:
            self.logger.error(f"验证操作失败: {e}")
            return False

    def update_order_status(self, matched_df: pd.DataFrame, platform: str) -> Dict[str, int]:
        """
        更新订单状态为退款（带验证）

        Args:
            matched_df: 匹配的记录DataFrame
            platform: 平台类型

        Returns:
            更新结果字典
        """
        result = {
            'attempted': 0,
            'successful': 0,
            'failed': 0,
            'verification_failed': 0
        }

        if matched_df.empty:
            return result

        try:
            with get_connection() as conn:
                table_name = f"{platform}_Sales"
                cursor = conn.connection.cursor()

                # 开始事务
                conn.connection.execute("BEGIN")

                # 🔧 修复：删除不需要的验证结果变量

                try:
                    for _, row in matched_df.iterrows():
                        result['attempted'] += 1
                        order_id = row['Order_No']  # 🔧 修复：使用正确的列名（销售数据中是Order_No）
                        equipment_id = row.get('Equipment_ID', '')  # 获取Equipment_ID作为额外验证

                        # 🔧 修复：增强更新逻辑，添加详细调试信息
                        self.logger.debug(f"🔄 准备更新订单: {order_id}, Equipment_ID: {equipment_id}")

                        # 🔒 安全修复：使用安全的表名验证和SQL构建
                        safe_table = SQLSecurityValidator.sanitize_table_name(table_name)

                        # 🔧 退款逻辑修复：使用多字段匹配确保更新精确性
                        if equipment_id:
                            # 使用Order_No和Equipment_ID双重匹配
                            cursor.execute(f"""
                                UPDATE {safe_table}
                                SET Order_status = 'Refund',
                                    Order_types = 'Refund'
                                WHERE Order_No = ? AND Equipment_ID = ?
                            """, (order_id, equipment_id))

                            # 🔧 日志级别优化：将数据库操作详情改为debug级别（技术细节）
                            affected_rows = cursor.rowcount
                            self.logger.debug(f"🔄 数据库更新操作 (双重匹配):")
                            self.logger.debug(f"  📊 表名: {safe_table}")
                            self.logger.debug(f"  🎯 匹配条件: Order_No={order_id}, Equipment_ID={equipment_id}")
                            self.logger.debug(f"  📈 影响行数: {affected_rows}")
                        else:
                            # 仅使用Order_No匹配（向后兼容）
                            cursor.execute(f"""
                                UPDATE {safe_table}
                                SET Order_status = 'Refund',
                                    Order_types = 'Refund'
                                WHERE Order_No = ?
                            """, (order_id,))

                            # 🔧 日志级别优化：将单字段匹配详情改为debug，保留warning提醒
                            affected_rows = cursor.rowcount
                            self.logger.warning(f"⚠️ 使用单字段匹配 (Equipment_ID缺失): {order_id}")
                            # 详细的技术信息改为debug级别
                            self.logger.debug(f"  📊 表名: {safe_table}")
                            self.logger.debug(f"  🎯 匹配条件: Order_No={order_id} (Equipment_ID缺失)")
                            self.logger.debug(f"  📈 影响行数: {affected_rows}")

                        affected_rows = cursor.rowcount
                        self.logger.debug(f"📊 SQL执行结果: 影响行数 = {affected_rows}")

                        if affected_rows > 0:
                            result['successful'] += 1
                            # 🔧 日志级别优化：将单个订单更新成功的详细信息改为debug级别
                            self.logger.debug(f"✅ 成功更新订单: {order_id} (影响{affected_rows}行)")
                        else:
                            result['failed'] += 1
                            # 更新失败是重要问题，保留error级别
                            self.logger.error(f"❌ 更新失败订单: {order_id} (影响0行，可能订单不存在)")

                    # 🔧 修复：改进提交逻辑 - 只要有成功的更新就提交
                    if result['successful'] > 0:
                        conn.connection.commit()
                        # 🔧 用户友好信息：优化数据库更新结果展示
                        self.logger.info(f"✅ 退款处理完成: 成功更新 {result['successful']} 笔订单")
                        if result['failed'] > 0:
                            self.logger.warning(f"⚠️ 注意: {result['failed']} 笔订单处理失败，请检查数据")
                    else:
                        # 如果没有成功的更新，不需要提交
                        self.logger.info("ℹ️ 没有需要提交的更新")
                        if result['failed'] > 0:
                            self.logger.error(f"❌ 所有更新操作都失败: 失败{result['failed']}条")

                except Exception as e:
                    conn.connection.rollback()
                    self.logger.error(f"❌ 更新过程中出错，已回滚: {e}")
                    result['successful'] = 0
                    raise

                return result

        except Exception as e:
            raise DatabaseError(f"更新订单状态失败: {e}", operation="状态更新", table=f"{platform}_Sales")
    
    def create_refund_log(self, matched_df: pd.DataFrame, unmatched_df: pd.DataFrame,
                         file_path: str, platform: str) -> str:
        """
        创建退款处理日志 - 只在有失败记录时创建Excel文件

        Args:
            matched_df: 匹配的记录
            unmatched_df: 未匹配的记录
            file_path: 处理的文件路径
            platform: 平台类型

        Returns:
            日志文件路径，如果无需创建则返回空字符串
        """
        try:
            # 🔧 检查是否真的需要创建Excel文件
            has_unmatched = not unmatched_df.empty
            has_matched_for_reference = not matched_df.empty and has_unmatched  # 只有在有未匹配时才记录匹配的作为参考

            if not has_unmatched:
                self.logger.info("所有退款记录都成功处理，无需创建Excel日志文件")
                return ""

            # 创建日志目录
            log_dir = Path(self.db_path).parent / "refund_logs"
            log_dir.mkdir(exist_ok=True)

            # 生成日志文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = log_dir / f"refund_log_{platform}_{timestamp}.xlsx"

            # 创建Excel写入器
            with pd.ExcelWriter(log_file, engine='openpyxl') as writer:
                # 🔧 优先写入未匹配的记录（这是主要目的）
                unmatched_df.to_excel(writer, sheet_name='未找到匹配记录', index=False)

                # 🔧 只有在有未匹配记录时，才写入匹配的记录作为参考
                if has_matched_for_reference:
                    matched_df.to_excel(writer, sheet_name='已处理退款_参考', index=False)
                
                # 🔧 写入处理摘要，重点突出失败信息
                summary_data = {
                    '处理时间': [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                    '源文件': [os.path.basename(file_path)],
                    '平台': [platform],
                    '总退款记录': [len(matched_df) + len(unmatched_df)],
                    '成功处理': [len(matched_df)],
                    '⚠️ 需要处理的失败记录': [len(unmatched_df)],
                    '说明': ['请查看"未找到匹配记录"工作表处理失败的退款']
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='处理摘要', index=False)

            self.logger.info(f"退款失败记录日志已创建: {log_file}")
            return str(log_file)
            
        except Exception as e:
            self.logger.error(f"创建退款日志失败: {e}")
            return ""

    def _get_match_failure_suggestion(self, matching_result: Dict[str, Any], refund_row: pd.Series) -> str:
        """
        🔧 修正：为匹配失败提供具体建议 - 区分正常情况和问题情况

        Args:
            matching_result: 匹配结果
            refund_row: 退款记录

        Returns:
            具体的修复建议
        """
        reason = matching_result.get('reason', '')
        skip_reason = matching_result.get('skip_reason', '')
        transaction_id = refund_row.get('Transaction ID', '')
        order_id = refund_row.get('Order ID', '')

        # 🔧 业务逻辑修正：优先处理"不需要退款"的正常情况
        if skip_reason == 'no_refund_needed':
            return "该Transaction ID在数据库中不存在，表示该订单不需要退款，这是正常情况"

        # 处理真正的匹配失败情况
        if 'Transaction ID' in reason and '未找到匹配' in reason:
            # 🔧 修正：移除错误的建议，因为不应该回退到Order ID匹配
            return "Transaction ID在数据库中不存在，该订单不需要退款"

        elif 'Order ID' in reason and '未找到匹配' in reason:
            return "Order ID在数据库中不存在，请确认订单号是否正确或检查数据完整性"

        elif 'Transaction ID为空' in reason:
            if pd.notna(order_id) and str(order_id).strip():
                return "Transaction ID为空，系统将使用Order ID进行匹配"
            else:
                return "Transaction ID和Order ID都为空，无法进行匹配，请补充完整数据"

        elif 'Order ID为空' in reason:
            return "Order ID为空，这是必需字段，请补充完整的订单信息"

        elif '数据缺少' in reason:
            return "数据库缺少必要字段，请检查数据库结构或联系技术支持"

        elif '匹配过程出错' in reason:
            return "匹配过程中发生技术错误，请检查数据格式或联系技术支持"

        else:
            return "请检查数据格式和完整性，确保ID字段准确无误"

    def _assess_data_quality(self, refund_df: pd.DataFrame) -> Dict[str, Any]:
        """
        🔧 新增：数据质量预检 - 预测处理成功率

        Args:
            refund_df: 退款数据DataFrame

        Returns:
            数据质量评估结果
        """
        quality_assessment = {
            'total_records': len(refund_df),
            'transaction_id_coverage': 0.0,
            'order_id_coverage': 0.0,
            'refund_amount_validity': 0.0,
            'data_type_consistency': 0.0,
            'predicted_success_rate': 0.0,
            'quality_score': 0.0,
            'estimated_no_refund_rate': 0.0,  # 🔧 新增：估算无需退款比例
            'issues': [],
            'recommendations': []
        }

        try:
            if refund_df.empty:
                quality_assessment['issues'].append("数据文件为空")
                return quality_assessment

            total_records = len(refund_df)

            # 1. Transaction ID覆盖率分析
            if 'Transaction ID' in refund_df.columns:
                valid_transaction_ids = refund_df['Transaction ID'].notna().sum()
                non_empty_transaction_ids = (refund_df['Transaction ID'].astype(str).str.strip() != '').sum()
                quality_assessment['transaction_id_coverage'] = (non_empty_transaction_ids / total_records) * 100

                # 🔧 修正：Transaction ID覆盖率分析建议
                if quality_assessment['transaction_id_coverage'] > 80:
                    quality_assessment['recommendations'].append("✅ Transaction ID覆盖率高，大部分记录将使用精确匹配")
                    quality_assessment['recommendations'].append("ℹ️ 注意：部分Transaction ID匹配失败是正常的（订单无需退款）")
                elif quality_assessment['transaction_id_coverage'] > 50:
                    quality_assessment['recommendations'].append("⚠️ Transaction ID覆盖率中等，建议检查数据完整性")
                    quality_assessment['recommendations'].append("💡 提示：无Transaction ID的记录将使用Order ID匹配")
                else:
                    quality_assessment['issues'].append("❌ Transaction ID覆盖率低，大部分记录将使用Order ID匹配")
                    quality_assessment['recommendations'].append("💡 建议：如果可能，请补充Transaction ID以提高匹配精度")
            else:
                quality_assessment['issues'].append("❌ 缺少Transaction ID列，将使用Order ID匹配")

            # 2. Order ID完整性分析
            if 'Order ID' in refund_df.columns:
                valid_order_ids = refund_df['Order ID'].notna().sum()
                non_empty_order_ids = (refund_df['Order ID'].astype(str).str.strip() != '').sum()
                quality_assessment['order_id_coverage'] = (non_empty_order_ids / total_records) * 100

                if quality_assessment['order_id_coverage'] < 90:
                    quality_assessment['issues'].append(f"⚠️ Order ID完整性不足: {quality_assessment['order_id_coverage']:.1f}%")
            else:
                quality_assessment['issues'].append("❌ 缺少Order ID列，这是必需字段")

            # 3. 退款金额有效性分析
            if 'Refund' in refund_df.columns:
                try:
                    numeric_refunds = pd.to_numeric(refund_df['Refund'], errors='coerce')
                    valid_amounts = (numeric_refunds > 0).sum()
                    quality_assessment['refund_amount_validity'] = (valid_amounts / total_records) * 100

                    if quality_assessment['refund_amount_validity'] < 95:
                        invalid_count = total_records - valid_amounts
                        quality_assessment['issues'].append(f"⚠️ 发现{invalid_count}条无效退款金额记录")

                    # 检查异常金额
                    if not numeric_refunds.empty:
                        max_amount = numeric_refunds.max()
                        mean_amount = numeric_refunds.mean()
                        if max_amount > mean_amount * 10:
                            quality_assessment['issues'].append(f"⚠️ 发现异常大额退款: {max_amount:.2f}")

                except Exception as e:
                    quality_assessment['issues'].append(f"❌ 退款金额分析失败: {e}")
            else:
                quality_assessment['issues'].append("❌ 缺少Refund列，这是必需字段")

            # 4. 数据类型一致性分析
            consistency_score = 0
            consistency_checks = 0

            # 检查Transaction ID数据类型一致性
            if 'Transaction ID' in refund_df.columns:
                tid_types = refund_df['Transaction ID'].dropna().apply(type).value_counts()
                if len(tid_types) == 1:
                    consistency_score += 1
                consistency_checks += 1

            # 检查Order ID数据类型一致性
            if 'Order ID' in refund_df.columns:
                oid_types = refund_df['Order ID'].dropna().apply(type).value_counts()
                if len(oid_types) == 1:
                    consistency_score += 1
                consistency_checks += 1

            if consistency_checks > 0:
                quality_assessment['data_type_consistency'] = (consistency_score / consistency_checks) * 100

            # 🔧 修正：预测成功率计算 - 考虑"不需要退款"的正常情况

            # 估算不需要退款的记录比例
            estimated_no_refund_rate = 0.0
            if 'Transaction ID' in refund_df.columns:
                valid_tids = refund_df['Transaction ID'].notna().sum()
                if valid_tids > 0:
                    # 🔧 业务逻辑修正：估算Transaction ID不存在的比例（正常情况）
                    # 基于经验值，通常有5-15%的Transaction ID在数据库中不存在（这是正常的）
                    estimated_no_refund_rate = min(valid_tids / total_records * 0.1, 0.15)  # 最多15%
                    quality_assessment['estimated_no_refund_rate'] = estimated_no_refund_rate * 100

                    self.logger.debug(f"📊 估算无需退款比例: {estimated_no_refund_rate * 100:.1f}%")

            # 重新计算成功率：成功率 = 实际匹配成功率 + 无需退款率
            success_factors = []

            # Transaction ID因子（权重40%）- 修正计算
            if quality_assessment['transaction_id_coverage'] > 0:
                # Transaction ID匹配成功率 + 无需退款率
                tid_success_rate = quality_assessment['transaction_id_coverage'] / 100
                tid_factor = min(tid_success_rate + estimated_no_refund_rate, 1.0) * 0.4
                success_factors.append(tid_factor)

                self.logger.debug(f"📊 Transaction ID综合成功率: {(tid_success_rate + estimated_no_refund_rate) * 100:.1f}%")

            # Order ID因子（权重35%）
            oid_factor = min(quality_assessment['order_id_coverage'] / 100, 1.0) * 0.35
            success_factors.append(oid_factor)

            # 退款金额因子（权重15%）
            amount_factor = min(quality_assessment['refund_amount_validity'] / 100, 1.0) * 0.15
            success_factors.append(amount_factor)

            # 数据一致性因子（权重10%）
            consistency_factor = min(quality_assessment['data_type_consistency'] / 100, 1.0) * 0.1
            success_factors.append(consistency_factor)

            quality_assessment['predicted_success_rate'] = sum(success_factors) * 100

            # 6. 综合质量评分
            quality_assessment['quality_score'] = (
                quality_assessment['transaction_id_coverage'] * 0.3 +
                quality_assessment['order_id_coverage'] * 0.3 +
                quality_assessment['refund_amount_validity'] * 0.2 +
                quality_assessment['data_type_consistency'] * 0.2
            )

            # 🔧 修正：生成建议 - 考虑无需退款的正常情况
            predicted_rate = quality_assessment['predicted_success_rate']
            no_refund_rate = quality_assessment.get('estimated_no_refund_rate', 0.0)

            if predicted_rate >= 90:
                quality_assessment['recommendations'].append("🎉 数据质量优秀，预期处理效果很好")
                if no_refund_rate > 5:
                    quality_assessment['recommendations'].append(f"ℹ️ 预计约{no_refund_rate:.1f}%的订单无需退款（正常情况）")
            elif predicted_rate >= 75:
                quality_assessment['recommendations'].append("👍 数据质量良好，预期处理效果较好")
                if no_refund_rate > 5:
                    quality_assessment['recommendations'].append(f"ℹ️ 预计约{no_refund_rate:.1f}%的订单无需退款（正常情况）")
            elif predicted_rate >= 60:
                quality_assessment['recommendations'].append("⚠️ 数据质量一般，建议检查和清理数据")
                quality_assessment['recommendations'].append("💡 注意：部分Transaction ID匹配失败可能是正常的无需退款情况")
            else:
                quality_assessment['recommendations'].append("❌ 数据质量较差，强烈建议先清理数据")
                quality_assessment['recommendations'].append("💡 提示：请区分真正的数据问题和正常的无需退款情况")

            # 🔧 日志级别优化：简化质量评估结果显示，突出核心信息
            self.logger.info(f"📊 数据质量评估: 预期成功率 {quality_assessment['predicted_success_rate']:.1f}%，质量评分 {quality_assessment['quality_score']:.1f}/100")

            # 技术细节改为debug级别
            self.logger.debug(f"  🔍 Transaction ID覆盖率: {quality_assessment['transaction_id_coverage']:.1f}%")
            self.logger.debug(f"  📋 Order ID覆盖率: {quality_assessment['order_id_coverage']:.1f}%")

            # 🔧 保留重要的业务信息：无需退款比例估算
            if quality_assessment.get('estimated_no_refund_rate', 0) > 0:
                self.logger.info(f"ℹ️ 预计约 {quality_assessment['estimated_no_refund_rate']:.1f}% 的订单无需退款（正常情况）")

            if quality_assessment['issues']:
                self.logger.warning(f"⚠️ 发现{len(quality_assessment['issues'])}个质量问题")
                for issue in quality_assessment['issues']:
                    self.logger.warning(f"  {issue}")

            return quality_assessment

        except Exception as e:
            self.logger.error(f"数据质量评估失败: {e}")
            quality_assessment['issues'].append(f"评估过程异常: {e}")
            return quality_assessment

    def _validate_refund_completeness(self, refund_df: pd.DataFrame, matched_df: pd.DataFrame, unmatched_df: pd.DataFrame) -> Dict[str, Any]:
        """
        🔧 新增：验证退款处理的完整性

        Args:
            refund_df: 原始退款数据
            matched_df: 匹配的记录
            unmatched_df: 未匹配的记录

        Returns:
            验证结果字典
        """
        validation_result = {
            'total_refunds': len(refund_df),
            'matched_count': len(matched_df),
            'unmatched_count': len(unmatched_df),
            'completeness_rate': 0.0,
            'total_refund_amount': 0.0,
            'matched_refund_amount': 0.0,
            'unmatched_refund_amount': 0.0,
            'validation_passed': False,
            'issues': []
        }

        try:
            # 计算完整性比率
            if validation_result['total_refunds'] > 0:
                validation_result['completeness_rate'] = (validation_result['matched_count'] / validation_result['total_refunds']) * 100

            # 计算退款金额统计
            validation_result['total_refund_amount'] = refund_df['Refund'].astype(float).sum()

            if not matched_df.empty:
                validation_result['matched_refund_amount'] = matched_df['Refund_Amount'].astype(float).sum()

            if not unmatched_df.empty:
                validation_result['unmatched_refund_amount'] = unmatched_df['Refund'].astype(float).sum()

            # 验证数据一致性
            expected_total = validation_result['matched_refund_amount'] + validation_result['unmatched_refund_amount']
            amount_diff = abs(validation_result['total_refund_amount'] - expected_total)

            if amount_diff > 0.01:  # 允许小数点精度误差
                validation_result['issues'].append(f"退款金额不一致: 总计{validation_result['total_refund_amount']}, 分项合计{expected_total}")

            # 检查记录数一致性
            record_sum = validation_result['matched_count'] + validation_result['unmatched_count']
            if record_sum != validation_result['total_refunds']:
                validation_result['issues'].append(f"记录数不一致: 总计{validation_result['total_refunds']}, 分项合计{record_sum}")

            # 判断验证是否通过
            validation_result['validation_passed'] = len(validation_result['issues']) == 0

            # 🔧 用户友好信息：优化最终统计结果展示
            self.logger.info(f"📋 处理结果: 共{validation_result['total_refunds']}笔订单，成功处理{validation_result['matched_count']}笔，无需退款{validation_result['unmatched_count']}笔")
            self.logger.info(f"💰 退款金额: 总计 ¥{validation_result['total_refund_amount']:.2f}，实际退款 ¥{validation_result['matched_refund_amount']:.2f}")
            self.logger.info(f"✅ 处理完成率: {validation_result['completeness_rate']:.1f}%")

            if validation_result['issues']:
                for issue in validation_result['issues']:
                    self.logger.warning(f"⚠️ 完整性问题: {issue}")
            else:
                self.logger.info("✅ 退款完整性验证通过")

            return validation_result

        except Exception as e:
            self.logger.error(f"退款完整性验证失败: {e}")
            validation_result['issues'].append(f"验证过程异常: {e}")
            return validation_result

    def _create_refund_backup(self, file_path: str, platform: str) -> Optional[str]:
        """
        🔧 新增：创建退款处理前的数据库备份

        Args:
            file_path: 退款文件路径
            platform: 平台类型

        Returns:
            备份文件路径，失败时返回None
        """
        if not self.backup_manager:
            self.logger.warning("⚠️ 备份管理器未初始化，跳过备份")
            return None

        try:
            filename = os.path.basename(file_path)
            backup_operation = f"退款处理_{platform}_{filename}"

            self.logger.info("🔄 退款处理前自动备份数据库...")
            backup_path = self.backup_manager.create_backup(backup_operation)

            if backup_path:
                self.logger.info(f"✅ 数据库备份成功: {os.path.basename(backup_path)}")
                return backup_path
            else:
                self.logger.error("❌ 数据库备份失败")
                return None

        except Exception as e:
            self.logger.error(f"❌ 备份过程异常: {e}")
            return None

    def process_file(self, file_path: str, platform: str) -> Dict[str, Any]:
        """
        处理退款文件
        
        Args:
            file_path: 文件路径
            platform: 平台类型
            
        Returns:
            处理结果字典
        """
        result = {
            'success': False,
            'file_path': file_path,
            'platform': platform,
            'total_refunds': 0,
            'processed_refunds': 0,
            'unmatched_refunds': 0,
            'updated_records': 0,
            'log_file': '',
            'errors': []
        }
        
        backup_file = None
        try:
            self.logger.info(f"开始处理退款文件: {file_path}, 平台: {platform}")

            # 🔧 新增：退款处理前创建数据库备份 (5%)
            print("进度: 5%")
            backup_file = self._create_refund_backup(file_path, platform)
            if backup_file:
                result['backup_file'] = backup_file
                self.logger.info(f"📋 备份文件: {os.path.basename(backup_file)}")
            else:
                # 🔧 备份安全修复：备份失败时停止退款处理
                error_msg = "❌ 数据库备份失败，停止退款处理以保护数据安全"
                self.logger.error(error_msg)
                result['success'] = False
                result['error'] = error_msg
                result['processed_count'] = 0
                result['failed_count'] = 0
                return result

            # 🔧 修复：添加UI进度条支持
            # 步骤1: 验证文件 (15%)
            print("进度: 15%")
            self.validate_file(file_path)

            # 步骤2: 加载退款数据 (35%)
            print("进度: 35%")
            refund_df = self.load_refund_data(file_path)
            result['total_refunds'] = len(refund_df)

            # 🔧 新增：数据质量预检 (45%)
            print("进度: 45%")
            quality_assessment = self._assess_data_quality(refund_df)
            result['data_quality'] = quality_assessment

            # 根据质量评估给出用户提示
            if quality_assessment['predicted_success_rate'] < 60:
                self.logger.warning("⚠️ 数据质量较差，建议先清理数据后再处理")
                result['warnings'].append(f"数据质量评分: {quality_assessment['quality_score']:.1f}/100")
                result['warnings'].append(f"预期成功率: {quality_assessment['predicted_success_rate']:.1f}%")
            else:
                self.logger.info(f"✅ 数据质量良好，预期成功率: {quality_assessment['predicted_success_rate']:.1f}%")

            # 步骤3: 查找匹配记录 (65%)
            print("进度: 65%")
            matched_df, unmatched_df = self.find_matching_records(refund_df, platform)
            result['processed_refunds'] = len(matched_df)
            result['unmatched_refunds'] = len(unmatched_df)

            # 🔧 退款逻辑修复：添加完整性验证 (75%)
            print("进度: 75%")
            validation_result = self._validate_refund_completeness(refund_df, matched_df, unmatched_df)
            result['validation'] = validation_result

            if not validation_result['validation_passed']:
                self.logger.warning("⚠️ 退款完整性验证未通过，但继续处理")
                for issue in validation_result['issues']:
                    result['warnings'].append(f"完整性问题: {issue}")

            # 🔧 退款逻辑修复：添加详细的处理统计
            result['completeness_rate'] = validation_result['completeness_rate']
            result['total_refund_amount'] = validation_result['total_refund_amount']
            result['matched_refund_amount'] = validation_result['matched_refund_amount']

            # 步骤4: 更新订单状态 (90%)
            if not matched_df.empty:
                print("进度: 90%")
                update_result = self.update_order_status(matched_df, platform)
                result['updated_records'] = update_result['successful']
                result['attempted_updates'] = update_result['attempted']
                result['failed_updates'] = update_result['failed']
                result['verification_failed'] = update_result['verification_failed']
            
            # 步骤5: 创建处理日志 (100%) - 只在有失败记录时创建
            print("进度: 95%")
            log_file = ""
            # 🔧 修复：只有在有未匹配记录或处理失败时才创建Excel日志
            if not unmatched_df.empty or (update_result and update_result.get('failed', 0) > 0):
                log_file = self.create_refund_log(matched_df, unmatched_df, file_path, platform)
                self.logger.info(f"发现失败记录，已创建Excel日志: {log_file}")
            else:
                self.logger.info("所有退款处理成功，无需创建Excel日志文件")
            result['log_file'] = log_file

            print("进度: 100%")
            result['success'] = True
            self.logger.info(f"退款文件处理完成: {file_path}")
            
        except Exception as e:
            error_msg = str(e)
            result['errors'].append(error_msg)
            self.logger.error(f"处理退款文件失败: {file_path}, 错误: {error_msg}")
        
        return result


def main():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description='优化版退款处理脚本')
    parser.add_argument('--file', required=True, help='要处理的退款文件路径')
    parser.add_argument('--platform', required=True, choices=['IOT', 'ZERO', 'APP'], help='平台类型')
    parser.add_argument('--db_path', help='数据库路径')
    
    args = parser.parse_args()
    
    try:
        # 创建处理器
        processor = RefundProcessor(args.db_path)
        
        # 处理文件
        result = processor.process_file(args.file, args.platform)
        
        # 输出结果
        if result['success']:
            print(f"✅ 退款处理成功")
            print(f"📁 文件: {os.path.basename(args.file)}")
            print(f"🏷️ 平台: {args.platform}")
            print(f"📊 总退款记录: {result['total_refunds']}")
            print(f"✅ 成功处理: {result['processed_refunds']}")
            print(f"❌ 未找到匹配: {result['unmatched_refunds']}")
            # 🔧 修复：显示实际更新的记录数
            actual_updates = result.get('updated_records', 0)
            print(f"💾 实际更新记录数: {actual_updates}")

            # 显示详细的验证结果
            if 'attempted_updates' in result:
                print(f"🔍 验证详情:")
                print(f"  • 尝试更新: {result['attempted_updates']} 条")
                print(f"  • 成功更新: {actual_updates} 条")
                if result.get('failed_updates', 0) > 0:
                    print(f"  • 更新失败: {result['failed_updates']} 条")
                if result.get('verification_failed', 0) > 0:
                    print(f"  • 验证失败: {result['verification_failed']} 条")

            # 🔧 新增：显示数据库操作统计
            print(f"📊 数据库操作统计:")
            print(f"  • 数据库提交: {'是' if actual_updates > 0 else '否'}")
            print(f"  • 事务状态: {'已提交' if actual_updates > 0 else '已回滚或无更新'}")

            if result['log_file']:
                print(f"📋 日志文件: {os.path.basename(result['log_file'])}")
            return 0
        else:
            print(f"❌ 退款处理失败")
            for error in result['errors']:
                print(f"错误: {error}")
            return 1
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
