#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版数据导入脚本 - 用于测试和诊断
"""

print("🔧 [SIMPLE] === 简化脚本开始执行 ===", flush=True)

import sys
print("🔧 [SIMPLE] sys模块导入完成", flush=True)

import os
print("🔧 [SIMPLE] os模块导入完成", flush=True)

import argparse
print("🔧 [SIMPLE] argparse模块导入完成", flush=True)

# 添加数据库和数据处理模块
print("🔧 [SIMPLE] 开始导入数据库模块", flush=True)
import sqlite3
from datetime import datetime
print("🔧 [SIMPLE] 数据库模块导入完成", flush=True)

print("🔧 [SIMPLE] 导入pandas到全局作用域", flush=True)
import pandas as pd
print("🔧 [SIMPLE] pandas全局导入完成", flush=True)

def separate_api_orders(df):
    """分离API订单数据"""
    if df.empty or 'Order types' not in df.columns:
        return df, pd.DataFrame()

    # 检测API订单（支持多种格式，与原始脚本一致）
    api_mask = df['Order types'].astype(str).str.strip().str.lower().str.contains('api', na=False)

    # 分离数据
    api_df = df[api_mask].copy().reset_index(drop=True)
    regular_df = df[~api_mask].copy().reset_index(drop=True)

    if not api_df.empty:
        print(f"🔧 [SIMPLE] 分离出 {len(api_df)} 条API订单", flush=True)

    return regular_df, api_df

def determine_target_table(platform, order_status):
    """确定目标表（简化版）"""
    status = str(order_status).strip().lower() if order_status else ""

    if not status:
        return f"{platform}_Sales"

    # 简化的状态映射
    if status in ['finished', 'completed', 'success', 'finish', 'done']:
        return f"{platform}_Sales"
    elif status in ['refunded', 'refunding', 'cancelled', 'cancel']:
        return f"{platform}_Sales_Refunding"
    elif status in ['closed', 'close', 'failed', 'expired']:
        return f"{platform}_Sales_Close"
    else:
        return f"{platform}_Sales"

def analyze_data_distribution(df, platform):
    """分析数据分布"""
    distribution = {}

    if 'Order status' in df.columns:
        for _, row in df.iterrows():
            target_table = determine_target_table(platform, row.get('Order status', ''))
            distribution[target_table] = distribution.get(target_table, 0) + 1
    else:
        # 如果没有状态列，全部导入到默认表
        default_table = f"{platform}_Sales"
        distribution[default_table] = len(df)

    return distribution

def get_data_for_table(df, table_name, platform):
    """获取属于指定表的数据"""
    if df.empty:
        return pd.DataFrame()

    if 'Order status' not in df.columns:
        return df.copy()

    # 🔧 Bug修复：使用向量化操作替代apply，提高性能
    # 先为所有行计算目标表
    df_temp = df.copy()
    df_temp['_target_table'] = df_temp['Order status'].apply(lambda x: determine_target_table(platform, x))

    # 筛选属于该表的数据
    mask = df_temp['_target_table'] == table_name
    result_df = df_temp[mask].copy().reset_index(drop=True)

    # 删除临时列
    if '_target_table' in result_df.columns:
        result_df = result_df.drop('_target_table', axis=1)

    return result_df

def prepare_data_for_insert(df):
    """准备插入数据（列名映射和过滤）"""
    # 完整的列映射（与原始脚本一致）
    column_mapping = {
        'Copartner name': 'Copartner_name',
        'Order No.': 'Order_No',
        'Order No': 'Order_No',  # 处理不带点号的情况
        'Order types': 'Order_types',
        'Order status': 'Order_status',
        'Order price': 'Order_price',
        'Order time': 'Order_time',
        'Equipment ID': 'Equipment_ID',
        'Equipment name': 'Equipment_name',
        'Branch name': 'Branch_name',
        'Payment date': 'Payment_date',
        'User name': 'User_name',
        'Transaction Num': 'Transaction_Num',
        'Transaction_Num': 'Transaction_Num',
        'Payment': 'Payment',
        'Time': 'Time',
        'Matched Order ID': 'Matched_Order_ID',
        'OrderTime_dt': 'OrderTime_dt'
    }

    # 数据库表中实际存在的列（与原始脚本一致）
    valid_db_columns = [
        'Copartner_name', 'Order_No', 'Order_types', 'Order_status',
        'Order_price', 'Payment', 'Order_time', 'Equipment_ID',
        'Equipment_name', 'Branch_name', 'Payment_date', 'User_name',
        'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num'
    ]

    # 重命名列
    insert_df = df.copy()
    insert_df = insert_df.rename(columns=column_mapping)

    # 只保留数据库表中存在的列
    available_columns = [col for col in insert_df.columns if col in valid_db_columns]

    if available_columns:
        insert_df = insert_df[available_columns]
        print(f"🔧 [SIMPLE] 保留的列: {len(available_columns)} 个", flush=True)
    else:
        print(f"🔧 [SIMPLE] 警告：没有找到有效列，保留所有列", flush=True)

    return insert_df

def process_data_with_strategy(file_path, platform, db_path, strategy):
    """根据策略处理数据"""
    print(f"🔧 [SIMPLE] 开始处理数据: {strategy}", flush=True)

    try:
        # 读取Excel文件
        print("🔧 [SIMPLE] 读取Excel文件", flush=True)
        df = pd.read_excel(file_path)
        print(f"🔧 [SIMPLE] 读取到 {len(df)} 条数据", flush=True)

        # 连接数据库
        print("🔧 [SIMPLE] 连接数据库", flush=True)
        conn = sqlite3.connect(db_path)

        # 确定所有相关表
        related_tables = [
            f"{platform}_Sales",
            f"{platform}_Sales_Close",
            f"{platform}_Sales_Refunding"
        ]
        # 如果是IOT平台，还要删除APP_Sales表的相同日期数据
        if platform == "IOT":
            related_tables.append("APP_Sales")

        print(f"🔧 [SIMPLE] 相关表: {related_tables}", flush=True)

        if strategy == "refresh_daily":
            # 删除文件中数据日期对应的数据库记录
            print("🔧 [SIMPLE] 执行refresh_daily策略", flush=True)

            # 从文件数据中提取日期
            print(f"🔧 [SIMPLE] 文件列名: {list(df.columns)}", flush=True)

            # 查找时间列（可能的列名）
            time_column = None
            possible_time_columns = ['Order_time', 'Order time', 'OrderTime', 'Order_Time', 'order_time', 'order time']

            for col in possible_time_columns:
                if col in df.columns:
                    time_column = col
                    print(f"🔧 [SIMPLE] 找到时间列: {time_column}", flush=True)
                    break

            if time_column:
                # 获取文件中所有的日期
                file_dates = set()
                for _, row in df.iterrows():
                    try:
                        order_time = pd.to_datetime(row[time_column])
                        date_str = order_time.strftime('%Y-%m-%d')
                        file_dates.add(date_str)
                    except:
                        continue

                print(f"🔧 [SIMPLE] 文件中包含的日期: {file_dates}", flush=True)

                # 删除所有相关表中这些日期的数据
                cursor = conn.cursor()
                total_deleted = 0

                for date_str in file_dates:
                    for table in related_tables:
                        try:
                            # 🔧 Bug修复：验证表名安全性，避免SQL注入
                            import re
                            if not re.match(r'^[a-zA-Z0-9_]+$', table):
                                print(f"🔧 [SIMPLE] 跳过不安全的表名: {table}", flush=True)
                                continue

                            # 使用安全的表名构建查询
                            delete_query = f'DELETE FROM "{table}" WHERE DATE(Order_time) = ?'
                            cursor.execute(delete_query, (date_str,))
                            deleted_count = cursor.rowcount
                            total_deleted += deleted_count
                            if deleted_count > 0:
                                print(f"🔧 [SIMPLE] 从表 {table} 删除日期 {date_str} 的 {deleted_count} 条数据", flush=True)
                        except Exception as e:
                            print(f"🔧 [SIMPLE] 删除表 {table} 数据失败: {e}", flush=True)

                print(f"🔧 [SIMPLE] 总共删除了 {total_deleted} 条数据", flush=True)
            else:
                print(f"🔧 [SIMPLE] 未找到时间列，尝试的列名: {possible_time_columns}", flush=True)

            # 重新插入所有数据，按状态分配到不同表
            print(f"🔧 [SIMPLE] 准备重新插入 {len(df)} 条数据", flush=True)

            try:
                # 首先分离API订单
                regular_df, api_df = separate_api_orders(df)

                total_inserted = 0

                # 处理常规订单（IOT平台）
                if not regular_df.empty:
                    distribution = analyze_data_distribution(regular_df, platform)
                    print(f"🔧 [SIMPLE] 常规数据分布: {distribution}", flush=True)

                    # 按表分别插入常规数据
                    for table_name, count in distribution.items():
                        if count > 0:
                            # 获取属于该表的数据
                            table_data = get_data_for_table(regular_df, table_name, platform)

                            if not table_data.empty:
                                # 准备插入数据
                                insert_df = prepare_data_for_insert(table_data)

                                # 插入数据
                                insert_df.to_sql(table_name, conn, if_exists='append', index=False)
                                total_inserted += len(insert_df)
                                print(f"🔧 [SIMPLE] 成功插入 {len(insert_df)} 条数据到 {table_name}", flush=True)

                # 处理API订单（APP平台）
                if not api_df.empty:
                    api_distribution = analyze_data_distribution(api_df, 'APP')
                    print(f"🔧 [SIMPLE] API数据分布: {api_distribution}", flush=True)

                    # 按表分别插入API数据
                    for table_name, count in api_distribution.items():
                        if count > 0:
                            # 获取属于该表的数据
                            table_data = get_data_for_table(api_df, table_name, 'APP')

                            if not table_data.empty:
                                # 准备插入数据
                                insert_df = prepare_data_for_insert(table_data)

                                # 插入数据
                                insert_df.to_sql(table_name, conn, if_exists='append', index=False)
                                total_inserted += len(insert_df)
                                print(f"🔧 [SIMPLE] 成功插入 {len(insert_df)} 条API数据到 {table_name}", flush=True)

                print(f"🔧 [SIMPLE] 总共重新插入 {total_inserted} 条数据", flush=True)

            except Exception as e:
                print(f"🔧 [SIMPLE] 数据插入失败: {e}", flush=True)
                return False

        elif strategy == "overwrite":
            print("🔧 [SIMPLE] 执行overwrite策略（暂未实现）", flush=True)

        elif strategy == "incremental":
            print("🔧 [SIMPLE] 执行incremental策略（暂未实现）", flush=True)

        elif strategy == "skip":
            print("🔧 [SIMPLE] 执行skip策略（暂未实现）", flush=True)

        # 提交更改
        conn.commit()
        conn.close()

        print(f"🔧 [SIMPLE] {strategy}策略执行完成", flush=True)
        return True

    except Exception as e:
        print(f"🔧 [SIMPLE] 数据处理失败: {e}", flush=True)
        return False

def main():
    print("🔧 [SIMPLE] main函数开始", flush=True)
    
    parser = argparse.ArgumentParser(description='简化版数据导入脚本')
    parser.add_argument('--file', required=True, help='文件路径')
    parser.add_argument('--platform', required=True, help='平台类型')
    parser.add_argument('--db_path', help='数据库路径')
    parser.add_argument('--order_type', default='智能识别导入', help='订单类型')
    
    print("🔧 [SIMPLE] 参数解析器创建完成", flush=True)
    
    args = parser.parse_args()
    print(f"🔧 [SIMPLE] 参数解析完成: {args}", flush=True)
    
    print("🔧 [SIMPLE] 检查pandas可用性", flush=True)
    try:
        print(f"🔧 [SIMPLE] pandas版本: {pd.__version__}", flush=True)
        print("🔧 [SIMPLE] pandas可用", flush=True)
    except Exception as e:
        print(f"🔧 [SIMPLE] pandas不可用: {e}", flush=True)
        return 1
    
    print("🔧 [SIMPLE] 开始真实的重复数据检测", flush=True)

    # 真实的重复数据检测和处理
    try:
        print("🔧 [SIMPLE] 测试tkinter导入", flush=True)
        import tkinter as tk
        from tkinter import simpledialog
        print("🔧 [SIMPLE] tkinter导入成功", flush=True)

        # 模拟检测到重复数据
        print("🔧 [SIMPLE] 模拟检测到重复数据", flush=True)

        # 创建重复数据处理对话框
        root = tk.Tk()
        root.withdraw()
        root.attributes('-topmost', True)

        # 创建自定义重复数据处理对话框
        from tkinter import ttk

        class SimpleDuplicateDialog:
            def __init__(self):
                self.result = None
                # 创建隐藏的根窗口
                self.root = tk.Tk()
                self.root.withdraw()  # 隐藏根窗口
                # 创建实际的对话框窗口
                self.dialog = tk.Toplevel(self.root)
                self.setup_dialog()

            def setup_dialog(self):
                # 窗口基本设置
                self.dialog.title("🔍 检测到重复数据")
                self.dialog.geometry("550x450")
                self.dialog.resizable(False, False)

                # 居中显示
                self.center_window()

                # 设置为置顶
                self.dialog.attributes('-topmost', True)
                self.dialog.focus_force()

                # 创建主框架
                main_frame = ttk.Frame(self.dialog, padding="20")
                main_frame.pack(fill=tk.BOTH, expand=True)

                # 标题
                title_label = ttk.Label(
                    main_frame,
                    text="🔍 检测到重复数据",
                    font=("Arial", 14, "bold")
                )
                title_label.pack(pady=(0, 15))

                # 信息显示
                info_text = """检测到重复数据！

请选择处理方式："""

                info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
                info_label.pack(pady=(0, 15))

                # 选项框架
                options_frame = ttk.LabelFrame(main_frame, text="处理选项", padding="10")
                options_frame.pack(fill=tk.X, pady=(0, 15))

                # 选项变量
                self.choice_var = tk.StringVar(value="skip")

                # 选项按钮
                options = [
                    ("overwrite", "📝 覆盖更新 - 覆盖更新现有数据"),
                    ("incremental", "🔧 增量更新 - 增量更新不同字段"),
                    ("skip", "🔄 跳过重复 - 跳过重复数据，仅插入新数据"),
                    ("refresh_daily", "🗑️ 重新更新 - 删除当天数据后重新导入"),
                    ("cancel", "❌ 取消导入 - 取消导入操作")
                ]

                for value, text in options:
                    rb = ttk.Radiobutton(
                        options_frame,
                        text=text,
                        variable=self.choice_var,
                        value=value
                    )
                    rb.pack(anchor=tk.W, pady=2)

                # 按钮区域
                button_frame = ttk.Frame(main_frame)
                button_frame.pack(fill=tk.X, pady=(15, 0))

                # 确认按钮
                confirm_btn = ttk.Button(
                    button_frame,
                    text="✅ 确认",
                    command=self.confirm_choice
                )
                confirm_btn.pack(side=tk.RIGHT, padx=(5, 0))

                # 取消按钮
                cancel_btn = ttk.Button(
                    button_frame,
                    text="❌ 取消",
                    command=self.cancel_choice
                )
                cancel_btn.pack(side=tk.RIGHT)

                # 绑定键盘事件
                self.dialog.bind('<Return>', lambda e: self.confirm_choice())
                self.dialog.bind('<Escape>', lambda e: self.cancel_choice())

                # 设置默认焦点
                confirm_btn.focus_set()

            def center_window(self):
                self.dialog.update_idletasks()
                width = 550
                height = 450
                x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
                y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
                self.dialog.geometry(f"{width}x{height}+{x}+{y}")

            def confirm_choice(self):
                self.result = self.choice_var.get()
                self.root.quit()
                self.root.destroy()

            def cancel_choice(self):
                self.result = "cancel"
                self.root.quit()
                self.root.destroy()

            def show(self):
                self.root.mainloop()
                return self.result

        print("🔧 [SIMPLE] 显示重复数据处理对话框", flush=True)
        dialog = SimpleDuplicateDialog()
        choice = dialog.show()
        print(f"🔧 [SIMPLE] 用户选择: {choice}", flush=True)

        # 处理用户选择
        if choice:
            strategy = choice
        else:
            print("🔧 [SIMPLE] 用户取消选择，默认取消", flush=True)
            strategy = "cancel"

        print(f"🔧 [SIMPLE] 选择的策略: {strategy}", flush=True)

        if strategy == "cancel":
            print("🔧 [SIMPLE] 用户取消导入", flush=True)
            return 1
        else:
            print(f"🔧 [SIMPLE] 执行策略: {strategy}", flush=True)

            # 真实的数据处理
            success = process_data_with_strategy(args.file, args.platform, args.db_path, strategy)
            if not success:
                print("🔧 [SIMPLE] 数据处理失败", flush=True)
                return 1

            print("🔧 [SIMPLE] 数据导入成功", flush=True)

    except Exception as e:
        print(f"🔧 [SIMPLE] 重复数据处理失败: {e}", flush=True)
        return 1
    
    print("🔧 [SIMPLE] 脚本执行完成", flush=True)
    return 0

if __name__ == "__main__":
    print("🔧 [SIMPLE] 脚本main入口", flush=True)
    try:
        result = main()
        print(f"🔧 [SIMPLE] main函数返回: {result}", flush=True)
        sys.exit(result)
    except Exception as e:
        print(f"🔧 [SIMPLE] 脚本执行异常: {e}", flush=True)
        import traceback
        traceback.print_exc()
        sys.exit(1)
