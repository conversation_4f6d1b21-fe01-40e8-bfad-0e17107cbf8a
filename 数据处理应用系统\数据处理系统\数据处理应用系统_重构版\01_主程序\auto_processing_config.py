#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动处理配置管理器
"""

import json
import os
from typing import Dict, Any, Optional
from datetime import datetime

class AutoProcessingConfig:
    """自动处理配置管理器"""
    
    def __init__(self, config_file: str = "auto_processing_config.json"):
        self.config_file = config_file
        self.default_settings = {
            "auto_processing": {
                "enabled": True,
                "mode": "auto",  # auto, manual, scheduled
                "last_updated": datetime.now().isoformat(),
                "trigger_conditions": {
                    "file_change_detection": True,
                    "scheduled_processing": False,
                    "manual_trigger_only": False,
                    "startup_auto_trigger": True  # 控制启动时是否自动触发
                },
                "file_settings": {
                    "auto_detect_files": True,
                    "file_paths": {
                        "file1": "",  # 动态检测，不使用硬编码路径
                        "file2": "",  # 动态检测，不使用硬编码路径
                        "sheet_name": "TRANSACTION_LIST"
                    },
                    "default_search_paths": [
                        "./IOT/",  # 使用相对路径
                        "./ZERO/",  # 使用相对路径
                        "./"  # 项目根目录
                    ]
                },
                "processing_settings": {
                    "timeout_base": 60,
                    "timeout_per_mb": 10,
                    "timeout_multiplier": 1.0,
                    "retry_attempts": 3,
                    "retry_delay": 5,
                    "error_handling": "continue",  # continue, stop, retry
                    "script_name": "report 模块化设计 7.0.py"
                },
                "ui_settings": {
                    "show_debug_output": True,
                    "auto_scroll_logs": True,
                    "notification_enabled": True,
                    "progress_updates": True
                }
            },
            "system": {
                "version": "1.0.0",
                "created": datetime.now().isoformat(),
                "last_modified": datetime.now().isoformat()
            }
        }
        self.settings = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    # 合并默认设置和加载的设置
                    return self._merge_settings(self.default_settings, loaded_settings)
            else:
                # 创建默认配置文件
                self.save_config(self.default_settings)
                return self.default_settings.copy()
        except Exception as e:
            print(f"⚠️ 加载配置文件失败: {e}")
            return self.default_settings.copy()
    
    def save_config(self, settings: Optional[Dict[str, Any]] = None) -> bool:
        """保存配置文件"""
        try:
            if settings is None:
                settings = self.settings
            
            # 更新最后修改时间
            settings["system"]["last_modified"] = datetime.now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
            return False
    
    def _merge_settings(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """合并默认设置和加载的设置"""
        result = default.copy()
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_settings(result[key], value)
            else:
                result[key] = value
        return result
    
    # 自动处理控制方法
    def is_auto_processing_enabled(self) -> bool:
        """检查自动处理是否启用"""
        return self.settings["auto_processing"]["enabled"]
    
    def enable_auto_processing(self) -> bool:
        """启用自动处理"""
        self.settings["auto_processing"]["enabled"] = True
        return self.save_config()
    
    def disable_auto_processing(self) -> bool:
        """禁用自动处理"""
        self.settings["auto_processing"]["enabled"] = False
        return self.save_config()
    
    def get_processing_mode(self) -> str:
        """获取处理模式"""
        return self.settings["auto_processing"]["mode"]
    
    def set_processing_mode(self, mode: str) -> bool:
        """设置处理模式"""
        valid_modes = ["auto", "manual", "scheduled"]
        if mode in valid_modes:
            self.settings["auto_processing"]["mode"] = mode
            return self.save_config()
        return False
    
    def is_startup_auto_trigger_enabled(self) -> bool:
        """检查启动时是否自动触发"""
        return self.settings["auto_processing"]["trigger_conditions"]["startup_auto_trigger"]
    
    def set_startup_auto_trigger(self, enabled: bool) -> bool:
        """设置启动时自动触发"""
        self.settings["auto_processing"]["trigger_conditions"]["startup_auto_trigger"] = enabled
        return self.save_config()
    
    # 文件路径管理方法
    def get_file_paths(self) -> Dict[str, str]:
        """获取文件路径"""
        return self.settings["auto_processing"]["file_settings"]["file_paths"]
    
    def set_file_paths(self, file1: str = None, file2: str = None, sheet_name: str = None) -> bool:
        """设置文件路径"""
        paths = self.settings["auto_processing"]["file_settings"]["file_paths"]
        if file1 is not None:
            paths["file1"] = file1
        if file2 is not None:
            paths["file2"] = file2
        if sheet_name is not None:
            paths["sheet_name"] = sheet_name
        return self.save_config()
    
    def get_search_paths(self) -> list:
        """获取搜索路径"""
        return self.settings["auto_processing"]["file_settings"]["default_search_paths"]
    
    # 处理设置方法
    def get_processing_settings(self) -> Dict[str, Any]:
        """获取处理设置"""
        return self.settings["auto_processing"]["processing_settings"]
    
    def get_retry_attempts(self) -> int:
        """获取重试次数"""
        return self.settings["auto_processing"]["processing_settings"]["retry_attempts"]
    
    def set_retry_attempts(self, attempts: int) -> bool:
        """设置重试次数"""
        if attempts >= 0:
            self.settings["auto_processing"]["processing_settings"]["retry_attempts"] = attempts
            return self.save_config()
        return False
    
    def get_script_name(self) -> str:
        """获取脚本名称"""
        return self.settings["auto_processing"]["processing_settings"]["script_name"]
    
    # UI设置方法
    def is_debug_output_enabled(self) -> bool:
        """检查是否显示调试输出"""
        return self.settings["auto_processing"]["ui_settings"]["show_debug_output"]
    
    def set_debug_output(self, enabled: bool) -> bool:
        """设置调试输出"""
        self.settings["auto_processing"]["ui_settings"]["show_debug_output"] = enabled
        return self.save_config()
    
    # 实用方法
    def reset_to_defaults(self) -> bool:
        """重置为默认设置"""
        self.settings = self.default_settings.copy()
        return self.save_config()
    
    def export_config(self, export_file: str) -> bool:
        """导出配置"""
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"❌ 导出配置失败: {e}")
            return False
    
    def get_config_summary(self) -> str:
        """获取配置摘要"""
        auto_proc = self.settings["auto_processing"]
        return f"""
🔧 自动处理配置摘要:
  状态: {'启用' if auto_proc['enabled'] else '禁用'}
  模式: {auto_proc['mode']}
  启动触发: {'是' if auto_proc['trigger_conditions']['startup_auto_trigger'] else '否'}
  重试次数: {auto_proc['processing_settings']['retry_attempts']}
  调试输出: {'是' if auto_proc['ui_settings']['show_debug_output'] else '否'}
  脚本: {auto_proc['processing_settings']['script_name']}
"""

# 全局配置实例
auto_config = AutoProcessingConfig()

if __name__ == "__main__":
    # 测试配置管理器
    print("🔧 自动处理配置管理器测试")
    print("="*50)
    
    config = AutoProcessingConfig()
    print(config.get_config_summary())
    
    # 测试配置修改
    print("\n测试配置修改:")
    config.disable_auto_processing()
    print(f"禁用自动处理: {not config.is_auto_processing_enabled()}")
    
    config.enable_auto_processing()
    print(f"启用自动处理: {config.is_auto_processing_enabled()}")
    
    print("\n✅ 测试完成")
