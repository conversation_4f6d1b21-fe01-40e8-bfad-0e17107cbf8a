# coding: utf-8
import os
import re
import logging
from datetime import datetime

# 配置日志
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"date_matching_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger()

# 自动处理系统文件路径
system_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "自动处理系统.py")

def fix_date_extraction_function():
    """修复日期提取函数，解决SETTLEMENT文件和CHINA文件无法匹配的问题"""
    try:
        if not os.path.exists(system_path):
            logger.error(f"自动处理系统文件不存在: {system_path}")
            return False
        
        # 读取文件内容
        with open(system_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原始文件
        backup_path = system_path + ".bak"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"已备份原始文件到: {backup_path}")
        
        # 查找并替换日期提取函数
        old_function = r"def extract_date_from_filename\(filename, file_type\):[\s\S]*?return None\n"
        
        # 新的日期提取函数
        new_function = """def extract_date_from_filename(filename, file_type):
    try:
        if file_type in ["CHINA_IOT", "CHINA_ZERO"]:
            # 格式如: 110525 CHINA IOT.xlsx
            match = re.search(r'(\d{6})', filename)
            if match:
                date_str = match.group(1)
                # 假设格式为 DDMMYY
                day = date_str[:2]
                month = date_str[2:4]
                year = "20" + date_str[4:6]  # 假设是21世纪
                
                # 标准化日期格式为 YYYY-MM-DD
                return f"{year}-{month}-{day}"
        
        elif file_type in ["SETTLEMENT_IOT", "SETTLEMENT_ZERO"]:
            # 尝试多种格式匹配
            # 格式1: SETTLEMENT_REPORT_FROM_DATE_13052025_TO_13052025_zeroiot.xlsx
            match1 = re.search(r'FROM_DATE_(\d{8})_TO_(\d{8})', filename)
            if match1:
                start_date = match1.group(1)
                # 假设格式为 DDMMYYYY
                start_day = start_date[:2]
                start_month = start_date[2:4]
                start_year = start_date[4:8]
                
                # 标准化日期格式为 YYYY-MM-DD
                return f"{start_year}-{start_month}-{start_day}"
            
            # 格式2: SETTLEMENT_REPORT_FROM_08 TO 11052025_zeroiot.xlsx
            match2 = re.search(r'FROM_(\d{2})\s+TO\s+(\d{8})', filename)
            if match2:
                end_date = match2.group(2)
                # 假设格式为 DDMMYYYY
                end_day = end_date[:2]
                end_month = end_date[2:4]
                end_year = end_date[4:8]
                
                # 标准化日期格式为 YYYY-MM-DD
                return f"{end_year}-{end_month}-{end_day}"
        
        # 处理线上订单流水信息文件格式 (如: 线上订单流水信息20250514.xlsx)
        match_order = re.search(r'线上订单流水信息(\d{8})', filename)
        if match_order:
            date_str = match_order.group(1)
            # 格式为 YYYYMMDD
            year = date_str[:4]
            month = date_str[4:6]
            day = date_str[6:8]
            return f"{year}-{month}-{day}"
        
        # 如果无法匹配，尝试从文件名中提取任何可能的日期
        # 尝试匹配YYYYMMDD格式
        match_yyyymmdd = re.search(r'(\d{8})', filename)
        if match_yyyymmdd:
            date_str = match_yyyymmdd.group(1)
            # 检查是否可能是DDMMYYYY格式
            if int(date_str[4:8]) >= 2000 and int(date_str[4:8]) <= 2100:
                # 可能是DDMMYYYY格式
                day = date_str[:2]
                month = date_str[2:4]
                year = date_str[4:8]
                return f"{year}-{month}-{day}"
            elif int(date_str[:4]) >= 2000 and int(date_str[:4]) <= 2100:
                # 可能是YYYYMMDD格式
                year = date_str[:4]
                month = date_str[4:6]
                day = date_str[6:8]
                return f"{year}-{month}-{day}"
        
        # 尝试匹配DDMMYY格式
        match_ddmmyy = re.search(r'(\d{6})', filename)
        if match_ddmmyy:
            date_str = match_ddmmyy.group(1)
            day = date_str[:2]
            month = date_str[2:4]
            year = "20" + date_str[4:6]  # 假设是21世纪
            return f"{year}-{month}-{day}"
        
        # 如果无法匹配，返回None
        logger.warning(f"无法从文件名提取日期: {filename}")
        return None
    
    except Exception as e:
        logger.error(f"从文件名提取日期时出错: {filename}, 错误: {str(e)}")
        return None
"""
        
        # 替换函数
        modified_content = re.sub(old_function, new_function, content)
        
        # 修改匹配函数，使其更灵活
        old_match_function = r"def match_settlement_with_china\(settlement_file, china_file, settlement_type, config\):[\s\S]*?return False\n"
        
        # 新的匹配函数
        new_match_function = """def match_settlement_with_china(settlement_file, china_file, settlement_type, config):
    try:
        # 提取日期
        settlement_date = extract_date_from_filename(settlement_file, settlement_type)
        
        # 检查china_file是否是线上订单流水信息文件
        if "线上订单流水信息" in china_file:
            china_type = "线上订单流水信息"
        else:
            china_type = "CHINA_IOT" if settlement_type == "SETTLEMENT_IOT" else "CHINA_ZERO"
        
        china_date = extract_date_from_filename(china_file, china_type)
        
        if settlement_date and china_date:
            logger.info(f"提取的日期 - SETTLEMENT: {settlement_date}, 流水/CHINA: {china_date}")
            
            # 只检查日期是否精确匹配，不允许有误差
            if settlement_date == china_date:
                logger.info(f"找到精确匹配: {settlement_file} 与 {china_file} (日期: {settlement_date})")
                return True
        
        return False
    
    except Exception as e:
        logger.error(f"匹配文件时出错: {settlement_file} 与 {china_file}, 错误: {str(e)}")
        return False
"""
        
        # 替换匹配函数
        modified_content = re.sub(old_match_function, new_match_function, modified_content)
        
        # 写入修改后的内容
        with open(system_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        logger.info("已成功修复日期提取和匹配函数")
        return True
    
    except Exception as e:
        logger.error(f"修复日期提取函数时出错: {str(e)}")
        return False

def main():
    logger.info("=== 开始修复日期匹配问题 ===")
    
    if fix_date_extraction_function():
        logger.info("=== 日期匹配问题修复成功 ===")
    else:
        logger.error("=== 日期匹配问题修复失败 ===")

if __name__ == "__main__":
    main()