#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最小化测试脚本 - 只测试基本功能
"""

# 立即输出，不导入任何模块
print("MINIMAL_TEST_START", flush=True)

# 测试基本功能
import sys
print("SYS_IMPORTED", flush=True)

import os
print("OS_IMPORTED", flush=True)

print("PYTHON_VERSION:" + sys.version, flush=True)
print("CURRENT_DIR:" + os.getcwd(), flush=True)

# 测试参数
if len(sys.argv) > 1:
    print("ARGS:" + str(sys.argv), flush=True)
else:
    print("NO_ARGS", flush=True)

print("MINIMAL_TEST_END", flush=True)
