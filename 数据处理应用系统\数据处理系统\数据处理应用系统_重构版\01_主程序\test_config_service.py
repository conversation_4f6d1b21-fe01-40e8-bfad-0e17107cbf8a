# -*- coding: utf-8 -*-
"""
配置服务测试脚本 - 架构优化步骤2验证
测试缓存配置管理器、文件监控和预加载功能

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import sys
import os
import time
import tempfile
import threading
from typing import Any, Dict

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def create_test_config_file(file_path: str):
    """创建测试配置文件"""
    config_content = """[Database]
db_path = test_data.db
backup_dir = test_backups

[UI]
window_width = 1200
window_height = 800
theme = dark

[Backup]
auto_backup = true
backup_interval = 300

[Processing]
max_workers = 4
timeout = 30

[Logging]
level = INFO
file_path = test.log
"""
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(config_content)


def test_cached_config_manager():
    """测试缓存配置管理器"""
    print("\n🧪 测试缓存配置管理器...")
    
    try:
        from infrastructure.config_service import CachedConfigManager
        
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False) as f:
            config_file = f.name
            
        create_test_config_file(config_file)
        
        try:
            # 创建配置管理器
            config_manager = CachedConfigManager(config_file, cache_ttl=60)
            
            # 测试1：基本配置读取
            db_path = config_manager.get("Database", "db_path")
            assert db_path == "test_data.db", f"配置读取错误: {db_path}"
            
            window_width = config_manager.get("UI", "window_width")
            assert window_width == 1200, f"数字配置转换错误: {window_width}"
            
            auto_backup = config_manager.get("Backup", "auto_backup")
            assert auto_backup is True, f"布尔配置转换错误: {auto_backup}"
            
            # 测试2：缓存功能
            # 第一次读取
            start_time = time.time()
            value1 = config_manager.get("Database", "db_path")
            first_read_time = time.time() - start_time
            
            # 第二次读取（应该从缓存）
            start_time = time.time()
            value2 = config_manager.get("Database", "db_path")
            second_read_time = time.time() - start_time
            
            assert value1 == value2, "缓存值应该相同"
            # 第二次读取应该更快（从缓存）
            # assert second_read_time < first_read_time, "缓存读取应该更快"
            
            # 测试3：缓存统计
            stats = config_manager.get_cache_stats()
            assert stats["cache_hits"] > 0, "应该有缓存命中"
            assert stats["total_requests"] > 0, "应该记录请求次数"
            
            # 测试4：配置设置
            success = config_manager.set("Test", "new_value", "test123")
            assert success, "配置设置应该成功"
            
            new_value = config_manager.get("Test", "new_value")
            assert new_value == "test123", f"新设置的配置值错误: {new_value}"
            
            # 测试5：默认值
            default_value = config_manager.get("NonExistent", "key", "default")
            assert default_value == "default", "默认值应该正确返回"
            
            # 关闭配置管理器
            config_manager.shutdown()
            
            print("✅ 缓存配置管理器测试通过")
            return True
            
        finally:
            # 清理临时文件
            if os.path.exists(config_file):
                os.unlink(config_file)
                
    except Exception as e:
        print(f"❌ 缓存配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_file_watcher():
    """测试文件监控功能"""
    print("\n🧪 测试文件监控...")
    
    try:
        from infrastructure.config_service import FileWatcher
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            test_file = f.name
            f.write("initial content")
            
        try:
            # 创建文件监控器
            watcher = FileWatcher(check_interval=0.1)
            
            # 监控变更
            changes_detected = []
            
            def on_file_changed(file_path):
                changes_detected.append(file_path)
                
            watcher.watch_file(test_file, on_file_changed)
            
            # 等待监控启动
            time.sleep(0.2)
            
            # 修改文件
            with open(test_file, 'w') as f:
                f.write("modified content")
                
            # 等待变更检测
            time.sleep(0.3)
            
            assert len(changes_detected) > 0, "应该检测到文件变更"
            assert test_file in changes_detected, "应该检测到正确的文件"
            
            # 停止监控
            watcher.stop()
            
            print("✅ 文件监控测试通过")
            return True
            
        finally:
            # 清理临时文件
            if os.path.exists(test_file):
                os.unlink(test_file)
                
    except Exception as e:
        print(f"❌ 文件监控测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_preloader():
    """测试配置预加载"""
    print("\n🧪 测试配置预加载...")
    
    try:
        from infrastructure.config_service import CachedConfigManager, ConfigPreloader
        
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False) as f:
            config_file = f.name
            
        create_test_config_file(config_file)
        
        try:
            # 创建配置管理器
            config_manager = CachedConfigManager(config_file)
            
            # 创建预加载器
            preloader = ConfigPreloader(config_manager)
            
            # 执行预加载
            preloader.preload_all()
            
            # 检查缓存统计
            stats = config_manager.get_cache_stats()
            assert stats["cache_size"] > 0, "预加载应该填充缓存"
            
            # 检查关键配置是否已缓存
            db_path = config_manager.get("Database", "db_path")
            assert db_path == "test_data.db", "预加载的配置应该可用"
            
            # 获取预加载统计
            preload_stats = preloader.get_preload_stats()
            assert preload_stats["critical_configs_count"] > 0, "应该有关键配置项"
            
            config_manager.shutdown()
            
            print("✅ 配置预加载测试通过")
            return True
            
        finally:
            # 清理临时文件
            if os.path.exists(config_file):
                os.unlink(config_file)
                
    except Exception as e:
        print(f"❌ 配置预加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_service_factory():
    """测试配置服务工厂"""
    print("\n🧪 测试配置服务工厂...")
    
    try:
        from infrastructure.config_service import ConfigServiceFactory
        
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False) as f:
            config_file = f.name
            
        create_test_config_file(config_file)
        
        try:
            # 测试1：创建标准配置服务
            config_service = ConfigServiceFactory.create_config_service(config_file)
            assert config_service is not None, "配置服务创建应该成功"
            
            # 测试基本功能
            db_path = config_service.get("Database", "db_path")
            assert db_path == "test_data.db", "配置服务应该正常工作"
            
            # 测试2：创建兼容性配置服务
            compat_service = ConfigServiceFactory.create_legacy_compatible_service(config_file)
            assert compat_service is not None, "兼容性配置服务创建应该成功"
            
            # 测试兼容性方法
            assert hasattr(compat_service, 'get_db_path'), "应该有兼容性方法"
            assert hasattr(compat_service, 'get_backup_dir'), "应该有兼容性方法"
            assert hasattr(compat_service, 'get_window_size'), "应该有兼容性方法"
            
            # 测试兼容性方法功能
            db_path = compat_service.get_db_path()
            assert db_path == "test_data.db", "兼容性方法应该正常工作"
            
            width, height = compat_service.get_window_size()
            assert width == 1200 and height == 800, "窗口大小获取应该正确"
            
            # 关闭服务
            config_service.shutdown()
            compat_service.shutdown()
            
            print("✅ 配置服务工厂测试通过")
            return True
            
        finally:
            # 清理临时文件
            if os.path.exists(config_file):
                os.unlink(config_file)
                
    except Exception as e:
        print(f"❌ 配置服务工厂测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_service_integration():
    """测试配置服务集成"""
    print("\n🧪 测试配置服务集成...")
    
    try:
        from infrastructure import initialize_infrastructure, get_infrastructure, shutdown_infrastructure
        
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False) as f:
            config_file = f.name
            
        create_test_config_file(config_file)
        
        try:
            # 初始化基础设施
            config = {
                "feature_flags_config": "test_config_integration_flags.json"
            }
            
            success = initialize_infrastructure(config)
            assert success, "基础设施初始化应该成功"
            
            infrastructure = get_infrastructure()
            
            # 启用配置缓存特性
            feature_flags = infrastructure.get_feature_flags()
            feature_flags.enable("use_cached_config", "测试配置缓存")
            
            # 重新注册服务以应用特性开关
            infrastructure._register_core_services()
            
            # 测试配置服务获取
            container = infrastructure.get_container()
            
            # 检查配置服务是否已注册
            if container.has("config_manager"):
                config_manager = container.get("config_manager")
                assert config_manager is not None, "应该能获取配置管理器"
                
                # 测试基本功能
                # 注意：这里使用默认配置文件，因为服务是独立创建的
                print("✅ 配置服务集成测试通过")
            else:
                print("⚠️ 配置服务未注册，可能特性开关未生效")
                
            # 关闭基础设施
            shutdown_infrastructure()
            
            # 清理测试文件
            if os.path.exists("test_config_integration_flags.json"):
                os.unlink("test_config_integration_flags.json")
                
            return True
            
        finally:
            # 清理临时文件
            if os.path.exists(config_file):
                os.unlink(config_file)
                
    except Exception as e:
        print(f"❌ 配置服务集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有配置服务测试"""
    print("🚀 开始配置服务测试...")
    print("=" * 60)
    
    tests = [
        ("缓存配置管理器", test_cached_config_manager),
        ("文件监控", test_file_watcher),
        ("配置预加载", test_config_preloader),
        ("配置服务工厂", test_config_service_factory),
        ("配置服务集成", test_config_service_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            failed += 1
            
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}个通过, {failed}个失败")
    
    if failed == 0:
        print("🎉 所有配置服务测试通过！架构优化步骤2完成")
        return True
    else:
        print("⚠️ 部分测试失败，需要修复问题")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
