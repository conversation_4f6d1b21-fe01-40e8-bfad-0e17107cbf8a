#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进版数据处理与导入应用
"""

import os
import sys
import unittest
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
import tkinter as tk

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入改进版应用的组件
from 数据处理与导入应用_改进版 import (
    SafeGUIUpdater, ConfigManager, FileManager, ProcessRunner,
    BaseTab, ProcessingTab, ImportTab, RefundTab, ImprovedDataApp
)

class TestSafeGUIUpdater(unittest.TestCase):
    """测试线程安全的GUI更新器"""
    
    def setUp(self):
        self.root = tk.Tk()
        self.root.withdraw()  # 隐藏窗口
        self.updater = SafeGUIUpdater(self.root)
    
    def tearDown(self):
        self.root.destroy()
    
    def test_safe_log(self):
        """测试线程安全的日志记录"""
        # 创建模拟的日志控件
        log_widget = tk.Text(self.root)
        self.updater.register_log_widget("test", log_widget)

        # 测试日志记录
        self.updater.safe_log("测试消息", "test")

        # 验证消息被添加到队列（在处理之前检查）
        self.assertFalse(self.updater.message_queue.empty())

        # 处理队列
        self.root.update()

        # 验证日志控件内容
        log_content = log_widget.get(1.0, tk.END)
        self.assertIn("测试消息", log_content)
    
    def test_safe_status_update(self):
        """测试线程安全的状态更新"""
        status_var = tk.StringVar()
        self.updater.register_status_var(status_var)
        
        # 测试状态更新
        self.updater.safe_status_update("测试状态")
        
        # 处理队列
        self.root.update()
        
        # 验证消息是否被添加到队列
        self.assertFalse(self.updater.message_queue.empty())

class TestConfigManager(unittest.TestCase):
    """测试配置管理器"""
    
    def setUp(self):
        # 创建临时配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.ini")
        self.config = ConfigManager(self.config_file)
    
    def tearDown(self):
        # 清理临时文件
        shutil.rmtree(self.temp_dir)
    
    def test_default_config_creation(self):
        """测试默认配置创建"""
        self.assertTrue(os.path.exists(self.config_file))
        self.assertIsNotNone(self.config.get('Database', 'db_path'))
        self.assertIsNotNone(self.config.get('Scripts', 'report_script'))
    
    def test_get_and_set(self):
        """测试配置的获取和设置"""
        # 设置配置
        self.config.set('Test', 'key', 'value')
        self.config.save_config()
        
        # 获取配置
        value = self.config.get('Test', 'key')
        self.assertEqual(value, 'value')
    
    def test_get_scripts_list(self):
        """测试获取脚本列表"""
        scripts = self.config.get_scripts_list()
        self.assertIsInstance(scripts, list)
        self.assertGreater(len(scripts), 0)

class TestFileManager(unittest.TestCase):
    """测试文件管理器"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        config_file = os.path.join(self.temp_dir, "test_config.ini")
        self.config = ConfigManager(config_file)
        self.file_manager = FileManager(self.config)
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_join_and_split_file_paths(self):
        """测试文件路径的连接和分割"""
        file_paths = ["file1.txt", "file2.txt", "file3.txt"]
        
        # 测试连接
        joined = self.file_manager.join_file_paths(file_paths)
        self.assertIsInstance(joined, str)
        
        # 测试分割
        split_paths = self.file_manager.split_file_paths(joined)
        self.assertEqual(split_paths, file_paths)
    
    def test_validate_files(self):
        """测试文件验证"""
        # 创建测试文件
        test_file = os.path.join(self.temp_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        
        # 测试存在的文件
        valid, msg = self.file_manager.validate_files([test_file])
        self.assertTrue(valid)
        self.assertEqual(msg, "")
        
        # 测试不存在的文件
        valid, msg = self.file_manager.validate_files(["nonexistent.txt"])
        self.assertFalse(valid)
        self.assertIn("文件不存在", msg)
    
    def test_temp_file_operations(self):
        """测试临时文件操作"""
        # 创建临时文件
        temp_file = self.file_manager.create_temp_file()
        self.assertTrue(temp_file.endswith('.xlsx'))
        
        # 创建实际文件
        with open(temp_file, 'w') as f:
            f.write("test")
        
        self.assertTrue(os.path.exists(temp_file))
        
        # 清理临时文件
        self.file_manager.cleanup_temp_file(temp_file)
        self.assertFalse(os.path.exists(temp_file))

class TestProcessRunner(unittest.TestCase):
    """测试进程运行器"""
    
    def setUp(self):
        self.root = tk.Tk()
        self.root.withdraw()
        self.gui_updater = SafeGUIUpdater(self.root)
        self.process_runner = ProcessRunner(self.gui_updater)
    
    def tearDown(self):
        self.root.destroy()
    
    @patch('subprocess.Popen')
    def test_run_process_success(self, mock_popen):
        """测试成功运行进程"""
        # 模拟成功的进程
        mock_process = Mock()
        mock_process.returncode = 0
        mock_process.stdout = iter(["输出行1", "输出行2"])
        mock_process.stderr = iter([])
        mock_popen.return_value = mock_process
        
        # 创建回调函数
        success_callback = Mock()
        error_callback = Mock()
        
        # 运行进程
        result = self.process_runner.run_process(
            ["python", "--version"], 
            "test",
            success_callback=success_callback,
            error_callback=error_callback
        )
        
        # 验证结果
        self.assertTrue(result)
        success_callback.assert_called_once()
        error_callback.assert_not_called()

class TestImprovedDataApp(unittest.TestCase):
    """测试改进版应用程序"""
    
    def setUp(self):
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 模拟配置文件路径
        self.original_cwd = os.getcwd()
        os.chdir(self.temp_dir)
    
    def tearDown(self):
        os.chdir(self.original_cwd)
        shutil.rmtree(self.temp_dir)
    
    def test_app_initialization(self):
        """测试应用程序初始化"""
        try:
            app = ImprovedDataApp()
            
            # 验证组件是否正确初始化
            self.assertIsInstance(app.config, ConfigManager)
            self.assertIsInstance(app.gui_updater, SafeGUIUpdater)
            self.assertIsInstance(app.file_manager, FileManager)
            self.assertIsInstance(app.process_runner, ProcessRunner)
            
            # 验证UI组件
            self.assertIsInstance(app.notebook, tk.ttk.Notebook)
            self.assertIsInstance(app.processing_tab, ProcessingTab)
            self.assertIsInstance(app.import_tab, ImportTab)
            self.assertIsInstance(app.refund_tab, RefundTab)
            
            app.destroy()
            
        except Exception as e:
            self.fail(f"应用程序初始化失败: {e}")

def run_tests():
    """运行所有测试"""
    print("🧪 开始测试改进版数据处理与导入应用...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestSafeGUIUpdater,
        TestConfigManager,
        TestFileManager,
        TestProcessRunner,
        TestImprovedDataApp
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print(f"\n📊 测试结果:")
    print(f"✅ 成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ 失败: {len(result.failures)}")
    print(f"💥 错误: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    if success:
        print("\n🎉 所有测试通过！改进版应用程序可以正常使用。")
    else:
        print("\n⚠️ 部分测试失败，请检查代码。")
    
    sys.exit(0 if success else 1)
