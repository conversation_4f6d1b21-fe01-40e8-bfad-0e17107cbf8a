#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果脚本 - 验证导入功能是否正常工作
"""

import os
import sys
import pandas as pd

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

def test_column_filtering():
    """测试列过滤功能"""
    print("🔧 测试列过滤功能")
    print("=" * 40)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        # 创建处理器
        processor = DataImportProcessor()
        
        # 创建测试DataFrame，包含一些不存在的列
        test_df = pd.DataFrame({
            'Copartner_name': ['测试合作伙伴'],
            'Order_No': ['TEST001'],
            'Order_types': ['Normal'],
            'Order_status': ['Finished'],
            'Order_price': ['10.00'],
            'Payment': ['10.00'],
            'Order_time': ['2025-01-01 10:00:00'],
            'Equipment_ID': ['E001'],
            'Equipment_name': ['测试设备'],
            'Branch_name': ['测试分店'],
            'Payment_date': ['2025-01-01'],
            'User_name': ['测试用户'],
            'Time': ['10:00:00'],
            'Matched_Order_ID': [''],
            'OrderTime_dt': ['2025-01-01 10:00:00'],
            'Transaction_Num': ['TXN001'],
            'Import_Date': ['2025-01-01'],
            # 这些列在数据库表中不存在
            'Matched_Flag': [True],
            'Transaction ID': ['TXN001'],
            'Extra_Column': ['额外数据']
        })
        
        print(f"原始DataFrame列数: {len(test_df.columns)}")
        print(f"原始列名: {list(test_df.columns)}")
        
        # 测试过滤功能
        filtered_df = processor._filter_columns_for_table(test_df, 'ZERO_Sales')
        
        print(f"\n过滤后DataFrame列数: {len(filtered_df.columns)}")
        print(f"过滤后列名: {list(filtered_df.columns)}")
        
        # 验证结果
        expected_columns = [
            'Copartner_name', 'Order_No', 'Order_types', 'Order_status',
            'Order_price', 'Payment', 'Order_time', 'Equipment_ID',
            'Equipment_name', 'Branch_name', 'Payment_date', 'User_name',
            'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num',
            'Import_Date'
        ]
        
        missing_columns = set(expected_columns) - set(filtered_df.columns)
        extra_columns = set(filtered_df.columns) - set(expected_columns)
        
        if missing_columns:
            print(f"⚠️ 缺失的预期列: {missing_columns}")
        
        if extra_columns:
            print(f"⚠️ 意外的额外列: {extra_columns}")
        
        if not missing_columns and not extra_columns:
            print("✅ 列过滤功能正常工作")
            return True
        else:
            print("❌ 列过滤功能存在问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_import():
    """测试基本导入功能"""
    print("\n🔧 测试基本导入功能")
    print("=" * 40)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        # 创建处理器
        processor = DataImportProcessor()
        
        # 测试文件验证
        try:
            processor.validate_file("不存在的文件.xlsx")
        except Exception as e:
            print(f"✅ 文件验证正常工作（预期的错误）: {type(e).__name__}")
        
        print("✅ 基本导入功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本导入功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 测试修复效果")
    print("=" * 60)
    
    # 测试1: 列过滤功能
    test1_result = test_column_filtering()
    
    # 测试2: 基本导入功能
    test2_result = test_basic_import()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   列过滤功能: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   基本导入功能: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    overall_success = test1_result and test2_result
    print(f"   总体状态: {'✅ 修复成功' if overall_success else '❌ 需要进一步修复'}")
    
    if overall_success:
        print("\n🎉 修复效果良好，可以尝试实际的文件导入！")
        print("建议使用以下命令测试实际导入：")
        print("python data_import_optimized.py 030725\\ CHINA\\ ZERO.xlsx ZERO")
    else:
        print("\n⚠️ 仍有问题需要解决")

if __name__ == "__main__":
    main()
