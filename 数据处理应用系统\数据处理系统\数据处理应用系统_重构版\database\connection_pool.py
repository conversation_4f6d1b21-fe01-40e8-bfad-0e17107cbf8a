# -*- coding: utf-8 -*-
"""
数据库连接池管理
提供线程安全的数据库连接池，避免连接泄漏和性能问题
"""

import sqlite3
import threading
import time
from contextlib import contextmanager
from queue import Queue, Empty, Full
from typing import Optional, Dict, Any
from pathlib import Path

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.exceptions import DatabaseError, ConnectionPoolError


class DatabaseConnection:
    """数据库连接包装器"""
    
    def __init__(self, connection: sqlite3.Connection, pool: 'DatabaseConnectionPool'):
        self.connection = connection
        self.pool = pool
        self.created_at = time.time()
        self.last_used = time.time()
        self.in_use = False
        self.transaction_active = False
    
    def execute(self, sql: str, params: tuple = ()) -> sqlite3.Cursor:
        """执行SQL语句"""
        self.last_used = time.time()
        return self.connection.execute(sql, params)
    
    def executemany(self, sql: str, params_list) -> sqlite3.Cursor:
        """批量执行SQL语句"""
        self.last_used = time.time()
        return self.connection.executemany(sql, params_list)
    
    def commit(self):
        """提交事务"""
        self.connection.commit()
        self.transaction_active = False
    
    def rollback(self):
        """回滚事务"""
        self.connection.rollback()
        self.transaction_active = False
    
    def begin_transaction(self):
        """开始事务"""
        self.connection.execute("BEGIN")
        self.transaction_active = True
    
    def close(self):
        """关闭连接"""
        if self.connection:
            self.connection.close()
            self.connection = None


class DatabaseConnectionPool:
    """数据库连接池"""
    
    def __init__(self, db_path: str, max_connections: int = 10, 
                 connection_timeout: float = 30.0, max_idle_time: float = 300.0):
        """
        初始化连接池
        
        Args:
            db_path: 数据库文件路径
            max_connections: 最大连接数
            connection_timeout: 连接超时时间（秒）
            max_idle_time: 最大空闲时间（秒）
        """
        self.db_path = Path(db_path)
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.max_idle_time = max_idle_time
        
        # 连接池
        self._pool = Queue(maxsize=max_connections)
        self._active_connections: Dict[int, DatabaseConnection] = {}
        self._lock = threading.RLock()
        self._created_connections = 0
        self._closed = False
        
        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 预创建一些连接
        self._initialize_pool()
        
        # 启动清理线程
        self._start_cleanup_thread()
        
        # print(f"数据库连接池已初始化: {db_path}, 最大连接数: {max_connections}")  # 避免Unicode编码问题
    
    def _initialize_pool(self):
        """初始化连接池，预创建一些连接"""
        initial_connections = min(3, self.max_connections)
        for _ in range(initial_connections):
            try:
                conn = self._create_connection()
                self._pool.put(conn, block=False)
            except Exception as e:
                # print(f"预创建连接失败: {e}")  # 避免Unicode编码问题
                break
    
    def _create_connection(self) -> DatabaseConnection:
        """创建新的数据库连接"""
        try:
            # 创建SQLite连接
            raw_conn = sqlite3.connect(
                str(self.db_path),
                check_same_thread=False,
                timeout=self.connection_timeout,
                isolation_level=None  # 自动提交模式
            )
            
            # 设置连接属性
            raw_conn.row_factory = sqlite3.Row  # 返回字典式结果
            raw_conn.execute("PRAGMA foreign_keys = ON")  # 启用外键约束
            # 🔧 修复：使用WAL模式支持并发访问，解决数据库锁定问题
            raw_conn.execute("PRAGMA journal_mode = WAL")  # 使用WAL模式，支持并发读写
            raw_conn.execute("PRAGMA synchronous = NORMAL")  # 🔧 修复：使用NORMAL同步，平衡性能和安全
            raw_conn.execute("PRAGMA temp_store = MEMORY")  # 临时数据存储在内存中
            raw_conn.execute("PRAGMA cache_size = 10000")  # 增加缓存大小提高性能
            raw_conn.execute("PRAGMA busy_timeout = 30000")  # 🔧 新增：设置30秒忙等待超时
            raw_conn.execute("PRAGMA wal_autocheckpoint = 1000")  # 🔧 新增：WAL自动检查点
            
            # 包装连接
            conn = DatabaseConnection(raw_conn, self)
            
            with self._lock:
                self._created_connections += 1
                self._active_connections[id(conn)] = conn
            
            # print(f"创建新数据库连接: {id(conn)}")  # 避免Unicode编码问题
            return conn
            
        except Exception as e:
            raise ConnectionPoolError(f"创建数据库连接失败: {e}")
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接（上下文管理器）

        Yields:
            DatabaseConnection: 数据库连接
        """
        if self._closed:
            raise ConnectionPoolError("连接池已关闭")

        conn = None
        try:
            # 尝试从池中获取连接
            try:
                conn = self._pool.get(timeout=5.0)
                # 🔧 新增：连接健康检查
                if not self._is_connection_healthy(conn):
                    self._close_connection(conn)
                    conn = None
                    raise Empty("连接不健康，需要重新创建")
                # print(f"从池中获取连接: {id(conn)}")
            except Empty:
                # 池为空，尝试创建新连接
                with self._lock:
                    if self._created_connections < self.max_connections:
                        conn = self._create_connection()
                        # print(f"创建新连接: {id(conn)}")
                    else:
                        # 等待可用连接
                        conn = self._pool.get(timeout=self.connection_timeout)
                        # print(f"等待后获取连接: {id(conn)}")

            # 标记连接为使用中
            conn.in_use = True
            conn.last_used = time.time()

            yield conn

        except Exception as e:
            # 如果连接有事务，回滚
            if conn and conn.transaction_active:
                try:
                    conn.rollback()
                except Exception:
                    pass
            raise
        finally:
            # 归还连接到池中
            if conn:
                conn.in_use = False
                if conn.transaction_active:
                    try:
                        conn.rollback()  # 确保事务被清理
                    except Exception:
                        pass

                try:
                    self._pool.put(conn, timeout=1.0)
                    # print(f"归还连接到池: {id(conn)}")
                except Full:
                    # 池已满，关闭连接
                    self._close_connection(conn)
    
    def _is_connection_healthy(self, conn: DatabaseConnection) -> bool:
        """
        🔧 新增：检查连接是否健康

        Args:
            conn: 要检查的连接

        Returns:
            bool: 连接是否健康
        """
        try:
            # 执行简单查询测试连接
            cursor = conn.connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            return True
        except Exception:
            return False

    def _close_connection(self, conn: DatabaseConnection):
        """关闭单个连接"""
        try:
            conn.close()
            with self._lock:
                if id(conn) in self._active_connections:
                    del self._active_connections[id(conn)]
                self._created_connections -= 1
            # print(f"关闭连接: {id(conn)}")
        except Exception as e:
            print(f"关闭连接时出错: {e}")
    
    def _start_cleanup_thread(self):
        """启动清理线程，定期清理空闲连接"""
        def cleanup_worker():
            while not self._closed:
                try:
                    time.sleep(60)  # 每分钟检查一次
                    self._cleanup_idle_connections()
                except Exception as e:
                    print(f"清理线程出错: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        connections_to_close = []
        
        # 检查池中的连接
        temp_connections = []
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                if current_time - conn.last_used > self.max_idle_time:
                    connections_to_close.append(conn)
                else:
                    temp_connections.append(conn)
            except Empty:
                break
        
        # 将非空闲连接放回池中
        for conn in temp_connections:
            try:
                self._pool.put_nowait(conn)
            except Full:
                connections_to_close.append(conn)
        
        # 关闭空闲连接
        for conn in connections_to_close:
            self._close_connection(conn)
        
        if connections_to_close:
            print(f"清理了 {len(connections_to_close)} 个空闲连接")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._lock:
            return {
                'total_connections': self._created_connections,
                'active_connections': len([c for c in self._active_connections.values() if c.in_use]),
                'idle_connections': self._pool.qsize(),
                'max_connections': self.max_connections,
                'pool_closed': self._closed
            }
    
    def close(self):
        """关闭连接池"""
        if self._closed:
            return
        
        self._closed = True
        
        # 关闭池中的所有连接
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                self._close_connection(conn)
            except Empty:
                break
        
        # 关闭活跃连接
        with self._lock:
            for conn in list(self._active_connections.values()):
                self._close_connection(conn)
        
        print("数据库连接池已关闭")


# 全局连接池实例
_connection_pool: Optional[DatabaseConnectionPool] = None
_pool_lock = threading.Lock()


def initialize_connection_pool(db_path: str = None) -> DatabaseConnectionPool:
    """
    初始化全局连接池

    Args:
        db_path: 数据库路径，如果为None则尝试从配置获取

    Returns:
        DatabaseConnectionPool: 连接池实例
    """
    global _connection_pool

    with _pool_lock:
        if _connection_pool is None:
            if db_path is None:
                # 🔧 修复：从配置文件读取数据库路径，确保与主应用一致
                import os
                import configparser

                # 尝试从配置文件读取数据库路径
                config_paths = [
                    # 项目根目录的配置文件
                    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))), "config.ini"),
                    # 重构版配置文件
                    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "03_配置文件", "config.ini"),
                    # 主程序配置文件
                    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "01_主程序", "config.ini")
                ]

                db_path_found = False
                for config_path in config_paths:
                    if os.path.exists(config_path):
                        try:
                            config = configparser.ConfigParser()
                            config.read(config_path, encoding='utf-8')
                            if config.has_section('Database'):
                                db_path = config.get('Database', 'db_path', fallback=None)
                                if db_path:
                                    print(f"🔧 从配置文件读取数据库路径: {db_path}")
                                    db_path_found = True
                                    break
                        except Exception as e:
                            print(f"⚠️ 读取配置文件失败 {config_path}: {e}")

                # 如果配置文件中没有找到路径，使用默认路径
                if not db_path_found:
                    script_dir = os.path.dirname(os.path.abspath(__file__))
                    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(script_dir))))
                    db_path = os.path.join(project_root, "database", "sales_reports.db")
                    print(f"🔧 使用默认数据库路径: {db_path}")

                # 检查数据库文件是否存在
                if not os.path.exists(db_path):
                    db_dir = os.path.dirname(db_path)
                    if not os.path.exists(db_dir):
                        os.makedirs(db_dir, exist_ok=True)
                        print(f"🔧 创建数据库目录: {db_dir}")
                    print(f"ℹ️ 数据库文件不存在，将在首次使用时创建: {db_path}")

            # 使用默认配置
            db_config = {
                'connection_pool_size': 10,
                'timeout': 30.0,
                'max_idle_time': 300.0
            }
            _connection_pool = DatabaseConnectionPool(
                db_path=db_path,
                max_connections=db_config.get('connection_pool_size', 10),
                connection_timeout=db_config.get('timeout', 30.0),
                max_idle_time=db_config.get('max_idle_time', 300.0)
            )

    return _connection_pool


def get_connection():
    """
    获取数据库连接的便捷函数

    Returns:
        上下文管理器，用于获取数据库连接
    """
    global _connection_pool

    with _pool_lock:
        if _connection_pool is None:
            initialize_connection_pool()

    return _connection_pool.get_connection()


def reinitialize_connection_pool(db_path: str) -> DatabaseConnectionPool:
    """
    重新初始化全局连接池（强制使用新的数据库路径）

    Args:
        db_path: 新的数据库路径

    Returns:
        DatabaseConnectionPool: 新的连接池实例
    """
    global _connection_pool

    with _pool_lock:
        # 🔧 修复：确保完全关闭现有连接池
        if _connection_pool:
            try:
                _connection_pool.close()
                # 等待一小段时间确保所有连接都关闭
                import time
                time.sleep(0.2)
            except Exception as e:
                print(f"关闭连接池时出错: {e}")
            finally:
                _connection_pool = None

        # 🔧 修复：验证数据库文件存在
        import os
        if not os.path.exists(db_path):
            raise FileNotFoundError(f"数据库文件不存在: {db_path}")

        # 创建新的连接池
        db_config = {
            'connection_pool_size': 10,
            'timeout': 30.0,
            'max_idle_time': 300.0
        }
        _connection_pool = DatabaseConnectionPool(
            db_path=db_path,
            max_connections=db_config.get('connection_pool_size', 10),
            connection_timeout=db_config.get('timeout', 30.0),
            max_idle_time=db_config.get('max_idle_time', 300.0)
        )
        print(f"🔧 连接池已重新初始化，数据库路径: {db_path}")

    return _connection_pool


def get_connection_pool(db_path: str = None) -> Optional[DatabaseConnectionPool]:
    """
    获取全局连接池实例

    Args:
        db_path: 数据库路径，如果为None则使用当前连接池

    Returns:
        DatabaseConnectionPool: 连接池实例，如果不存在则返回None
    """
    global _connection_pool

    with _pool_lock:
        if _connection_pool is None and db_path:
            # 如果连接池不存在且提供了路径，则初始化
            initialize_connection_pool(db_path)
        return _connection_pool


def close_connection_pool():
    """关闭全局连接池"""
    global _connection_pool

    with _pool_lock:
        if _connection_pool:
            try:
                _connection_pool.close()
                print("🔧 全局连接池已关闭")
            except Exception as e:
                print(f"关闭连接池时出错: {e}")
            finally:
                _connection_pool = None


def close_all_connections():
    """🔧 修复：关闭所有数据库连接的便捷函数"""
    close_connection_pool()


def force_close_all_connections():
    """🔧 强制关闭所有数据库连接，用于紧急情况"""
    global _connection_pool

    with _pool_lock:
        if _connection_pool:
            try:
                # 强制关闭所有活跃连接
                with _connection_pool._lock:
                    for conn in list(_connection_pool._active_connections.values()):
                        try:
                            if conn.transaction_active:
                                conn.rollback()
                            conn.close()
                        except Exception:
                            pass
                    _connection_pool._active_connections.clear()

                # 清空连接池
                while not _connection_pool._pool.empty():
                    try:
                        conn = _connection_pool._pool.get_nowait()
                        conn.close()
                    except Exception:
                        pass

                _connection_pool._closed = True
                print("🔧 强制关闭所有数据库连接完成")

            except Exception as e:
                print(f"强制关闭连接时出错: {e}")
            finally:
                _connection_pool = None

        # 强制垃圾回收
        import gc
        gc.collect()
