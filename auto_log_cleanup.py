#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动日志清理脚本
定期运行以维护日志文件
"""

import os
import json
import shutil
import gzip
from datetime import datetime, timedelta
from pathlib import Path

def load_config():
    """加载日志轮转配置"""
    config_file = "log_rotation_config.json"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None

def compress_old_logs():
    """压缩旧日志文件"""
    config = load_config()
    if not config or not config.get('log_rotation', {}).get('compression'):
        return
    
    logs_dir = config['log_directories']['main_logs']
    if not os.path.exists(logs_dir):
        return
    
    cutoff_date = datetime.now() - timedelta(days=7)
    
    for file in os.listdir(logs_dir):
        if file.endswith('.log') and not file.endswith('.gz'):
            file_path = os.path.join(logs_dir, file)
            if os.path.isfile(file_path):
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                if file_time < cutoff_date:
                    # 压缩文件
                    gz_path = file_path + '.gz'
                    with open(file_path, 'rb') as f_in:
                        with gzip.open(gz_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    os.remove(file_path)
                    print(f"压缩日志: {file} -> {file}.gz")

def cleanup_old_logs():
    """清理过期日志"""
    config = load_config()
    if not config:
        return
    
    retention_days = config['log_rotation']['retention_days']
    cutoff_date = datetime.now() - timedelta(days=retention_days)
    
    logs_dir = config['log_directories']['main_logs']
    if os.path.exists(logs_dir):
        for file in os.listdir(logs_dir):
            file_path = os.path.join(logs_dir, file)
            if os.path.isfile(file_path):
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                if file_time < cutoff_date:
                    os.remove(file_path)
                    print(f"删除过期日志: {file}")

if __name__ == "__main__":
    print("🔄 开始日志维护...")
    compress_old_logs()
    cleanup_old_logs()
    print("✅ 日志维护完成")
