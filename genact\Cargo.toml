[package]
name = "genact"
description = "A nonsense activity generator"
version = "1.4.2"
repository = "https://github.com/svenstaro/genact"
authors = ["<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"]
license = "MIT"
readme = "README.md"
keywords = ["fake", "nonsense", "useless"]
categories = ["command-line-utilities", "command-line-interface"]
edition = "2024"

[profile.release]
lto = true
opt-level = 'z'
codegen-units = 1

# We can't set strip = true because of https://github.com/rust-lang/rust/issues/93294
strip = "debuginfo"

[dependencies]
anyhow = "1"
async-std = { version = "1", features = ["attributes"] }
async-trait = "0.1"
chrono = { version = "0.4", features = ["clock", "wasmbind"], default-features = false }
fake = { version = "4.3", features = ["chrono"] }
colorgrad = "0.7"
humansize = "2"
humantime = "2"
instant = { version = "0.1", features = ["now", "wasm-bindgen"] }
lazy_static = "1.5"
progress_string = "0.2"
getrandom = { version = "0.3", features = ["wasm_js"] }
rand = "0.9"
rand_distr = "0.5"
regex = "1.11"
sha2 = "0.10.9"
yansi = "1"

[target.'cfg(not(target_arch = "wasm32"))'.dependencies]
clap = { version = "4.5", features = ["derive", "cargo", "wrap_help"] }
clap_complete = "4"
clap_mangen = "0.2"
ctrlc = { version = "3.4", features = ["termination"] }
terminal_size = "0.4"

[target.'cfg(target_arch = "wasm32")'.dependencies]
console_error_panic_hook = "0.1"
js-sys = "0.3"
url = "2.5"
wasm-bindgen = "0.2"
wasm-bindgen-futures = "0.4"
web-sys = { version = "0.3", features = ["Window", "Location", "console"] }
