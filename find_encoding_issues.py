#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确查找编码问题的脚本
"""

import re
import os

def find_open_without_encoding(file_path):
    """查找没有指定编码的文件打开操作"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line_num, line in enumerate(lines, 1):
            # 查找open(调用
            if 'open(' in line and 'subprocess' not in line:
                # 检查是否包含encoding参数
                if 'encoding=' not in line:
                    # 排除二进制模式
                    if "'rb'" not in line and '"rb"' not in line and "'wb'" not in line and '"wb"' not in line:
                        issues.append({
                            'line': line_num,
                            'content': line.strip()
                        })
    
    except Exception as e:
        print(f"检查文件失败 {file_path}: {e}")
    
    return issues

def main():
    """主函数"""
    files_to_check = [
        "main_app.py",
        "数据导入脚本_完整版.py",
        "Refund_process 脚本.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n🔍 检查文件: {file_path}")
            issues = find_open_without_encoding(file_path)
            
            if issues:
                print(f"  ❌ 发现 {len(issues)} 个问题:")
                for issue in issues:
                    print(f"    第{issue['line']}行: {issue['content']}")
            else:
                print(f"  ✅ 未发现编码问题")
        else:
            print(f"⚠️ 文件不存在: {file_path}")

if __name__ == "__main__":
    main()
