#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立测试数据库路径记忆功能
不导入main_app.py，避免GUI启动
"""

import os
import configparser
from datetime import datetime

def test_config_db_path():
    """测试配置文件中的数据库路径"""
    print("🔍 测试配置文件中的数据库路径...")
    
    config_files = ["config.ini", "数据处理应用系统/config.ini"]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"\n📄 检查配置文件: {config_file}")
            
            config = configparser.ConfigParser()
            config.read(config_file, encoding='utf-8')
            
            if config.has_section('Database'):
                db_path = config.get('Database', 'db_path', fallback='未配置')
                print(f"  📍 数据库路径: {db_path}")
                
                # 检查路径是否存在
                if db_path != '未配置':
                    db_dir = os.path.dirname(db_path)
                    if os.path.exists(db_dir):
                        print(f"  ✅ 数据库目录存在: {db_dir}")
                    else:
                        print(f"  ❌ 数据库目录不存在: {db_dir}")
                        
                    if os.path.exists(db_path):
                        print(f"  ✅ 数据库文件存在")
                    else:
                        print(f"  ⚠️ 数据库文件不存在（首次使用时会创建）")
            else:
                print(f"  ❌ 缺少Database配置段")
        else:
            print(f"❌ 配置文件不存在: {config_file}")

def update_config_db_path(new_path):
    """更新配置文件中的数据库路径"""
    print(f"\n🔧 更新数据库路径为: {new_path}")
    
    config_file = "config.ini"
    config = configparser.ConfigParser()
    
    # 读取现有配置
    if os.path.exists(config_file):
        config.read(config_file, encoding='utf-8')
    
    # 确保Database段存在
    if not config.has_section('Database'):
        config.add_section('Database')
    
    # 备份旧路径
    old_path = config.get('Database', 'db_path', fallback='无')
    print(f"  📍 旧路径: {old_path}")
    
    # 设置新路径
    config.set('Database', 'db_path', new_path)
    
    # 保存配置
    with open(config_file, 'w', encoding='utf-8') as f:
        config.write(f)
    
    print(f"  ✅ 已更新配置文件: {config_file}")
    
    # 记录变更历史
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    history_entry = f"{timestamp} | {old_path} -> {new_path}\n"
    
    with open("db_path_history.txt", 'a', encoding='utf-8') as f:
        f.write(history_entry)
    
    print(f"  📚 已记录到历史文件")

def create_database_directory(db_path):
    """创建数据库目录"""
    db_dir = os.path.dirname(db_path)
    
    if not os.path.exists(db_dir):
        try:
            os.makedirs(db_dir, exist_ok=True)
            print(f"✅ 创建数据库目录: {db_dir}")
            return True
        except Exception as e:
            print(f"❌ 创建数据库目录失败: {e}")
            return False
    else:
        print(f"✅ 数据库目录已存在: {db_dir}")
        return True

def show_path_history():
    """显示路径变更历史"""
    print("\n📚 数据库路径变更历史:")
    
    history_file = "db_path_history.txt"
    if os.path.exists(history_file):
        with open(history_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if lines:
            print("  最近的变更记录:")
            for line in lines[-5:]:  # 显示最近5条
                print(f"    {line.strip()}")
        else:
            print("  暂无变更记录")
    else:
        print("  历史文件不存在")

def fix_hardcoded_paths():
    """修复硬编码路径问题"""
    print("\n🔧 检查和修复硬编码路径问题...")
    
    # 获取当前项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    correct_db_path = os.path.join(project_root, "database", "sales_reports.db")
    
    print(f"  📍 项目根目录: {project_root}")
    print(f"  📍 正确的数据库路径: {correct_db_path}")
    
    # 检查当前配置
    config = configparser.ConfigParser()
    config.read("config.ini", encoding='utf-8')
    
    if config.has_section('Database'):
        current_path = config.get('Database', 'db_path', fallback='')
        print(f"  📍 当前配置路径: {current_path}")
        
        # 检查是否需要修复
        if "Day Report" in current_path and "Day report 3" not in current_path:
            print("  ⚠️ 发现旧的硬编码路径，需要修复")
            update_config_db_path(correct_db_path)
            create_database_directory(correct_db_path)
        elif current_path == correct_db_path:
            print("  ✅ 数据库路径配置正确")
        else:
            print("  ℹ️ 使用自定义数据库路径")
    else:
        print("  ❌ 缺少Database配置，创建默认配置")
        update_config_db_path(correct_db_path)
        create_database_directory(correct_db_path)

def main():
    """主函数"""
    print("🚀 数据库路径记忆功能测试")
    print("=" * 60)
    
    # 1. 测试当前配置
    test_config_db_path()
    
    # 2. 显示历史记录
    show_path_history()
    
    # 3. 修复硬编码路径
    fix_hardcoded_paths()
    
    # 4. 再次测试配置
    print("\n" + "=" * 60)
    print("🔍 修复后的配置检查:")
    test_config_db_path()
    
    print("\n💡 数据库路径记忆功能说明:")
    print("  ✅ 用户选择的数据库路径会自动保存到config.ini")
    print("  ✅ 应用启动时会自动加载保存的路径")
    print("  ✅ 路径变更历史会记录到db_path_history.txt")
    print("  ✅ 支持动态路径，不再使用硬编码路径")
    print("  ✅ 自动创建数据库目录")

if __name__ == "__main__":
    main()
