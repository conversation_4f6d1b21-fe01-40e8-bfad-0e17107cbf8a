2025-06-18 17:09:04 - data_import - ERROR - ❌ 智能处理文件失败: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, 错误: DataProcessingError: 加载数据失败: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-18 17:21:36 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-19 08:40:20 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-19 08:50:56 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-19 08:57:15 - data_import - ERROR - Table IOT_Sales insert failed: DatabaseError: Batch insert failed: table IOT_Sales has no column named Copartner name
2025-06-19 08:57:20 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Copartner name
2025-06-19 09:03:36 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DatabaseError: Failed to check duplicate data: You are trying to merge on int64 and object columns for key 'Equipment_ID'. If you wish to proceed you should use pd.concat
2025-06-19 09:16:59 - data_import - ERROR - Table IOT_Sales_Close insert failed: DatabaseError: Batch insert failed: table IOT_Sales_Close has no column named Transaction_ID
2025-06-19 09:17:03 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/online order Flow20250618 (1).xlsx, error: DatabaseError: Failed to insert into table IOT_Sales_Close: DatabaseError: Batch insert failed: table IOT_Sales_Close has no column named Transaction_ID
2025-06-19 17:16:45 - data_import - ERROR - Table ZERO_Sales insert failed: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:49 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/June/ZERO/130625 CHINA ZERO.xlsx, error: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:51 - data_import - ERROR - Table ZERO_Sales insert failed: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:54 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/June/ZERO/140625 CHINA ZERO.xlsx, error: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:57 - data_import - ERROR - Table ZERO_Sales insert failed: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:17:01 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/June/ZERO/150625 CHINA ZERO.xlsx, error: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-30 15:33:19 - data_import - ERROR - Table ZERO_Sales insert failed: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-30 15:33:25 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/June/ZERO/250625 CHINA ZERO.xlsx, error: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-30 16:01:19 - data_import - ERROR - Table ZERO_Sales insert failed: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Transaction ID
2025-06-30 16:01:23 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/June/ZERO/250625 CHINA ZERO.xlsx, error: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Transaction ID
2025-07-08 15:19:51 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/ZERO/030725 CHINA ZERO.xlsx, error: DataProcessingError: Failed to load data: 'NoneType' object has no attribute 'columns'
2025-07-08 15:41:46 - data_import - ERROR - Table ZERO_Sales insert failed: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Matched_Flag
2025-07-08 15:41:51 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/ZERO/030725 CHINA ZERO.xlsx, error: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Matched_Flag
2025-07-08 16:51:00 - data_import - ERROR - Failed to process API orders: 'DataImportProcessor' object has no attribute '_get_data_for_table'
2025-07-08 16:58:22 - data_import - ERROR - Failed to process API orders: 'DataImportProcessor' object has no attribute '_get_data_for_table'
2025-07-08 17:03:54 - data_import - ERROR - Failed to process API orders: 'DataImportProcessor' object has no attribute '_get_data_for_table'
2025-07-08 17:04:01 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/030725 CHINA IOT.xlsx, error: DatabaseError: Failed to process API orders: 'DataImportProcessor' object has no attribute '_get_data_for_table'
2025-07-08 17:08:02 - data_import - ERROR - Table IOT_Sales_Close insert failed: DatabaseError: Batch insert failed: table IOT_Sales_Close has no column named source_table
2025-07-08 17:11:00 - data_import - ERROR - Table IOT_Sales_Close insert failed: DatabaseError: Batch insert failed: table IOT_Sales_Close has no column named source_table
2025-07-08 17:11:03 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/030725 CHINA IOT.xlsx, error: DatabaseError: Failed to insert into table IOT_Sales_Close: DatabaseError: Batch insert failed: table IOT_Sales_Close has no column named source_table
2025-07-09 14:58:14 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/ZERO/040725 CHINA ZERO.xlsx, error: 'AppLogger' object has no attribute 'addHandler'
2025-07-10 10:45:01 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 10:51:18 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 11:46:58 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 11:52:21 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 11:58:49 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 12:12:06 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 12:15:03 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 12:42:42 - data_import - ERROR - ❌ Transaction_Num检查失败: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 12:42:42 - data_import - ERROR - 检测缺失记录时出错: cannot reindex on an axis with duplicate labels
2025-07-10 12:42:43 - data_import - ERROR - Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:02:23 - data_import - ERROR - ❌ Transaction_Num检查失败: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 14:02:24 - data_import - ERROR - 检测缺失记录时出错: cannot reindex on an axis with duplicate labels
2025-07-10 14:02:24 - data_import - ERROR - Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:02:27 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:04:46 - data_import - ERROR - ❌ Transaction_Num检查失败: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 14:04:47 - data_import - ERROR - 检测缺失记录时出错: cannot reindex on an axis with duplicate labels
2025-07-10 14:04:47 - data_import - ERROR - Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:04:52 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:08:20 - data_import - ERROR - ❌ Transaction_Num检查失败: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 14:08:21 - data_import - ERROR - 检测缺失记录时出错: cannot reindex on an axis with duplicate labels
2025-07-10 14:08:21 - data_import - ERROR - Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:08:25 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:15:09 - data_import - ERROR - ❌ Transaction_Num检查失败: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 14:15:09 - data_import - ERROR - 检测缺失记录时出错: cannot reindex on an axis with duplicate labels
2025-07-10 14:15:09 - data_import - ERROR - Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:15:14 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:17:02 - data_import - ERROR - ❌ Transaction_Num检查失败: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 14:17:03 - data_import - ERROR - 检测缺失记录时出错: cannot reindex on an axis with duplicate labels
2025-07-10 14:17:03 - data_import - ERROR - Table IOT_Sales insert failed: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:17:07 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/050725 CHINA IOT.xlsx, error: DatabaseError: Failed to insert into table IOT_Sales: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:18:54 - data_import - ERROR - ❌ Transaction_Num检查失败: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 14:18:54 - data_import - ERROR - 检测缺失记录时出错: cannot reindex on an axis with duplicate labels
2025-07-10 14:18:55 - data_import - ERROR - Data integrity validation failed: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 14:18:55 - data_import - ERROR - 完整性验证错误: 插入记录数 (7634) 超过预期 (7404)，差异: 230 条
2025-07-10 14:18:55 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 16:31:51 - data_import - ERROR - 完整性验证错误: 插入记录数 (4741728) 超过预期 (7404)，差异: 4734324 条
2025-07-11 17:03:04 - data_import - ERROR - ❌ 未找到包含 'IOT' 标识的工作表
2025-07-11 17:03:04 - data_import - ERROR - 可用的工作表: ['Sheet0']
2025-07-11 17:03:04 - data_import - ERROR - 要求: 必须包含 'IOT' 标识的工作表才能导入
2025-07-11 17:03:11 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/June/REFUND/IOT 06 REF.xlsx, error: DataProcessingError: Failed to load data: 未找到包含 'IOT' 标识的工作表，无法开始导入。可用工作表: ['Sheet0']
2025-07-11 17:03:16 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/June/REFUND/IOT 06.xlsx, error: DatabaseError: 创建数据库备份失败: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\Users\\<USER>\\Desktop\\Day Report\\database\\backups\\backup_操作前_import_IOT_IOT_06xlsx_20250711_170313.db'
2025-07-14 09:54:38 - data_import - ERROR - 月度数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-14 10:03:43 - data_import - ERROR - 月度数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-14 10:21:18 - data_import - ERROR - 月度数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-14 10:25:12 - data_import - ERROR - 月度数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-14 10:27:11 - data_import - ERROR - 月度数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-14 10:29:10 - data_import - ERROR - 月度数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-14 10:33:10 - data_import - ERROR - 月度数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-14 10:36:20 - data_import - ERROR - 月度数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-14 10:40:47 - data_import - ERROR - 月度数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-14 11:49:38 - data_import - ERROR - 月度数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-14 11:57:12 - data_import - ERROR - 月度数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-14 12:23:36 - data_import - ERROR - 月度数据检测失败: database disk image is malformed
2025-07-14 12:23:42 - data_import - ERROR - 删除表 IOT_Sales_Close 的数据失败: database disk image is malformed
2025-07-14 12:44:57 - data_import - ERROR - 月度数据检测失败: database disk image is malformed
2025-07-14 12:53:14 - data_import - ERROR - 月度数据检测失败: database disk image is malformed
2025-07-14 14:04:20 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-14 14:24:09 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-14 14:24:14 - data_import - ERROR - 数据库损坏: IOT_Sales_Close - database disk image is malformed
2025-07-14 14:24:54 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-14 14:24:59 - data_import - ERROR - 数据库损坏: IOT_Sales_Close - database disk image is malformed
2025-07-14 14:26:19 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-14 14:26:24 - data_import - ERROR - 数据库损坏: IOT_Sales_Close - database disk image is malformed
2025-07-14 15:44:25 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-14 15:44:25 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-14 15:45:42 - data_import - ERROR - 单天数据检测失败: database disk image is malformed
2025-07-14 15:45:45 - data_import - ERROR - 数据库损坏: IOT_Sales_Close - database disk image is malformed
2025-07-14 15:45:45 - data_import - ERROR - Table IOT_Sales_Close insert failed: DatabaseError: Failed to insert data: database disk image is malformed
2025-07-14 15:45:45 - data_import - ERROR - 数据插入失败: DatabaseError: Failed to insert into table IOT_Sales_Close: DatabaseError: Failed to insert data: database disk image is malformed
2025-07-14 16:43:55 - data_import - ERROR - 单天数据检测失败: database disk image is malformed
2025-07-14 16:43:58 - data_import - ERROR - 数据库损坏: IOT_Sales_Close - database disk image is malformed
2025-07-14 16:44:07 - data_import - ERROR - Table IOT_Sales_Close insert failed: DatabaseError: Failed to insert data: database disk image is malformed
2025-07-14 16:44:07 - data_import - ERROR - 数据插入失败: DatabaseError: Failed to insert into table IOT_Sales_Close: DatabaseError: Failed to insert data: database disk image is malformed
2025-07-14 17:15:22 - data_import - ERROR - 单天数据检测失败: database disk image is malformed
2025-07-14 17:15:25 - data_import - ERROR - 数据库损坏: IOT_Sales_Close - database disk image is malformed
2025-07-14 17:15:33 - data_import - ERROR - Table IOT_Sales_Close insert failed: DatabaseError: Failed to insert data: database disk image is malformed
2025-07-14 17:15:33 - data_import - ERROR - 数据插入失败: DatabaseError: Failed to insert into table IOT_Sales_Close: DatabaseError: Failed to insert data: database disk image is malformed
2025-07-15 09:36:57 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-15 09:36:57 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-15 09:44:22 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-15 09:44:22 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-15 14:41:28 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-15 14:41:28 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-15 14:41:47 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-15 14:41:47 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-15 14:44:21 - data_import - ERROR - 单天数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-15 14:45:05 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-15 14:45:05 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-16 15:14:45 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-16 15:14:45 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-16 15:14:51 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-16 15:14:51 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-16 15:14:57 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-16 15:14:57 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-16 15:16:21 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-16 15:16:21 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-16 15:17:01 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-16 15:17:01 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-16 15:17:58 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-16 15:17:58 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-17 14:10:15 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-17 14:10:15 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-17 14:11:01 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-17 14:11:01 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-17 14:35:55 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 14:36:45 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 14:37:42 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 14:38:11 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 14:38:12 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-17 14:38:12 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-17 14:53:02 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 14:53:02 - data_import - ERROR - ❌ 未找到包含 'IOT' 标识的工作表
2025-07-17 14:53:02 - data_import - ERROR - 可用的工作表: ['Sheet0']
2025-07-17 14:53:02 - data_import - ERROR - 要求: 必须包含 'IOT' 标识的工作表才能导入
2025-07-17 14:53:02 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Downloads/05  IOT REF.xlsx, error: DataProcessingError: Failed to load data: 未找到包含 'IOT' 标识的工作表，无法开始导入。可用工作表: ['Sheet0']
2025-07-17 14:56:37 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 14:58:23 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-17 14:58:23 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-17 14:58:42 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 14:59:31 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-17 14:59:31 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-17 15:00:43 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 15:01:31 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-17 15:01:31 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-17 15:01:57 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 15:03:23 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-17 15:03:23 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-17 15:03:39 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 15:03:46 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-17 15:03:46 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-17 15:04:32 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 15:04:35 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-17 15:04:35 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-17 15:04:49 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 15:04:53 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-17 15:04:53 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-17 15:05:20 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-17 15:05:33 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-17 15:05:33 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-18 09:55:56 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-18 09:55:56 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-18 09:56:41 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-18 09:56:41 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-21 15:10:23 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-21 15:10:23 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-21 15:23:47 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-21 15:23:47 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-22 15:50:31 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-22 15:50:31 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-22 15:52:12 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-22 15:52:12 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-23 10:58:34 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-23 10:58:34 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-23 10:58:42 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-23 10:58:42 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-23 10:59:29 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-23 10:59:29 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-23 11:00:36 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-23 11:00:36 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-23 11:00:48 - data_import - ERROR - ❌ 未找到包含 'IOT' 标识的工作表
2025-07-23 11:00:48 - data_import - ERROR - 可用的工作表: ['Sheet0', 'ZERO200725', 'LOG']
2025-07-23 11:00:48 - data_import - ERROR - 要求: 必须包含 'IOT' 标识的工作表才能导入
2025-07-23 11:00:48 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/200725 CHINA IOT.xlsx, error: DataProcessingError: Failed to load data: 未找到包含 'IOT' 标识的工作表，无法开始导入。可用工作表: ['Sheet0', 'ZERO200725', 'LOG']
2025-07-23 11:02:37 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-23 11:02:37 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-24 11:27:18 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-24 11:27:18 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-24 11:27:57 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-24 11:27:57 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-24 12:18:41 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-24 12:18:41 - data_import - ERROR - ❌ 未找到包含 'APP' 标识的工作表
2025-07-24 12:18:41 - data_import - ERROR - 可用的工作表: ['IOT']
2025-07-24 12:18:41 - data_import - ERROR - 要求: 必须包含 'APP' 标识的工作表才能导入
2025-07-24 12:18:41 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/June/REFUND/4月 app sales.xlsx, error: DataProcessingError: Failed to load data: 未找到包含 'APP' 标识的工作表，无法开始导入。可用工作表: ['IOT']
2025-07-24 12:34:21 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-24 12:34:30 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-24 12:34:30 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-25 10:34:27 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-25 10:34:27 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-25 10:35:13 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-25 10:35:13 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-28 11:50:05 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-28 11:50:05 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-29 09:08:44 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-29 09:08:49 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-29 09:08:49 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-29 09:09:08 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-29 09:09:12 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-29 09:09:12 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-29 09:09:33 - data_import - ERROR - 月度数据检测失败: 'NoneType' object has no attribute 'columns'
2025-07-29 09:09:59 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-29 09:09:59 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-29 12:27:19 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-29 12:27:19 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-30 13:59:17 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-30 13:59:17 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-30 13:59:33 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-30 13:59:33 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-30 13:59:50 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-30 13:59:50 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-30 14:05:30 - data_import - ERROR - 单天数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-30 14:06:43 - data_import - ERROR - 单天数据检测失败: 'DataImportProcessor' object has no attribute '_is_gui_available'
2025-07-30 14:10:33 - data_import - ERROR - Smart file processing failed: C:/Users/<USER>/Desktop/July/IOT/260725 CHINA IOT.xlsx, error: [Errno 22] Invalid argument
2025-07-30 14:16:52 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-30 14:16:52 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-30 14:17:08 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-30 14:17:08 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-30 14:17:26 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-30 14:17:26 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-07-31 10:46:28 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-07-31 10:46:28 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-08-01 09:29:10 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-08-01 09:29:10 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
2025-08-04 10:19:42 - data_import - ERROR - Data integrity validation failed: 'file_info'
2025-08-04 10:19:42 - data_import - ERROR - 完整性验证错误: 完整性验证过程中发生错误: 'file_info'
