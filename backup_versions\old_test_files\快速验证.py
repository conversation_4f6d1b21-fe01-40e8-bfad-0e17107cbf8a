
# 快速验证脚本
import pandas as pd

def quick_test():
    """快速验证Transaction ID格式统一"""
    print("🔧 快速验证开始")
    
    # 测试数据
    test_data = [2926885694, 2926885694.0, "2926885694.0", "2926885694"]
    
    def clean_format(value):
        try:
            if value and str(value).replace('.', '').replace('-', '').isdigit():
                return str(int(float(value)))
            else:
                return str(value).strip()
        except:
            return str(value).strip()
    
    print("📊 格式统一测试:")
    for val in test_data:
        result = clean_format(val)
        print(f"   {val} -> {result}")
    
    print("✅ 如果所有结果都是'2926885694'，则修复成功！")

if __name__ == "__main__":
    quick_test()
