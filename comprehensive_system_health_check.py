#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合系统健康检查脚本
对整个数据库路径记忆功能和系统稳定性进行全面验证
"""

import os
import sys
import sqlite3
import configparser
import subprocess
from datetime import datetime
from pathlib import Path

def check_database_memory_functionality():
    """检查数据库路径记忆功能"""
    print("💾 检查数据库路径记忆功能...")
    
    # 1. 检查配置文件存在性和正确性
    config_file = "config.ini"
    if not os.path.exists(config_file):
        print("  ❌ 主配置文件不存在")
        return False
    
    try:
        config = configparser.ConfigParser()
        config.read(config_file, encoding='utf-8')
        
        if not config.has_section('Database'):
            print("  ❌ 配置文件缺少Database段")
            return False
        
        db_path = config.get('Database', 'db_path', fallback='')
        if not db_path:
            print("  ❌ 数据库路径未配置")
            return False
        
        print(f"  ✅ 配置的数据库路径: {db_path}")
        
        # 2. 检查数据库文件和目录
        if os.path.exists(db_path):
            print(f"  ✅ 数据库文件存在")
            
            # 检查数据库大小
            file_size = os.path.getsize(db_path)
            print(f"  📊 数据库文件大小: {file_size / (1024*1024):.2f} MB")
        else:
            print(f"  ⚠️ 数据库文件不存在（首次使用时会创建）")
        
        # 3. 检查数据库目录
        db_dir = os.path.dirname(db_path)
        if os.path.exists(db_dir):
            print(f"  ✅ 数据库目录存在: {db_dir}")
        else:
            print(f"  ❌ 数据库目录不存在: {db_dir}")
            return False
        
        # 4. 检查备份目录
        backup_dir = os.path.join(db_dir, "backups")
        if os.path.exists(backup_dir):
            backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.db')]
            print(f"  📦 备份文件数量: {len(backup_files)}")
        else:
            print(f"  ℹ️ 备份目录不存在（使用时会创建）")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置检查失败: {e}")
        return False

def check_all_config_files_consistency():
    """检查所有配置文件的一致性"""
    print("\n📄 检查配置文件一致性...")
    
    config_files = [
        "config.ini",
        "数据处理应用系统/config.ini",
        "数据处理应用系统/数据处理系统/数据处理应用系统_重构版/03_配置文件/config.ini",
        "数据处理应用系统/数据处理系统/数据处理应用系统_重构版/01_主程序/config.ini"
    ]
    
    db_paths = {}
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                config = configparser.ConfigParser()
                config.read(config_file, encoding='utf-8')
                
                if config.has_section('Database'):
                    db_path = config.get('Database', 'db_path', fallback='')
                    if db_path:
                        db_paths[config_file] = db_path
                        print(f"  ✅ {config_file}: {db_path}")
                    else:
                        print(f"  ⚠️ {config_file}: 数据库路径为空")
                else:
                    print(f"  ⚠️ {config_file}: 缺少Database段")
                    
            except Exception as e:
                print(f"  ❌ {config_file}: 读取失败 - {e}")
        else:
            print(f"  ℹ️ {config_file}: 文件不存在")
    
    # 检查路径一致性
    unique_paths = set(db_paths.values())
    if len(unique_paths) <= 1:
        print(f"  ✅ 所有配置文件路径一致")
        return True
    else:
        print(f"  ⚠️ 发现不一致的路径:")
        for path in unique_paths:
            print(f"    - {path}")
        return False

def check_hardcoded_paths_eliminated():
    """检查硬编码路径是否完全消除"""
    print("\n🔧 检查硬编码路径消除情况...")
    
    critical_files = [
        "main_app.py",
        "main_workflow.py",
        "数据导入脚本_完整版.py",
        "Refund_process 脚本.py",
        "数据处理应用系统/数据处理系统/数据处理应用系统_重构版/database/connection_pool.py"
    ]
    
    hardcoded_issues = 0
    
    for file_path in critical_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查旧的硬编码路径
                old_patterns = [
                    "Day Report/",
                    "Day Report\\",
                    "June/",
                    "June\\", 
                    "July/",
                    "July\\"
                ]
                
                found_old = False
                for pattern in old_patterns:
                    if pattern in content:
                        found_old = True
                        break
                
                if found_old:
                    print(f"  ❌ {file_path}: 仍包含旧的硬编码路径")
                    hardcoded_issues += 1
                else:
                    print(f"  ✅ {file_path}: 无硬编码路径")
                    
            except Exception as e:
                print(f"  ⚠️ {file_path}: 检查失败 - {e}")
        else:
            print(f"  ℹ️ {file_path}: 文件不存在")
    
    return hardcoded_issues == 0

def check_database_connection_robustness():
    """检查数据库连接的健壮性"""
    print("\n🔗 检查数据库连接健壮性...")
    
    # 获取数据库路径
    config = configparser.ConfigParser()
    config.read("config.ini", encoding='utf-8')
    
    if not config.has_section('Database'):
        print("  ❌ 无法获取数据库配置")
        return False
    
    db_path = config.get('Database', 'db_path', fallback='')
    if not db_path or not os.path.exists(db_path):
        print(f"  ❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 测试基本连接
        with sqlite3.connect(db_path, timeout=10) as conn:
            cursor = conn.cursor()
            
            # 检查WAL模式
            cursor.execute("PRAGMA journal_mode")
            journal_mode = cursor.fetchone()[0]
            print(f"  📊 日志模式: {journal_mode}")
            
            # 检查外键约束
            cursor.execute("PRAGMA foreign_keys")
            foreign_keys = cursor.fetchone()[0]
            print(f"  🔗 外键约束: {'启用' if foreign_keys else '禁用'}")
            
            # 检查数据库完整性
            cursor.execute("PRAGMA integrity_check")
            integrity = cursor.fetchone()[0]
            if integrity == 'ok':
                print(f"  ✅ 数据库完整性检查通过")
            else:
                print(f"  ❌ 数据库完整性问题: {integrity}")
                return False
            
            # 检查表数量
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            print(f"  📊 数据库表数量: {table_count}")
            
            return True
            
    except Exception as e:
        print(f"  ❌ 数据库连接测试失败: {e}")
        return False

def check_system_directories():
    """检查系统目录结构"""
    print("\n📁 检查系统目录结构...")
    
    required_dirs = [
        "database",
        "IOT", 
        "ZERO",
        "已处理",
        "需人工检查"
    ]
    
    optional_dirs = [
        "logs",
        "temp_data",
        "Refunding"
    ]
    
    all_good = True
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"  ✅ {dir_name}: 存在")
        else:
            print(f"  ⚠️ {dir_name}: 不存在（使用时会创建）")
    
    for dir_name in optional_dirs:
        if os.path.exists(dir_name):
            print(f"  ✅ {dir_name}: 存在")
        else:
            print(f"  ℹ️ {dir_name}: 不存在（可选目录）")
    
    return all_good

def generate_final_health_report(results):
    """生成最终健康报告"""
    print("\n" + "="*60)
    print("🏥 系统健康检查最终报告")
    print("="*60)
    
    checks = [
        ("数据库路径记忆功能", results.get('db_memory', False)),
        ("配置文件一致性", results.get('config_consistency', False)),
        ("硬编码路径消除", results.get('hardcoded_eliminated', False)),
        ("数据库连接健壮性", results.get('db_connection', False)),
        ("系统目录结构", results.get('system_dirs', False))
    ]
    
    passed = sum(1 for _, result in checks if result)
    total = len(checks)
    
    print(f"\n📋 健康检查结果:")
    for check_name, result in checks:
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
    
    success_rate = (passed / total) * 100
    print(f"\n🎯 系统健康评估:")
    print(f"  📊 健康度: {success_rate:.1f}% ({passed}/{total})")
    
    if success_rate == 100:
        print(f"  🎉 完美！系统完全健康，所有功能正常！")
        print(f"  ✨ 数据库路径记忆功能已完美实现！")
    elif success_rate >= 80:
        print(f"  ✅ 优秀！系统基本健康，少数问题不影响核心功能")
    else:
        print(f"  ⚠️ 需要关注！发现重要问题，建议及时修复")
    
    return success_rate == 100

def main():
    """主函数"""
    print("🚀 开始综合系统健康检查...")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"项目路径: {os.path.abspath('.')}")
    
    results = {}
    
    # 1. 检查数据库路径记忆功能
    results['db_memory'] = check_database_memory_functionality()
    
    # 2. 检查配置文件一致性
    results['config_consistency'] = check_all_config_files_consistency()
    
    # 3. 检查硬编码路径消除
    results['hardcoded_eliminated'] = check_hardcoded_paths_eliminated()
    
    # 4. 检查数据库连接健壮性
    results['db_connection'] = check_database_connection_robustness()
    
    # 5. 检查系统目录结构
    results['system_dirs'] = check_system_directories()
    
    # 6. 生成最终报告
    perfect_health = generate_final_health_report(results)
    
    if perfect_health:
        print("\n🎊 恭喜！系统完全健康，数据库路径记忆功能完美实现！")
        print("🎯 所有应用和脚本都能正确连接数据库！")
        print("💾 用户选择的数据库路径会永久记忆！")
    else:
        print("\n🔧 请根据上述报告关注发现的问题")
    
    return perfect_health

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
