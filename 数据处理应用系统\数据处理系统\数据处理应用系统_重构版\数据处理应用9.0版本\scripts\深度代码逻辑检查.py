#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度代码逻辑检查 - 检查所有潜在的逻辑错误和边界条件问题
"""

import os
import re
import sys
from pathlib import Path

def check_index_access_safety():
    """检查索引访问安全性"""
    print("🔍 检查索引访问安全性")
    print("=" * 50)
    
    issues_found = []
    
    # 检查主要脚本文件
    script_files = [
        "../01_主程序/report 模块化设计 7.0.py",
        "data_import_optimized.py",
        "refund_process_optimized.py"
    ]
    
    for script_file in script_files:
        if not os.path.exists(script_file):
            continue
            
        print(f"\n📄 检查文件: {os.path.basename(script_file)}")
        
        with open(script_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 检查 .iloc[0] 使用
        for i, line in enumerate(lines, 1):
            if '.iloc[0]' in line:
                # 检查前面几行是否有适当的边界检查
                context_lines = lines[max(0, i-5):i]
                has_empty_check = any('empty' in ctx_line for ctx_line in context_lines)
                has_len_check = any('len(' in ctx_line for ctx_line in context_lines)
                
                if not (has_empty_check or has_len_check):
                    issues_found.append({
                        'file': script_file,
                        'line': i,
                        'issue': 'Potential IndexError: .iloc[0] without boundary check',
                        'code': line.strip()
                    })
                    print(f"   ⚠️ 第{i}行: 可能的索引错误 - {line.strip()}")
                else:
                    print(f"   ✅ 第{i}行: 索引访问安全 - {line.strip()}")
        
        # 检查 .index[0] 使用
        for i, line in enumerate(lines, 1):
            if '.index[0]' in line:
                context_lines = lines[max(0, i-5):i]
                has_empty_check = any('empty' in ctx_line for ctx_line in context_lines)
                has_len_check = any('len(' in ctx_line for ctx_line in context_lines)
                
                if not (has_empty_check or has_len_check):
                    issues_found.append({
                        'file': script_file,
                        'line': i,
                        'issue': 'Potential IndexError: .index[0] without boundary check',
                        'code': line.strip()
                    })
                    print(f"   ⚠️ 第{i}行: 可能的索引错误 - {line.strip()}")
                else:
                    print(f"   ✅ 第{i}行: 索引访问安全 - {line.strip()}")
    
    return issues_found

def check_column_access_safety():
    """检查列访问安全性"""
    print("\n🔍 检查列访问安全性")
    print("=" * 50)
    
    issues_found = []
    
    script_files = [
        "../01_主程序/report 模块化设计 7.0.py",
        "data_import_optimized.py"
    ]
    
    for script_file in script_files:
        if not os.path.exists(script_file):
            continue
            
        print(f"\n📄 检查文件: {os.path.basename(script_file)}")
        
        with open(script_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 查找直接列访问模式
        column_access_pattern = r'df\[[\'"](.*?)[\'"]\]'
        matches = re.finditer(column_access_pattern, content)
        
        for match in matches:
            column_name = match.group(1)
            line_num = content[:match.start()].count('\n') + 1
            line_content = lines[line_num - 1].strip()
            
            # 检查是否有列存在性检查
            context_start = max(0, line_num - 5)
            context_lines = lines[context_start:line_num]
            
            has_column_check = any(f"'{column_name}' in" in ctx_line or f'"{column_name}" in' in ctx_line 
                                 for ctx_line in context_lines)
            
            if not has_column_check and 'columns' not in line_content:
                issues_found.append({
                    'file': script_file,
                    'line': line_num,
                    'issue': f'Potential KeyError: accessing column "{column_name}" without existence check',
                    'code': line_content
                })
                print(f"   ⚠️ 第{line_num}行: 可能的列访问错误 - {column_name}")
    
    return issues_found

def check_division_by_zero():
    """检查除零错误"""
    print("\n🔍 检查除零错误")
    print("=" * 50)
    
    issues_found = []
    
    script_files = [
        "../01_主程序/report 模块化设计 7.0.py",
        "data_import_optimized.py",
        "refund_process_optimized.py"
    ]
    
    for script_file in script_files:
        if not os.path.exists(script_file):
            continue
            
        print(f"\n📄 检查文件: {os.path.basename(script_file)}")
        
        with open(script_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for i, line in enumerate(lines, 1):
            # 检查除法操作
            if '/' in line and not '//' in line and not '/*' in line and not '*/' in line:
                # 排除注释和字符串
                if line.strip().startswith('#') or line.strip().startswith('"""') or line.strip().startswith("'''"):
                    continue
                
                # 检查是否有零检查
                context_lines = lines[max(0, i-3):i+2]
                has_zero_check = any('!= 0' in ctx_line or '> 0' in ctx_line or 'if.*len' in ctx_line 
                                   for ctx_line in context_lines)
                
                if not has_zero_check and 'len(' in line:
                    issues_found.append({
                        'file': script_file,
                        'line': i,
                        'issue': 'Potential division by zero',
                        'code': line.strip()
                    })
                    print(f"   ⚠️ 第{i}行: 可能的除零错误 - {line.strip()}")
    
    return issues_found

def check_thread_safety():
    """检查线程安全性"""
    print("\n🔍 检查线程安全性")
    print("=" * 50)
    
    issues_found = []
    
    main_app_file = "../01_主程序/数据处理与导入应用_完整版.py"
    
    if os.path.exists(main_app_file):
        print(f"📄 检查文件: {os.path.basename(main_app_file)}")
        
        with open(main_app_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 检查GUI更新是否在主线程中
        gui_update_pattern = r'\.configure\(|\.config\(|\.pack\(|\.grid\(|\.place\('
        thread_pattern = r'threading\.Thread'
        
        in_thread_function = False
        current_function = ""
        
        for i, line in enumerate(lines, 1):
            if 'def ' in line and 'thread' in line.lower():
                in_thread_function = True
                current_function = line.strip()
            elif 'def ' in line:
                in_thread_function = False
                current_function = ""
            
            if in_thread_function and re.search(gui_update_pattern, line):
                if 'root.after' not in line and 'gui_updater' not in line:
                    issues_found.append({
                        'file': main_app_file,
                        'line': i,
                        'issue': 'Potential thread safety issue: GUI update in thread without root.after',
                        'code': line.strip(),
                        'function': current_function
                    })
                    print(f"   ⚠️ 第{i}行: 可能的线程安全问题 - {line.strip()}")
    
    return issues_found

def check_resource_management():
    """检查资源管理"""
    print("\n🔍 检查资源管理")
    print("=" * 50)
    
    issues_found = []
    
    script_files = [
        "../01_主程序/数据处理与导入应用_完整版.py",
        "data_import_optimized.py",
        "refund_process_optimized.py"
    ]
    
    for script_file in script_files:
        if not os.path.exists(script_file):
            continue
            
        print(f"\n📄 检查文件: {os.path.basename(script_file)}")
        
        with open(script_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 检查文件操作是否使用with语句
        file_open_pattern = r'open\s*\('
        
        for i, line in enumerate(lines, 1):
            if re.search(file_open_pattern, line) and 'with' not in line:
                # 检查前后几行是否有with语句
                context_lines = lines[max(0, i-2):i+2]
                has_with = any('with' in ctx_line for ctx_line in context_lines)
                
                if not has_with:
                    issues_found.append({
                        'file': script_file,
                        'line': i,
                        'issue': 'File operation without with statement',
                        'code': line.strip()
                    })
                    print(f"   ⚠️ 第{i}行: 文件操作未使用with语句 - {line.strip()}")
    
    return issues_found

def generate_comprehensive_report():
    """生成全面的代码逻辑检查报告"""
    print("🔧 深度代码逻辑检查")
    print("=" * 60)
    
    all_issues = []
    
    # 执行各项检查
    all_issues.extend(check_index_access_safety())
    all_issues.extend(check_column_access_safety())
    all_issues.extend(check_division_by_zero())
    all_issues.extend(check_thread_safety())
    all_issues.extend(check_resource_management())
    
    # 生成报告
    print("\n" + "=" * 60)
    print("📋 深度代码逻辑检查报告")
    print("=" * 60)
    
    if not all_issues:
        print("🎉 代码逻辑检查结果: 优秀！")
        print("   未发现潜在的逻辑错误或边界条件问题。")
        print("   代码质量良好，可以安全运行。")
    else:
        print(f"⚠️ 发现 {len(all_issues)} 个潜在问题:")
        
        # 按文件分组显示问题
        issues_by_file = {}
        for issue in all_issues:
            file_name = os.path.basename(issue['file'])
            if file_name not in issues_by_file:
                issues_by_file[file_name] = []
            issues_by_file[file_name].append(issue)
        
        for file_name, file_issues in issues_by_file.items():
            print(f"\n📄 {file_name}:")
            for issue in file_issues:
                print(f"   第{issue['line']}行: {issue['issue']}")
                print(f"   代码: {issue['code']}")
    
    print(f"\n📊 检查统计:")
    print(f"   索引访问安全性: 已检查")
    print(f"   列访问安全性: 已检查")
    print(f"   除零错误: 已检查")
    print(f"   线程安全性: 已检查")
    print(f"   资源管理: 已检查")
    
    return len(all_issues) == 0

def main():
    """主函数"""
    try:
        is_clean = generate_comprehensive_report()
        
        print("\n" + "=" * 60)
        if is_clean:
            print("✅ 深度代码逻辑检查完成！代码质量优秀。")
        else:
            print("⚠️ 深度代码逻辑检查完成！发现一些潜在问题。")
        
        return 0 if is_clean else 1
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
