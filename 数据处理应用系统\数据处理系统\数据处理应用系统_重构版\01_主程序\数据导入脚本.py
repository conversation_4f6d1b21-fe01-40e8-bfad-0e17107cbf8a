import sys


import os
import re
import sqlite3
import shutil
import datetime
import pandas as pd
from dateutil import parser as dateparser
from datetime import timed<PERSON>ta
import tkinter as tk
from tkinter import messagebox
import traceback
from tqdm import tqdm
import json
import logging
import logging.handlers
from contextlib import contextmanager
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

# —— 配置管理 ——
def load_config():
    """加载配置文件，统一使用config.ini"""
    import configparser
    app_root = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(app_root, "config.ini")

    # 🔧 修复：统一使用INI配置文件
    if os.path.exists(config_path):
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')

        # 获取数据库路径
        db_path = config.get('Database', 'db_path', fallback=os.path.join(app_root, "database", "sales_reports.db"))
        if not os.path.isabs(db_path):
            db_path = os.path.join(app_root, db_path)

        # 获取备份路径
        backup_path = os.path.join(os.path.dirname(db_path), "backups")

        return {
            "database": {
                "path": db_path,
                "backup_enabled": config.getboolean('Backup', 'auto_backup', fallback=True),
                "backup_path": backup_path
            },
            "platforms": {
                "IOT": os.path.join(app_root, "..", "..", "Day Report", "IOT"),
                "ZERO": os.path.join(app_root, "..", "..", "Day Report", "ZERO"),
                "APP": os.path.join(app_root, "..", "..", "Day Report", "APP")
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs/import.log",
                "max_file_size": 10485760,
                "backup_count": 5,
                "console_output": True
            },
            "processing": {
                "batch_size": 1000,
                "max_file_size_mb": 100,
                "supported_extensions": [".xls", ".xlsx"],
                "required_columns": ["Order_price", "Order_time", "Equipment_ID", "Equipment_name", "Branch_name"]
            },
            "validation": {
                "price_min": 0,
                "price_max": 999999,
                "date_format": "%Y-%m-%d",
                "equipment_id_pattern": "^[A-Za-z0-9_-]+$"
            },
            "folders": {
                "processed": "已处理",
                "manual_check": "需人工检查",
                "backup": "备份"
            }
        }

    # 默认配置 - 使用相对路径
    default_config = {
        "database": {
            "path": os.path.join(app_root, "database", "sales_reports.db"),
            "backup_enabled": True,
            "backup_path": os.path.join(app_root, "database", "backups")
        },
        "platforms": {
            "IOT": os.path.join(app_root, "IOT"),
            "ZERO": os.path.join(app_root, "ZERO"),
            "APP": os.path.join(app_root, "APP")
        },
        "logging": {
            "level": "INFO",
            "file_path": "logs/import.log",
            "max_file_size": 10485760,
            "backup_count": 5,
            "console_output": True
        },
        "processing": {
            "batch_size": 1000,
            "max_file_size_mb": 100,
            "supported_extensions": [".xls", ".xlsx"],
            "required_columns": ["Order_price", "Order_time", "Equipment_ID", "Equipment_name", "Branch_name"]
        },
        "validation": {
            "price_min": 0,
            "price_max": 999999,
            "date_format": "%Y-%m-%d",
            "equipment_id_pattern": "^[A-Za-z0-9_-]+$"
        },
        "folders": {
            "processed": "已处理",
            "manual_check": "需人工检查",
            "backup": "备份"
        }
    }

    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置，确保所有必需的键都存在
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                    elif isinstance(value, dict):
                        for subkey, subvalue in value.items():
                            if subkey not in config[key]:
                                config[key][subkey] = subvalue
                return config
        else:
            # 创建默认配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
            return default_config
    except Exception as e:
        print(f"配置文件加载失败，使用默认配置: {e}")
        return default_config

# 加载全局配置
CONFIG = load_config()

# 兼容性：保持原有变量名
DEFAULT_DB_PATH = CONFIG['database']['path']
PLATFORMS = CONFIG['platforms']
LOG_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs", "import_error.log")
os.makedirs(os.path.dirname(LOG_PATH), exist_ok=True)

# —— 改进的日志系统 ——
class ImportLogger:
    """数据导入专用日志记录器 - 支持基于文件位置的日志存储"""

    def __init__(self, config: dict, file_path: str = None):
        self.config = config['logging']
        self.file_path = file_path  # 🎯 新增：导入文件路径
        self.logger = None
        self._setup_logger()

    def _setup_logger(self):
        """设置日志记录器 - 支持基于文件位置的日志存储"""
        self.logger = logging.getLogger('data_import')
        self.logger.setLevel(getattr(logging, self.config['level']))

        # 清除现有的处理器
        self.logger.handlers.clear()

        # 🎯 统一日志存储逻辑：基于文件位置创建日志
        if self.file_path and os.path.exists(self.file_path):
            # 在选择文件的目录下创建日志文件夹
            file_dir = os.path.dirname(self.file_path)
            log_dir = os.path.join(file_dir, "数据处理日志")

            # 生成基于文件位置的日志文件名
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            log_filename = f"数据导入日志_{timestamp}.txt"
            log_file_path = os.path.join(log_dir, log_filename)
        else:
            # 降级到原有的固定位置
            log_dir = os.path.dirname(self.config['file_path'])
            log_file_path = self.config['file_path']

        # 创建日志目录
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)

        # 文件处理器（带轮转）
        file_handler = logging.handlers.RotatingFileHandler(
            log_file_path,
            maxBytes=self.config['max_file_size'],
            backupCount=self.config['backup_count'],
            encoding='utf-8'
        )
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)

        # 控制台处理器
        if self.config['console_output']:
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%H:%M:%S'
            )
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)

    def info(self, message: str):
        """记录一般信息"""
        self.logger.info(message)
        # 兼容原有的log_error函数
        with open(LOG_PATH, "a", encoding="utf-8") as f:
            f.write(f"{datetime.datetime.now()} {message}\n")

    def error(self, message: str):
        """记录错误信息"""
        self.logger.error(message)
        with open(LOG_PATH, "a", encoding="utf-8") as f:
            f.write(f"{datetime.datetime.now()} ERROR: {message}\n")

    def warning(self, message: str):
        """记录警告信息"""
        self.logger.warning(message)
        with open(LOG_PATH, "a", encoding="utf-8") as f:
            f.write(f"{datetime.datetime.now()} WARNING: {message}\n")

# 全局日志实例
_logger_instance = ImportLogger(CONFIG)

def log_error(msg):
    """兼容原有的log_error函数"""
    _logger_instance.info(msg)

def get_logger():
    """获取日志实例"""
    return _logger_instance

def setup_file_based_logger(file_path: str):
    """🎯 设置基于文件位置的日志记录器"""
    global _logger_instance
    _logger_instance = ImportLogger(CONFIG, file_path)
    return _logger_instance

# —— 数据验证系统 ——
@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]

    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []

    def add_error(self, error: str):
        """添加错误"""
        self.errors.append(error)
        self.is_valid = False

    def add_warning(self, warning: str):
        """添加警告"""
        self.warnings.append(warning)

class DataValidator:
    """数据验证器"""

    def __init__(self, config: dict):
        self.config = config
        self.validation_config = config.get('validation', {})
        self.processing_config = config.get('processing', {})
        self.logger = get_logger()

    def validate_file(self, file_path: str) -> ValidationResult:
        """验证文件"""
        result = ValidationResult(True, [], [])

        # 检查文件是否存在
        if not os.path.exists(file_path):
            result.add_error(f"文件不存在: {file_path}")
            return result

        # 检查文件大小
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        max_size = self.processing_config.get('max_file_size_mb', 100)
        if file_size_mb > max_size:
            result.add_warning(f"文件大小 {file_size_mb:.1f}MB 超过建议大小 {max_size}MB")

        # 检查文件扩展名
        _, ext = os.path.splitext(file_path.lower())
        supported_exts = self.processing_config.get('supported_extensions', ['.xls', '.xlsx'])
        if ext not in supported_exts:
            result.add_error(f"不支持的文件格式: {ext}")

        return result

    def validate_dataframe(self, df: pd.DataFrame, platform: str) -> ValidationResult:
        """验证DataFrame数据"""
        result = ValidationResult(True, [], [])

        # 检查是否为空
        if df.empty:
            result.add_error("数据为空")
            return result

        # 检查必需列
        required_cols = self.processing_config.get('required_columns', [])
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            result.add_error(f"缺少必需列: {missing_cols}")

        return result

    def validate_price(self, price: Any, row_index: int) -> ValidationResult:
        """验证价格"""
        result = ValidationResult(True, [], [])

        if pd.isna(price) or price == '':
            result.add_warning(f"第 {row_index + 1} 行价格为空")
            return result

        try:
            price_float = float(str(price).replace(',', ''))
            min_price = self.validation_config.get('price_min', 0)
            max_price = self.validation_config.get('price_max', 999999)

            if price_float < min_price:
                result.add_error(f"第 {row_index + 1} 行价格 {price_float} 小于最小值 {min_price}")
            elif price_float > max_price:
                result.add_warning(f"第 {row_index + 1} 行价格 {price_float} 超过最大值 {max_price}")

        except (ValueError, TypeError):
            result.add_error(f"第 {row_index + 1} 行价格格式无效: {price}")

        return result

# 全局验证器实例
_validator_instance = DataValidator(CONFIG)

def standardize_date(date_str):
    if pd.isna(date_str) or not str(date_str).strip():
        return None
    s = str(date_str).strip()
    m = re.fullmatch(r'(\d{4})[-/.](\d{1,2})[-/.](\d{1,2})[ T](\d{1,2}):(\d{1,2}):(\d{1,2})', s)
    if m:
        y, mo, d, h, mi, sec = m.groups()
        return f"{int(y):04d}-{int(mo):02d}-{int(d):02d}"
    if s.isdigit() and len(s) >= 5:
        try:
            serial = int(s)
            if serial >= 60:
                serial -= 1
            base = datetime.datetime(1899, 12, 30)
            dt = base + timedelta(days=serial)
            if 1950 <= dt.year <= 2100:
                return dt.strftime("%Y-%m-%d")
        except Exception:
            pass
    try:
        dt = dateparser.parse(s, dayfirst=False, yearfirst=True)
        return dt.strftime("%Y-%m-%d")
    except (ValueError, OverflowError):
        return s

# —— 优化的数据库操作 ——
class DatabaseManager:
    """数据库管理器"""

    def __init__(self, config: dict):
        self.config = config
        self.db_path = config['database']['path']
        self.backup_enabled = config['database'].get('backup_enabled', False)
        self.backup_path = config['database'].get('backup_path', '')
        self.logger = get_logger()

        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        # 确保备份目录存在
        if self.backup_enabled and self.backup_path:
            os.makedirs(self.backup_path, exist_ok=True)

    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def check_duplicate_data(self, df: pd.DataFrame, platform: str) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """检查重复数据"""
        with self.get_connection() as conn:
            # 查询现有数据
            try:
                existing_df = pd.read_sql(
                    f"SELECT Order_time, Payment_date, Equipment_ID, Order_price FROM {platform}_Sales",
                    conn
                )
            except Exception:
                # 表可能不存在，返回空DataFrame
                existing_df = pd.DataFrame()

            if existing_df.empty:
                return pd.DataFrame(), pd.DataFrame(), df

            # 合并查找重复
            merged = df.merge(
                existing_df,
                on=["Order_time", "Payment_date", "Equipment_ID"],
                how="left",
                suffixes=("", "_db"),
                indicator=True
            )

            # 分类数据
            fully_duplicate = merged[
                (merged["_merge"] == "both") &
                (merged["Order_price"] == merged["Order_price_db"])
            ]

            partial_different = merged[
                (merged["_merge"] == "both") &
                (merged["Order_price"] != merged["Order_price_db"])
            ]

            new_data = merged[merged["_merge"] == "left_only"]

            # 清理辅助列
            for result_df in [fully_duplicate, partial_different, new_data]:
                if not result_df.empty:
                    cols_to_drop = [col for col in result_df.columns if col.endswith('_db') or col == '_merge']
                    result_df.drop(columns=cols_to_drop, inplace=True)

            return fully_duplicate, partial_different, new_data

    def insert_data(self, df: pd.DataFrame, platform: str, batch_size: int = 1000) -> int:
        """批量插入数据"""
        if df.empty:
            return 0

        with self.get_connection() as conn:
            cursor = conn.cursor()
            table_name = f"{platform}_Sales"

            # 获取插入前的记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count_before = cursor.fetchone()[0]

            # 分批插入数据
            total_inserted = 0
            for i in range(0, len(df), batch_size):
                batch_df = df.iloc[i:i+batch_size]
                try:
                    batch_df.to_sql(table_name, conn, if_exists='append', index=False)
                    total_inserted += len(batch_df)
                except Exception as e:
                    conn.rollback()
                    self.logger.error(f"批量插入数据失败: {e}")
                    raise

            # 验证插入结果
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count_after = cursor.fetchone()[0]
            actual_inserted = count_after - count_before

            conn.commit()
            return actual_inserted

# 全局数据库管理器实例
_db_manager_instance = DatabaseManager(CONFIG)

def check_date_exists(platform, import_date, db_path=None):
    """兼容原有函数"""
    db_path = db_path or DEFAULT_DB_PATH
    table = f"{platform}_Sales"
    try:
        with sqlite3.connect(db_path) as conn:
            cur = conn.cursor()
            cur.execute(f"SELECT COUNT(*) FROM {table} WHERE Import_Date=?", (import_date,))
            cnt = cur.fetchone()[0]
        return cnt > 0
    except Exception as e:
        log_error(f"检查日期存在性时出错: {e}")
        return False

def delete_existing_data(platform, import_date, db_path=None):
    """兼容原有函数"""
    db_path = db_path or DEFAULT_DB_PATH
    table = f"{platform}_Sales"
    try:
        with sqlite3.connect(db_path) as conn:
            cur = conn.cursor()
            cur.execute(f"DELETE FROM {table} WHERE Import_Date=?", (import_date,))
            return cur.rowcount
    except Exception as e:
        log_error(f"删除数据时出错: {e}")
        return 0

def create_tables(db_path=None):
    db_path = db_path or DEFAULT_DB_PATH
    try:
        with sqlite3.connect(db_path) as conn:
            cur = conn.cursor()
            
            # 创建IOT_Sales表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS IOT_Sales (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Copartner_name TEXT,
                Order_No TEXT,
                Order_types TEXT,
                Order_status TEXT,
                Order_price TEXT,
                Payment TEXT,
                Order_time TEXT,
                Equipment_ID TEXT,
                Equipment_name TEXT,
                Branch_name TEXT,
                Payment_date TEXT,
                User_name TEXT,
                Time TEXT,
                Matched_Order_ID TEXT,
                OrderTime_dt TEXT,
                Transaction_Num TEXT,
                Import_Date TEXT,
                Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 创建ZERO_Sales表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS ZERO_Sales (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Copartner_name TEXT,
                Order_No TEXT,
                Order_types TEXT,
                Order_status TEXT,
                Order_price TEXT,
                Payment TEXT,
                Order_time TEXT,
                Equipment_ID TEXT,
                Equipment_name TEXT,
                Branch_name TEXT,
                Payment_date TEXT,
                User_name TEXT,
                Time TEXT,
                Matched_Order_ID TEXT,
                OrderTime_dt TEXT,
                Transaction_Num TEXT,
                Import_Date TEXT,
                Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 创建APP_Sales表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS APP_Sales (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Copartner_name TEXT,
                Order_No TEXT,
                Order_types TEXT,
                Order_status TEXT,
                Order_price TEXT,
                Payment TEXT,
                Order_time TEXT,
                Equipment_ID TEXT,
                Equipment_name TEXT,
                Branch_name TEXT,
                Payment_date TEXT,
                User_name TEXT,
                Time TEXT,
                Matched_Order_ID TEXT,
                OrderTime_dt TEXT,
                Transaction_Num TEXT,
                Import_Date TEXT,
                Import_Timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # 创建导入日志表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS Import_Logs (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Platform TEXT,
                Filename TEXT,
                Sheet_Name TEXT,
                Import_Date TEXT,
                Status TEXT,
                Message TEXT,
                Log_Time TEXT
            )
            """)
            
            # 创建设备ID异常表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS Equipment_ID_Anomalies (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Equipment_ID TEXT,
                Platform TEXT,
                File_Source TEXT,
                Detection_Date TEXT,
                Notes TEXT
            )
            """)
            
            # 创建椅子序列号表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS Chair_Serial_No (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Serial_No TEXT UNIQUE,
                Equipment_ID TEXT,
                Platform TEXT,
                Registration_Date TEXT,
                Last_Update TEXT
            )
            """)
            
            conn.commit()
            log_error("数据库表结构已创建或已存在")
    except Exception as e:
        log_error(f"初始化数据库表结构时出错: {e}")


# —— 重构的数据处理函数 ——
def process_sheet_data(df: pd.DataFrame, filename: str) -> pd.DataFrame:
    """处理单个sheet的数据 - 使用智能列名匹配"""
    logger = get_logger()

    # 🔧 新增：使用智能列名匹配系统
    try:
        from intelligent_column_mapper import IntelligentColumnMapper

        # 创建智能映射器
        mapper = IntelligentColumnMapper()

        # 应用智能映射
        df_mapped, analysis = mapper.apply_intelligent_mapping(df)

        # 记录映射结果
        safe_print(f"📊 文件 {filename} 列名智能映射结果:")
        safe_print(f"  原始列数: {analysis['total_columns']}")
        safe_print(f"  识别核心列: {len(analysis['recognized_columns'])}")
        safe_print(f"  忽略列数: {len(analysis['ignored_columns'])}")
        safe_print(f"  映射置信度: {analysis['mapping_confidence']:.1%}")

        if analysis['unknown_columns']:
            safe_print(f"  ⚠️ 未知列: {analysis['unknown_columns']}")

        if analysis['missing_required']:
            safe_print(f"  ⚠️ 缺失必需列: {analysis['missing_required']}")

        df = df_mapped

    except ImportError:
        # 回退到传统列名映射
        safe_print(f"⚠️ 智能列名映射器不可用，使用传统映射方式")

        # 传统列名映射
        column_map = {
            'Copartner name': 'Copartner_name',
            'Order No.': 'Order_No',
            'Order types': 'Order_types',
            'Order status': 'Order_status',
            'Order price': 'Order_price',
            'Payment': 'Payment',
            'Order time': 'Order_time',
            'Equipment ID': 'Equipment_ID',
            'Equipment name': 'Equipment_name',
            'Branch name': 'Branch_name',
            'Payment date': 'Payment_date',
            'User name': 'User_name',
            'Time': 'Time',
            'Matched Order ID': 'Matched_Order_ID',
            'OrderTime_dt': 'OrderTime_dt',
            'Transaction Num': 'Transaction_Num',
            'Transaction_Num': 'Transaction_Num'
        }

        required_cols = CONFIG['processing']['required_columns']

        # 重命名列
        df = df.rename(columns=column_map)

        # 确保必需列存在
        for col in required_cols:
            if col not in df.columns:
                df[col] = None

    # 删除完全空白的行
    df = df.dropna(how='all')

    # 标准化日期
    df['Order_time'] = df['Order_time'].apply(standardize_date)
    if 'Payment_date' in df.columns:
        df['Payment_date'] = df['Payment_date'].apply(standardize_date)
    else:
        df['Payment_date'] = df['Order_time']

    # 标准化价格和设备ID
    for col in ['Order_price', 'Equipment_ID']:
        if col in df.columns:
            df[col] = df[col].astype(str)

    return df

def separate_api_orders(df: pd.DataFrame, filename: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """分离API订单数据"""
    logger = get_logger()

    if 'Order_types' not in df.columns:
        return df, pd.DataFrame()

    # 提取API订单
    app_df = df[df['Order_types'] == 'Api order'].copy()
    if not app_df.empty:
        safe_print(f"文件 {filename} 中发现 {len(app_df)} 条Api order类型订单，将导入到APP_Sales表")
        logger.info(f"文件 {filename} 中发现 {len(app_df)} 条Api order类型订单，将导入到APP_Sales表")

    # 移除API订单
    regular_df = df[df['Order_types'] != 'Api order'].copy()

    return regular_df, app_df

def process_platform_data(df: pd.DataFrame, platform: str, filename: str, conn=None) -> bool:
    """处理平台数据"""
    logger = get_logger()
    db_manager = _db_manager_instance

    if df.empty:
        return True

    try:
        # 使用新的重复检测方法
        fully_dup, partial_diff, new_rows = db_manager.check_duplicate_data(df, platform)

        # 记录统计信息
        total_rows = len(df)
        repeat_count = len(fully_dup)
        safe_print(f"文件 {filename} 总数据: {total_rows}，完全重复的: {repeat_count}，部分字段不同的: {len(partial_diff)}，新增: {len(new_rows)}")
        logger.info(f"文件 {filename} 总数据: {total_rows}，完全重复的: {repeat_count}，部分字段不同的: {len(partial_diff)}，新增: {len(new_rows)}")

        unique_dates = df['Order_time'].dropna().unique()
        safe_print(f"文件 {filename} 涉及日期: {unique_dates}")
        logger.info(f"文件 {filename} 涉及日期: {unique_dates}")

        # 只导入新增数据
        if not new_rows.empty:
            batch_size = CONFIG['processing']['batch_size']
            inserted_count = db_manager.insert_data(new_rows, platform, batch_size)
            safe_print(f"实际写入数据库 {inserted_count} 条")
            logger.info(f"实际写入数据库 {inserted_count} 条")
        else:
            safe_print(f"无新增数据写入数据库。")
            logger.info(f"无新增数据写入数据库。")

        return True

    except Exception as e:
        logger.error(f"处理平台数据失败: {e}")
        return False

def import_excel(file_path, platform, db_path=None):
    """重构后的Excel导入函数"""
    db_path = db_path or DEFAULT_DB_PATH
    filename = os.path.basename(file_path)
    logger = get_logger()
    validator = _validator_instance

    # 验证文件
    file_validation = validator.validate_file(file_path)
    if not file_validation.is_valid:
        error_msg = f"{filename} 文件验证失败: {'; '.join(file_validation.errors)}"
        logger.error(error_msg)
        return False, error_msg

    # 记录警告
    if file_validation.warnings:
        for warning in file_validation.warnings:
            logger.warning(f"{filename}: {warning}")

    try:
        # 🔧 资源管理修复：使用with语句确保ExcelFile正确关闭
        with pd.ExcelFile(file_path) as excel_file:
            sheets = excel_file.sheet_names
    except Exception as e:
        error_msg = f"{filename} 读取sheet失败: {e}"
        logger.error(error_msg)
        return False, error_msg

    valid = [s for s in sheets if s.startswith(platform)]
    if not valid:
        error_msg = f"未找到 {platform} 前缀的 sheet"
        return False, error_msg

    with sqlite3.connect(db_path) as conn:
        for sheet in valid:
            try:
                # 读取Excel数据
                df = pd.read_excel(file_path, sheet_name=sheet, engine='openpyxl')

                # 处理数据
                df = process_sheet_data(df, filename)

                # 验证数据
                data_validation = validator.validate_dataframe(df, platform)
                if not data_validation.is_valid:
                    logger.error(f"{filename} sheet {sheet} 数据验证失败: {'; '.join(data_validation.errors)}")
                    continue

                # 分离API订单
                regular_df, app_df = separate_api_orders(df, filename)

                # 处理常规平台数据
                if not process_platform_data(regular_df, platform, filename, conn):
                    return False, f"{filename} 的 sheet {sheet} 处理失败"

                # 处理APP数据
                if not app_df.empty:
                    if not process_platform_data(app_df, "APP", filename, conn):
                        logger.warning(f"APP数据处理失败，但继续处理主平台数据")

            except Exception as e:
                tb = traceback.format_exc()
                error_msg = f"{filename} 的 sheet {sheet} 导入失败: {e}\n{tb}"
                logger.error(error_msg)
                return False, f"{filename} 的 sheet {sheet} 导入失败，错误信息：{e}"

    return True, "完成"



def process_folder(path, platform, db_path=None):
    """处理文件夹中的所有Excel文件"""
    db_path = db_path or CONFIG['database']['path']
    logger = get_logger()

    # 创建子文件夹
    processed_folder = CONFIG['folders']['processed']
    manual_check_folder = CONFIG['folders']['manual_check']

    os.makedirs(os.path.join(path, processed_folder), exist_ok=True)
    os.makedirs(os.path.join(path, manual_check_folder), exist_ok=True)

    # 获取支持的文件扩展名
    supported_exts = tuple(CONFIG['processing']['supported_extensions'])
    files = [fname for fname in os.listdir(path) if fname.lower().endswith(supported_exts)]
    total = len(files)

    if total == 0:
        safe_print(f"{platform} 文件夹无待处理文件。")
        logger.info(f"{platform} 文件夹无待处理文件。")
        return 0, 0

    safe_print(f"开始导入 {platform} 平台数据，共 {total} 个文件。")
    logger.info(f"开始导入 {platform} 平台数据，共 {total} 个文件。")

    success_count = 0
    error_count = 0

    for fname in tqdm(files, desc=f"{platform} 进度", ncols=80):
        fp = os.path.join(path, fname)
        try:
            ok, msg = import_excel(fp, platform, db_path)
        except Exception as e:
            tb = traceback.format_exc()
            logger.error(f"文件 {fname} 导入主流程异常: {e}\n{tb}")
            ok = False
            msg = f"主流程异常: {e}"

        # 确定目标文件夹
        target = processed_folder if ok else manual_check_folder

        # 移动文件
        try:
            target_path = os.path.join(path, target, fname)
            shutil.move(fp, target_path)
        except Exception as e:
            logger.error(f"移动文件 {fname} 到 {target} 失败: {e}")

        # 记录结果
        if ok:
            success_count += 1
            logger.info(f"文件 {fname} 导入成功，已移动到 {target}。")
        else:
            error_count += 1
            logger.error(f"文件 {fname} 导入失败，已移动到 {target}。错误信息：{msg}")

    logger.info(f"{platform} 平台处理完成：成功 {success_count} 个，失败 {error_count} 个")
    return success_count, error_count

def main(db_path=None, file=None, platform=None):
    """重构后的主函数"""
    db_path = db_path or CONFIG['database']['path']
    logger = get_logger()

    try:
        # 初始化数据库
        create_tables(db_path)
        logger.info("数据库初始化完成")

        # 如果指定了文件和平台，则只处理该文件
        if file and platform:
            safe_print(f"开始处理单个文件: {file}, 平台: {platform}")
            logger.info(f"开始处理单个文件: {file}, 平台: {platform}")

            success, message = import_excel(file, platform, db_path)
            if success:
                safe_print(f"文件 {os.path.basename(file)} 导入成功")
                logger.info(f"文件 {os.path.basename(file)} 导入成功")
                return 0
            else:
                safe_print(f"文件 {os.path.basename(file)} 导入失败: {message}")
                logger.error(f"文件 {os.path.basename(file)} 导入失败: {message}")
                return 1
        else:
            # 批量处理文件夹
            platforms_to_process = {k: v for k, v in CONFIG['platforms'].items() if k != "APP"}
            logger.info(f"开始批量处理，平台: {list(platforms_to_process.keys())}")

            total_success = 0
            total_error = 0

            for plat, folder in platforms_to_process.items():
                logger.info(f"开始处理 {plat} 平台")
                try:
                    result = process_folder(folder, plat, db_path)
                    if isinstance(result, tuple):
                        success_count, error_count = result
                        total_success += success_count
                        total_error += error_count
                except Exception as e:
                    logger.error(f"处理 {plat} 平台时出错: {e}")
                    total_error += 1

            safe_print("全部平台数据导入处理完成。")
            safe_print("注意：APP数据已从IOT和ZERO文件中的'Api order'类型订单中提取并导入。")
            logger.info(f"批量处理完成：成功 {total_success} 个，失败 {total_error} 个")

            return 0 if total_error == 0 else 1

    except Exception as e:
        tb = traceback.format_exc()
        error_msg = f"主流程异常: {e}\n{tb}"
        safe_print(error_msg)
        logger.error(error_msg)
        return 1

# 设置标准输出编码为UTF-8，解决Windows终端中文显示问题
def safe_print(text):
    """安全打印函数，处理编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 如果遇到编码错误，尝试使用ASCII编码，忽略无法编码的字符
        print(text.encode('ascii', 'ignore').decode('ascii'))

if __name__ == "__main__":
    import argparse
    import sys
    import io


def backup_before_import(db_path, file_path=None):
    """导入前备份数据库 - 使用统一备份命名"""
    try:
        # 导入统一备份命名工具
        import sys
        import os
        script_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, script_dir)

        from backup_naming_utils import BackupNamingUtils, OperationType, PlatformType

        backup_dir = os.path.join(os.path.dirname(db_path), "backups")
        os.makedirs(backup_dir, exist_ok=True)

        # 检测平台类型
        platform_type = PlatformType.UNKNOWN
        if file_path:
            platform_type = BackupNamingUtils.detect_platform_from_file(file_path)

        # 生成标准化备份文件名
        backup_filename = BackupNamingUtils.generate_backup_filename(
            OperationType.DATA_IMPORT, platform_type
        )
        backup_path = os.path.join(backup_dir, backup_filename)

        shutil.copy2(db_path, backup_path)
        print(f"✅ 导入前备份已创建: {backup_filename}")
        return backup_path

    except Exception as e:
        print(f"❌ 导入前备份失败: {e}")
        return None

    
    # 尝试设置标准输出编码为UTF-8
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        # Python 3.7及以下版本没有reconfigure方法
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='导入销售数据到数据库')
    parser.add_argument('--file', help='要导入的Excel文件路径')
    parser.add_argument('--platform', help='平台类型 (IOT 或 ZERO)')
    parser.add_argument('--db_path', help='数据库路径')
    parser.add_argument('--setup_file_logger', help='🎯 设置基于文件位置的日志存储')

    args = parser.parse_args()

    # 🎯 如果提供了文件路径，设置基于文件位置的日志记录器
    if args.setup_file_logger:
        setup_file_based_logger(args.setup_file_logger)

    # 调用主函数
    exit_code = main(db_path=args.db_path, file=args.file, platform=args.platform)
    sys.exit(exit_code)