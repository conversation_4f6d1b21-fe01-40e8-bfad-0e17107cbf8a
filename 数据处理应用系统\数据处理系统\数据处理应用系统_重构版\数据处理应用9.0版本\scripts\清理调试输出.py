#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理调试输出脚本 - 移除所有DEBUG打印语句，优化界面显示
"""

import os
import re

def clean_debug_prints(file_path):
    """清理文件中的调试打印语句"""
    print(f"🔧 清理文件: {file_path}")
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_lines = content.count('\n')
        
        # 定义需要清理的调试打印模式
        debug_patterns = [
            # 匹配 print(f"[DEBUG] ...")
            r'^\s*print\(f"\[DEBUG\].*?\)\s*$',
            # 匹配 print(f"DEBUG: ...")
            r'^\s*print\(f"DEBUG:.*?\)\s*$',
            # 匹配 print(f"🚀 SAFE DEBUG: ...")
            r'^\s*print\(f"🚀 SAFE DEBUG:.*?\)\s*$',
            # 匹配其他调试相关的print
            r'^\s*print\(f".*?DEBUG.*?\)\s*$',
        ]
        
        lines = content.split('\n')
        cleaned_lines = []
        removed_count = 0
        
        for line in lines:
            should_remove = False
            
            # 检查是否匹配任何调试模式
            for pattern in debug_patterns:
                if re.match(pattern, line, re.MULTILINE):
                    should_remove = True
                    removed_count += 1
                    print(f"  移除: {line.strip()[:80]}...")
                    break
            
            if not should_remove:
                cleaned_lines.append(line)
        
        # 重新组合内容
        cleaned_content = '\n'.join(cleaned_lines)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        new_lines = cleaned_content.count('\n')
        
        print(f"✅ 清理完成:")
        print(f"   原始行数: {original_lines}")
        print(f"   清理后行数: {new_lines}")
        print(f"   移除调试语句: {removed_count} 条")
        
        return removed_count
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return 0

def optimize_backup_logging(file_path):
    """优化备份日志显示"""
    print(f"🔧 优化备份日志: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 优化备份相关的日志输出
        optimizations = [
            # 简化备份成功消息
            (
                r'self\.log_message\(f"✅ 备份完成: \{backup_filename\}"\)',
                'self.log_message("✅ 数据库备份完成")'
            ),
            # 简化备份验证消息
            (
                r'self\.log_message\(f"备份文件验证通过: \{backup_filename\}"\)',
                '# 备份验证通过（简化显示）'
            ),
            # 简化删除旧备份消息
            (
                r'self\.log_message\(f"已删除旧备份文件: \{.*?\}"\)',
                '# 已清理旧备份文件（简化显示）'
            ),
        ]
        
        optimized_content = content
        changes_made = 0
        
        for pattern, replacement in optimizations:
            new_content = re.sub(pattern, replacement, optimized_content)
            if new_content != optimized_content:
                changes_made += 1
                optimized_content = new_content
        
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(optimized_content)
            print(f"✅ 优化了 {changes_made} 处备份日志")
        else:
            print("ℹ️ 没有找到需要优化的备份日志")
        
        return changes_made
        
    except Exception as e:
        print(f"❌ 优化失败: {e}")
        return 0

def add_debug_control():
    """添加调试控制机制"""
    print("🔧 添加调试控制机制")
    
    debug_control_code = '''
# 🔧 调试控制 - 可以通过环境变量控制调试输出
import os
DEBUG_MODE = os.environ.get('APP_DEBUG', '').lower() in ['1', 'true', 'yes']

def debug_print(*args, **kwargs):
    """条件性调试输出"""
    if DEBUG_MODE:
        print(*args, **kwargs)
'''
    
    print("建议在应用程序开头添加以下代码:")
    print(debug_control_code)
    
    print("然后将所有 print(f\"[DEBUG] ...\") 替换为 debug_print(f\"[DEBUG] ...\")")
    print("这样可以通过设置环境变量 APP_DEBUG=1 来启用调试输出")

def main():
    """主函数"""
    print("🔧 清理调试输出，优化界面显示")
    print("=" * 60)
    
    # 要清理的文件列表
    files_to_clean = [
        "../01_主程序/数据处理与导入应用_完整版.py",
        # 可以添加更多文件
    ]
    
    total_removed = 0
    total_optimized = 0
    
    for file_path in files_to_clean:
        if os.path.exists(file_path):
            print(f"\n处理文件: {file_path}")
            
            # 清理调试打印
            removed = clean_debug_prints(file_path)
            total_removed += removed
            
            # 优化备份日志
            optimized = optimize_backup_logging(file_path)
            total_optimized += optimized
            
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 清理结果总结:")
    print(f"   移除调试语句: {total_removed} 条")
    print(f"   优化日志输出: {total_optimized} 处")
    
    if total_removed > 0 or total_optimized > 0:
        print("🎉 界面显示优化完成！")
        print("\n优化效果:")
        print("1. ✅ 移除了所有 [DEBUG] 调试输出")
        print("2. ✅ 简化了备份相关的日志消息")
        print("3. ✅ 减少了终端输出的混乱")
        print("4. ✅ 保持了重要的功能性日志")
        
        print("\n现在应用程序的输出将更加简洁:")
        print("- 只显示重要的操作信息")
        print("- 移除了技术性的调试信息")
        print("- 保持了用户需要的状态更新")
        print("- 终端输出更加清晰易读")
        
    else:
        print("ℹ️ 没有找到需要清理的内容")
    
    # 添加调试控制建议
    add_debug_control()

if __name__ == "__main__":
    main()
