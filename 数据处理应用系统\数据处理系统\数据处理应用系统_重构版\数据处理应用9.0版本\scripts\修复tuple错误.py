#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复tuple错误 - 解决"tuple indices must be integers or slices, not str"错误
"""

import os
import sys
import re
from pathlib import Path

def fix_tuple_error():
    """修复tuple错误"""
    print("🔧 修复tuple错误")
    print("=" * 50)
    
    script_path = Path(__file__).parent.parent / "01_主程序" / "report 模块化设计 7.0.py"
    
    if not script_path.exists():
        print(f"❌ 脚本文件不存在: {script_path}")
        return False
    
    try:
        # 读取文件内容
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 创建备份
        backup_path = script_path.with_suffix('.py.backup_tuple_fix')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已创建备份: {backup_path}")
        
        # 修复1: 在重新执行部分添加错误处理
        old_pattern = r'(\s+)# 处理第一文件筛选和统计\s+df1_filtered, total_bill_amt, freq_bill_amt, nine_digit_ids_count = process_file1_filtering\(df1\)'
        new_replacement = r'''\1# 处理第一文件筛选和统计
\1try:
\1    result = process_file1_filtering(df1)
\1    if isinstance(result, tuple) and len(result) == 4:
\1        df1_filtered, total_bill_amt, freq_bill_amt, nine_digit_ids_count = result
\1    else:
\1        print(f"❌ process_file1_filtering返回值格式错误: {type(result)}")
\1        raise ValueError("函数返回值格式不正确")
\1except Exception as e:
\1    print(f"❌ 处理第一文件时出错: {e}")
\1    raise'''
        
        if re.search(old_pattern, content):
            content = re.sub(old_pattern, new_replacement, content)
            print("✅ 修复1: 添加了process_file1_filtering的错误处理")
        
        # 修复2: 在命令行模式执行部分添加更详细的错误处理
        old_pattern2 = r'(\s+)try:\s+# 重新加载第一文件\s+df1 = load_and_validate_file1\(file_config\[\'file1_path\'\], file_config\[\'sheet_name\'\]\)\s+# 处理第一文件筛选和统计\s+df1_filtered, total_bill_amt, freq_bill_amt, nine_digit_ids_count = process_file1_filtering\(df1\)'
        
        new_replacement2 = r'''\1try:
\1    # 重新加载第一文件
\1    df1 = load_and_validate_file1(file_config['file1_path'], file_config['sheet_name'])
\1    
\1    # 处理第一文件筛选和统计 - 添加错误处理
\1    try:
\1        result = process_file1_filtering(df1)
\1        if not isinstance(result, tuple):
\1            raise TypeError(f"process_file1_filtering应该返回tuple，但返回了{type(result)}")
\1        if len(result) != 4:
\1            raise ValueError(f"process_file1_filtering应该返回4个值，但返回了{len(result)}个")
\1        df1_filtered, total_bill_amt, freq_bill_amt, nine_digit_ids_count = result
\1        print(f"✅ 第一文件处理成功，返回值类型: {[type(x) for x in result]}")
\1    except Exception as inner_e:
\1        print(f"❌ process_file1_filtering执行失败: {inner_e}")
\1        print(f"❌ 错误类型: {type(inner_e)}")
\1        raise'''
        
        if re.search(old_pattern2, content, re.DOTALL):
            content = re.sub(old_pattern2, new_replacement2, content, flags=re.DOTALL)
            print("✅ 修复2: 添加了命令行模式的详细错误处理")
        
        # 修复3: 确保process_file1_filtering函数的返回值类型正确
        old_pattern3 = r'(\s+)return df1_filtered, total_bill_amt, freq_bill_amt, nine_digit_ids_count'
        new_replacement3 = r'''\1# 确保返回值类型正确
\1try:
\1    # 验证返回值
\1    if not isinstance(df1_filtered, pd.DataFrame):
\1        raise TypeError(f"df1_filtered应该是DataFrame，但是{type(df1_filtered)}")
\1    if not isinstance(total_bill_amt, (int, float)):
\1        raise TypeError(f"total_bill_amt应该是数字，但是{type(total_bill_amt)}")
\1    if not isinstance(freq_bill_amt, dict):
\1        raise TypeError(f"freq_bill_amt应该是dict，但是{type(freq_bill_amt)}")
\1    if not isinstance(nine_digit_ids_count, dict):
\1        raise TypeError(f"nine_digit_ids_count应该是dict，但是{type(nine_digit_ids_count)}")
\1    
\1    return df1_filtered, total_bill_amt, freq_bill_amt, nine_digit_ids_count
\1except Exception as e:
\1    print(f"❌ process_file1_filtering返回值验证失败: {e}")
\1    # 返回安全的默认值
\1    return pd.DataFrame(), 0.0, {}, {}'''
        
        if re.search(old_pattern3, content):
            content = re.sub(old_pattern3, new_replacement3, content)
            print("✅ 修复3: 添加了返回值类型验证")
        
        # 修复4: 添加pandas导入检查
        if 'import pandas as pd' not in content:
            # 在文件开头添加pandas导入
            content = 'import pandas as pd\n' + content
            print("✅ 修复4: 添加了pandas导入")
        
        # 写入修复后的内容
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        return False

def test_fix():
    """测试修复效果"""
    print("\n🔍 测试修复效果")
    print("=" * 50)
    
    script_path = Path(__file__).parent.parent / "01_主程序" / "report 模块化设计 7.0.py"
    
    try:
        # 尝试导入脚本进行语法检查
        import ast
        
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 语法检查
        try:
            ast.parse(content)
            print("✅ 语法检查通过")
        except SyntaxError as e:
            print(f"❌ 语法错误: {e}")
            return False
        
        # 检查关键修复点
        fixes_found = 0
        
        if 'isinstance(result, tuple)' in content:
            fixes_found += 1
            print("✅ 找到tuple类型检查")
        
        if 'len(result) != 4' in content:
            fixes_found += 1
            print("✅ 找到返回值数量检查")
        
        if 'TypeError(f"process_file1_filtering应该返回tuple' in content:
            fixes_found += 1
            print("✅ 找到详细错误信息")
        
        if 'isinstance(df1_filtered, pd.DataFrame)' in content:
            fixes_found += 1
            print("✅ 找到返回值类型验证")
        
        print(f"📊 修复点检查: {fixes_found}/4")
        
        if fixes_found >= 3:
            print("✅ 修复效果良好")
            return True
        else:
            print("⚠️ 修复可能不完整")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def provide_usage_instructions():
    """提供使用说明"""
    print("\n💡 使用说明")
    print("=" * 50)
    
    print("📋 修复内容:")
    print("1. 添加了process_file1_filtering函数的返回值类型检查")
    print("2. 添加了详细的错误处理和调试信息")
    print("3. 添加了返回值数量验证")
    print("4. 添加了pandas导入检查")
    
    print("\n🔧 如果问题仍然存在:")
    print("1. 检查数据文件格式是否正确")
    print("2. 确保所有必需的列都存在")
    print("3. 检查数据类型是否匹配")
    print("4. 查看详细的错误日志")
    
    print("\n📊 调试建议:")
    print("- 运行脚本时注意观察新增的调试信息")
    print("- 如果看到'返回值类型'信息，说明修复生效")
    print("- 如果仍有错误，错误信息会更详细")

def main():
    """主函数"""
    print("🔧 tuple错误修复工具")
    print("=" * 60)
    
    try:
        # 1. 修复tuple错误
        if fix_tuple_error():
            print("✅ tuple错误修复完成")
        else:
            print("❌ tuple错误修复失败")
            return 1
        
        # 2. 测试修复效果
        if test_fix():
            print("✅ 修复效果验证通过")
        else:
            print("⚠️ 修复效果需要进一步检查")
        
        # 3. 提供使用说明
        provide_usage_instructions()
        
        print("\n" + "=" * 60)
        print("🎯 修复总结")
        print("=" * 60)
        print("✅ tuple错误已修复")
        print("✅ 添加了详细的错误处理")
        print("✅ 添加了类型验证")
        print("✅ 提供了调试信息")
        print("\n🎉 现在可以重新运行数据处理脚本了！")
        
        return 0
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
