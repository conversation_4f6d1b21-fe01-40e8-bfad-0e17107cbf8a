#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库请求队列系统
解决并发访问和数据库损坏问题
"""

import asyncio
import threading
import time
import sqlite3
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
import logging

class RequestPriority(Enum):
    """请求优先级"""
    CRITICAL = 1    # 关键操作（备份、恢复）
    HIGH = 2        # 用户交互操作（查询、验证）
    MEDIUM = 3      # 数据导入操作
    LOW = 4         # 维护操作（清理、统计）

@dataclass
class DatabaseRequest:
    """数据库请求"""
    id: str
    priority: RequestPriority
    operation: Callable
    args: tuple
    kwargs: dict
    timeout: float
    created_at: datetime
    callback: Optional[Callable] = None
    error_callback: Optional[Callable] = None

class DatabaseRequestQueue:
    """数据库请求队列管理器"""
    
    def __init__(self, db_path: str, max_concurrent: int = 1):
        self.db_path = db_path
        self.max_concurrent = max_concurrent
        self.queue = asyncio.PriorityQueue()
        self.active_requests = {}
        self.request_counter = 0
        self.is_running = False
        self.worker_task = None
        self.lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'completed_requests': 0,
            'failed_requests': 0,
            'timeout_requests': 0
        }
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
    def start(self):
        """启动队列处理器 - 🔧 临时禁用"""
        # 🔧 修复：临时禁用队列启动，防止异步问题
        self.logger.info("数据库请求队列已禁用（临时修复）")
        return
    
    def stop(self):
        """停止队列处理器"""
        self.is_running = False
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5)
        self.logger.info("数据库请求队列已停止")
    
    def _run_worker(self):
        """运行工作线程"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._process_requests())
        finally:
            loop.close()
    
    async def _process_requests(self):
        """处理请求队列"""
        while self.is_running:
            try:
                # 等待请求，超时1秒检查是否需要停止
                try:
                    priority, request_id, request = await asyncio.wait_for(
                        self.queue.get(), timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                # 执行请求
                await self._execute_request(request)
                
            except Exception as e:
                self.logger.error(f"处理请求时出错: {e}")
    
    async def _execute_request(self, request: DatabaseRequest):
        """执行单个请求"""
        start_time = time.time()
        
        try:
            # 记录开始执行
            self.active_requests[request.id] = request
            self.logger.debug(f"开始执行请求: {request.id}")
            
            # 执行操作（在线程池中执行以避免阻塞）
            loop = asyncio.get_event_loop()
            result = await asyncio.wait_for(
                loop.run_in_executor(None, self._safe_execute, request),
                timeout=request.timeout
            )
            
            # 执行成功回调
            if request.callback:
                try:
                    request.callback(result)
                except Exception as e:
                    self.logger.error(f"回调函数执行失败: {e}")
            
            self.stats['completed_requests'] += 1
            execution_time = time.time() - start_time
            self.logger.debug(f"请求 {request.id} 执行成功，耗时: {execution_time:.2f}秒")
            
        except asyncio.TimeoutError:
            self.stats['timeout_requests'] += 1
            self.logger.warning(f"请求 {request.id} 执行超时")
            if request.error_callback:
                request.error_callback(TimeoutError("请求执行超时"))
                
        except Exception as e:
            self.stats['failed_requests'] += 1
            self.logger.error(f"请求 {request.id} 执行失败: {e}")
            if request.error_callback:
                request.error_callback(e)
                
        finally:
            # 清理
            self.active_requests.pop(request.id, None)
    
    def _safe_execute(self, request: DatabaseRequest):
        """安全执行数据库操作"""
        try:
            # 确保数据库连接是独立的
            with sqlite3.connect(self.db_path, timeout=30) as conn:
                # 设置安全的数据库配置
                conn.execute("PRAGMA journal_mode=DELETE")
                conn.execute("PRAGMA synchronous=FULL")
                conn.execute("PRAGMA foreign_keys=ON")
                
                # 执行操作
                if hasattr(request.operation, '__self__'):
                    # 方法调用，传入连接
                    return request.operation(conn, *request.args, **request.kwargs)
                else:
                    # 函数调用
                    return request.operation(conn, *request.args, **request.kwargs)
                    
        except Exception as e:
            self.logger.error(f"数据库操作执行失败: {e}")
            raise
    
    def submit_request(self,
                      operation: Callable,
                      args: tuple = (),
                      kwargs: dict = None,
                      priority: RequestPriority = RequestPriority.MEDIUM,
                      timeout: float = 30.0,
                      callback: Optional[Callable] = None,
                      error_callback: Optional[Callable] = None) -> str:
        """提交数据库请求 - 🔧 临时禁用"""
        # 🔧 修复：临时禁用队列系统，直接抛出异常
        raise RuntimeError("数据库请求队列系统已临时禁用，请使用直接的数据库操作")
    
    def _get_event_loop(self):
        """获取事件循环"""
        if hasattr(self, 'worker_thread') and self.worker_thread.is_alive():
            # 这里需要一个更好的方法来获取工作线程的事件循环
            # 暂时使用简单的方法
            return asyncio.new_event_loop()
        return asyncio.new_event_loop()
    
    def submit_sync_request(self,
                           operation: Callable,
                           args: tuple = (),
                           kwargs: dict = None,
                           priority: RequestPriority = RequestPriority.MEDIUM,
                           timeout: float = 30.0) -> Any:
        """提交同步请求（阻塞等待结果）- 🔧 临时禁用"""
        # 🔧 修复：临时禁用队列系统，直接抛出异常
        raise RuntimeError("数据库请求队列系统已临时禁用，请使用直接的数据库操作")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'queue_size': self.queue.qsize(),
            'active_requests': len(self.active_requests),
            'is_running': self.is_running
        }
    
    def clear_queue(self):
        """清空队列"""
        while not self.queue.empty():
            try:
                self.queue.get_nowait()
            except asyncio.QueueEmpty:
                break
        self.logger.info("请求队列已清空")

# 全局队列实例
_global_queue = None
_queue_lock = threading.Lock()

def get_database_queue(db_path: str = None) -> DatabaseRequestQueue:
    """获取全局数据库队列实例 - 🔧 临时禁用"""
    # 🔧 修复：临时禁用队列系统，直接返回None
    # 这将强制所有代码使用直接的数据库操作而不是队列
    return None

def shutdown_database_queue():
    """关闭全局数据库队列"""
    global _global_queue
    
    with _queue_lock:
        if _global_queue:
            _global_queue.stop()
            _global_queue = None
