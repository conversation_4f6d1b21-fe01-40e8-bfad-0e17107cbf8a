# -*- coding: utf-8 -*-
"""
GUI更新服务 - 架构优化步骤3
实现线程安全的GUI更新，解决循环依赖问题

版本: 1.0
作者: AI Assistant
日期: 2025-01-18
"""

import tkinter as tk
import threading
import time
import queue
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import traceback
import weakref


class GUIUpdateType(Enum):
    """GUI更新类型"""
    LOG_MESSAGE = "log_message"
    PROGRESS_UPDATE = "progress_update"
    STATUS_UPDATE = "status_update"
    WIDGET_UPDATE = "widget_update"
    CUSTOM = "custom"


@dataclass
class GUIUpdateRequest:
    """GUI更新请求"""
    update_type: GUIUpdateType
    data: Any
    target_widget: Optional[str] = None
    callback: Optional[Callable] = None
    timestamp: float = field(default_factory=time.time)
    priority: int = 0  # 0=normal, 1=high, 2=critical
    
    def __lt__(self, other):
        """用于优先级队列排序"""
        return self.priority > other.priority  # 高优先级先处理


class GUIUpdateService:
    """
    GUI更新服务
    
    功能：
    - 线程安全的GUI更新
    - 事件驱动的消息处理
    - 优先级队列
    - 组件注册管理
    """
    
    def __init__(self, root: tk.Tk, event_bus, max_queue_size: int = 500):
        self.root = root
        self.event_bus = event_bus
        self.max_queue_size = max_queue_size
        
        # GUI更新队列（优先级队列）
        self.update_queue = queue.PriorityQueue(maxsize=max_queue_size)
        
        # 注册的GUI组件
        self.widgets: Dict[str, Any] = {}
        self.widgets_lock = threading.RLock()
        
        # 弱引用回调
        self.weak_callbacks: Dict[str, weakref.ref] = {}
        
        # 性能统计
        self.stats = {
            "updates_processed": 0,
            "updates_dropped": 0,
            "average_update_time": 0.0,
            "total_update_time": 0.0,
            "queue_high_water_mark": 0,
            "update_errors": 0
        }
        
        # 订阅事件
        self._subscribe_to_events()
        
        # 启动GUI更新处理
        self._start_gui_updater()
        
    def register_widget(self, widget_name: str, widget: Any, 
                       update_method: Optional[str] = None):
        """
        注册GUI组件
        
        Args:
            widget_name: 组件名称
            widget: GUI组件实例
            update_method: 更新方法名（可选）
        """
        with self.widgets_lock:
            self.widgets[widget_name] = {
                "widget": widget,
                "update_method": update_method,
                "registered_time": time.time()
            }
            
        print(f"✅ [GUI] 注册GUI组件: {widget_name}")
        
    def unregister_widget(self, widget_name: str):
        """注销GUI组件"""
        with self.widgets_lock:
            if widget_name in self.widgets:
                del self.widgets[widget_name]
                print(f"🔄 [GUI] 注销GUI组件: {widget_name}")
                
    def register_weak_callback(self, callback_name: str, obj, method_name: str):
        """注册弱引用回调"""
        def weak_callback(*args, **kwargs):
            if obj_ref() is not None:
                getattr(obj_ref(), method_name)(*args, **kwargs)
                
        obj_ref = weakref.ref(obj)
        self.weak_callbacks[callback_name] = obj_ref
        
    def safe_update(self, update_type: GUIUpdateType, data: Any,
                   target_widget: Optional[str] = None,
                   callback: Optional[Callable] = None,
                   priority: int = 0) -> bool:
        """
        线程安全的GUI更新
        
        Args:
            update_type: 更新类型
            data: 更新数据
            target_widget: 目标组件名称
            callback: 回调函数
            priority: 优先级 (0=normal, 1=high, 2=critical)
            
        Returns:
            bool: 是否成功加入队列
        """
        try:
            update_request = GUIUpdateRequest(
                update_type=update_type,
                data=data,
                target_widget=target_widget,
                callback=callback,
                priority=priority
            )
            
            # 尝试加入队列
            try:
                self.update_queue.put(update_request, timeout=0.1)
                
                # 更新队列高水位标记
                current_size = self.update_queue.qsize()
                if current_size > self.stats["queue_high_water_mark"]:
                    self.stats["queue_high_water_mark"] = current_size
                    
                return True
                
            except queue.Full:
                self.stats["updates_dropped"] += 1
                print(f"⚠️ [GUI] GUI更新队列已满，丢弃更新: {update_type.value}")
                return False
                
        except Exception as e:
            print(f"❌ [GUI] GUI更新请求失败: {e}")
            return False
            
    def log_message(self, message: str, tab_type: str = "general"):
        """记录日志消息（兼容旧接口）"""
        self.safe_update(
            GUIUpdateType.LOG_MESSAGE,
            {"message": message, "tab_type": tab_type},
            priority=0
        )
        
    def update_progress(self, progress: float, message: str = ""):
        """更新进度"""
        self.safe_update(
            GUIUpdateType.PROGRESS_UPDATE,
            {"progress": progress, "message": message},
            priority=1
        )
        
    def update_status(self, status: str, level: str = "info"):
        """更新状态"""
        self.safe_update(
            GUIUpdateType.STATUS_UPDATE,
            {"status": status, "level": level},
            priority=1
        )
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "queue_size": self.update_queue.qsize(),
            "registered_widgets": len(self.widgets),
            "weak_callbacks": len(self.weak_callbacks)
        }
        
    def shutdown(self):
        """关闭GUI更新服务"""
        print("🔄 [GUI] 关闭GUI更新服务...")

        try:
            # 取消事件订阅
            if self.event_bus:
                try:
                    # 注意：这里需要订阅ID，但我们在订阅时没有保存
                    # 为了简化，我们跳过取消订阅，让事件总线在关闭时自动清理
                    pass
                except:
                    pass  # 忽略取消订阅的错误

            # 清空队列
            try:
                while not self.update_queue.empty():
                    self.update_queue.get_nowait()
            except:
                pass

            # 清空注册的组件
            with self.widgets_lock:
                self.widgets.clear()

            self.weak_callbacks.clear()

            print("✅ [GUI] GUI更新服务已关闭")

        except Exception as e:
            print(f"❌ [GUI] 关闭GUI更新服务时出错: {e}")
        
    def _subscribe_to_events(self):
        """订阅事件总线事件"""
        if self.event_bus:
            # 订阅日志消息事件
            self.event_bus.subscribe("log_message", self._on_log_message)
            
            # 订阅其他GUI相关事件
            self.event_bus.subscribe("progress_update", self._on_progress_update)
            self.event_bus.subscribe("status_update", self._on_status_update)
            
            print("✅ [GUI] 订阅事件总线事件")
            
    def _on_log_message(self, event):
        """处理日志消息事件"""
        try:
            log_data = event.data if hasattr(event, 'data') else event
            self.safe_update(
                GUIUpdateType.LOG_MESSAGE,
                log_data,
                priority=0
            )
        except Exception as e:
            print(f"❌ [GUI] 处理日志消息事件失败: {e}")
            
    def _on_progress_update(self, event):
        """处理进度更新事件"""
        try:
            progress_data = event.data if hasattr(event, 'data') else event
            self.safe_update(
                GUIUpdateType.PROGRESS_UPDATE,
                progress_data,
                priority=1
            )
        except Exception as e:
            print(f"❌ [GUI] 处理进度更新事件失败: {e}")
            
    def _on_status_update(self, event):
        """处理状态更新事件"""
        try:
            status_data = event.data if hasattr(event, 'data') else event
            self.safe_update(
                GUIUpdateType.STATUS_UPDATE,
                status_data,
                priority=1
            )
        except Exception as e:
            print(f"❌ [GUI] 处理状态更新事件失败: {e}")
            
    def _start_gui_updater(self):
        """启动GUI更新处理"""
        def process_updates():
            """处理GUI更新队列"""
            try:
                # 获取更新请求（非阻塞）
                try:
                    update_request = self.update_queue.get_nowait()
                    self._process_update_request(update_request)
                    self.update_queue.task_done()
                except queue.Empty:
                    pass
                    
            except Exception as e:
                print(f"❌ [GUI] GUI更新处理错误: {e}")
                
            # 继续处理
            if self.root:
                self.root.after(10, process_updates)  # 每10ms检查一次
                
        # 启动处理循环
        if self.root:
            self.root.after(10, process_updates)
            print("✅ [GUI] 启动GUI更新处理循环")
            
    def _process_update_request(self, update_request: GUIUpdateRequest):
        """处理单个GUI更新请求"""
        start_time = time.perf_counter()
        
        try:
            if update_request.update_type == GUIUpdateType.LOG_MESSAGE:
                self._handle_log_message_update(update_request.data)
            elif update_request.update_type == GUIUpdateType.PROGRESS_UPDATE:
                self._handle_progress_update(update_request.data)
            elif update_request.update_type == GUIUpdateType.STATUS_UPDATE:
                self._handle_status_update(update_request.data)
            elif update_request.update_type == GUIUpdateType.WIDGET_UPDATE:
                self._handle_widget_update(update_request)
            elif update_request.update_type == GUIUpdateType.CUSTOM:
                self._handle_custom_update(update_request)
                
            # 执行回调
            if update_request.callback:
                update_request.callback()
                
            # 更新统计信息
            processing_time = time.perf_counter() - start_time
            self.stats["updates_processed"] += 1
            self.stats["total_update_time"] += processing_time
            self.stats["average_update_time"] = (
                self.stats["total_update_time"] / self.stats["updates_processed"]
            )
            
        except Exception as e:
            self.stats["update_errors"] += 1
            print(f"❌ [GUI] GUI更新处理错误: {e}")
            traceback.print_exc()
            
    def _handle_log_message_update(self, data: Dict[str, Any]):
        """处理日志消息更新"""
        try:
            message = data.get("message", "")
            tab_type = data.get("tab_type", "general")
            level = data.get("level", "INFO")
            
            # 查找对应的日志组件
            log_widget_name = f"log_text_{tab_type}"
            
            with self.widgets_lock:
                if log_widget_name in self.widgets:
                    widget_info = self.widgets[log_widget_name]
                    widget = widget_info["widget"]
                    
                    # 更新日志文本框
                    if hasattr(widget, 'insert') and hasattr(widget, 'see'):
                        timestamp = time.strftime("%H:%M:%S")
                        formatted_message = f"[{timestamp}] {message}\n"
                        
                        widget.insert(tk.END, formatted_message)
                        widget.see(tk.END)
                        
                        # 限制日志行数
                        lines = widget.get("1.0", tk.END).split('\n')
                        if len(lines) > 1000:  # 保持最新1000行
                            widget.delete("1.0", f"{len(lines)-1000}.0")
                            
                else:
                    # 如果没有找到对应的组件，输出到控制台
                    print(f"[{tab_type.upper()}] {message}")
                    
        except Exception as e:
            print(f"❌ [GUI] 日志消息更新失败: {e}")
            
    def _handle_progress_update(self, data: Dict[str, Any]):
        """处理进度更新"""
        try:
            progress = data.get("progress", 0)
            message = data.get("message", "")
            
            with self.widgets_lock:
                if "progress_bar" in self.widgets:
                    progress_bar = self.widgets["progress_bar"]["widget"]
                    if hasattr(progress_bar, 'set'):
                        progress_bar.set(progress)
                        
                if "progress_label" in self.widgets:
                    progress_label = self.widgets["progress_label"]["widget"]
                    if hasattr(progress_label, 'config'):
                        progress_label.config(text=message)
                        
        except Exception as e:
            print(f"❌ [GUI] 进度更新失败: {e}")
            
    def _handle_status_update(self, data: Dict[str, Any]):
        """处理状态更新"""
        try:
            status = data.get("status", "")
            level = data.get("level", "info")
            
            with self.widgets_lock:
                if "status_label" in self.widgets:
                    status_label = self.widgets["status_label"]["widget"]
                    if hasattr(status_label, 'config'):
                        # 根据级别设置颜色
                        colors = {
                            "info": "black",
                            "warning": "orange",
                            "error": "red",
                            "success": "green"
                        }
                        color = colors.get(level, "black")
                        status_label.config(text=status, fg=color)
                        
        except Exception as e:
            print(f"❌ [GUI] 状态更新失败: {e}")
            
    def _handle_widget_update(self, update_request: GUIUpdateRequest):
        """处理组件更新"""
        try:
            target_widget = update_request.target_widget
            data = update_request.data
            
            if target_widget:
                with self.widgets_lock:
                    if target_widget in self.widgets:
                        widget_info = self.widgets[target_widget]
                        widget = widget_info["widget"]
                        update_method = widget_info.get("update_method")
                        
                        if update_method and hasattr(widget, update_method):
                            getattr(widget, update_method)(data)
                        elif hasattr(widget, 'update'):
                            widget.update(data)
                            
        except Exception as e:
            print(f"❌ [GUI] 组件更新失败: {e}")
            
    def _handle_custom_update(self, update_request: GUIUpdateRequest):
        """处理自定义更新"""
        try:
            if update_request.callback:
                update_request.callback(update_request.data)
        except Exception as e:
            print(f"❌ [GUI] 自定义更新失败: {e}")


class GUIServiceFactory:
    """GUI服务工厂"""
    
    @staticmethod
    def create_gui_service(root: tk.Tk, event_bus, 
                          config: Optional[Dict[str, Any]] = None) -> GUIUpdateService:
        """创建GUI更新服务实例"""
        print("🏗️ [GUI] 创建GUI更新服务...")
        
        # 默认配置
        default_config = {
            "max_queue_size": 500
        }
        
        if config:
            default_config.update(config)
            
        # 创建GUI更新服务
        gui_service = GUIUpdateService(
            root=root,
            event_bus=event_bus,
            max_queue_size=default_config["max_queue_size"]
        )
        
        print("✅ [GUI] GUI更新服务创建完成")
        return gui_service


# 全局GUI服务实例
_global_gui_service: Optional[GUIUpdateService] = None


def get_gui_service() -> Optional[GUIUpdateService]:
    """获取全局GUI服务实例"""
    return _global_gui_service


def set_gui_service(gui_service: GUIUpdateService):
    """设置全局GUI服务实例"""
    global _global_gui_service
    _global_gui_service = gui_service
