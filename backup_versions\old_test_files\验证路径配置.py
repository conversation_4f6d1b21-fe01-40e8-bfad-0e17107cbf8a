#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径配置验证脚本
验证所有路径配置是否正确，确保使用绝对路径和os.path.join()
"""

import os
import sys
import configparser
import json

def verify_path_configuration():
    """验证路径配置"""
    print("🔍 开始验证路径配置...")
    app_root = os.path.dirname(os.path.abspath(__file__))
    print(f"📁 应用程序根目录: {app_root}")
    
    issues = []
    
    # 1. 验证config.ini
    print("\n📋 检查 config.ini...")
    config_ini_path = os.path.join(app_root, "config.ini")
    if os.path.exists(config_ini_path):
        config = configparser.ConfigParser()
        config.read(config_ini_path, encoding='utf-8')
        
        # 检查数据库路径
        if 'Database' in config and 'db_path' in config['Database']:
            db_path = config['Database']['db_path']
            print(f"  📊 数据库路径: {db_path}")
            if os.path.isabs(db_path):
                issues.append("⚠️ config.ini中的数据库路径使用了绝对路径，建议使用相对路径")
            else:
                abs_db_path = os.path.join(app_root, db_path)
                if os.path.exists(os.path.dirname(abs_db_path)):
                    print(f"  ✅ 数据库目录存在: {os.path.dirname(abs_db_path)}")
                else:
                    issues.append(f"❌ 数据库目录不存在: {os.path.dirname(abs_db_path)}")
        
        # 检查脚本路径
        if 'Scripts' in config:
            print("  📜 脚本路径:")
            for key, script_path in config['Scripts'].items():
                print(f"    {key}: {script_path}")
                if os.path.isabs(script_path):
                    issues.append(f"⚠️ config.ini中的脚本路径 {key} 使用了绝对路径")
                else:
                    abs_script_path = os.path.join(app_root, script_path)
                    if os.path.exists(abs_script_path):
                        print(f"    ✅ 脚本存在: {os.path.basename(abs_script_path)}")
                    else:
                        issues.append(f"❌ 脚本不存在: {abs_script_path}")
    else:
        issues.append("❌ config.ini 文件不存在")
    
    # 2. 验证config.json
    print("\n📋 检查 config.json...")
    config_json_path = os.path.join(app_root, "config.json")
    if os.path.exists(config_json_path):
        with open(config_json_path, 'r', encoding='utf-8') as f:
            config_json = json.load(f)
        
        # 检查数据库路径
        if 'database' in config_json and 'path' in config_json['database']:
            db_path = config_json['database']['path']
            print(f"  📊 数据库路径: {db_path}")
            if os.path.isabs(db_path):
                issues.append("⚠️ config.json中的数据库路径使用了绝对路径，建议使用相对路径")
            else:
                abs_db_path = os.path.join(app_root, db_path)
                if os.path.exists(os.path.dirname(abs_db_path)):
                    print(f"  ✅ 数据库目录存在: {os.path.dirname(abs_db_path)}")
                else:
                    issues.append(f"❌ 数据库目录不存在: {os.path.dirname(abs_db_path)}")
        
        # 检查备份路径
        if 'database' in config_json and 'backup_path' in config_json['database']:
            backup_path = config_json['database']['backup_path']
            print(f"  💾 备份路径: {backup_path}")
            if os.path.isabs(backup_path):
                issues.append("⚠️ config.json中的备份路径使用了绝对路径，建议使用相对路径")
            else:
                abs_backup_path = os.path.join(app_root, backup_path)
                if os.path.exists(abs_backup_path):
                    print(f"  ✅ 备份目录存在: {abs_backup_path}")
                else:
                    print(f"  📁 创建备份目录: {abs_backup_path}")
                    os.makedirs(abs_backup_path, exist_ok=True)
    else:
        issues.append("❌ config.json 文件不存在")
    
    # 3. 验证关键脚本文件
    print("\n📜 检查关键脚本文件...")
    key_scripts = [
        "数据处理与导入应用_完整版.py",
        "report 脚本 3.0.py",
        "report 模块化设计 7.0.py",
        "数据导入脚本.py",
        "Refund_process_修复版.py"
    ]
    
    for script in key_scripts:
        script_path = os.path.join(app_root, script)
        if os.path.exists(script_path):
            print(f"  ✅ {script}")
        else:
            issues.append(f"❌ 关键脚本不存在: {script}")
    
    # 4. 验证目录结构
    print("\n📁 检查目录结构...")
    required_dirs = [
        "database",
        "database/backups",
        "logs",
        "detailed_logs"
    ]
    
    for dir_path in required_dirs:
        full_path = os.path.join(app_root, dir_path)
        if os.path.exists(full_path):
            print(f"  ✅ {dir_path}/")
        else:
            print(f"  📁 创建目录: {dir_path}/")
            os.makedirs(full_path, exist_ok=True)
    
    # 5. 总结
    print("\n" + "="*60)
    if issues:
        print("⚠️ 发现以下问题:")
        for issue in issues:
            print(f"  {issue}")
        print(f"\n📊 总计发现 {len(issues)} 个问题")
    else:
        print("✅ 所有路径配置检查通过！")
        print("🎯 路径配置优化完成，所有路径都使用了正确的配置方式")
    
    print("="*60)
    return len(issues) == 0

if __name__ == "__main__":
    success = verify_path_configuration()
    sys.exit(0 if success else 1)
