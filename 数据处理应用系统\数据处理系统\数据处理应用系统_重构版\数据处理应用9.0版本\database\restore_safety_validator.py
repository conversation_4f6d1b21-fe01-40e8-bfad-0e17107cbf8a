# -*- coding: utf-8 -*-
"""
🔧 恢复安全验证工具
确保恢复备份操作的安全性，防止数据库损坏
"""

import os
import sqlite3
import time
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime

from utils.logger import get_logger


class RestoreSafetyValidator:
    """🔧 恢复安全验证器，确保恢复操作的安全性"""
    
    def __init__(self, db_path: str):
        """
        初始化恢复安全验证器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.logger = get_logger('restore_safety')
    
    def validate_restore_safety(self, backup_path: str) -> Dict[str, Any]:
        """
        🔧 恢复安全验证：全面的恢复前安全检查
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            验证结果字典
        """
        validation_result = {
            'safe_to_restore': False,
            'checks': {},
            'warnings': [],
            'errors': [],
            'recommendations': []
        }
        
        try:
            # 检查1：备份文件完整性
            backup_check = self._check_backup_integrity(backup_path)
            validation_result['checks']['backup_integrity'] = backup_check
            
            # 检查2：数据库连接状态
            connection_check = self._check_database_connections()
            validation_result['checks']['database_connections'] = connection_check
            
            # 检查3：数据库锁定状态
            lock_check = self._check_database_lock_status()
            validation_result['checks']['database_lock'] = lock_check
            
            # 检查4：磁盘空间
            space_check = self._check_disk_space(backup_path)
            validation_result['checks']['disk_space'] = space_check
            
            # 检查5：备份文件兼容性
            compatibility_check = self._check_backup_compatibility(backup_path)
            validation_result['checks']['backup_compatibility'] = compatibility_check
            
            # 检查6：当前数据库状态
            current_db_check = self._check_current_database_status()
            validation_result['checks']['current_database'] = current_db_check
            
            # 综合评估
            validation_result['safe_to_restore'] = self._evaluate_overall_safety(validation_result['checks'])
            
            # 生成建议
            self._generate_recommendations(validation_result)
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"恢复安全验证失败: {e}")
            validation_result['errors'].append(f"验证过程失败: {e}")
            return validation_result
    
    def _check_backup_integrity(self, backup_path: str) -> Dict[str, Any]:
        """检查备份文件完整性"""
        check_result = {
            'passed': False,
            'details': {},
            'issues': []
        }
        
        try:
            backup_file = Path(backup_path)
            
            # 文件存在性检查
            if not backup_file.exists():
                check_result['issues'].append("备份文件不存在")
                return check_result
            
            # 文件大小检查
            file_size = backup_file.stat().st_size
            check_result['details']['file_size'] = file_size
            
            if file_size < 1024:  # 小于1KB
                check_result['issues'].append("备份文件太小，可能损坏")
                return check_result
            
            # SQLite完整性检查
            try:
                with sqlite3.connect(backup_path, timeout=5) as conn:
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA integrity_check")
                    integrity_result = cursor.fetchone()
                    
                    if integrity_result and integrity_result[0] == 'ok':
                        check_result['details']['integrity'] = 'ok'
                        
                        # 表结构检查
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                        tables = cursor.fetchall()
                        check_result['details']['table_count'] = len(tables)
                        
                        if len(tables) > 0:
                            check_result['passed'] = True
                        else:
                            check_result['issues'].append("备份文件中没有表")
                    else:
                        check_result['issues'].append(f"备份文件完整性检查失败: {integrity_result}")
                        
            except sqlite3.Error as e:
                check_result['issues'].append(f"SQLite完整性检查失败: {e}")
                
        except Exception as e:
            check_result['issues'].append(f"备份完整性检查异常: {e}")
        
        return check_result
    
    def _check_database_connections(self) -> Dict[str, Any]:
        """检查数据库连接状态"""
        check_result = {
            'passed': False,
            'details': {},
            'issues': []
        }
        
        try:
            # 检查是否有活跃连接
            active_connections = self._count_active_connections()
            check_result['details']['active_connections'] = active_connections
            
            if active_connections == 0:
                check_result['passed'] = True
            else:
                check_result['issues'].append(f"检测到 {active_connections} 个活跃连接")
                
        except Exception as e:
            check_result['issues'].append(f"连接状态检查异常: {e}")
        
        return check_result
    
    def _check_database_lock_status(self) -> Dict[str, Any]:
        """检查数据库锁定状态"""
        check_result = {
            'passed': False,
            'details': {},
            'issues': []
        }
        
        try:
            if not self.db_path.exists():
                check_result['passed'] = True
                check_result['details']['status'] = 'database_not_exists'
                return check_result
            
            # 尝试获取独占锁
            try:
                with sqlite3.connect(self.db_path, timeout=1) as conn:
                    conn.execute("BEGIN IMMEDIATE;")
                    conn.rollback()
                check_result['passed'] = True
                check_result['details']['status'] = 'unlocked'
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    check_result['issues'].append("数据库被锁定")
                    check_result['details']['status'] = 'locked'
                else:
                    check_result['issues'].append(f"数据库访问错误: {e}")
                    
        except Exception as e:
            check_result['issues'].append(f"锁定状态检查异常: {e}")
        
        return check_result
    
    def _check_disk_space(self, backup_path: str) -> Dict[str, Any]:
        """检查磁盘空间"""
        check_result = {
            'passed': False,
            'details': {},
            'issues': []
        }
        
        try:
            backup_size = Path(backup_path).stat().st_size
            db_dir = self.db_path.parent
            
            # 获取可用磁盘空间
            import shutil
            total, used, free = shutil.disk_usage(db_dir)
            
            check_result['details']['backup_size'] = backup_size
            check_result['details']['free_space'] = free
            check_result['details']['required_space'] = backup_size * 2  # 需要额外空间用于临时文件
            
            if free > backup_size * 2:
                check_result['passed'] = True
            else:
                check_result['issues'].append("磁盘空间不足")
                
        except Exception as e:
            check_result['issues'].append(f"磁盘空间检查异常: {e}")
        
        return check_result
    
    def _check_backup_compatibility(self, backup_path: str) -> Dict[str, Any]:
        """检查备份文件兼容性"""
        check_result = {
            'passed': False,
            'details': {},
            'issues': []
        }
        
        try:
            # 检查SQLite版本兼容性
            with sqlite3.connect(backup_path, timeout=5) as conn:
                cursor = conn.cursor()
                cursor.execute("PRAGMA user_version")
                user_version = cursor.fetchone()[0]
                
                cursor.execute("PRAGMA schema_version")
                schema_version = cursor.fetchone()[0]
                
                check_result['details']['user_version'] = user_version
                check_result['details']['schema_version'] = schema_version
                
                # 基本兼容性检查（可以根据需要扩展）
                check_result['passed'] = True
                
        except Exception as e:
            check_result['issues'].append(f"兼容性检查异常: {e}")
        
        return check_result
    
    def _check_current_database_status(self) -> Dict[str, Any]:
        """检查当前数据库状态"""
        check_result = {
            'passed': False,
            'details': {},
            'issues': []
        }
        
        try:
            if not self.db_path.exists():
                check_result['passed'] = True
                check_result['details']['status'] = 'not_exists'
                return check_result
            
            # 检查当前数据库完整性
            with sqlite3.connect(self.db_path, timeout=5) as conn:
                cursor = conn.cursor()
                cursor.execute("PRAGMA integrity_check")
                integrity_result = cursor.fetchone()
                
                if integrity_result and integrity_result[0] == 'ok':
                    check_result['passed'] = True
                    check_result['details']['integrity'] = 'ok'
                else:
                    check_result['issues'].append("当前数据库完整性检查失败")
                    
        except Exception as e:
            check_result['issues'].append(f"当前数据库状态检查异常: {e}")
        
        return check_result
    
    def _count_active_connections(self) -> int:
        """统计活跃连接数"""
        try:
            # 这里可以根据实际的连接池实现来统计
            # 目前返回0作为默认值
            return 0
        except Exception:
            return -1  # 表示无法确定
    
    def _evaluate_overall_safety(self, checks: Dict[str, Dict[str, Any]]) -> bool:
        """评估整体安全性"""
        critical_checks = ['backup_integrity', 'database_lock']
        
        for check_name in critical_checks:
            if check_name in checks and not checks[check_name]['passed']:
                return False
        
        return True
    
    def _generate_recommendations(self, validation_result: Dict[str, Any]):
        """生成建议"""
        checks = validation_result['checks']
        recommendations = validation_result['recommendations']
        
        if not checks.get('backup_integrity', {}).get('passed', False):
            recommendations.append("请使用有效的备份文件")
        
        if not checks.get('database_connections', {}).get('passed', False):
            recommendations.append("请关闭所有数据库连接后再进行恢复")
        
        if not checks.get('database_lock', {}).get('passed', False):
            recommendations.append("请等待数据库解锁后再进行恢复")
        
        if not checks.get('disk_space', {}).get('passed', False):
            recommendations.append("请释放足够的磁盘空间")
        
        if validation_result['safe_to_restore']:
            recommendations.append("所有安全检查通过，可以安全进行恢复操作")


def validate_restore_operation(db_path: str, backup_path: str) -> Dict[str, Any]:
    """
    验证恢复操作的安全性
    
    Args:
        db_path: 数据库文件路径
        backup_path: 备份文件路径
        
    Returns:
        验证结果
    """
    validator = RestoreSafetyValidator(db_path)
    return validator.validate_restore_safety(backup_path)
