# -*- coding: utf-8 -*-
"""
优化版数据导入脚本
使用重构后的基础模块，提供更好的性能、错误处理和日志记录
"""

print("START", flush=True)

import sys
print("SYS_OK", flush=True)

import os
print("OS_OK", flush=True)

import argparse
print("ARG_OK", flush=True)

print("BEFORE_PANDAS", flush=True)
import pandas as pd
print("PANDAS_OK", flush=True)

# import sqlite3  # 🔧 修复：通过connection_pool使用，不直接导入
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import re
from dateutil import parser as dateparser

# 🔧 Bug修复：将tkinter导入移到顶部，避免重复导入
try:
    import tkinter as tk
    from tkinter import messagebox
    import queue
    import threading
    TKINTER_AVAILABLE = True
    print(f"🔧 [SCRIPT] tkinter导入成功", flush=True)
except ImportError:
    TKINTER_AVAILABLE = False
    print(f"⚠️ [SCRIPT] tkinter不可用", flush=True)

print(f"🔧 [SCRIPT] 所有标准库导入完成", flush=True)

# 移除日志进度条，使用UI进度条

# 🔧 修复：使用统一的编码处理标准
# 添加父目录到路径，以便导入重构的模块
print(f"🔧 [SCRIPT] 设置sys.path", flush=True)
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print(f"🔧 [SCRIPT] sys.path设置完成", flush=True)

# 设置统一编码环境
print(f"🔧 [SCRIPT] 开始设置编码环境", flush=True)
try:
    from utils.standard_utils import setup_standard_environment
    setup_standard_environment()
    print(f"🔧 [SCRIPT] 标准编码环境设置完成", flush=True)
except ImportError:
    # 如果标准工具不可用，使用基本编码设置
    print(f"🔧 [SCRIPT] 标准工具不可用，跳过", flush=True)
    pass

# 🔒 导入数据库安全工具
print(f"🔒 [SECURITY] 开始导入数据库安全工具", flush=True)
try:
    from database.security_utils import (
        SQLSecurityValidator,
        SafeSQLBuilder,
        # get_safe_sql_builder,  # 🔧 Bug修复：删除未使用的导入
        # DatabaseSecurityError,  # 🔧 Bug修复：删除未使用的导入
        # validate_database_path  # 🔧 Bug修复：删除未使用的导入
    )
    print(f"🔒 [SECURITY] 数据库安全工具导入成功", flush=True)
except ImportError as e:
    print(f"⚠️ [SECURITY] 数据库安全工具导入失败: {e}", flush=True)
    # 创建空的安全工具类以保持兼容性
    class SQLSecurityValidator:
        @classmethod
        def validate_table_name(cls, table_name):
            _ = table_name  # 🔧 Bug修复：标记未使用的参数
            return True
        @classmethod
        def sanitize_table_name(cls, table_name): return f'"{table_name}"'

    class SafeSQLBuilder:
        def __init__(self): pass

    def get_safe_sql_builder(): return SafeSQLBuilder()

    class DatabaseSecurityError(Exception): pass

    def validate_database_path(path):
        _ = path  # 🔧 Bug修复：标记未使用的参数
        return True

print(f"🔧 [SCRIPT] 开始导入重构模块", flush=True)
from utils.logger import get_logger
print(f"🔧 [SCRIPT] logger模块导入完成", flush=True)
# 🔧 Bug修复：避免通配符导入，明确导入需要的异常类
from utils.exceptions import (
    DataImportError,
    DatabaseError,
    ValidationError,
    ConfigurationError,
    FileValidationError,
    DataProcessingError,
    PlatformError,
    BackupError,
    DuplicateDataError
)
print(f"🔧 [SCRIPT] exceptions模块导入完成", flush=True)
from utils.validators import input_validator
print(f"🔧 [SCRIPT] validators模块导入完成", flush=True)
from database.connection_pool import get_connection, reinitialize_connection_pool
print(f"🔧 [SCRIPT] connection_pool模块导入完成", flush=True)
from database.models import TABLE_SCHEMAS, STATUS_TABLE_MAPPING, SMART_STATUS_PATTERNS
print(f"🔧 [SCRIPT] models模块导入完成", flush=True)
from database.smart_backup_manager import get_smart_backup_manager
print(f"🔧 [SCRIPT] 所有重构模块导入完成", flush=True)


class DataImportProcessor:
    """优化版数据导入处理器"""

    def __init__(self, db_path: Optional[str] = None):
        """
        初始化数据导入处理器

        Args:
            db_path: 数据库路径，如果为None则使用配置中的路径
        """
        print(f"🔧 [TERMINAL] DataImportProcessor.__init__ 开始", flush=True)

        self.logger = get_logger('data_import')
        self.current_file_path = None  # 🔧 添加：当前处理的文件路径，用于日志保存
        self.log_file_path = None  # 🔧 添加：日志文件路径

        print(f"🔧 [TERMINAL] Logger初始化完成", flush=True)

        # 设置数据库路径
        if db_path:
            self.db_path = db_path
        else:
            # 🔧 修复：使用统一的配置处理标准
            try:
                from utils.standard_utils import get_standard_config
                config = get_standard_config()
                self.db_path = config.get_db_path()
            except ImportError:
                # 如果标准工具不可用，尝试原有配置管理器
                try:
                    from utils.config_manager import config_manager
                    self.db_path = config_manager.get_db_path()
                except ImportError:
                    # 最后回退到默认路径
                    self.db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "database", "sales_reports.db")

        # 🆕 性能优化配置
        self.batch_size = 500  # 🔧 修复：减小批处理大小，提高响应性
        self.max_memory_usage = 500 * 1024 * 1024  # 最大内存使用 500MB
        self.enable_batch_processing = True  # 启用分批处理
        self.progress_callback = None  # 进度回调函数

        # 🔧 添加调试信息
        print(f"🔧 [DEBUG] 初始化智能备份管理器")
        # 初始化智能备份管理器
        self.backup_manager = get_smart_backup_manager(self.db_path)
        print(f"🔧 [DEBUG] 备份管理器初始化完成")

        # 确保数据库目录存在
        print(f"🔧 [DEBUG] 确保数据库目录存在: {os.path.dirname(self.db_path)}")
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        # 确保连接池使用正确的数据库路径
        print(f"🔧 [DEBUG] 重新初始化连接池")
        reinitialize_connection_pool(self.db_path)
        print(f"🔧 [DEBUG] 连接池初始化完成")

        # 创建表结构（包括新的Refunding和Close表）
        print(f"🔧 [DEBUG] 开始创建表结构")
        self._create_all_tables()
        print(f"🔧 [DEBUG] 表结构创建完成")

        self.logger.info(f"Smart data import processor initialized, DB path: {self.db_path}")
        self.logger.info(f"Performance config: batch_size={self.batch_size}, max_memory={self.max_memory_usage//1024//1024}MB")
        print(f"🔧 [DEBUG] DataImportProcessor初始化完全完成")

    def _check_memory_usage(self) -> float:
        """🆕 检查当前内存使用情况（可选功能）"""
        try:
            import psutil  # type: ignore  # 可选依赖，仅在需要时导入
            process = psutil.Process()
            memory_info = process.memory_info()
            return memory_info.rss  # 返回字节数
        except ImportError:
            # 如果psutil不可用，返回0（表示无法监控内存）
            self.logger.debug("psutil不可用，无法监控内存使用情况")
            return 0
        except Exception as e:
            # 其他错误也返回0
            self.logger.debug(f"内存监控失败: {e}")
            return 0

    def _should_use_batch_processing(self, df: pd.DataFrame) -> bool:
        """🆕 判断是否应该使用分批处理"""
        if not self.enable_batch_processing:
            return False

        # 检查数据量
        if len(df) > self.batch_size:
            return True

        # 检查内存使用
        current_memory = self._check_memory_usage()
        if current_memory > self.max_memory_usage:
            self.logger.warning(f"内存使用过高: {current_memory//1024//1024}MB, 启用分批处理")
            return True

        return False

    def _detect_missing_records(self, df: pd.DataFrame, platform: str) -> Dict[str, Any]:
        """🆕 检测数据库中存在但文件中缺失的记录"""
        missing_report = {
            'has_missing': False,
            'missing_records': pd.DataFrame(),
            'missing_count': 0,
            'total_db_records': 0,
            'file_records': len(df),
            'tables_checked': []
        }

        try:
            with get_connection() as conn:
                # 确定要检查的表
                tables_to_check = set()
                if 'Order_status' in df.columns:
                    for _, row in df.iterrows():
                        order_status = row.get('Order_status', '')
                        order_types = row.get('Order_types', '') if 'Order_types' in df.columns else None
                        # 🔧 传递order_type参数以支持表路由限制
                        target_table = self._determine_target_table(platform, order_status, order_types, getattr(self, 'current_order_type', None))
                        tables_to_check.add(target_table)
                        # 🔧 调试：记录表映射
                        if target_table == "APP_Sales" and platform != "APP":
                            self.logger.info(f"✅ 平台 {platform} 的API订单 (Order_types: '{order_types}') 正确路由到 APP_Sales 表")
                else:
                    tables_to_check.add(f"{platform}_Sales")

                # 🔧 调试：显示要检查的表
                self.logger.debug(f"平台 {platform} 要检查的表: {list(tables_to_check)}")

                # 🔧 修复：使用向量化操作替代iterrows()提高性能
                file_identifiers = set()
                if 'Transaction_Num' in df.columns and 'Order_time' in df.columns:
                    # 向量化操作：过滤有效记录
                    valid_mask = df['Transaction_Num'].notna() & df['Order_time'].notna()
                    valid_df = df[valid_mask]
                    # 批量创建标识符
                    if not valid_df.empty:
                        identifiers = valid_df['Transaction_Num'].astype(str) + '_' + valid_df['Order_time'].astype(str)
                        file_identifiers = set(identifiers)

                # 检查每个表中的现有数据
                all_missing_records = []
                total_db_count = 0

                # 🔧 修复：只查询文件中包含日期的数据库记录
                file_dates = set()
                if 'Order_time' in df.columns:
                    for _, row in df.iterrows():
                        try:
                            order_time = pd.to_datetime(row['Order_time'])
                            date_str = order_time.strftime('%Y-%m-%d')
                            file_dates.add(date_str)
                        except (ValueError, TypeError, pd.errors.OutOfBoundsDatetime) as e:
                            # 🔧 Bug修复：具体化异常处理，记录调试信息
                            self.logger.debug(f"日期解析失败: {row.get('Order_time', 'N/A')} - {e}")
                            continue

                if not file_dates:
                    return missing_report

                for table_name in tables_to_check:
                    try:
                        # 🔧 修复：只查询文件中包含日期的记录
                        date_conditions = " OR ".join([f"DATE(Order_time) = '{date}'" for date in file_dates])
                        existing_df = pd.read_sql(
                            f"""SELECT Transaction_Num, Order_time, Order_No, Order_price, Equipment_ID
                                FROM {table_name}
                                WHERE {date_conditions}
                                ORDER BY Order_time DESC""",
                            conn.connection
                        )

                        if existing_df.empty:
                            continue

                        total_db_count += len(existing_df)
                        missing_report['tables_checked'].append(table_name)

                        # 🔧 修复：使用向量化操作替代iterrows()提高性能
                        if not existing_df.empty:
                            # 向量化操作：过滤有效的数据库记录
                            valid_db_mask = existing_df['Transaction_Num'].notna() & existing_df['Order_time'].notna()
                            valid_existing_df = existing_df[valid_db_mask].copy()

                            if not valid_existing_df.empty:
                                # 批量创建数据库标识符
                                db_identifiers = valid_existing_df['Transaction_Num'].astype(str) + '_' + valid_existing_df['Order_time'].astype(str)
                                # 找出缺失的记录
                                missing_mask = ~db_identifiers.isin(file_identifiers)
                                missing_records_df = valid_existing_df[missing_mask].copy()

                                if not missing_records_df.empty:
                                    missing_records_df['source_table'] = table_name
                                    all_missing_records.extend(missing_records_df.to_dict('records'))

                    except Exception as e:
                        self.logger.warning(f"检查表 {table_name} 时出错: {e}")
                        continue

                # 汇总结果
                if all_missing_records:
                    missing_report['has_missing'] = True
                    missing_report['missing_records'] = pd.DataFrame(all_missing_records)
                    missing_report['missing_count'] = len(all_missing_records)

                missing_report['total_db_records'] = total_db_count

                if missing_report['has_missing']:
                    self.logger.warning(f"检测到缺失记录: 数据库有{total_db_count}条，文件有{len(df)}条，缺失{missing_report['missing_count']}条")
                else:
                    self.logger.info(f"未检测到缺失记录: 数据库有{total_db_count}条，文件有{len(df)}条")

                return missing_report

        except Exception as e:
            self.logger.error(f"检测缺失记录时出错: {e}")
            missing_report['error'] = str(e)
            return missing_report

    def _standardize_datetime_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """🆕 标准化时间格式，提高重复检测准确性"""
        try:
            if 'Order_time' in df.columns:
                # 标准化Order_time格式
                df['Order_time'] = pd.to_datetime(df['Order_time'], errors='coerce')
                df['Order_time'] = df['Order_time'].dt.strftime('%Y-%m-%d %H:%M:%S')

            if 'Payment_date' in df.columns:
                # 标准化Payment_date格式
                df['Payment_date'] = pd.to_datetime(df['Payment_date'], errors='coerce')
                df['Payment_date'] = df['Payment_date'].dt.strftime('%Y-%m-%d %H:%M:%S')

            self.logger.debug("时间格式标准化完成")
            return df

        except Exception as e:
            self.logger.warning(f"时间格式标准化失败: {e}，使用原始数据")
            return df

    def _validate_critical_fields(self, df: pd.DataFrame) -> Dict[str, Any]:
        """🆕 验证关键字段，提供详细的数据质量报告"""
        validation_report = {
            'is_valid': True,
            'warnings': [],
            'errors': [],
            'field_quality': {}
        }

        critical_fields = {
            'Transaction_Num': {'required': False, 'type': 'string'},
            'Order_No': {'required': True, 'type': 'string'},
            'Order_price': {'required': True, 'type': 'numeric'},
            'Equipment_ID': {'required': True, 'type': 'string'},
            'Order_time': {'required': True, 'type': 'datetime'}
        }

        for field, rules in critical_fields.items():
            if field not in df.columns:
                if rules['required']:
                    validation_report['errors'].append(f"缺少必需字段: {field}")
                    validation_report['is_valid'] = False
                continue

            # 检查空值率
            null_rate = df[field].isnull().sum() / len(df) * 100
            validation_report['field_quality'][field] = {
                'null_rate': null_rate,
                'total_count': len(df),
                'valid_count': df[field].notna().sum()
            }

            if null_rate > 50 and rules['required']:
                validation_report['errors'].append(f"{field} 空值率过高: {null_rate:.1f}%")
                validation_report['is_valid'] = False
            elif null_rate > 20:
                validation_report['warnings'].append(f"{field} 空值率较高: {null_rate:.1f}%")

        return validation_report

    def _handle_missing_records_interaction(self, missing_report: Dict[str, Any], platform: str) -> str:
        """🆕 处理缺失记录的用户交互"""
        if not missing_report['has_missing']:
            return 'continue'  # 没有缺失记录，继续处理

        # 使用platform参数进行日志记录
        self.logger.info(f"处理平台 {platform} 的缺失记录交互")

        missing_count = missing_report['missing_count']
        total_db = missing_report['total_db_records']
        file_count = missing_report['file_records']

        # 🔧 智能判断：如果文件记录数大于数据库记录数，这是正常的增量导入
        if file_count > total_db:
            self.logger.info(f"✅ 正常增量导入：文件有{file_count}条记录，数据库有{total_db}条记录，自动导入新数据")
            return 'ignore'  # 自动忽略缺失记录，导入新数据

        # 🔧 智能判断：如果缺失记录比例较小（<30%），也自动处理
        missing_ratio = missing_count / total_db if total_db > 0 else 0
        if missing_ratio < 0.3:
            self.logger.info(f"✅ 缺失记录比例较小({missing_ratio:.1%})，自动忽略缺失记录")
            return 'ignore'

        self.logger.warning(f"⚠️ 检测到异常数据情况：数据库有{total_db}条记录，文件只有{file_count}条，缺失{missing_count}条({missing_ratio:.1%})")

        # 检查是否设置了自动处理模式
        import os
        auto_mode = os.environ.get('AUTO_MISSING_HANDLING', '').lower()
        if auto_mode in ['ignore', 'mark_deleted', 'backup_missing']:
            self.logger.info(f"🤖 自动模式：使用预设策略 '{auto_mode}'")
            return auto_mode

        # 🔧 修复：检查是否在非交互模式下运行（从主应用程序调用）
        non_interactive = os.environ.get('NON_INTERACTIVE', '').lower() in ['1', 'true', 'yes']
        if non_interactive:
            self.logger.info("🤖 非交互模式：自动忽略缺失记录")
            return 'ignore'

        # 显示缺失记录的详情（前5条）
        print(f"\n⚠️ 检测到异常数据情况")
        print(f"📊 数据库现有记录: {total_db} 条")
        print(f"📊 文件准备导入: {file_count} 条")
        print(f"📊 数据库中存在但文件中缺失: {missing_count} 条 ({missing_ratio:.1%})")
        print(f"\n💡 说明：这些是数据库中存在但当前文件中没有的记录")

        if missing_count > 0:
            print(f"\n📋 缺失记录详情 (前5条):")
            print("-" * 80)
            missing_df = missing_report['missing_records']
            display_cols = ['Transaction_Num', 'Order_No', 'Order_time', 'Order_price', 'source_table']
            available_cols = [col for col in display_cols if col in missing_df.columns]

            if available_cols:
                for i, (_, row) in enumerate(missing_df.head(5).iterrows()):
                    print(f"  {i+1}. ", end="")
                    for col in available_cols:
                        print(f"{col}: {row[col]} | ", end="")
                    print()

            if missing_count > 5:
                print(f"  ... 还有 {missing_count - 5} 条缺失记录")

        print(f"\n🤔 如何处理数据库中存在但文件中缺失的记录？")
        print("1) 忽略缺失记录，只导入文件中的新数据 (推荐)")
        print("2) 将缺失记录标记为已删除状态 (软删除)")
        print("3) 备份缺失记录到Excel文件 (用于审核)")
        print("4) 取消导入，手动检查数据 (谨慎选择)")

        # 🔧 使用GUI对话框进行交互
        try:
            import tkinter as tk
            from tkinter import ttk

            # 创建自定义缺失记录处理对话框
            class MissingRecordsDialog:
                def __init__(self):
                    self.result = None
                    # 创建隐藏的根窗口
                    self.root = tk.Tk()
                    self.root.withdraw()  # 隐藏根窗口
                    # 创建实际的对话框窗口
                    self.dialog = tk.Toplevel(self.root)
                    self.setup_dialog()

                def setup_dialog(self):
                    # 窗口基本设置
                    self.dialog.title("⚠️ 检测到异常数据情况")
                    self.dialog.geometry("700x600")
                    self.dialog.resizable(False, False)

                    # 居中显示
                    self.center_window()

                    # 设置为置顶
                    self.dialog.attributes('-topmost', True)
                    self.dialog.focus_force()

                    # 创建主框架
                    main_frame = ttk.Frame(self.dialog, padding="20")
                    main_frame.pack(fill=tk.BOTH, expand=True)

                    # 标题
                    title_label = ttk.Label(
                        main_frame,
                        text="⚠️ 检测到异常数据情况",
                        font=("Arial", 14, "bold")
                    )
                    title_label.pack(pady=(0, 15))

                    # 数据统计信息
                    stats_frame = ttk.LabelFrame(main_frame, text="数据统计", padding="10")
                    stats_frame.pack(fill=tk.X, pady=(0, 15))

                    stats_text = f"""📊 数据库现有记录: {total_db:,} 条
📊 文件准备导入: {file_count:,} 条
📊 数据库中存在但文件中缺失: {missing_count:,} 条 ({missing_ratio:.1%})

💡 说明：这些是数据库中存在但当前文件中没有的记录"""

                    stats_label = ttk.Label(stats_frame, text=stats_text, justify=tk.LEFT)
                    stats_label.pack(anchor=tk.W)

                    # 选项框架
                    options_frame = ttk.LabelFrame(main_frame, text="🤔 如何处理数据库中存在但文件中缺失的记录？", padding="10")
                    options_frame.pack(fill=tk.X, pady=(0, 15))

                    # 选项变量
                    self.choice_var = tk.StringVar(value="ignore")

                    # 选项按钮
                    options = [
                        ("ignore", "🔄 忽略缺失记录，只导入文件中的新数据 (推荐)\n   → 保持数据库中的旧记录不变，只添加文件中的新数据"),
                        ("mark_deleted", "🏷️ 将缺失记录标记为已删除状态 (软删除)\n   → 在数据库中标记这些记录为'已删除'，但不真正删除"),
                        ("backup_missing", "💾 备份缺失记录到Excel文件 (用于审核)\n   → 将这些记录导出到Excel文件供后续检查"),
                        ("cancel", "❌ 取消导入，手动检查数据 (谨慎选择)\n   → 停止导入，让您手动检查数据情况")
                    ]

                    for value, text in options:
                        rb = ttk.Radiobutton(
                            options_frame,
                            text=text,
                            variable=self.choice_var,
                            value=value
                        )
                        rb.pack(anchor=tk.W, pady=3)

                    # 按钮区域
                    button_frame = ttk.Frame(main_frame)
                    button_frame.pack(fill=tk.X, pady=(15, 0))

                    # 确认按钮
                    confirm_btn = ttk.Button(
                        button_frame,
                        text="✅ 确认",
                        command=self.confirm_choice
                    )
                    confirm_btn.pack(side=tk.RIGHT, padx=(5, 0))

                    # 取消按钮
                    cancel_btn = ttk.Button(
                        button_frame,
                        text="❌ 取消",
                        command=self.cancel_choice
                    )
                    cancel_btn.pack(side=tk.RIGHT)

                    # 绑定键盘事件
                    self.dialog.bind('<Return>', lambda e: self.confirm_choice())
                    self.dialog.bind('<Escape>', lambda e: self.cancel_choice())

                    # 设置默认焦点
                    confirm_btn.focus_set()

                def center_window(self):
                    self.dialog.update_idletasks()
                    width = 700
                    height = 600
                    x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
                    y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
                    self.dialog.geometry(f"{width}x{height}+{x}+{y}")

                def confirm_choice(self):
                    self.result = self.choice_var.get()
                    self.root.quit()
                    self.root.destroy()

                def cancel_choice(self):
                    self.result = "cancel"
                    self.root.quit()
                    self.root.destroy()

                def show(self):
                    self.root.mainloop()
                    return self.result

            # 显示对话框
            dialog = MissingRecordsDialog()
            choice = dialog.show()

            if choice:
                self.logger.info(f"用户选择：{choice}")
                return choice
            else:
                self.logger.info("用户取消选择，默认忽略缺失记录")
                return 'ignore'

        except Exception as e:
            print(f"GUI对话框失败: {e}")
            self.logger.info("GUI失败，默认选择：忽略缺失记录")
            return 'ignore'

    def _process_missing_records(self, missing_report: Dict[str, Any], strategy: str, file_path: str) -> Dict[str, Any]:
        """🆕 处理缺失记录"""
        result = {
            'success': True,
            'processed_count': 0,
            'backup_file': '',
            'errors': []
        }

        if not missing_report['has_missing'] or strategy == 'ignore':
            return result

        missing_df = missing_report['missing_records']

        try:
            if strategy == 'backup_missing':
                # 备份缺失记录到Excel文件
                backup_file = self._create_missing_records_backup(missing_df, file_path)
                result['backup_file'] = backup_file
                result['processed_count'] = len(missing_df)
                self.logger.info(f"缺失记录已备份到: {backup_file}")

            elif strategy == 'mark_deleted':
                # 将缺失记录标记为已删除状态
                deleted_count = self._mark_records_as_deleted(missing_df)
                result['processed_count'] = deleted_count
                self.logger.info(f"已标记 {deleted_count} 条记录为删除状态")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"处理缺失记录失败: {e}")
            self.logger.error(f"处理缺失记录失败: {e}")

        return result

    def _create_missing_records_backup(self, missing_df: pd.DataFrame, original_file_path: str) -> str:
        """🆕 创建缺失记录的备份文件"""
        try:
            # 生成备份文件名
            original_path = Path(original_file_path)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"缺失记录_{original_path.stem}_{timestamp}.xlsx"
            backup_path = original_path.parent / backup_filename

            # 创建备份Excel文件
            with pd.ExcelWriter(backup_path, engine='openpyxl') as writer:
                missing_df.to_excel(writer, sheet_name='缺失记录', index=False)

                # 添加说明工作表
                summary_data = {
                    '说明': [
                        '这些记录在数据库中存在，但在新导入的文件中缺失',
                        '请检查是否需要保留这些记录或标记为删除',
                        f'原始文件: {original_file_path}',
                        f'备份时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                        f'缺失记录数: {len(missing_df)}'
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='说明', index=False)

            return str(backup_path)

        except Exception as e:
            self.logger.error(f"创建缺失记录备份失败: {e}")
            return ""

    def _mark_records_as_deleted(self, missing_df: pd.DataFrame) -> int:
        """🆕 将缺失记录标记为已删除状态"""
        deleted_count = 0

        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                # 按表分组处理
                for table_name in missing_df['source_table'].unique():
                    table_records = missing_df[missing_df['source_table'] == table_name]

                    for _, record in table_records.iterrows():
                        try:
                            # 🔧 修复：安全添加删除标记字段（防止并发冲突）
                            self._safe_add_deleted_column_if_not_exists(cursor, table_name)

                            # 🔧 修复：使用安全的参数化查询标记记录为已删除
                            cursor.execute(f"""
                                UPDATE "{table_name}"
                                SET is_deleted = 1,
                                    deleted_at = datetime('now'),
                                    delete_reason = ?
                                WHERE Transaction_Num = ? AND Order_time = ?
                            """, ('文件中缺失', record['Transaction_Num'], record['Order_time']))

                            if cursor.rowcount > 0:
                                deleted_count += 1

                        except Exception as e:
                            self.logger.warning(f"标记记录删除失败: {record['Transaction_Num']}, 错误: {e}")
                            continue

                conn.commit()

        except Exception as e:
            self.logger.error(f"标记删除记录失败: {e}")

        return deleted_count

    def _safe_add_deleted_column_if_not_exists(self, cursor, table_name: str):
        """🔧 安全添加删除标记字段，防止并发冲突"""
        try:
            # 🔧 修复：使用参数化查询检查字段是否存在
            cursor.execute("""
                SELECT name FROM pragma_table_info(?)
                WHERE name='is_deleted'
            """, (table_name,))

            if not cursor.fetchone():
                try:
                    # 🔧 修复：使用双引号包围表名，防止SQL注入
                    cursor.execute(f'ALTER TABLE "{table_name}" ADD COLUMN is_deleted INTEGER DEFAULT 0')

                    # 添加相关字段
                    cursor.execute(f'ALTER TABLE "{table_name}" ADD COLUMN deleted_at TEXT')
                    cursor.execute(f'ALTER TABLE "{table_name}" ADD COLUMN delete_reason TEXT')

                    self.logger.info(f"为表 {table_name} 添加了删除标记字段")

                except Exception as alter_error:
                    # 🔧 修复：处理字段已存在的情况
                    if "duplicate column name" in str(alter_error).lower():
                        self.logger.debug(f"表 {table_name} 的删除字段已存在")
                    else:
                        raise alter_error

        except Exception as e:
            self.logger.warning(f"检查/添加删除字段失败: {table_name}, 错误: {e}")

    def _delete_duplicate_records_for_overwrite(self, duplicate_df: pd.DataFrame, platform: str) -> int:
        """🔧 为overwrite策略删除重复记录"""
        _ = platform  # 🔧 Bug修复：标记未使用的参数
        if duplicate_df.empty:
            return 0

        deleted_count = 0

        try:
            with get_connection() as conn:
                cursor = conn.cursor()

                # 按表分组删除
                for table_name in duplicate_df['source_table'].unique():
                    table_records = duplicate_df[duplicate_df['source_table'] == table_name]

                    for _, record in table_records.iterrows():
                        try:
                            # 🔧 修复：使用参数化查询删除重复记录
                            cursor.execute(f"""
                                DELETE FROM "{table_name}"
                                WHERE Transaction_Num = ? AND Order_time = ?
                            """, (record['Transaction_Num'], record['Order_time']))

                            if cursor.rowcount > 0:
                                deleted_count += cursor.rowcount

                        except Exception as e:
                            self.logger.warning(f"删除重复记录失败: {record['Transaction_Num']}, 错误: {e}")
                            continue

                conn.commit()
                self.logger.info(f"overwrite策略：删除了 {deleted_count} 条重复记录")

        except Exception as e:
            self.logger.error(f"删除重复记录失败: {e}")
            raise

        return deleted_count

    def _batch_delete_records(self, cursor, table_name: str, start_date: str, end_date: str, total_count: int) -> int:
        """🔧 批量删除记录，避免长时间锁定"""
        deleted_count = 0
        batch_size = 1000  # 每批删除1000条记录

        self.logger.info(f"开始分批删除 {table_name} 表中 {total_count} 条记录")

        while True:
            # 🔧 修复：使用LIMIT分批删除
            delete_query = f"""
                DELETE FROM "{table_name}"
                WHERE rowid IN (
                    SELECT rowid FROM "{table_name}"
                    WHERE Order_time >= ? AND Order_time < ?
                    LIMIT ?
                )
            """
            cursor.execute(delete_query, (start_date, end_date, batch_size))

            batch_deleted = cursor.rowcount
            deleted_count += batch_deleted

            if batch_deleted < batch_size:
                break  # 没有更多记录需要删除

            # 🔧 修复：短暂休息，释放数据库锁
            import time
            time.sleep(0.01)

            # 记录进度
            if deleted_count % 5000 == 0:
                self.logger.info(f"已删除 {deleted_count}/{total_count} 条记录")

        return deleted_count

    def cleanup_old_logs(self, max_days: int = 30):
        """🔧 清理超过指定天数的日志文件"""
        try:
            import glob
            from datetime import timedelta

            cutoff_date = datetime.now() - timedelta(days=max_days)
            cleaned_count = 0

            # 查找所有数据处理日志目录
            log_patterns = [
                "**/数据处理日志/*.log",
                "**/详细日志/*.txt"
            ]

            for pattern in log_patterns:
                for log_file in glob.glob(pattern, recursive=True):
                    try:
                        file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                        if file_time < cutoff_date:
                            os.remove(log_file)
                            cleaned_count += 1
                    except Exception as e:
                        self.logger.warning(f"清理日志文件失败: {log_file}, 错误: {e}")

            if cleaned_count > 0:
                self.logger.info(f"清理了 {cleaned_count} 个超过 {max_days} 天的旧日志文件")

        except Exception as e:
            self.logger.error(f"日志清理过程失败: {e}")

    def setup_file_based_logging(self, file_path: str):
        """🔧 设置基于文件位置的日志保存 - 和退款功能一样的逻辑"""
        self.current_file_path = file_path

        if file_path and os.path.exists(file_path):
            # 在选择文件的目录下创建日志文件夹
            file_dir = os.path.dirname(file_path)
            log_dir = os.path.join(file_dir, "数据处理日志")

            # 确保日志目录存在
            os.makedirs(log_dir, exist_ok=True)

            # 生成基于文件位置的日志文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            log_filename = f"数据导入日志_{timestamp}.log"
            self.log_file_path = os.path.join(log_dir, log_filename)

            # 添加文件处理器到logger
            import logging
            file_handler = logging.FileHandler(self.log_file_path, encoding='utf-8')
            file_formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_formatter)
            # 🔧 修复：现在AppLogger类已经有addHandler方法了
            self.logger.addHandler(file_handler)

            self.logger.info(f"🔧 数据导入日志将保存到: {self.log_file_path}")
        else:
            self.logger.warning("无法设置基于文件位置的日志：文件路径无效")

    def _create_all_tables(self):
        """创建所有数据库表结构（包括原表和新的Refunding、Close表）"""
        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                # 创建所有表（原表 + Refunding表 + Close表）
                for table_name, create_sql in TABLE_SCHEMAS.items():
                    try:
                        cursor.execute(create_sql)
                        self.logger.debug(f"Table {table_name} created/verified")
                    except Exception as e:
                        self.logger.error(f"Failed to create table {table_name}: {e}")
                        raise

                conn.commit()
                self.logger.info("All database table structures created/verified")
                self.logger.info("New tables: IOT_Sales_Refunding, IOT_Sales_Close, ZERO_Sales_Refunding, ZERO_Sales_Close, APP_Sales_Refunding, APP_Sales_Close")

        except Exception as e:
            raise DatabaseError(f"Failed to create database tables: {e}", operation="create_tables")
    
    def standardize_date(self, date_str: Any) -> Optional[str]:
        """
        🔧 恢复原始：标准化日期格式，只保留日期部分

        Args:
            date_str: 日期字符串或其他格式

        Returns:
            标准化的日期字符串 (YYYY-MM-DD) 或 None
        """
        if pd.isna(date_str) or not str(date_str).strip():
            return None

        s = str(date_str).strip()

        # 🔧 恢复：匹配完整的日期时间格式，但只返回日期部分
        m = re.fullmatch(r'(\d{4})[-/.](\d{1,2})[-/.](\d{1,2})[ T](\d{1,2}):(\d{1,2}):(\d{1,2})', s)
        if m:
            y, mo, d, _, _, _ = m.groups()  # 忽略时间部分
            return f"{int(y):04d}-{int(mo):02d}-{int(d):02d}"

        # 🔧 恢复：匹配只有日期的格式
        m = re.fullmatch(r'(\d{4})[-/.](\d{1,2})[-/.](\d{1,2})', s)
        if m:
            y, mo, d = m.groups()
            return f"{int(y):04d}-{int(mo):02d}-{int(d):02d}"

        # 🔧 Bug修复：正确处理Excel序列号，考虑Excel的1900年闰年bug
        if s.isdigit() and len(s) >= 5:
            try:
                serial = int(s)
                # Excel错误地认为1900年是闰年，所以序列号60对应1900-02-29（不存在的日期）
                # 序列号61对应1900-03-01，所以序列号>60的需要减1来修正
                if serial > 60:  # 🔧 修复：应该是>60而不是>=60
                    serial -= 1
                base = datetime(1899, 12, 30)
                dt = base + timedelta(days=serial)
                if 1950 <= dt.year <= 2100:
                    return dt.strftime("%Y-%m-%d")
            except (ValueError, TypeError, OverflowError) as e:
                # 🔧 Bug修复：具体化异常处理，记录调试信息
                self.logger.debug(f"Excel序列号转换失败: {s} - {e}")
                pass

        # 使用dateutil解析
        try:
            dt = dateparser.parse(s, dayfirst=False, yearfirst=True)
            if dt:
                # 🔧 恢复原始：只返回日期部分
                return dt.strftime("%Y-%m-%d")
        except (ValueError, OverflowError):
            pass

        return s
    
    def validate_file(self, file_path: str) -> bool:
        """
        验证文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否验证通过
        """
        is_valid, error_msg = input_validator.validate_file_path(file_path)
        if not is_valid:
            raise FileValidationError(file_path, error_msg)
        
        # 检查Excel文件格式
        is_valid, error_msg = input_validator.validate_excel_file(file_path)
        if not is_valid:
            raise FileValidationError(file_path, error_msg)
        
        return True
    
    def load_and_validate_data(self, file_path: str, platform: str) -> pd.DataFrame:
        """
        加载和验证数据
        
        Args:
            file_path: 文件路径
            platform: 平台类型
            
        Returns:
            验证后的DataFrame
        """
        try:
            # 智能检测并读取正确的sheet
            sheet_name = self._detect_correct_sheet(file_path, platform)

            # 读取Excel文件（处理编码问题）
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
            except UnicodeDecodeError:
                # 如果遇到编码问题，尝试使用xlrd引擎
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='xlrd')
            except Exception as e:
                raise DataProcessingError(f"读取Excel文件失败: {e}", file_path=file_path, stage="file_reading")

            # 🔧 安全修复：检查DataFrame是否为空
            if df is None:
                raise DataProcessingError("读取的DataFrame为None", file_path=file_path, stage="file_reading")

            if df.empty:
                raise DataProcessingError("文件中没有数据", file_path=file_path, stage="data_validation")

            # 处理DataFrame中的特殊字符
            df = self._clean_unicode_characters(df)

            self.logger.info(f"Successfully read file: {file_path}, rows: {len(df)}")
            
            # 验证数据完整性（只检查最基本的必需列）
            required_columns = ['Order_price', 'Order_time']  # 减少必需列，提高兼容性
            is_valid, error_msg = input_validator.validate_dataframe_columns(
                df, required_columns, os.path.basename(file_path)
            )
            if not is_valid:
                self.logger.warning(f"Data validation warning: {error_msg}")
                # 不抛出异常，而是警告并继续处理
                # print(f"WARNING: {error_msg}")  # 避免Unicode编码问题
                # print("Continuing with available columns...")  # 避免Unicode编码问题
            
            # 数据清洗
            df = self._clean_data(df)

            # 标准化列名（处理空格和特殊字符）
            df = self._standardize_column_names(df)

            # 处理缺失列（自动添加默认值）
            df = self._handle_missing_columns(df, platform)

            # 标准化日期
            if 'Order_time' in df.columns:
                df['Order_time'] = df['Order_time'].apply(self.standardize_date)
            if 'Payment_date' in df.columns:
                df['Payment_date'] = df['Payment_date'].apply(self.standardize_date)

            # 🔧 修复：移除 Import_Date（数据库中已删除此字段）
            # df['Import_Date'] = datetime.now().strftime("%Y-%m-%d")

            # 🆕 功能1：数据过滤验证 - 只保留Order_status为"Finished"的记录
            df = self._filter_finished_orders(df, platform)

            self.logger.info(f"Data validation and cleaning completed, valid rows: {len(df)}")
            return df
            
        except Exception as e:
            if isinstance(e, (DataProcessingError, FileValidationError)):
                raise
            raise DataProcessingError(f"Failed to load data: {e}", file_path=file_path, stage="data_loading")

    def _filter_finished_orders(self, df: pd.DataFrame, platform: str) -> pd.DataFrame:
        """
        🔧 修复：智能识别导入模式 - 保留所有状态的订单，用于智能路由到不同表

        Args:
            df: 原始数据框
            platform: 平台类型

        Returns:
            处理后的数据框（智能识别导入模式下保留所有状态）
        """
        original_count = len(df)

        # 🔧 关键修复：智能识别导入模式下，保留所有状态的订单
        # 不再过滤，让智能路由决定每个订单应该导入到哪个表
        self.logger.info(f"智能识别导入模式：保留所有 {original_count} 条记录，将根据订单状态智能路由到对应表")

        # 检查是否存在Order_status列
        if 'Order_status' not in df.columns:
            self.logger.warning("Order_status column not found, adding default 'Finished' status")
            df['Order_status'] = 'Finished'
            self.logger.info(f"Added default 'Finished' status to {original_count} records")
            return df

        # 🆕 分析订单状态分布（用于调试和日志）
        if 'Order_status' in df.columns:
            status_counts = df['Order_status'].value_counts()
            self.logger.info(f"订单状态分布: {dict(status_counts)}")

            # 预测路由分布
            routing_preview = {}
            for status, count in status_counts.items():
                target_table = self._determine_target_table(platform, status, None, getattr(self, 'current_order_type', None))
                if target_table not in routing_preview:
                    routing_preview[target_table] = 0
                routing_preview[target_table] += count

            self.logger.info(f"预计路由分布: {routing_preview}")

        return df

    def _detect_correct_sheet(self, file_path: str, platform: str) -> str:
        """
        智能检测正确的sheet名称

        Args:
            file_path: Excel文件路径
            platform: 平台类型 (IOT, ZERO, APP)

        Returns:
            正确的sheet名称，如果找不到则返回None（使用默认sheet）
        """
        try:
            # 获取所有sheet名称
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names

            self.logger.info(f"Available sheets: {sheet_names}")

            # 根据平台类型查找对应的sheet
            platform_upper = platform.upper()

            # 查找包含平台名称的sheet
            for sheet_name in sheet_names:
                if platform_upper in sheet_name.upper():
                    self.logger.info(f"Found matching sheet for {platform}: {sheet_name}")
                    return sheet_name

            # 🔧 智能处理：如果没找到特定的sheet，尝试智能选择
            self.logger.warning(f"⚠️ 未找到包含 '{platform}' 标识的工作表")
            self.logger.info(f"可用的工作表: {sheet_names}")

            # 智能处理策略
            if len(sheet_names) == 1:
                # 如果只有一个工作表，使用该工作表
                selected_sheet = sheet_names[0]
                self.logger.info(f"🔧 智能选择：文件只有一个工作表，将使用 '{selected_sheet}' 导入到 {platform}_Sales 表")
                self.logger.info(f"📝 注意：数据将从 '{selected_sheet}' 工作表导入到 {platform}_Sales 数据表")
                return selected_sheet
            elif len(sheet_names) > 1:
                # 如果有多个工作表，尝试使用第一个工作表
                selected_sheet = sheet_names[0]
                self.logger.info(f"🔧 智能选择：使用第一个工作表 '{selected_sheet}' 导入到 {platform}_Sales 表")
                self.logger.info(f"📝 注意：数据将从 '{selected_sheet}' 工作表导入到 {platform}_Sales 数据表")
                self.logger.info(f"💡 如果需要使用其他工作表，请重命名工作表包含 '{platform}' 标识")
                return selected_sheet
            else:
                # 没有工作表，这是一个错误
                self.logger.error(f"❌ Excel文件中没有任何工作表")
                raise ValueError(f"Excel文件中没有任何工作表")

        except Exception as e:
            if "未找到包含" in str(e):
                # 重新抛出我们的特定错误
                raise
            self.logger.error(f"Error detecting sheet: {e}")
            raise ValueError(f"检测工作表时发生错误: {e}")

    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗数据

        Args:
            df: 原始DataFrame

        Returns:
            清洗后的DataFrame
        """
        # 删除完全空白的行
        df = df.dropna(how='all')

        # 填充空值
        df = df.fillna('')

        # 清理字符串列的空白字符和特殊字符
        for col in df.select_dtypes(include=['object']).columns:
            try:
                # 安全地处理字符串列
                df[col] = df[col].apply(lambda x: self._clean_string(str(x)) if pd.notna(x) else '')
            except Exception as e:
                self.logger.warning(f"Failed to clean column {col}: {e}")
                # 如果清理失败，至少确保是字符串类型
                df[col] = df[col].astype(str).str.strip()

        return df

    def _clean_unicode_characters(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理DataFrame中的Unicode字符，避免编码问题

        Args:
            df: 原始DataFrame

        Returns:
            清理后的DataFrame
        """
        try:
            # 处理所有字符串列
            for col in df.select_dtypes(include=['object']).columns:
                if df[col].dtype == 'object':
                    # 转换为字符串并清理特殊字符
                    df[col] = df[col].astype(str).apply(lambda x: self._clean_string(x))

            return df
        except Exception as e:
            self.logger.warning(f"Unicode cleaning failed: {e}, continuing with original data")
            return df

    def _clean_transaction_num(self, value) -> str:
        """
        🔧 专门清理Transaction Num字段，处理多行文本和特殊格式

        Args:
            value: 原始Transaction Num值

        Returns:
            清理后的Transaction Num字符串
        """
        if pd.isna(value) or value == '' or str(value).lower() == 'nan':
            return ''

        try:
            # 转换为字符串
            text = str(value).strip()

            # 🔧 关键修复：处理多行文本，如 "603100001\n(TXN: 2983619124)"
            if '\n' in text:
                lines = text.split('\n')
                # 找到第一个非空且不以括号开头的行
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('(') and not line.startswith('TXN'):
                        text = line
                        break

            # 🔧 移除括号及其内容，如 "603100001 (TXN: 2983619124)" -> "603100001"
            if '(' in text:
                text = text.split('(')[0].strip()

            # 🔧 移除其他可能的分隔符和文本
            text = text.replace('TXN:', '').replace('TXN', '').strip()

            # 🔧 只保留数字字符
            cleaned_text = ''.join(c for c in text if c.isdigit())

            # 🔧 验证结果
            if cleaned_text and len(cleaned_text) >= 6:  # Transaction Num通常至少6位
                return cleaned_text
            else:
                # 如果清理后太短，返回原始文本的数字部分
                original_digits = ''.join(c for c in str(value) if c.isdigit())
                return original_digits if len(original_digits) >= 6 else ''

        except Exception as e:
            # 出错时尝试提取数字
            _ = e  # 🔧 Bug修复：标记未使用的变量
            try:
                return ''.join(c for c in str(value) if c.isdigit())
            except (TypeError, AttributeError) as fallback_error:
                # 🔧 Bug修复：具体化异常处理，记录调试信息
                self.logger.debug(f"数字提取失败: {value} - {fallback_error}")
                return ''

    def _clean_string(self, text: str) -> str:
        """
        清理字符串中的特殊Unicode字符

        Args:
            text: 原始字符串

        Returns:
            清理后的字符串
        """
        if pd.isna(text) or text == 'nan':
            return ''

        try:
            # 转换为字符串
            text = str(text)

            # 移除或替换常见的特殊字符
            replacements = {
                '\u2013': '-',  # en dash
                '\u2014': '-',  # em dash
                '\u2018': "'",  # left single quotation mark
                '\u2019': "'",  # right single quotation mark
                '\u201c': '"',  # left double quotation mark
                '\u201d': '"',  # right double quotation mark
                '\u2026': '...',  # horizontal ellipsis
                '\u00a0': ' ',  # non-breaking space
                '\u200b': '',   # zero width space
                '\ufeff': '',   # byte order mark
            }

            for old_char, new_char in replacements.items():
                text = text.replace(old_char, new_char)

            # 移除其他不可打印字符，但保留中文字符
            cleaned_text = ''.join(char for char in text if char.isprintable() or '\u4e00' <= char <= '\u9fff')

            return cleaned_text.strip()

        except (UnicodeError, AttributeError, TypeError) as e:
            # 🔧 Bug修复：具体化异常处理，记录调试信息
            self.logger.debug(f"字符串清理失败: {text[:50] if text else 'None'} - {e}")
            # 如果清理失败，返回空字符串
            return ''

    def _safe_convert_transaction_num(self, value) -> str:
        """
        🔧 安全转换Transaction Num，防止有效值变成NaN

        Args:
            value: 原始Transaction Num值

        Returns:
            清理后的Transaction Num字符串
        """
        # 🔧 关键修复：保护有效的Transaction Num不被转换为空值
        if pd.isna(value):
            return ''

        # 转换为字符串
        str_value = str(value).strip()

        # 🔧 修复：如果是'nan'字符串，返回空，但保护有效数字
        if str_value.lower() in ['nan', 'none', '']:
            return ''

        # 🔧 关键：如果是纯数字，直接返回（保护有效的Transaction Num）
        if str_value.isdigit():
            return str_value

        # 🔧 如果是浮点数格式（如603100001.0），转换为整数
        try:
            float_val = float(str_value)
            if not pd.isna(float_val) and float_val > 0:
                int_val = int(float_val)
                if int_val >= 100000:  # 至少6位数才是有效的Transaction Num
                    return str(int_val)
        except (ValueError, OverflowError):
            pass

        # 🔧 如果包含数字，提取数字部分
        digits_only = ''.join(c for c in str_value if c.isdigit())
        if digits_only and len(digits_only) >= 6:  # Transaction Num通常至少6位
            return digits_only

        # 🔧 最后的保护：如果原始值看起来像有效的Transaction Num，保留它
        if len(str_value) >= 6 and any(c.isdigit() for c in str_value):
            # 清理但保留主要内容
            cleaned = ''.join(c for c in str_value if c.isalnum())
            if cleaned:
                return cleaned

        return ''

    def _standardize_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化列名，处理空格和特殊字符

        Args:
            df: 原始DataFrame

        Returns:
            列名标准化后的DataFrame
        """
        # 列名映射表 - 将Excel中的列名映射到数据库列名
        column_mapping = {
            # 处理空格和标点问题
            'Copartner name': 'Copartner_name',
            'Order No': 'Order_No',
            'Order No.': 'Order_No',  # 处理带点号的情况
            'Order types': 'Order_types',
            'Order status': 'Order_status',
            'Order price': 'Order_price',
            'Order time': 'Order_time',
            'Equipment ID': 'Equipment_ID',
            'Equipment name': 'Equipment_name',
            'Branch name': 'Branch_name',
            'Payment date': 'Payment_date',
            'User name': 'User_name',
            'Transaction Num': 'Transaction_Num',
            # 🔧 修复：移除 Import_Date 映射
            # 'Import Date': 'Import_Date',

            # 处理已处理数据的额外列
            'Time': 'Time',
            'Matched Order ID': 'Matched_Order_ID',
            'OrderTime_dt': 'OrderTime_dt',
            'Payment': 'Payment',

            # 处理其他可能的变体
            'Copartner_name': 'Copartner_name',  # 已经正确的保持不变
            'Order_No': 'Order_No',
            'Order_types': 'Order_types',
            'Order_status': 'Order_status',
            'Order_price': 'Order_price',
            'Order_time': 'Order_time',
            'Equipment_ID': 'Equipment_ID',
            'Equipment_name': 'Equipment_name',
            'Branch_name': 'Branch_name',
            'Payment_date': 'Payment_date',
            'User_name': 'User_name',
            'Transaction_Num': 'Transaction_Num',
            # 🔧 修复：移除 Import_Date
            # 'Import_Date': 'Import_Date',
        }

        # 使用传统列名映射（简化版本，移除智能映射器依赖）
        self.logger.info(" 使用传统列名映射...")

        # 应用列名映射
        df_renamed = df.rename(columns=column_mapping)

        # 🔧 关键修复：只保留已知的数据库列，过滤掉额外列
        known_columns = set(column_mapping.values())
        # 🔧 修复：只添加数据库表中实际存在的列，移除 Matched_Flag（数据库表中不存在）
        # Transaction ID 也不在数据库表中，移除
        # known_columns 现在只包含数据库表中实际存在的列

        # 过滤列：只保留已知列
        available_columns = [col for col in df_renamed.columns if col in known_columns]

        # 🔧 关键修复：确保至少有一些列被保留
        if not available_columns:
            self.logger.warning("没有找到已知的列，保留所有列以避免数据丢失")
            available_columns = list(df_renamed.columns)

        df_filtered = df_renamed[available_columns].copy()

        # 🔧 关键修复：检查过滤后的DataFrame是否为空
        if df_filtered is None or df_filtered.empty:
            self.logger.warning("过滤后的DataFrame为空，使用原始数据")
            df_filtered = df_renamed.copy()

        # 记录过滤结果
        filtered_out = set(df_renamed.columns) - set(available_columns)
        if filtered_out:
            self.logger.info(f"🚫 过滤掉的额外列: {list(filtered_out)}")

        self.logger.info(f"✅ 保留的列: {available_columns}")

        # 标准化数据类型
        df_standardized = self._standardize_data_types(df_filtered)

        # 🔧 关键修复：最终检查
        if df_standardized is None:
            self.logger.error("标准化后的DataFrame为None，这不应该发生")
            raise DataProcessingError("数据标准化失败：结果为None")

        return df_standardized

    def _standardize_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化数据类型，确保与数据库兼容

        Args:
            df: 原始DataFrame

        Returns:
            数据类型标准化后的DataFrame
        """
        try:
            # 需要转换为字符串的列（数据库中是TEXT类型，包含所有列）
            text_columns = [
                'Copartner_name', 'Order_No', 'Order_types', 'Order_status',
                'Order_price', 'Payment', 'Order_time', 'Equipment_ID',
                'Equipment_name', 'Branch_name', 'Payment_date', 'User_name',
                'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num',
                # 🔧 修复：移除 Import_Date
                # 'Import_Date'
            ]

            # 转换数据类型
            for col in text_columns:
                if col in df.columns:
                    # 🔧 修复：对Transaction_Num字段使用特殊处理
                    if col == 'Transaction_Num':
                        df[col] = df[col].apply(self._safe_convert_transaction_num)
                    else:
                        # 将数值类型转换为字符串，保持空值
                        df[col] = df[col].astype(str).replace('nan', '').replace('None', '')
                        # 清理字符串
                        df[col] = df[col].apply(lambda x: self._clean_string(str(x)) if pd.notna(x) and str(x).strip() else '')

            self.logger.info("Data types standardized for database compatibility")
            return df

        except Exception as e:
            self.logger.warning(f"Data type standardization failed: {e}, continuing with original data")
            return df

    def _handle_missing_columns(self, df: pd.DataFrame, platform: str = None) -> pd.DataFrame:
        """
        处理缺失列，自动添加默认值

        Args:
            df: 数据框
            platform: 平台类型

        Returns:
            处理后的数据框
        """
        # 🔧 修复：根据平台设置不同的默认值
        platform_upper = platform.upper() if platform else 'UNKNOWN'

        # 定义常见的列和默认值（包含数据库表中的所有列）
        default_columns = {
            'Copartner_name': f'{platform_upper}_Partner' if platform else '',
            'Order_No': '',
            'Order_types': 'API' if platform_upper == 'APP' else 'Normal',  # APP平台默认为API订单
            'Order_status': 'Finished',  # 默认为完成状态
            'Order_price': '0.0',
            'Payment': '',
            'Order_time': '',
            'Equipment_ID': '',
            'Equipment_name': '',
            'Branch_name': '',
            'Payment_date': '',
            'User_name': '',
            'Time': '',
            'Matched_Order_ID': '',
            'OrderTime_dt': '',
            'Transaction_Num': '',
            # 🔧 修复：移除 Import_Date
            # 'Import_Date': ''
        }

        if platform:
            self.logger.debug(f"为 {platform_upper} 平台设置默认列值")

        added_columns = []

        # 检查并添加缺失的列
        for col_name, default_value in default_columns.items():
            if col_name not in df.columns:
                df[col_name] = default_value
                added_columns.append(col_name)

        if added_columns:
            # print(f"INFO: Added missing columns with defaults: {', '.join(added_columns)}")  # 避免Unicode编码问题
            self.logger.info(f"Added missing columns: {added_columns}")

        return df

    def _filter_columns_for_table(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """
        过滤DataFrame，只保留数据库表中实际存在的列

        Args:
            df: 要过滤的DataFrame
            table_name: 目标表名

        Returns:
            过滤后的DataFrame
        """
        # 🚨 修复：定义数据库表中实际存在的列（包含所有表）
        base_columns = [
            'Copartner_name', 'Order_No', 'Order_types', 'Order_status',
            'Order_price', 'Payment', 'Order_time', 'Equipment_ID',
            'Equipment_name', 'Branch_name', 'Payment_date', 'User_name',
            'Time', 'Matched_Order_ID', 'OrderTime_dt', 'Transaction_Num'
            # 🔧 修复：移除 Import_Date
        ]

        table_columns = {
            # IOT 平台表
            'IOT_Sales': base_columns.copy(),
            'IOT_Sales_Refunding': base_columns.copy(),
            'IOT_Sales_Close': base_columns.copy(),

            # ZERO 平台表
            'ZERO_Sales': base_columns.copy(),
            'ZERO_Sales_Refunding': base_columns.copy(),
            'ZERO_Sales_Close': base_columns.copy(),

            # APP 平台表
            'APP_Sales': base_columns.copy(),
            'APP_Sales_Refunding': base_columns.copy(),
            'APP_Sales_Close': base_columns.copy(),
        }

        # 获取目标表的列
        valid_columns = table_columns.get(table_name, [])

        if not valid_columns:
            self.logger.warning(f"未知的表名: {table_name}，保留所有列")
            return df

        # 过滤列：只保留数据库表中存在的列
        available_columns = [col for col in df.columns if col in valid_columns]

        if not available_columns:
            self.logger.warning(f"没有找到 {table_name} 表的有效列")
            return pd.DataFrame()  # 返回空DataFrame

        # 记录过滤结果
        filtered_out = set(df.columns) - set(available_columns)
        if filtered_out:
            self.logger.info(f"🚫 为表 {table_name} 过滤掉的列: {list(filtered_out)}")

        self.logger.info(f"✅ 为表 {table_name} 保留的列: {available_columns}")

        return df[available_columns].copy()

    def smart_incremental_duplicate_check(self, df: pd.DataFrame, platform: str) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        🔧 优化：智能增量重复检测 - 使用高效的样本检测策略

        Args:
            df: 要检查的DataFrame
            platform: 平台类型

        Returns:
            (完全重复数据, 部分重复数据, 新数据)
        """
        try:
            self.logger.info(f"🔍 开始智能增量重复检测，数据量: {len(df)}")
            print(f"🔧 [DEBUG] 开始智能增量重复检测")

            # 🔧 优化：使用与check_duplicate_data相同的高效逻辑
            # 只检查每种状态的第一条数据
            sample_data = self._get_sample_data_by_status(df, platform)
            if sample_data.empty:
                self.logger.info("没有有效的样本数据")
                return pd.DataFrame(), pd.DataFrame(), df

            print(f"🔧 [DEBUG] 增量检测样本数据: {len(sample_data)} 条")

            # 🔧 优化：基于Order_time提取日期，只检查当天数据
            has_duplicates = self._check_daily_duplicates(sample_data, platform)

            if has_duplicates:
                print(f"🔧 [DEBUG] 增量检测发现当天重复数据")
                # 增量模式：将所有数据标记为"部分重复"，让用户选择增量更新策略
                self.logger.warning(f"增量检测发现当天重复数据，建议使用增量更新")
                return pd.DataFrame(), df, pd.DataFrame()  # (完全重复, 部分重复, 新数据)
            else:
                print(f"🔧 [DEBUG] 增量检测：当天没有重复数据")
                return pd.DataFrame(), pd.DataFrame(), df

        except Exception as e:
            self.logger.error(f"智能增量重复检测失败: {e}")
            print(f"🔧 [DEBUG] 增量检测失败: {e}")
            # 发生错误时，将所有数据视为新数据
            return pd.DataFrame(), pd.DataFrame(), df



    def _check_duplicates_by_table(self, df: pd.DataFrame, target_table: str, platform: str) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        根据目标表使用不同的重复检测策略

        Args:
            df: 要检查的数据
            target_table: 目标表名
            platform: 平台类型

        Returns:
            (重复数据, 部分重复数据, 新数据)
        """
        try:
            with get_connection() as conn:  # 🔧 修复：恢复conn变量名
                self.logger.debug(f"检查表 {target_table}，平台 {platform}")  # 使用platform参数
                # 检查表是否存在
                cursor = conn.connection.cursor()
                # 🔧 安全修复：使用参数化查询防止SQL注入
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (target_table,))
                if not cursor.fetchone():
                    self.logger.info(f"表 {target_table} 不存在，所有数据都是新的")
                    return pd.DataFrame(), pd.DataFrame(), df

                # 根据表类型选择匹配策略
                if '_Refunding' in target_table:
                    return self._check_refunding_duplicates(df, target_table, conn)
                elif '_Close' in target_table:
                    return self._check_close_duplicates(df, target_table, conn)
                else:
                    # 主表数据，使用标准检测
                    return self._check_standard_duplicates(df, target_table, conn)

        except Exception as e:
            self.logger.error(f"表 {target_table} 重复检测失败: {e}")
            # 出错时保守处理，返回所有数据为新数据
            return pd.DataFrame(), pd.DataFrame(), df

    def _check_refunding_duplicates(self, df: pd.DataFrame, target_table: str, conn) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        检查Refunding表的重复数据 - 使用Order_time + Transaction_Num匹配
        """
        self.logger.info(f"使用Refunding匹配策略: Order_time + Transaction_Num")

        # 🔧 检查必需列是否存在
        required_columns = ['Order_time', 'Transaction_Num']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            self.logger.warning(f"缺少必需列 {missing_columns}，使用标准检测")
            return self._check_standard_duplicates(df, target_table, conn)

        # 获取现有数据
        try:
            # 🔒 安全修复：使用安全的表名验证和SQL构建
            safe_table = SQLSecurityValidator.sanitize_table_name(target_table)
            existing_df = pd.read_sql(
                f"SELECT Order_time, Transaction_Num, Order_No, Payment FROM {safe_table}",
                conn.connection
            )
        except Exception as e:
            self.logger.warning(f"查询表 {target_table} 失败: {e}，假设表为空")
            return pd.DataFrame(), pd.DataFrame(), df

        if existing_df.empty:
            self.logger.info(f"表 {target_table} 为空，所有数据都是新的")
            return pd.DataFrame(), pd.DataFrame(), df

        # 🔧 修复：标准化时间格式，只保留日期部分，与standardize_date方法保持一致
        df['Order_time_normalized'] = pd.to_datetime(df['Order_time'], errors='coerce').dt.strftime('%Y-%m-%d')
        existing_df['Order_time_normalized'] = pd.to_datetime(existing_df['Order_time'], errors='coerce').dt.strftime('%Y-%m-%d')

        # 🔧 安全创建匹配键，处理空值
        df['match_key'] = (df['Order_time_normalized'].fillna('').astype(str) + '|' +
                          df['Transaction_Num'].fillna('').astype(str))
        existing_df['match_key'] = (existing_df['Order_time_normalized'].fillna('').astype(str) + '|' +
                                   existing_df['Transaction_Num'].fillna('').astype(str))

        # 过滤掉无效的匹配键（全为空或只有分隔符）
        df['match_key'] = df['match_key'].replace(['|', 'NaT|', '|nan', 'NaT|nan'], pd.NA)
        existing_df['match_key'] = existing_df['match_key'].replace(['|', 'NaT|', '|nan', 'NaT|nan'], pd.NA)

        # 查找重复
        existing_keys = set(existing_df['match_key'].dropna())
        df['is_duplicate'] = df['match_key'].isin(existing_keys)

        duplicates = df[df['is_duplicate']].copy()
        new_data = df[~df['is_duplicate']].copy()

        # 清理临时列
        for temp_df in [duplicates, new_data]:
            if not temp_df.empty:
                temp_df.drop(['Order_time_normalized', 'match_key', 'is_duplicate'], axis=1, inplace=True, errors='ignore')

        self.logger.info(f"Refunding重复检测: 重复 {len(duplicates)} 条, 新数据 {len(new_data)} 条")
        return duplicates, pd.DataFrame(), new_data

    def _check_close_duplicates(self, df: pd.DataFrame, target_table: str, conn) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        检查Close表的重复数据 - 使用Order_time + Equipment_ID + Payment匹配
        """
        self.logger.info(f"使用Close匹配策略: Order_time + Equipment_ID + Payment")

        # 🔧 检查必需列是否存在
        required_columns = ['Order_time', 'Equipment_ID', 'Payment']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            self.logger.warning(f"缺少必需列 {missing_columns}，使用标准检测")
            return self._check_standard_duplicates(df, target_table, conn)

        # 获取现有数据
        try:
            # 🔒 安全修复：使用安全的表名验证和SQL构建
            safe_table = SQLSecurityValidator.sanitize_table_name(target_table)
            existing_df = pd.read_sql(
                f"SELECT Order_time, Equipment_ID, Payment, Order_No FROM {safe_table}",
                conn.connection
            )
        except Exception as e:
            self.logger.warning(f"查询表 {target_table} 失败: {e}，假设表为空")
            return pd.DataFrame(), pd.DataFrame(), df

        if existing_df.empty:
            self.logger.info(f"表 {target_table} 为空，所有数据都是新的")
            return pd.DataFrame(), pd.DataFrame(), df

        # 🔧 修复：标准化时间格式，只保留日期部分，与standardize_date方法保持一致
        df['Order_time_normalized'] = pd.to_datetime(df['Order_time'], errors='coerce').dt.strftime('%Y-%m-%d')
        existing_df['Order_time_normalized'] = pd.to_datetime(existing_df['Order_time'], errors='coerce').dt.strftime('%Y-%m-%d')

        # 🔧 安全创建匹配键 (Order_time + Equipment_ID + Payment)，处理空值
        df['match_key'] = (df['Order_time_normalized'].fillna('').astype(str) + '|' +
                          df['Equipment_ID'].fillna('').astype(str) + '|' +
                          df['Payment'].fillna('').astype(str))
        existing_df['match_key'] = (existing_df['Order_time_normalized'].fillna('').astype(str) + '|' +
                                   existing_df['Equipment_ID'].fillna('').astype(str) + '|' +
                                   existing_df['Payment'].fillna('').astype(str))

        # 过滤掉无效的匹配键
        invalid_patterns = ['||', 'NaT||', '||nan', 'NaT||nan', '|nan|', '|nan|nan']
        df['match_key'] = df['match_key'].replace(invalid_patterns, pd.NA)
        existing_df['match_key'] = existing_df['match_key'].replace(invalid_patterns, pd.NA)

        # 查找重复
        existing_keys = set(existing_df['match_key'].dropna())
        df['is_duplicate'] = df['match_key'].isin(existing_keys)

        duplicates = df[df['is_duplicate']].copy()
        new_data = df[~df['is_duplicate']].copy()

        # 清理临时列
        for temp_df in [duplicates, new_data]:
            if not temp_df.empty:
                temp_df.drop(['Order_time_normalized', 'match_key', 'is_duplicate'], axis=1, inplace=True, errors='ignore')

        self.logger.info(f"Close重复检测: 重复 {len(duplicates)} 条, 新数据 {len(new_data)} 条")
        return duplicates, pd.DataFrame(), new_data

    def _check_standard_duplicates(self, df: pd.DataFrame, target_table: str, conn) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        检查主表的重复数据 - 使用标准策略
        """
        self.logger.info(f"使用标准匹配策略: Transaction_Num + Order_time")

        # 获取现有数据
        # 🔒 安全修复：使用安全的表名验证和SQL构建
        safe_table = SQLSecurityValidator.sanitize_table_name(target_table)
        existing_df = pd.read_sql(
            f"SELECT Transaction_Num, Order_time, Payment_date, Equipment_ID, Order_price FROM {safe_table}",
            conn.connection
        )

        if existing_df.empty:
            self.logger.info(f"表 {target_table} 为空，所有数据都是新的")
            return pd.DataFrame(), pd.DataFrame(), df

        # 使用现有的增强重复检测逻辑
        return self._enhanced_duplicate_detection(df, existing_df)

    def check_duplicate_data(self, df: pd.DataFrame, platform: str) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        🔧 修复：增强重复数据检测机制 - 检查所有相关表（主表、Refunding表、Close表）

        Args:
            df: 要检查的DataFrame
            platform: 平台类型

        Returns:
            (完全重复数据, 部分重复数据, 新数据)
        """
        try:
            print(f"🔧 [DEBUG] 开始高效重复检测")

            # 🔧 优化：只检查每种状态的第一条数据
            sample_data = self._get_sample_data_by_status(df, platform)
            if sample_data.empty:
                self.logger.info("没有有效的样本数据")
                return pd.DataFrame(), pd.DataFrame(), df

            print(f"🔧 [DEBUG] 样本数据: {len(sample_data)} 条")

            # 🔧 优化：基于Order_time提取日期，只检查当天数据
            has_duplicates = self._check_daily_duplicates(sample_data, platform)

            if has_duplicates:
                print(f"🔧 [DEBUG] 检测到当天重复数据，开始精确分类")
                # 🔧 改进：使用精确的重复数据分类，而不是简单地将所有数据标记为"部分重复"
                self.logger.info(f"检测到当天重复数据，开始精确分类分析")

                try:
                    # 获取相关表的现有数据进行精确比较
                    tables_to_check = self._get_tables_to_clear(platform)
                    all_existing_data = []

                    with get_connection() as conn:
                        for table_name in tables_to_check:
                            try:
                                # 🔧 优化：只获取必要的列，提高查询效率
                                # 🔒 安全修复：使用安全的表名验证和SQL构建
                                safe_table = SQLSecurityValidator.sanitize_table_name(table_name)
                                existing_df = pd.read_sql(
                                    f"SELECT Transaction_Num, Order_time, Payment_date, Equipment_ID, Order_price, Order_No FROM {safe_table}",
                                    conn.connection
                                )
                                if not existing_df.empty:
                                    all_existing_data.append(existing_df)
                                    print(f"🔧 [DEBUG] 从表 {table_name} 获取 {len(existing_df)} 条现有数据")
                            except Exception as e:
                                print(f"🔧 [DEBUG] 查询表 {table_name} 失败: {e}")
                                self.logger.debug(f"查询表 {table_name} 失败: {e}")
                                continue

                    if all_existing_data:
                        # 合并所有现有数据
                        combined_existing_df = pd.concat(all_existing_data, ignore_index=True)
                        print(f"🔧 [DEBUG] 合并现有数据总计: {len(combined_existing_df)} 条")

                        # 使用精确的重复检测方法
                        result = self._enhanced_duplicate_detection(df, combined_existing_df, platform)
                        print(f"🔧 [DEBUG] 精确分类结果: 完全重复={len(result[0])}, 部分重复={len(result[1])}, 新数据={len(result[2])}")
                        self.logger.info(f"精确分类完成: 完全重复={len(result[0])}, 部分重复={len(result[1])}, 新数据={len(result[2])}")
                        return result
                    else:
                        # 没有现有数据，所有数据都是新的
                        print(f"🔧 [DEBUG] 没有找到现有数据，所有数据都是新的")
                        self.logger.info("没有找到现有数据，所有数据都是新的")
                        return pd.DataFrame(), pd.DataFrame(), df

                except Exception as e:
                    # 精确分类失败时，回退到原有逻辑
                    print(f"🔧 [DEBUG] 精确分类失败，回退到原有逻辑: {e}")
                    self.logger.warning(f"精确分类失败，回退到原有逻辑: {e}")
                    return pd.DataFrame(), df, pd.DataFrame()  # (完全重复, 部分重复, 新数据)
            else:
                print(f"🔧 [DEBUG] 当天没有重复数据")
                return pd.DataFrame(), pd.DataFrame(), df

        except Exception as e:
            raise DatabaseError(f"Failed to check duplicate data across tables: {e}", operation="duplicate_check", table=f"{platform}_Sales")

    def _get_sample_data_by_status(self, df: pd.DataFrame, platform: str) -> pd.DataFrame:
        """
        🔧 优化：获取每种状态的第一条数据作为样本

        Args:
            df: 要检查的DataFrame
            platform: 平台类型

        Returns:
            包含每种状态第一条数据的DataFrame
        """
        try:
            if df.empty:
                print(f"🔧 [DEBUG] 输入DataFrame为空")
                return pd.DataFrame()

            sample_data = []

            # 定义四种状态对应的表
            status_mapping = {
                'Finished': f"{platform}_Sales",
                'Close': f"{platform}_Sales_Close",
                'Refunded': f"{platform}_Sales_Refunding",
                'API': "APP_Sales"
            }

            if 'Order_status' in df.columns:
                # 🔧 修复：使用精确匹配，避免误匹配
                for status, table_name in status_mapping.items():
                    _ = table_name  # 🔧 Bug修复：标记未使用的变量（在某些分支中）
                    if status == 'API':
                        # API订单通过Order_types列识别
                        if 'Order_types' in df.columns:
                            # 精确匹配API订单类型
                            api_mask = (
                                (df['Order_types'].str.upper() == 'API') |
                                (df['Order_types'].str.upper() == 'API ORDER') |
                                (df['Order_types'].str.upper() == 'API_ORDER')
                            )
                            api_data = df[api_mask]
                            if not api_data.empty:
                                sample_data.append(api_data.iloc[0:1])
                    else:
                        # 🔧 修复：精确匹配订单状态
                        if status == 'Finished':
                            status_mask = (
                                (df['Order_status'].str.upper() == 'FINISHED') |
                                (df['Order_status'].str.upper() == 'FINISH') |
                                (df['Order_status'].str.upper() == 'COMPLETE') |
                                (df['Order_status'].str.upper() == 'COMPLETED')
                            )
                        elif status == 'Close':
                            status_mask = (
                                (df['Order_status'].str.upper() == 'CLOSE') |
                                (df['Order_status'].str.upper() == 'CLOSED') |
                                (df['Order_status'].str.upper() == 'CANCEL') |
                                (df['Order_status'].str.upper() == 'CANCELLED')
                            )
                        elif status == 'Refunded':
                            status_mask = (
                                (df['Order_status'].str.upper() == 'REFUNDED') |
                                (df['Order_status'].str.upper() == 'REFUND') |
                                (df['Order_status'].str.upper() == 'REFUNDING')
                            )
                        else:
                            # 默认使用包含匹配作为后备
                            status_mask = df['Order_status'].str.contains(status, case=False, na=False)

                        status_data = df[status_mask]
                        if not status_data.empty:
                            sample_data.append(status_data.iloc[0:1])
            else:
                # 如果没有状态列，取前5条作为样本
                sample_data.append(df.head(5))

            if sample_data:
                result = pd.concat(sample_data, ignore_index=True)
                print(f"🔧 [DEBUG] 获取样本数据: {len(result)} 条")
                return result
            else:
                print(f"🔧 [DEBUG] 没有找到样本数据")
                return pd.DataFrame()

        except Exception as e:
            print(f"🔧 [DEBUG] 获取样本数据失败: {e}")
            self.logger.error(f"获取样本数据失败: {e}")
            return pd.DataFrame()

    def _check_daily_duplicates(self, sample_data: pd.DataFrame, platform: str) -> bool:
        """
        🔧 优化：检查当天是否有重复数据

        Args:
            sample_data: 样本数据
            platform: 平台类型

        Returns:
            是否检测到当天重复数据
        """
        try:
            if sample_data.empty or 'Order_time' not in sample_data.columns:
                return False

            # 提取样本数据的日期
            sample_dates = set()
            for _, row in sample_data.iterrows():
                try:
                    order_time = pd.to_datetime(row['Order_time'])
                    date_str = order_time.strftime('%Y-%m-%d')
                    sample_dates.add(date_str)
                except (ValueError, TypeError, pd.errors.OutOfBoundsDatetime) as e:
                    # 🔧 Bug修复：具体化异常处理，记录调试信息
                    self.logger.debug(f"样本日期解析失败: {row.get('Order_time', 'N/A')} - {e}")
                    continue

            if not sample_dates:
                return False

            print(f"🔧 [DEBUG] 检查日期: {sample_dates}")

            # 检查数据库中这些日期是否有数据
            with get_connection() as conn:
                # 🔧 修复：根据平台确定要检查的表，不要硬编码APP_Sales
                tables_to_check = self._get_tables_to_clear(platform)

                for table_name in tables_to_check:
                    for date_str in sample_dates:
                        try:
                            # 🔧 修复：使用SQLite兼容的日期查询语法
                            # 支持多种日期格式的查询
                            query = f"""
                            SELECT COUNT(*) as count
                            FROM {table_name}
                            WHERE DATE(Order_time) = '{date_str}'
                               OR strftime('%Y-%m-%d', Order_time) = '{date_str}'
                               OR substr(Order_time, 1, 10) = '{date_str}'
                            LIMIT 1
                            """
                            result = pd.read_sql(query, conn.connection)
                            if not result.empty and result.iloc[0]['count'] > 0:
                                print(f"🔧 [DEBUG] 在表 {table_name} 发现 {date_str} 的数据")
                                return True
                        except Exception as e:
                            # 🔧 增强错误处理：记录具体错误信息
                            print(f"🔧 [DEBUG] 查询表 {table_name} 失败: {e}")
                            self.logger.debug(f"查询表 {table_name} 失败: {e}")
                            continue

            print(f"🔧 [DEBUG] 当天没有重复数据")
            return False

        except Exception as e:
            print(f"🔧 [DEBUG] 检查当天重复数据失败: {e}")
            return False

    def _enhanced_duplicate_detection(self, df: pd.DataFrame, existing_df: pd.DataFrame, platform: str = None) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        🆕 增强的重复数据检测逻辑 - 分级检测

        Args:
            df: 新数据DataFrame
            existing_df: 现有数据DataFrame
            platform: 平台类型

        Returns:
            (完全重复数据, 部分重复数据, 新数据)
        """
        # 🔧 新增：详细日志记录检测开始
        platform_info = f" (平台: {platform})" if platform else ""
        self.logger.info(f"🔍 开始增强重复检测{platform_info} - 新数据: {len(df)} 条, 现有数据: {len(existing_df)} 条")
        print(f"🔧 [DEBUG] 开始增强重复检测{platform_info} - 新数据: {len(df)} 条, 现有数据: {len(existing_df)} 条")

        # 🔧 新增：检查DataFrame基本信息
        self.logger.info(f"📊 新数据形状: {df.shape}, 现有数据形状: {existing_df.shape}")
        print(f"🔧 [DEBUG] 新数据形状: {df.shape}, 现有数据形状: {existing_df.shape}")

        # 🔧 添加列名检查
        print(f"🔧 [DEBUG] 新数据列名: {list(df.columns)}")
        print(f"🔧 [DEBUG] 现有数据列名: {list(existing_df.columns) if not existing_df.empty else '空DataFrame'}")

        # 🔧 修复：第一级检测：Transaction_Num + Order_time（如果Transaction_Num存在且不为空）
        # 分步检查每个条件，便于调试
        cond1 = 'Transaction_Num' in df.columns
        cond2 = 'Transaction_Num' in existing_df.columns
        cond3 = not df.empty
        cond4 = not existing_df.empty

        self.logger.info(f"🔍 Transaction_Num检测条件:")
        self.logger.info(f"  新数据有Transaction_Num列: {cond1}")
        self.logger.info(f"  现有数据有Transaction_Num列: {cond2}")
        self.logger.info(f"  新数据非空: {cond3}")
        self.logger.info(f"  现有数据非空: {cond4}")

        if cond1 and cond2 and cond3 and cond4:
            # 🔧 新增：安全检查Series操作
            try:
                new_txn_all_na = df['Transaction_Num'].isna().all()
                existing_txn_all_na = existing_df['Transaction_Num'].isna().all()
                self.logger.info(f"  新数据Transaction_Num全为空: {new_txn_all_na}")
                self.logger.info(f"  现有数据Transaction_Num全为空: {existing_txn_all_na}")

                has_transaction_num = not new_txn_all_na and not existing_txn_all_na
            except Exception as e:
                self.logger.error(f"❌ Transaction_Num检查失败: {e}")
                has_transaction_num = False
        else:
            has_transaction_num = False

        self.logger.info(f"🎯 使用Transaction_Num检测: {has_transaction_num}")

        if has_transaction_num:
            self.logger.info("使用第一级检测：Transaction_Num + Order_time")
            print(f"🔧 [DEBUG] 使用Transaction_Num检测")
            result = self._detect_by_transaction_num(df, existing_df)
            print(f"🔧 [DEBUG] Transaction_Num检测结果: 完全重复={len(result[0])}, 部分重复={len(result[1])}, 新数据={len(result[2])}")
            return result
        else:
            self.logger.info("Transaction_Num不可用，使用第二级检测：Order_time + Payment_date + Equipment_ID")
            print(f"🔧 [DEBUG] 使用传统方法检测")
            result = self._detect_by_legacy_method(df, existing_df)
            print(f"🔧 [DEBUG] 传统方法检测结果: 完全重复={len(result[0])}, 部分重复={len(result[1])}, 新数据={len(result[2])}")
            return result

    def _detect_by_transaction_num(self, df: pd.DataFrame, existing_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        基于Transaction_Num + Order_time的重复检测
        """
        # 🔧 修复：确保数据类型一致，防止NaN传播
        merge_columns = ["Transaction_Num", "Order_time"]
        for col in merge_columns:
            if col in df.columns:
                # 🔧 关键修复：对Transaction_Num使用安全转换
                if col == 'Transaction_Num':
                    df[col] = df[col].apply(self._safe_convert_transaction_num)
                else:
                    df[col] = df[col].astype(str).replace('nan', '').replace('None', '')
            if col in existing_df.columns:
                # 🔧 修复：清理数据库中的NaN值
                if col == 'Transaction_Num':
                    existing_df[col] = existing_df[col].apply(self._safe_convert_transaction_num)
                else:
                    existing_df[col] = existing_df[col].astype(str).replace('nan', '').replace('None', '')

        # 合并查找重复
        try:
            merged = df.merge(
                existing_df,
                on=merge_columns,
                how="left",
                suffixes=("", "_db"),
                indicator=True
            )
        except Exception as e:
            self.logger.warning(f"Transaction_Num merge failed: {e}, falling back to legacy method")
            return self._detect_by_legacy_method(df, existing_df)

        # 🔧 修复：正确比较Order_price（数值比较）
        def safe_price_compare(price1, price2):
            try:
                return float(price1) == float(price2)
            except (ValueError, TypeError):
                return str(price1) == str(price2)

        # 🔧 修复：分类数据，先检查merged是否为空
        if merged.empty:
            return pd.DataFrame(), pd.DataFrame(), df

        both_mask = merged["_merge"] == "both"
        if both_mask.any():  # 🔧 修复：只有当有匹配数据时才执行apply
            price_equal_mask = merged.apply(
                lambda row: safe_price_compare(row["Order_price"], row["Order_price_db"]),
                axis=1
            )
        else:
            price_equal_mask = pd.Series([False] * len(merged), index=merged.index)

        fully_duplicate = merged[both_mask & price_equal_mask].copy()
        partial_different = merged[both_mask & ~price_equal_mask].copy()

        new_data = merged[merged["_merge"] == "left_only"].copy()

        # 清理辅助列
        for result_df in [fully_duplicate, partial_different, new_data]:
            if not result_df.empty:
                cols_to_drop = [col for col in result_df.columns if col.endswith('_db') or col == '_merge']
                result_df.drop(columns=cols_to_drop, inplace=True)

        return fully_duplicate, partial_different, new_data

    def _detect_by_legacy_method(self, df: pd.DataFrame, existing_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        基于传统方法的重复检测：Order_time + Payment_date + Equipment_ID
        """
        # 确保数据类型一致
        merge_columns = ["Order_time", "Payment_date", "Equipment_ID"]
        for col in merge_columns:
            if col in df.columns:
                df[col] = df[col].astype(str)
            if col in existing_df.columns:
                existing_df[col] = existing_df[col].astype(str)

        # 合并查找重复
        try:
            merged = df.merge(
                existing_df,
                on=merge_columns,
                how="left",
                suffixes=("", "_db"),
                indicator=True
            )
        except Exception as e:
            self.logger.warning(f"Legacy merge failed: {e}, treating all data as new")
            return pd.DataFrame(), pd.DataFrame(), df

        # 🔧 修复：正确比较Order_price（数值比较）
        def safe_price_compare(price1, price2):
            try:
                return float(price1) == float(price2)
            except (ValueError, TypeError):
                return str(price1) == str(price2)

        # 🔧 修复：分类数据，先检查merged是否为空
        if merged.empty:
            return pd.DataFrame(), pd.DataFrame(), df

        both_mask = merged["_merge"] == "both"
        if both_mask.any():  # 🔧 修复：只有当有匹配数据时才执行apply
            price_equal_mask = merged.apply(
                lambda row: safe_price_compare(row["Order_price"], row["Order_price_db"]),
                axis=1
            )
        else:
            price_equal_mask = pd.Series([False] * len(merged), index=merged.index)

        fully_duplicate = merged[both_mask & price_equal_mask].copy()
        partial_different = merged[both_mask & ~price_equal_mask].copy()

        new_data = merged[merged["_merge"] == "left_only"].copy()

        # 清理辅助列
        for result_df in [fully_duplicate, partial_different, new_data]:
            if not result_df.empty:
                cols_to_drop = [col for col in result_df.columns if col.endswith('_db') or col == '_merge']
                result_df.drop(columns=cols_to_drop, inplace=True)

        return fully_duplicate, partial_different, new_data

    def validate_data_integrity(self, df: pd.DataFrame, platform: str, insert_results: Dict[str, int]) -> Dict[str, Any]:
        """
        🆕 功能4：数据完整性验证 - 导入完成后执行完整性检查

        Args:
            df: 原始导入数据
            platform: 平台类型
            insert_results: 插入结果统计

        Returns:
            验证报告字典
        """
        report = {
            'validation_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'platform': platform,
            'total_records_processed': len(df),
            'total_records_inserted': sum(insert_results.values()),
            'insert_distribution': insert_results,
            'validation_results': {},
            'data_quality_score': 0,
            'warnings': [],
            'errors': [],
            'recommendations': []
        }

        try:
            # 1. 记录数量一致性验证
            self._validate_record_count_consistency(df, insert_results, report)

            # 2. 关键字段完整性验证
            self._validate_key_fields_integrity(df, report)

            # 3. 数据类型和格式验证
            self._validate_data_types_and_formats(df, report)

            # 4. 业务逻辑验证
            self._validate_business_logic(df, report)

            # 5. 计算数据质量评分
            self._calculate_data_quality_score(report)

            # 6. 生成建议和警告
            self._generate_recommendations(report)

            self.logger.info(f"数据完整性验证完成 - 质量评分: {report['data_quality_score']}/100")

        except Exception as e:
            report['errors'].append(f"完整性验证过程中发生错误: {str(e)}")
            self.logger.error(f"Data integrity validation failed: {e}")

        return report

    def _validate_record_count_consistency(self, df: pd.DataFrame, insert_results: Dict[str, int], report: Dict[str, Any]):
        """验证记录数量一致性"""
        expected_count = len(df)
        actual_count = sum(insert_results.values())

        if expected_count == actual_count:
            report['validation_results']['record_count'] = 'PASS'
        elif actual_count < expected_count:
            diff = expected_count - actual_count
            report['validation_results']['record_count'] = 'WARNING'
            report['warnings'].append(f"插入记录数 ({actual_count}) 少于预期 ({expected_count})，差异: {diff} 条")
        else:
            diff = actual_count - expected_count
            report['validation_results']['record_count'] = 'ERROR'
            report['errors'].append(f"插入记录数 ({actual_count}) 超过预期 ({expected_count})，差异: {diff} 条")

    def _validate_key_fields_integrity(self, df: pd.DataFrame, report: Dict[str, Any]):
        """验证关键字段完整性"""
        key_fields = ['Transaction_Num', 'Order_No', 'Order_price', 'Equipment_ID']
        field_results = {}

        for field in key_fields:
            if field in df.columns:
                non_empty_count = df[field].notna().sum()
                total_count = len(df)
                fill_rate = (non_empty_count / total_count) * 100 if total_count > 0 else 0

                if fill_rate >= 95:
                    field_results[field] = 'EXCELLENT'
                elif fill_rate >= 80:
                    field_results[field] = 'GOOD'
                elif fill_rate >= 60:
                    field_results[field] = 'WARNING'
                    report['warnings'].append(f"{field} 字段填充率较低: {fill_rate:.1f}%")
                else:
                    field_results[field] = 'POOR'
                    report['errors'].append(f"{field} 字段填充率过低: {fill_rate:.1f}%")
            else:
                field_results[field] = 'MISSING'
                report['warnings'].append(f"缺少关键字段: {field}")

        report['validation_results']['key_fields'] = field_results

    def _validate_data_types_and_formats(self, df: pd.DataFrame, report: Dict[str, Any]):
        """验证数据类型和格式"""
        format_results = {}

        # 验证日期字段格式
        date_fields = ['Order_time', 'Payment_date']
        for field in date_fields:
            if field in df.columns:
                valid_dates = 0
                total_dates = 0
                for value in df[field]:
                    if pd.notna(value) and str(value).strip():
                        total_dates += 1
                        try:
                            # 尝试解析日期
                            if self.standardize_date(value):
                                valid_dates += 1
                        except (ValueError, TypeError, AttributeError):
                            pass  # 日期解析失败，跳过该值

                if total_dates > 0:
                    valid_rate = (valid_dates / total_dates) * 100
                    if valid_rate >= 95:
                        format_results[field] = 'EXCELLENT'
                    elif valid_rate >= 80:
                        format_results[field] = 'GOOD'
                    else:
                        format_results[field] = 'WARNING'
                        report['warnings'].append(f"{field} 日期格式有效率: {valid_rate:.1f}%")

        # 验证数值字段
        if 'Order_price' in df.columns:
            try:
                numeric_values = pd.to_numeric(df['Order_price'], errors='coerce')
                valid_count = numeric_values.notna().sum()
                total_count = df['Order_price'].notna().sum()

                if total_count > 0:
                    valid_rate = (valid_count / total_count) * 100
                    if valid_rate >= 95:
                        format_results['Order_price'] = 'EXCELLENT'
                    else:
                        format_results['Order_price'] = 'WARNING'
                        report['warnings'].append(f"Order_price 数值格式有效率: {valid_rate:.1f}%")
            except Exception as e:
                format_results['Order_price'] = 'ERROR'
                report['errors'].append(f"Order_price 字段数值格式验证失败: {e}")

        report['validation_results']['data_formats'] = format_results

    def _validate_business_logic(self, df: pd.DataFrame, report: Dict[str, Any]):
        """验证业务逻辑"""
        business_results = {}

        # 验证订单金额合理性
        if 'Order_price' in df.columns:
            try:
                numeric_prices = pd.to_numeric(df['Order_price'], errors='coerce')
                valid_prices = numeric_prices[numeric_prices.notna()]

                if len(valid_prices) > 0:
                    negative_count = (valid_prices < 0).sum()
                    zero_count = (valid_prices == 0).sum()
                    extreme_high = (valid_prices > 100000).sum()  # 假设超过10万的订单需要关注

                    if negative_count > 0:
                        business_results['negative_prices'] = 'WARNING'
                        report['warnings'].append(f"发现 {negative_count} 个负数订单金额")

                    if zero_count > 0:
                        business_results['zero_prices'] = 'INFO'
                        report['warnings'].append(f"发现 {zero_count} 个零金额订单")

                    if extreme_high > 0:
                        business_results['high_prices'] = 'INFO'
                        report['warnings'].append(f"发现 {extreme_high} 个高金额订单 (>100,000)")
            except Exception as e:
                business_results['price_validation'] = 'ERROR'
                report['errors'].append(f"订单金额业务逻辑验证失败: {e}")

        report['validation_results']['business_logic'] = business_results

    def _calculate_data_quality_score(self, report: Dict[str, Any]):
        """计算数据质量评分"""
        score = 100

        # 根据错误和警告扣分
        error_count = len(report['errors'])
        warning_count = len(report['warnings'])

        score -= error_count * 10  # 每个错误扣10分
        score -= warning_count * 3  # 每个警告扣3分

        # 确保分数在0-100范围内
        score = max(0, min(100, score))
        report['data_quality_score'] = score

    def _generate_recommendations(self, report: Dict[str, Any]):
        """生成建议和推荐"""
        recommendations = []

        if report['data_quality_score'] < 70:
            recommendations.append("数据质量较低，建议检查数据源和导入流程")

        if len(report['errors']) > 0:
            recommendations.append("存在数据错误，建议修复后重新导入")

        if len(report['warnings']) > 5:
            recommendations.append("警告较多，建议优化数据清洗流程")

        # 根据具体验证结果添加建议
        key_fields = report['validation_results'].get('key_fields', {})
        for field, status in key_fields.items():
            if status in ['WARNING', 'POOR']:
                recommendations.append(f"建议改善 {field} 字段的数据质量")

        report['recommendations'] = recommendations

    def handle_duplicate_data_interaction(self, fully_duplicate: pd.DataFrame, partial_different: pd.DataFrame, platform: str) -> str:
        """
        🆕 功能3：重复数据处理策略 - 用户交互处理

        Args:
            fully_duplicate: 完全重复的数据
            partial_different: 部分重复的数据
            platform: 平台类型

        Returns:
            用户选择的处理方式 ('overwrite', 'incremental', 'skip', 'cancel')
        """
        total_duplicates = len(fully_duplicate) + len(partial_different)

        if total_duplicates == 0:
            return 'continue'  # 没有重复数据，继续处理

        self.logger.warning(f"检测到重复数据 - 完全重复: {len(fully_duplicate)} 条, 部分重复: {len(partial_different)} 条")
        print(f"🔧 [DEBUG] 检测到重复数据，开始处理交互逻辑")

        # 🚨 修复：优先使用命令行模式，避免GUI卡住问题
        # 检查是否强制使用命令行模式
        import os
        force_console = os.environ.get('FORCE_CONSOLE', '').lower() in ['1', 'true', 'yes']
        print(f"🔧 [DEBUG] FORCE_CONSOLE环境变量: {os.environ.get('FORCE_CONSOLE', 'None')}")

        if force_console:
            print(f"🔧 [DEBUG] 强制使用命令行模式")
            self.logger.info("强制使用命令行模式处理重复数据")
            return self._handle_duplicate_console(fully_duplicate, partial_different, platform)

        # 检查是否在GUI环境中运行
        gui_available = self._is_gui_environment()
        print(f"🔧 [DEBUG] GUI环境检测结果: {gui_available}")

        if gui_available:
            print(f"🔧 [DEBUG] 尝试显示GUI对话框")
            self.logger.info("检测到GUI环境，尝试显示GUI对话框")
            return self._handle_duplicate_gui(fully_duplicate, partial_different, platform)
        else:
            print(f"🔧 [DEBUG] 使用命令行交互")
            self.logger.info("检测到命令行环境，使用命令行交互")
            return self._handle_duplicate_console(fully_duplicate, partial_different, platform)

    def _is_gui_environment(self) -> bool:
        """🚨 修复：更准确地检测是否在GUI环境中运行"""
        try:
            # 检查环境变量
            import os

            print(f"🔧 [DEBUG] 环境变量检查:")
            print(f"🔧 [DEBUG] NON_INTERACTIVE: {os.environ.get('NON_INTERACTIVE', 'None')}")
            print(f"🔧 [DEBUG] NO_GUI: {os.environ.get('NO_GUI', 'None')}")
            print(f"🔧 [DEBUG] AUTO_DUPLICATE_HANDLING: {os.environ.get('AUTO_DUPLICATE_HANDLING', 'None')}")

            # 🔧 修复：检查非交互模式，直接返回False避免GUI检测卡顿
            if os.environ.get('NON_INTERACTIVE', '').lower() in ['1', 'true', 'yes']:
                print(f"🔧 [DEBUG] NON_INTERACTIVE模式，返回False")
                return False

            # 如果明确设置为非GUI模式
            if os.environ.get('NO_GUI', '').lower() in ['1', 'true', 'yes']:
                print(f"🔧 [DEBUG] NO_GUI模式，返回False")
                return False

            # 检查是否有显示器
            if os.environ.get('DISPLAY') is None and os.name != 'nt':  # Linux/Unix
                return False

            # 🔧 修复：快速检测，避免创建测试窗口导致卡顿
            try:
                import tkinter
                _ = tkinter  # 🔧 Bug修复：标记未使用的导入
                # 只检查导入是否成功，不创建窗口避免卡顿
                return True
            except ImportError:
                return False

        except ImportError:
            return False
        except Exception:
            # 任何其他异常都认为不在GUI环境中
            return False

    def _handle_duplicate_console(self, fully_duplicate: pd.DataFrame, partial_different: pd.DataFrame, platform: str) -> str:
        """🚨 修复：命令行环境下的重复数据处理，支持自动模式"""
        print("\n" + "="*60)
        print("🔍 检测到重复数据")
        print("="*60)
        print(f"平台: {platform}")
        print(f"完全重复记录: {len(fully_duplicate)} 条")
        print(f"部分重复记录: {len(partial_different)} 条")

        # 🚨 修复：检查是否设置了自动处理模式
        import os
        auto_mode = os.environ.get('AUTO_DUPLICATE_HANDLING', '').lower()
        if auto_mode in ['skip', 'overwrite', 'incremental', 'refresh_daily']:
            print(f"🤖 自动模式：使用预设策略 '{auto_mode}'")
            return auto_mode

        # 🔧 修复：检查是否在非交互模式下运行（从主应用程序调用）
        non_interactive = os.environ.get('NON_INTERACTIVE', '').lower() in ['1', 'true', 'yes']
        if non_interactive:
            self.logger.info("🤖 非交互模式：自动覆盖重复数据")
            return 'overwrite'

        if len(partial_different) > 0:
            print("\n📋 部分重复记录详情 (前5条):")
            print("-"*60)
            # 显示部分重复记录的关键信息
            display_cols = ['Transaction_Num', 'Order_No', 'Order_time', 'Order_price']
            available_cols = [col for col in display_cols if col in partial_different.columns]
            if available_cols:
                print(partial_different[available_cols].head().to_string(index=False))

        print("\n🔧 处理选项:")
        print("a) 覆盖更新现有数据")
        print("b) 只进行增量更新（仅更新不同的字段）")
        print("c) 跳过重复数据")
        print("d) 🆕 重新更新（删除当天数据后重新导入）")
        print("e) 取消导入操作")

        # 🔧 使用自定义GUI对话框进行交互
        try:
            import tkinter as tk
            from tkinter import ttk

            # 创建自定义对话框类
            class DuplicateDataDialog:
                def __init__(self):
                    self.result = None
                    # 创建隐藏的根窗口
                    self.root = tk.Tk()
                    self.root.withdraw()  # 隐藏根窗口
                    # 创建实际的对话框窗口
                    self.dialog = tk.Toplevel(self.root)
                    self.setup_dialog()

                def setup_dialog(self):
                    # 窗口基本设置
                    self.dialog.title("🔍 检测到重复数据")
                    self.dialog.geometry("500x450")
                    self.dialog.resizable(False, False)

                    # 居中显示
                    self.center_window()

                    # 设置为置顶
                    self.dialog.attributes('-topmost', True)
                    self.dialog.focus_force()

                    # 创建主框架
                    main_frame = ttk.Frame(self.dialog, padding="20")
                    main_frame.pack(fill=tk.BOTH, expand=True)

                    # 标题
                    title_label = ttk.Label(
                        main_frame,
                        text="🔍 检测到重复数据",
                        font=("Arial", 14, "bold")
                    )
                    title_label.pack(pady=(0, 15))

                    # 信息显示
                    info_text = f"""检测到重复数据情况：

• 完全重复: {len(fully_duplicate)} 条
• 部分重复: {len(partial_different)} 条

请选择处理方式："""

                    info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
                    info_label.pack(pady=(0, 15))

                    # 选项框架
                    options_frame = ttk.LabelFrame(main_frame, text="处理选项", padding="10")
                    options_frame.pack(fill=tk.X, pady=(0, 15))

                    # 选项变量
                    self.choice_var = tk.StringVar(value="skip")

                    # 选项按钮
                    options = [
                        ("skip", "🔄 跳过重复数据 - 只导入新数据，保持现有数据不变"),
                        ("overwrite", "📝 覆盖更新 - 用新数据完全覆盖现有重复数据"),
                        ("incremental", "🔧 增量更新 - 只更新不同的字段，保留相同字段"),
                        ("refresh_daily", "🗑️ 重新更新 - 删除当天数据后重新导入"),
                        ("cancel", "❌ 取消导入 - 停止导入操作")
                    ]

                    for value, text in options:
                        rb = ttk.Radiobutton(
                            options_frame,
                            text=text,
                            variable=self.choice_var,
                            value=value
                        )
                        rb.pack(anchor=tk.W, pady=2)

                    # 按钮区域
                    button_frame = ttk.Frame(main_frame)
                    button_frame.pack(fill=tk.X, pady=(15, 0))

                    # 确认按钮
                    confirm_btn = ttk.Button(
                        button_frame,
                        text="✅ 确认",
                        command=self.confirm_choice
                    )
                    confirm_btn.pack(side=tk.RIGHT, padx=(5, 0))

                    # 取消按钮
                    cancel_btn = ttk.Button(
                        button_frame,
                        text="❌ 取消",
                        command=self.cancel_choice
                    )
                    cancel_btn.pack(side=tk.RIGHT)

                    # 绑定键盘事件
                    self.dialog.bind('<Return>', lambda e: self.confirm_choice())
                    self.dialog.bind('<Escape>', lambda e: self.cancel_choice())

                    # 设置默认焦点
                    confirm_btn.focus_set()

                def center_window(self):
                    self.dialog.update_idletasks()
                    width = 500
                    height = 450
                    x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
                    y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
                    self.dialog.geometry(f"{width}x{height}+{x}+{y}")

                def confirm_choice(self):
                    self.result = self.choice_var.get()
                    self.root.quit()
                    self.root.destroy()

                def cancel_choice(self):
                    self.result = "cancel"
                    self.root.quit()
                    self.root.destroy()

                def show(self):
                    self.root.mainloop()
                    return self.result

            # 显示对话框
            dialog = DuplicateDataDialog()
            choice = dialog.show()

            return choice if choice else 'cancel'

        except Exception as e:
            print(f"GUI对话框失败: {e}")
            return 'cancel'

    def _handle_duplicate_gui(self, fully_duplicate: pd.DataFrame, partial_different: pd.DataFrame, platform: str = None) -> str:
        """🔧 修复：GUI环境下显示重复数据处理对话框"""
        total_duplicates = len(fully_duplicate) + len(partial_different)

        if total_duplicates == 0:
            return 'continue'  # 没有重复数据，继续处理

        try:
            import tkinter as tk
            from tkinter import simpledialog  # 🔧 修复：移除未使用的 messagebox

            # 记录重复数据信息到日志
            self.logger.warning(f"检测到重复数据: 完全重复 {len(fully_duplicate)} 条, 部分重复 {len(partial_different)} 条")

            # 构建详细信息
            details = []
            if len(fully_duplicate) > 0:
                details.append(f"完全重复记录: {len(fully_duplicate)} 条")
            if len(partial_different) > 0:
                details.append(f"部分重复记录: {len(partial_different)} 条")

            # 显示重复数据示例
            sample_info = ""
            if len(fully_duplicate) > 0:
                display_cols = ['Transaction_Num', 'Order_No', 'Order_time', 'Order_price']
                available_cols = [col for col in display_cols if col in fully_duplicate.columns]
                if available_cols:
                    sample_data = fully_duplicate[available_cols].head(2)
                    sample_info = f"\n\n重复数据示例:\n{sample_data.to_string(index=False)}"

            # 构建完整消息
            message = f"""检测到重复数据：

平台: {platform}
{chr(10).join(details)}{sample_info}

请选择处理方式：

1. 覆盖更新 - 用新数据覆盖现有重复数据
2. 增量更新 - 只更新不同的字段
3. 跳过重复 - 只导入新数据，跳过重复数据
4. 🆕 重新更新 - 删除当天数据后重新导入
5. 取消导入 - 停止导入操作

请输入选项编号 (1-5):"""

            # 🚨 关键修复：使用自定义对话框替代simpledialog
            root = None
            choice = None

            try:
                # 创建自定义对话框类
                class DuplicateDataDialog:
                    def __init__(self):
                        self.result = None
                        # 创建隐藏的根窗口
                        self.root = tk.Tk()
                        self.root.withdraw()  # 隐藏根窗口
                        # 创建实际的对话框窗口
                        self.dialog = tk.Toplevel(self.root)
                        self.setup_dialog()

                    def setup_dialog(self):
                        # 窗口基本设置
                        self.dialog.title("🔍 检测到重复数据")
                        self.dialog.geometry("600x500")
                        self.dialog.resizable(False, False)

                        # 居中显示
                        self.center_window()

                        # 设置为置顶
                        self.dialog.attributes('-topmost', True)
                        self.dialog.focus_force()

                        # 创建主框架
                        main_frame = ttk.Frame(self.dialog, padding="20")
                        main_frame.pack(fill=tk.BOTH, expand=True)

                        # 标题
                        title_label = ttk.Label(
                            main_frame,
                            text="🔍 检测到重复数据",
                            font=("Arial", 14, "bold")
                        )
                        title_label.pack(pady=(0, 15))

                        # 详细信息显示
                        info_text = f"""平台: {platform}
完全重复记录: {len(fully_duplicate)} 条
部分重复记录: {len(partial_different)} 条

请选择处理方式："""

                        info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
                        info_label.pack(pady=(0, 15))

                        # 选项框架
                        options_frame = ttk.LabelFrame(main_frame, text="处理选项", padding="10")
                        options_frame.pack(fill=tk.X, pady=(0, 15))

                        # 选项变量
                        self.choice_var = tk.StringVar(value="skip")

                        # 选项按钮
                        options = [
                            ("overwrite", "📝 覆盖更新 - 用新数据覆盖现有重复数据"),
                            ("incremental", "🔧 增量更新 - 只更新不同的字段"),
                            ("skip", "🔄 跳过重复 - 只导入新数据，跳过重复数据"),
                            ("refresh_daily", "🗑️ 重新更新 - 删除当天数据后重新导入"),
                            ("cancel", "❌ 取消导入 - 停止导入操作")
                        ]

                        for value, text in options:
                            rb = ttk.Radiobutton(
                                options_frame,
                                text=text,
                                variable=self.choice_var,
                                value=value
                            )
                            rb.pack(anchor=tk.W, pady=2)

                        # 按钮区域
                        button_frame = ttk.Frame(main_frame)
                        button_frame.pack(fill=tk.X, pady=(15, 0))

                        # 确认按钮
                        confirm_btn = ttk.Button(
                            button_frame,
                            text="✅ 确认",
                            command=self.confirm_choice
                        )
                        confirm_btn.pack(side=tk.RIGHT, padx=(5, 0))

                        # 取消按钮
                        cancel_btn = ttk.Button(
                            button_frame,
                            text="❌ 取消",
                            command=self.cancel_choice
                        )
                        cancel_btn.pack(side=tk.RIGHT)

                        # 绑定键盘事件
                        self.dialog.bind('<Return>', lambda e: self.confirm_choice())
                        self.dialog.bind('<Escape>', lambda e: self.cancel_choice())

                        # 设置默认焦点
                        confirm_btn.focus_set()

                    def center_window(self):
                        self.dialog.update_idletasks()
                        width = 600
                        height = 500
                        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
                        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
                        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

                    def confirm_choice(self):
                        self.result = self.choice_var.get()
                        self.root.quit()
                        self.root.destroy()

                    def cancel_choice(self):
                        self.result = "cancel"
                        self.root.quit()
                        self.root.destroy()

                    def show(self):
                        self.root.mainloop()
                        return self.result

                # 显示对话框
                dialog = DuplicateDataDialog()
                choice = dialog.show()

            except Exception as e:
                self.logger.warning(f"GUI对话框创建失败: {e}，切换到命令行模式")
                # 切换到命令行模式
                return self._handle_duplicate_console(fully_duplicate, partial_different, platform)

            # 处理用户选择
            if choice:
                self.logger.info(f"用户选择：{choice}")
                return choice
            else:
                self.logger.info("用户取消选择，停止导入")
                return 'cancel'

        except Exception as e:
            # 如果GUI对话框失败，回退到日志模式
            self.logger.error(f"GUI对话框显示失败: {e}")
            self.logger.error(f"检测到重复数据: 完全重复 {len(fully_duplicate)} 条, 部分重复 {len(partial_different)} 条")
            self.logger.error("为防止数据重复导入，请先清理重复数据或使用不同的文件")
            return 'cancel'

    def _apply_duplicate_handling_strategy(self, strategy: str, fully_duplicate: pd.DataFrame, partial_different: pd.DataFrame, new_data: pd.DataFrame, platform: str) -> Tuple[pd.DataFrame, Dict[str, int]]:
        """
        应用重复数据处理策略

        Args:
            strategy: 处理策略
            fully_duplicate: 完全重复数据
            partial_different: 部分重复数据
            new_data: 新数据
            platform: 平台类型

        Returns:
            (要插入的数据, 处理结果统计)
        """
        result_stats = {
            'new_inserted': 0,
            'overwritten': 0,
            'updated': 0,
            'skipped': 0
        }

        # 🔧 Bug修复：使用platform参数记录日志
        platform_info = f" (平台: {platform})" if platform else ""
        self.logger.info(f"🔄 应用重复数据处理策略: {strategy}{platform_info}")

        if strategy == 'cancel':
            self.logger.info("用户取消导入操作")
            return pd.DataFrame(), result_stats

        elif strategy == 'skip':
            self.logger.info("跳过重复数据，仅插入新数据")
            result_stats['new_inserted'] = len(new_data)
            result_stats['skipped'] = len(fully_duplicate) + len(partial_different)
            return new_data, result_stats

        elif strategy == 'overwrite':
            self.logger.info("覆盖更新现有数据")
            # 🔧 修复：标记需要删除重复数据
            all_data = pd.concat([new_data, fully_duplicate, partial_different], ignore_index=True)
            result_stats['new_inserted'] = len(new_data)
            result_stats['overwritten'] = len(fully_duplicate) + len(partial_different)
            result_stats['need_delete_duplicates'] = True  # 标记需要删除重复数据
            result_stats['duplicate_records'] = pd.concat([fully_duplicate, partial_different], ignore_index=True)
            return all_data, result_stats

        elif strategy == 'incremental':
            self.logger.info("增量更新不同字段")
            # 只插入新数据和部分不同的数据
            incremental_data = pd.concat([new_data, partial_different], ignore_index=True)
            result_stats['new_inserted'] = len(new_data)
            result_stats['updated'] = len(partial_different)
            result_stats['skipped'] = len(fully_duplicate)
            return incremental_data, result_stats

        elif strategy == 'refresh_daily':
            self.logger.info("🆕 重新更新：删除当天数据后重新导入")
            # 这个策略需要特殊处理，返回特殊标记
            result_stats['refresh_daily'] = True
            result_stats['new_inserted'] = len(new_data) + len(fully_duplicate) + len(partial_different)

            # 🔧 安全修复：安全地合并DataFrame
            dataframes_to_concat = []
            if not new_data.empty:
                dataframes_to_concat.append(new_data)
            if not fully_duplicate.empty:
                dataframes_to_concat.append(fully_duplicate)
            if not partial_different.empty:
                dataframes_to_concat.append(partial_different)

            if dataframes_to_concat:
                try:
                    all_data = pd.concat(dataframes_to_concat, ignore_index=True)
                except Exception as e:
                    self.logger.error(f"合并DataFrame失败: {e}")
                    # 如果合并失败，只返回新数据
                    all_data = new_data.copy() if not new_data.empty else pd.DataFrame()
            else:
                all_data = pd.DataFrame()

            return all_data, result_stats

        else:
            self.logger.warning(f"未知的处理策略: {strategy}，默认跳过重复数据")
            result_stats['new_inserted'] = len(new_data)
            result_stats['skipped'] = len(fully_duplicate) + len(partial_different)
            return new_data, result_stats

    def _extract_file_date(self, file_path: str, df: pd.DataFrame) -> str:
        """
        🆕 从文件名或文件内容中提取日期

        Args:
            file_path: 文件路径
            df: 数据框（用于从内容中提取日期）

        Returns:
            日期字符串 (YYYY-MM-DD 格式)
        """
        import re
        from datetime import datetime

        # 方法1: 从文件名中提取日期
        filename = os.path.basename(file_path)

        # 匹配各种日期格式：DDMMYY, YYMMDD, YYYY-MM-DD 等
        date_patterns = [
            r'(\d{6})',  # DDMMYY 或 YYMMDD
            r'(\d{4}-\d{2}-\d{2})',  # YYYY-MM-DD
            r'(\d{4}\d{2}\d{2})',  # YYYYMMDD
            r'(\d{2}\d{2}\d{2})',  # DDMMYY
        ]

        for pattern in date_patterns:
            match = re.search(pattern, filename)
            if match:
                date_str = match.group(1)
                try:
                    # 尝试解析不同格式的日期
                    if len(date_str) == 6:
                        # 假设是 DDMMYY 格式 (如 030725)
                        if int(date_str[:2]) <= 31:  # 前两位是日期
                            parsed_date = datetime.strptime(date_str, '%d%m%y')
                        else:  # 前两位是年份 (如 250703)
                            parsed_date = datetime.strptime(date_str, '%y%m%d')
                    elif len(date_str) == 8:
                        parsed_date = datetime.strptime(date_str, '%Y%m%d')
                    elif len(date_str) == 10:
                        parsed_date = datetime.strptime(date_str, '%Y-%m-%d')
                    else:
                        continue

                    result_date = parsed_date.strftime('%Y-%m-%d')
                    self.logger.info(f"从文件名提取日期: {date_str} → {result_date}")
                    return result_date

                except ValueError:
                    continue

        # 方法2: 从文件内容中提取日期 - 统计所有日期
        if 'Order_time' in df.columns and not df.empty and df['Order_time'].notna().any():
            try:
                # 🔧 修复：统计所有日期，不只是第一个
                order_time_clean = df['Order_time'].dropna()
                if len(order_time_clean) > 0:
                    # 统计所有唯一日期
                    unique_dates = set()
                    for order_time in order_time_clean:
                        if pd.notna(order_time):
                            try:
                                if isinstance(order_time, str):
                                    parsed_date = pd.to_datetime(order_time)
                                else:
                                    parsed_date = order_time
                                date_str = parsed_date.strftime('%Y-%m-%d')
                                unique_dates.add(date_str)
                            except:
                                continue

                    print(f"🔧 [DEBUG] 文件中的所有日期: {sorted(unique_dates)}")
                    print(f"🔧 [DEBUG] 总计 {len(unique_dates)} 个不同日期")
                    sys.stdout.flush()

                    if unique_dates:
                        # 返回最常见的日期（或第一个日期）
                        first_order_time = order_time_clean.iloc[0]
                        if isinstance(first_order_time, str):
                            parsed_date = pd.to_datetime(first_order_time)
                        else:
                            parsed_date = first_order_time
                        result_date = parsed_date.strftime('%Y-%m-%d')

                        print(f"🔧 [DEBUG] 选择的代表日期: {first_order_time} → {result_date}")
                        sys.stdout.flush()
                        self.logger.info(f"从文件内容提取日期: {first_order_time} → {result_date}")
                        self.logger.info(f"文件包含 {len(unique_dates)} 个不同日期: {sorted(unique_dates)}")
                        return result_date
            except Exception as e:
                print(f"🔧 [DEBUG] 从文件内容提取日期失败: {e}")
                sys.stdout.flush()
                self.logger.warning(f"从文件内容提取日期失败: {e}")

        # 方法3: 使用当前日期作为默认值
        current_date = datetime.now().strftime('%Y-%m-%d')
        self.logger.warning(f"无法从文件中提取日期，使用当前日期: {current_date}")
        self.logger.warning("建议：请确保文件名包含日期信息（如 030725 格式）或文件中包含有效的Order_time数据")
        return current_date

    def _get_tables_to_clear(self, platform: str) -> list:
        """
        🆕 根据平台确定需要清理的表

        Args:
            platform: 平台类型

        Returns:
            需要清理的表名列表
        """
        # 🔧 安全修复：验证平台参数，防止SQL注入
        if not platform or not isinstance(platform, str):
            raise ValueError("平台参数必须是非空字符串")

        # 🔧 安全修复：只允许预定义的平台类型
        valid_platforms = {'IOT', 'ZERO', 'APP'}
        platform_upper = platform.upper()

        if platform_upper == 'IOT':
            return [
                'IOT_Sales',
                'IOT_Sales_Refunding',
                'IOT_Sales_Close',
                'APP_Sales'  # IOT平台包括APP_Sales
            ]
        elif platform_upper == 'ZERO':
            return [
                'ZERO_Sales',
                'ZERO_Sales_Refunding',
                'ZERO_Sales_Close'
            ]
        elif platform_upper == 'APP':
            return [
                'APP_Sales',
                'APP_Sales_Refunding',
                'APP_Sales_Close'
            ]
        else:
            # 🔧 安全修复：对于未知平台，抛出异常而不是构造表名
            raise ValueError(f"不支持的平台类型: {platform}。支持的平台: {', '.join(valid_platforms)}")

    def _delete_data_by_month(self, target_year: str, target_month: str, platform: str, tables_to_clear: list = None) -> Dict[str, int]:
        """
        🆕 根据年月删除数据库中的数据（月度刷新功能）

        Args:
            target_year: 目标年份 (YYYY格式)
            target_month: 目标月份 (MM格式)
            platform: 平台类型
            tables_to_clear: 要清理的表列表，如果为None则使用默认表

        Returns:
            删除结果字典 {table_name: deleted_count}
        """
        if not target_year or not target_month:
            raise ValueError("年份和月份不能为空")

        try:
            # 验证年月格式
            year_int = int(target_year)
            month_int = int(target_month)
            if not (2020 <= year_int <= 2030):
                raise ValueError(f"无效的年份: {target_year}")
            if not (1 <= month_int <= 12):
                raise ValueError(f"无效的月份: {target_month}")

            # 格式化为标准格式
            formatted_year = f"{year_int:04d}"
            formatted_month = f"{month_int:02d}"

        except ValueError as e:
            raise ValueError(f"无效的日期: {target_year}-{target_month}, 错误: {e}")

        delete_results = {}
        if tables_to_clear is None:
            tables_to_clear = self._get_tables_to_clear(platform)

        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                for table_name in tables_to_clear:
                    # 🔧 修复：使用参数化查询检查表是否存在
                    cursor.execute("""
                        SELECT name FROM sqlite_master
                        WHERE type='table' AND name=?
                    """, (table_name,))
                    if not cursor.fetchone():
                        self.logger.warning(f"表 {table_name} 不存在，跳过")
                        delete_results[table_name] = 0
                        continue

                    # 🔧 修复：使用索引友好的范围查询替代strftime
                    start_date = f"{formatted_year}-{formatted_month}-01"
                    if formatted_month == "12":
                        end_date = f"{int(formatted_year)+1:04d}-01-01"
                    else:
                        end_date = f"{formatted_year}-{int(formatted_month)+1:02d}-01"

                    # 统计要删除的记录数
                    count_query = f"""
                        SELECT COUNT(*) FROM "{table_name}"
                        WHERE Order_time >= ? AND Order_time < ?
                    """
                    cursor.execute(count_query, (start_date, end_date))
                    count_to_delete = cursor.fetchone()[0]

                    if count_to_delete > 0:
                        # 🔧 修复：分批删除，避免长时间锁定
                        deleted_count = self._batch_delete_records(cursor, table_name, start_date, end_date, count_to_delete)
                        delete_results[table_name] = deleted_count
                        self.logger.info(f"从 {table_name} 删除了 {deleted_count} 条 {formatted_year}-{formatted_month} 的数据")
                    else:
                        self.logger.info(f"表 {table_name} 中没有 {formatted_year}-{formatted_month} 的数据")
                        delete_results[table_name] = 0

                # 🔧 修复：使用连接池的提交方法
                conn.commit()

                total_deleted = sum(count for count in delete_results.values() if count > 0)
                self.logger.info(f"总计删除了 {total_deleted} 条 {formatted_year}-{formatted_month} 的数据")

        except Exception as e:
            # 🔧 增强异常处理：回滚事务并提供详细错误信息
            try:
                conn.rollback()
                self.logger.error("数据库事务已回滚")
            except:
                pass

            self.logger.error(f"删除月度数据失败: {e}")
            self.logger.error(f"目标年月: {formatted_year}-{formatted_month}, 平台: {platform}")
            raise DatabaseError(f"Failed to delete monthly data: {e}", operation="delete_by_month")

        return delete_results

    def _extract_month_from_file_content(self, file_path: str) -> tuple:
        """
        从文件内容中提取年月信息（更准确的方法）

        Args:
            file_path: 文件路径

        Returns:
            (year, month, confidence, total_records) 元组
            confidence: 置信度 (0.0-1.0)
            total_records: 该月份的记录总数
        """
        import pandas as pd
        from collections import Counter
        import os

        filename = os.path.basename(file_path)
        self.logger.info(f"🔍 从文件内容提取月份信息: {filename}")

        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            self.logger.debug(f"文件读取成功，共 {len(df)} 行数据")

            # 🔧 用户要求：只查找Order_time字段
            primary_date_col = None
            if 'Order_time' in df.columns:
                primary_date_col = 'Order_time'
            elif 'Order time' in df.columns:
                primary_date_col = 'Order time'
            else:
                self.logger.error("未找到Order_time字段")
                return None, None, 0.0, 0
            self.logger.info(f"使用日期字段: {primary_date_col}")

            # 🔧 改进：提取年月日信息，支持单天/多天检测
            year_month_counter = Counter()
            year_month_day_counter = Counter()  # 新增：统计具体日期
            valid_dates = 0

            for idx, row in df.iterrows():
                try:
                    date_value = row[primary_date_col]
                    if pd.isna(date_value):
                        continue

                    # 转换为datetime
                    if isinstance(date_value, str):
                        date_obj = pd.to_datetime(date_value, errors='coerce')
                    else:
                        date_obj = pd.to_datetime(date_value, errors='coerce')

                    if pd.isna(date_obj):
                        continue

                    # 提取年月和年月日
                    year_month = f"{date_obj.year:04d}-{date_obj.month:02d}"
                    year_month_day = f"{date_obj.year:04d}-{date_obj.month:02d}-{date_obj.day:02d}"

                    year_month_counter[year_month] += 1
                    year_month_day_counter[year_month_day] += 1
                    valid_dates += 1

                except Exception as e:
                    self.logger.debug(f"解析日期失败 (行 {idx}): {e}")
                    continue

            if not year_month_counter:
                self.logger.error("未找到有效的日期数据")
                return None, None, 0.0, 0

            # 找出最主要的年月
            most_common = year_month_counter.most_common(1)[0]
            target_year_month, record_count = most_common
            year, month = target_year_month.split('-')

            # 🔧 新增：检查是否为单天数据
            target_month_days = [day for day in year_month_day_counter.keys() if day.startswith(target_year_month)]
            is_single_day = len(target_month_days) == 1
            target_date = target_month_days[0] if is_single_day else None

            # 计算置信度
            confidence = record_count / valid_dates if valid_dates > 0 else 0.0

            self.logger.info(f"✅ 识别主要月份: {year}年{month}月")
            self.logger.info(f"📊 该月份记录数: {record_count}/{valid_dates} (置信度: {confidence:.1%})")

            if is_single_day:
                self.logger.info(f"📅 单天数据: {target_date}")
            else:
                self.logger.info(f"📅 多天数据: {len(target_month_days)}天")
                # 显示日期分布（最多显示前5天）
                day_distribution = [(day, year_month_day_counter[day]) for day in target_month_days]
                day_distribution.sort(key=lambda x: x[1], reverse=True)
                for day, count in day_distribution[:5]:
                    self.logger.info(f"   {day}: {count}条")
                if len(day_distribution) > 5:
                    self.logger.info(f"   ... 还有{len(day_distribution)-5}天")

            # 如果有多个月份，显示分布
            if len(year_month_counter) > 1:
                self.logger.info("📅 文件中的月份分布:")
                for ym, count in year_month_counter.most_common():
                    y, m = ym.split('-')
                    percentage = count / valid_dates * 100
                    self.logger.info(f"   {y}年{m}月: {count}条 ({percentage:.1f}%)")

            # 🔧 保持向后兼容的返回格式，同时添加扩展信息
            return year, month, confidence, record_count, {
                'is_single_day': is_single_day,
                'target_date': target_date,
                'total_days': len(target_month_days)
            }

        except Exception as e:
            self.logger.error(f"读取文件内容失败: {e}")
            self.logger.error(f"文件路径: {file_path}")
            # 🔧 改进：提供更详细的错误信息
            if "Permission" in str(e):
                self.logger.error("可能是文件权限问题，请检查文件是否被其他程序占用")
            elif "No such file" in str(e):
                self.logger.error("文件不存在，请检查文件路径")
            elif "not supported" in str(e):
                self.logger.error("文件格式不支持，请确保是有效的Excel文件")

            # 降级到文件名识别
            self.logger.info("降级使用文件名识别...")
            return self._extract_month_from_filename_fallback(file_path)

    def _extract_month_from_filename_fallback(self, file_path: str) -> tuple:
        """
        文件名识别的降级方案
        """
        import re
        import os

        filename = os.path.basename(file_path)
        self.logger.debug(f"尝试从文件名提取月份: {filename}")

        # 简化的文件名识别，只保留最可靠的模式
        patterns = [
            (r'(\d{4})(\d{2})_.*\.xlsx?$', lambda m: (m.group(1), m.group(2))),  # 202506_IOT.xlsx
            (r'(\w+)\s+(\d{2})\.xlsx?$', lambda m: ("2025", m.group(2))),        # IOT 06.xlsx
        ]

        for pattern, extractor in patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                year, month = extractor(match)
                # 验证月份
                if 1 <= int(month) <= 12:
                    self.logger.warning(f"从文件名识别月份: {year}-{month} (降级方案)")
                    return year, month, 0.5, 0, {
                        'is_single_day': False,
                        'target_date': None,
                        'total_days': 0
                    }

        self.logger.error(f"无法识别月份: {filename}")
        return None, None, 0.0, 0, {}

    def _handle_single_day_data(self, df: pd.DataFrame, platform: str, target_date: str) -> str:
        """
        处理单天数据的重复检测逻辑

        Args:
            df: 要导入的数据
            platform: 平台类型
            target_date: 目标日期 (YYYY-MM-DD)

        Returns:
            'continue': 继续正常流程
            'skip_duplicate_check': 跳过重复检测
            'cancel': 取消操作
        """
        try:
            # 检查数据库中是否已有该日期的数据
            refunding_close_tables = [
                f"{platform}_Sales_Refunding",
                f"{platform}_Sales_Close"
            ]

            existing_records = 0
            table_counts = {}

            with get_connection() as conn:
                cursor = conn.connection.cursor()

                for table_name in refunding_close_tables:
                    # 🔧 安全修复：检查表是否存在，使用参数化查询
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                    if not cursor.fetchone():
                        table_counts[table_name] = 0
                        continue

                    # 🔧 修复：使用日期范围查询，更精确
                    count_query = f"""
                        SELECT COUNT(*) FROM {table_name}
                        WHERE Order_time >= ? AND Order_time < date(?, '+1 day')
                    """
                    cursor.execute(count_query, (target_date, target_date))
                    count = cursor.fetchone()[0]
                    table_counts[table_name] = count
                    existing_records += count

            if existing_records > 0:
                self.logger.info(f"🔍 发现数据库中已有 {target_date} 的数据:")
                for table_name, count in table_counts.items():
                    if count > 0:
                        self.logger.info(f"   {table_name}: {count}条记录")

                # 显示重复处理选项（类似现有逻辑）
                if self._is_gui_available():
                    choice = self._show_single_day_duplicate_dialog(target_date, existing_records, len(df))
                else:
                    choice = self._console_single_day_duplicate_choice(target_date, existing_records, len(df))

                if choice == 'cancel':
                    self.logger.info("❌ 用户取消了单天数据导入")
                    return 'cancel'
                elif choice == 'overwrite':
                    self.logger.info(f"✅ 用户选择覆盖 {target_date} 的数据")
                    # 删除该日期的数据
                    self._delete_single_day_data(target_date, platform, refunding_close_tables)
                    return 'skip_duplicate_check'
                elif choice == 'append':
                    self.logger.info(f"✅ 用户选择追加到 {target_date} 的数据")
                    return 'continue'
            else:
                self.logger.info(f"✅ 数据库中没有 {target_date} 的数据，可以直接导入")
                return 'continue'

        except Exception as e:
            self.logger.error(f"单天数据检测失败: {e}")
            return 'continue'  # 出错时继续正常流程

        return 'continue'

    def _handle_monthly_data(self, df: pd.DataFrame, platform: str, target_year: str, target_month: str, total_days: int) -> str:
        """
        处理多天数据的月度重复检测逻辑

        Args:
            df: 要导入的数据
            platform: 平台类型
            target_year: 目标年份
            target_month: 目标月份
            total_days: 总天数

        Returns:
            'continue': 继续正常流程
            'skip_duplicate_check': 跳过重复检测
            'cancel': 取消操作
        """
        try:
            # 检查数据库中是否已有该月份的数据
            refunding_close_tables = [
                f"{platform}_Sales_Refunding",
                f"{platform}_Sales_Close"
            ]

            existing_records = 0
            table_counts = {}

            with get_connection() as conn:
                cursor = conn.connection.cursor()

                for table_name in refunding_close_tables:
                    # 🔧 安全修复：检查表是否存在，使用参数化查询
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                    if not cursor.fetchone():
                        table_counts[table_name] = 0
                        continue

                    # 统计该月份的记录数
                    count_query = f"""
                        SELECT COUNT(*) FROM {table_name}
                        WHERE strftime('%Y', Order_time) = ? AND strftime('%m', Order_time) = ?
                    """
                    cursor.execute(count_query, (target_year, target_month))
                    count = cursor.fetchone()[0]
                    table_counts[table_name] = count
                    existing_records += count

            if existing_records > 0:
                self.logger.info(f"🔍 发现数据库中已有 {target_year}-{target_month} 的数据:")
                for table_name, count in table_counts.items():
                    if count > 0:
                        self.logger.info(f"   {table_name}: {count}条记录")

                # 显示月度重复处理选项
                if self._is_gui_available():
                    choice = self._show_monthly_duplicate_dialog(target_year, target_month, existing_records, len(df), total_days)
                else:
                    choice = self._console_monthly_duplicate_choice(target_year, target_month, existing_records, len(df), total_days)

                if choice == 'cancel':
                    self.logger.info("❌ 用户取消了月度数据导入")
                    return 'cancel'
                elif choice == 'overwrite':
                    self.logger.info(f"✅ 用户选择覆盖 {target_year}-{target_month} 的数据")
                    # 删除该月份的数据
                    self._delete_data_by_month(target_year, target_month, platform, refunding_close_tables)
                    return 'skip_duplicate_check'
                elif choice == 'append':
                    self.logger.info(f"✅ 用户选择追加到 {target_year}-{target_month} 的数据")
                    return 'continue'
            else:
                self.logger.info(f"✅ 数据库中没有 {target_year}-{target_month} 的数据，可以直接导入")
                return 'continue'

        except Exception as e:
            self.logger.error(f"月度数据检测失败: {e}")
            return 'continue'  # 出错时继续正常流程

        return 'continue'

    def _show_single_day_duplicate_dialog(self, target_date: str, existing_count: int, new_count: int) -> str:
        """显示单天数据重复处理对话框（线程安全版本）"""
        try:
            # 🔧 线程安全修复：使用队列在主线程中显示对话框
            import queue
            import threading

            result_queue = queue.Queue()

            def show_dialog_in_main_thread():
                try:
                    # 🔧 逻辑修复：当tkinter不可用时，使用控制台交互
                    if not TKINTER_AVAILABLE:
                        print(f"\n🔍 检测到单天数据重复")
                        print(f"📅 目标日期: {target_date}")
                        print(f"📊 数据库现有: {existing_count}条记录")
                        print(f"📥 准备导入: {new_count}条记录")
                        print(f"\n请选择处理方式：")
                        print(f"1. 覆盖：删除现有数据，导入新数据")
                        print(f"2. 追加：保留现有数据，追加新数据")
                        print(f"3. 取消：停止导入操作")

                        while True:
                            choice = input("请输入选择 (1/2/3): ").strip()
                            if choice == '1':
                                result_queue.put('overwrite')
                                break
                            elif choice == '2':
                                result_queue.put('append')
                                break
                            elif choice == '3':
                                result_queue.put('cancel')
                                break
                            else:
                                print("无效选择，请重新输入")
                        return

                    message = f"""🔍 检测到单天数据重复

📅 目标日期: {target_date}
📊 数据库现有: {existing_count}条记录
📥 准备导入: {new_count}条记录

请选择处理方式：
• 覆盖：删除现有数据，导入新数据
• 追加：保留现有数据，追加新数据
• 取消：停止导入操作"""

                    # 使用简单的messagebox，更安全
                    choice = messagebox.askyesnocancel(
                        "单天数据重复处理",
                        f"{message}\n\n点击'是'覆盖，'否'追加，'取消'停止",
                        icon='warning'
                    )

                    if choice is True:
                        result_queue.put('overwrite')
                    elif choice is False:
                        result_queue.put('append')
                    else:
                        result_queue.put('cancel')

                except Exception as e:
                    self.logger.error(f"GUI对话框错误: {e}")
                    result_queue.put('cancel')

            # 检查是否在主线程中
            if threading.current_thread() is threading.main_thread():
                show_dialog_in_main_thread()
            else:
                # 在工作线程中，降级到控制台
                self.logger.warning("在工作线程中，使用控制台交互")
                return self._console_single_day_duplicate_choice(target_date, existing_count, new_count)

            # 🔧 修复：等待结果，提示框不需要超时
            try:
                return result_queue.get()  # 移除超时限制，允许用户充分考虑
            except Exception as e:
                self.logger.warning(f"获取用户选择失败: {e}")
                return 'cancel'

        except Exception as e:
            self.logger.error(f"显示单天重复对话框失败: {e}")
            return 'cancel'

    def _show_monthly_duplicate_dialog(self, target_year: str, target_month: str, existing_count: int, new_count: int, total_days: int) -> str:
        """显示月度数据重复处理对话框"""
        try:
            # 🔧 逻辑修复：当tkinter不可用时，使用控制台交互
            if not TKINTER_AVAILABLE:
                print(f"\n🔍 检测到月度数据重复")
                print(f"📅 目标月份: {target_year}年{target_month}月")
                print(f"📊 数据库现有: {existing_count}条记录")
                print(f"📥 准备导入: {new_count}条记录 ({total_days}天)")
                print(f"\n请选择处理方式：")
                print(f"1. 覆盖：删除该月所有数据，导入新数据")
                print(f"2. 追加：保留现有数据，追加新数据")
                print(f"3. 取消：停止导入操作")

                while True:
                    choice = input("请输入选择 (1/2/3): ").strip()
                    if choice == '1':
                        return 'overwrite'
                    elif choice == '2':
                        return 'append'
                    elif choice == '3':
                        return 'cancel'
                    else:
                        print("无效选择，请重新输入")

            message = f"""🔍 检测到月度数据重复

📅 目标月份: {target_year}年{target_month}月
📊 数据库现有: {existing_count}条记录
📥 准备导入: {new_count}条记录 ({total_days}天)

请选择处理方式：
• 覆盖：删除该月所有数据，导入新数据
• 追加：保留现有数据，追加新数据
• 取消：停止导入操作"""

            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口

            # 创建自定义对话框
            dialog = tk.Toplevel()
            dialog.title("月度数据重复处理")
            dialog.geometry("400x300")
            dialog.resizable(False, False)

            # 居中显示
            dialog.transient(root)
            dialog.grab_set()

            result = {'choice': 'cancel'}

            # 消息文本
            tk.Label(dialog, text=message, justify=tk.LEFT, padx=20, pady=20).pack()

            # 按钮框架
            button_frame = tk.Frame(dialog)
            button_frame.pack(pady=20)

            def on_overwrite():
                result['choice'] = 'overwrite'
                dialog.destroy()

            def on_append():
                result['choice'] = 'append'
                dialog.destroy()

            def on_cancel():
                result['choice'] = 'cancel'
                dialog.destroy()

            tk.Button(button_frame, text="覆盖整月", command=on_overwrite, width=10).pack(side=tk.LEFT, padx=5)
            tk.Button(button_frame, text="追加", command=on_append, width=10).pack(side=tk.LEFT, padx=5)
            tk.Button(button_frame, text="取消", command=on_cancel, width=10).pack(side=tk.LEFT, padx=5)

            dialog.wait_window()
            root.destroy()

            return result['choice']

        except Exception as e:
            self.logger.error(f"显示月度重复对话框失败: {e}")
            return 'cancel'

    def _console_single_day_duplicate_choice(self, target_date: str, existing_count: int, new_count: int) -> str:
        """控制台版本的单天重复选择"""
        print(f"\n🔍 检测到单天数据重复")
        print(f"📅 目标日期: {target_date}")
        print(f"📊 数据库现有: {existing_count}条记录")
        print(f"📥 准备导入: {new_count}条记录")
        print("\n请选择处理方式:")
        print("1. 覆盖 - 删除现有数据，导入新数据")
        print("2. 追加 - 保留现有数据，追加新数据")
        print("3. 取消 - 停止导入操作")

        while True:
            choice = input("请输入选择 (1/2/3): ").strip()
            if choice == '1':
                return 'overwrite'
            elif choice == '2':
                return 'append'
            elif choice == '3':
                return 'cancel'
            else:
                print("无效选择，请输入 1、2 或 3")

    def _console_monthly_duplicate_choice(self, target_year: str, target_month: str, existing_count: int, new_count: int, total_days: int) -> str:
        """控制台版本的月度重复选择"""
        print(f"\n🔍 检测到月度数据重复")
        print(f"📅 目标月份: {target_year}年{target_month}月")
        print(f"📊 数据库现有: {existing_count}条记录")
        print(f"📥 准备导入: {new_count}条记录 ({total_days}天)")
        print("\n请选择处理方式:")
        print("1. 覆盖 - 删除该月所有数据，导入新数据")
        print("2. 追加 - 保留现有数据，追加新数据")
        print("3. 取消 - 停止导入操作")

        while True:
            choice = input("请输入选择 (1/2/3): ").strip()
            if choice == '1':
                return 'overwrite'
            elif choice == '2':
                return 'append'
            elif choice == '3':
                return 'cancel'
            else:
                print("无效选择，请输入 1、2 或 3")

    def _delete_single_day_data(self, target_date: str, platform: str, tables_to_clear: list) -> Dict[str, int]:
        """删除单天数据"""
        delete_results = {}

        # 🔧 Bug修复：使用platform参数记录日志
        platform_info = f" (平台: {platform})" if platform else ""
        self.logger.info(f"🗑️ 开始删除单天数据{platform_info}: {target_date}")

        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                for table_name in tables_to_clear:
                    # 🔧 安全修复：检查表是否存在，使用参数化查询
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                    if not cursor.fetchone():
                        delete_results[table_name] = 0
                        continue

                    # 🔧 修复：使用日期范围查询，更精确
                    count_query = f"SELECT COUNT(*) FROM {table_name} WHERE Order_time >= ? AND Order_time < date(?, '+1 day')"
                    cursor.execute(count_query, (target_date, target_date))
                    count_to_delete = cursor.fetchone()[0]

                    if count_to_delete > 0:
                        # 执行删除
                        delete_query = f"DELETE FROM {table_name} WHERE Order_time >= ? AND Order_time < date(?, '+1 day')"
                        cursor.execute(delete_query, (target_date, target_date))
                        delete_results[table_name] = count_to_delete
                        self.logger.info(f"从 {table_name} 删除了 {count_to_delete} 条 {target_date} 的数据")
                    else:
                        delete_results[table_name] = 0

                # 🔧 修复：使用连接池的提交方法
                conn.commit()

        except Exception as e:
            self.logger.error(f"删除单天数据失败: {e}")
            raise

        return delete_results

    def _handle_single_day_data_universal(self, df: pd.DataFrame, platform: str, target_date: str, file_path: str) -> str:
        """
        通用的单天数据处理逻辑（适用于所有模式）
        """
        try:
            # 检查数据库中是否已有该日期的数据
            all_tables = [
                f"{platform}_Sales",
                f"{platform}_Sales_Refunding",
                f"{platform}_Sales_Close"
            ]

            existing_records = 0
            table_counts = {}

            with get_connection() as conn:
                cursor = conn.connection.cursor()

                for table_name in all_tables:
                    # 🔧 安全修复：检查表是否存在，使用参数化查询
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                    if not cursor.fetchone():
                        table_counts[table_name] = 0
                        continue

                    # 统计该日期的记录数
                    count_query = f"""
                        SELECT COUNT(*) FROM {table_name}
                        WHERE Order_time >= ? AND Order_time < date(?, '+1 day')
                    """
                    cursor.execute(count_query, (target_date, target_date))
                    count = cursor.fetchone()[0]
                    table_counts[table_name] = count
                    existing_records += count

            if existing_records > 0:
                self.logger.info(f"🔍 发现数据库中已有 {target_date} 的数据:")
                for table_name, count in table_counts.items():
                    if count > 0:
                        self.logger.info(f"   {table_name}: {count}条记录")

                # 显示智能处理选项
                if self._is_gui_available():
                    choice = self._show_universal_single_day_dialog(target_date, existing_records, file_path)
                else:
                    choice = self._console_universal_single_day_choice(target_date, existing_records, file_path)

                if choice == 'cancel':
                    self.logger.info("❌ 用户取消了单天数据导入")
                    return 'cancel'
                elif choice == 'overwrite':
                    self.logger.info(f"✅ 用户选择覆盖 {target_date} 的数据")
                    # 删除该日期的数据
                    self._delete_universal_single_day_data(target_date, platform, all_tables)
                    return 'skip_duplicate_check'
                elif choice == 'append':
                    self.logger.info(f"✅ 用户选择追加到 {target_date} 的数据")
                    return 'continue'
            else:
                self.logger.info(f"✅ 数据库中没有 {target_date} 的数据，可以直接导入")
                return 'continue'

        except Exception as e:
            self.logger.error(f"单天数据检测失败: {e}")
            return 'continue'

        return 'continue'

    def _handle_monthly_data_universal(self, df, platform: str, target_year: str, target_month: str, total_days: int, file_path: str) -> str:
        """
        通用的多天数据处理逻辑（适用于所有模式）
        """
        try:
            print(f"🔧 [DEBUG] 开始检查数据库中的月度数据: {target_year}-{target_month}")
            sys.stdout.flush()

            # 🔧 修复：根据文件内容确定目标表，只检查相关表
            target_table = self._determine_target_table_from_data(df, platform)

            # 只检查目标表，不检查所有表
            tables_to_check = [target_table]

            print(f"🔧 [DEBUG] 根据文件内容确定目标表: {target_table}")
            print(f"🔧 [DEBUG] 只检查目标表: {tables_to_check}")
            sys.stdout.flush()

            existing_records = 0
            table_counts = {}

            with get_connection() as conn:
                cursor = conn.connection.cursor()

                for table_name in tables_to_check:
                    print(f"🔧 [DEBUG] 检查表: {table_name}")
                    sys.stdout.flush()

                    # 🔧 安全修复：检查表是否存在，使用参数化查询
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                    table_exists = cursor.fetchone()
                    if not table_exists:
                        print(f"🔧 [DEBUG] 表不存在: {table_name}")
                        sys.stdout.flush()
                        table_counts[table_name] = 0
                        continue

                    print(f"🔧 [DEBUG] 表存在，先查看实际数据格式")
                    sys.stdout.flush()

                    # 查看实际的Order_time格式
                    sample_query = f"SELECT Order_time FROM {table_name} LIMIT 3"
                    cursor.execute(sample_query)
                    samples = cursor.fetchall()
                    print(f"🔧 [DEBUG] {table_name}中的Order_time样本: {samples}")
                    sys.stdout.flush()

                    # 使用LIKE进行更可靠的匹配
                    count_query = f"""
                        SELECT COUNT(*) FROM {table_name}
                        WHERE Order_time LIKE ?
                    """
                    like_pattern = f"{target_year}-{target_month}%"
                    print(f"🔧 [DEBUG] 执行查询: {count_query} with pattern '{like_pattern}'")
                    sys.stdout.flush()

                    cursor.execute(count_query, (like_pattern,))
                    count = cursor.fetchone()[0]
                    table_counts[table_name] = count
                    existing_records += count

                    print(f"🔧 [DEBUG] 表 {table_name} 中找到 {count} 条记录")
                    sys.stdout.flush()

            print(f"🔧 [DEBUG] 总计找到 {existing_records} 条现有记录")
            print(f"🔧 [DEBUG] 各表统计: {table_counts}")
            sys.stdout.flush()

            if existing_records > 0:
                self.logger.info(f"🔍 发现数据库中已有 {target_year}-{target_month} 的数据:")
                for table_name, count in table_counts.items():
                    if count > 0:
                        self.logger.info(f"   {table_name}: {count}条记录")

                # 显示智能处理选项
                if self._is_gui_available():
                    choice = self._show_universal_monthly_dialog(target_year, target_month, existing_records, total_days, file_path)
                else:
                    choice = self._console_universal_monthly_choice(target_year, target_month, existing_records, total_days, file_path)

                if choice == 'cancel':
                    self.logger.info("❌ 用户取消了月度数据导入")
                    return 'cancel'
                elif choice == 'overwrite':
                    self.logger.info(f"✅ 用户选择覆盖 {target_year}-{target_month} 的数据")
                    # 删除该月份的数据
                    self._delete_universal_monthly_data(target_year, target_month, platform, tables_to_check)
                    return 'skip_duplicate_check'
                elif choice == 'append':
                    self.logger.info(f"✅ 用户选择追加到 {target_year}-{target_month} 的数据")
                    return 'continue'
            else:
                self.logger.info(f"✅ 数据库中没有 {target_year}-{target_month} 的数据，可以直接导入")
                return 'continue'

        except Exception as e:
            self.logger.error(f"月度数据检测失败: {e}")
            return 'continue'

        return 'continue'

    def _show_universal_single_day_dialog(self, target_date: str, existing_count: int, file_path: str) -> str:
        """显示通用的单天数据处理对话框"""
        try:
            import queue
            import threading

            result_queue = queue.Queue()

            def show_dialog_in_main_thread():
                try:
                    # 🔧 逻辑修复：当tkinter不可用时，使用控制台交互
                    if not TKINTER_AVAILABLE:
                        filename = os.path.basename(file_path)
                        print(f"\n🔍 检测到单天数据重复")
                        print(f"📁 文件: {filename}")
                        print(f"📅 目标日期: {target_date}")
                        print(f"📊 数据库现有: {existing_count}条记录")
                        print(f"\n请选择处理方式：")
                        print(f"1. 覆盖：删除该日期的所有数据，导入新数据")
                        print(f"2. 追加：保留现有数据，追加新数据")
                        print(f"3. 取消：停止导入操作")

                        while True:
                            choice = input("请输入选择 (1/2/3): ").strip()
                            if choice == '1':
                                result_queue.put('overwrite')
                                break
                            elif choice == '2':
                                result_queue.put('append')
                                break
                            elif choice == '3':
                                result_queue.put('cancel')
                                break
                            else:
                                print("无效选择，请重新输入")
                        return

                    filename = os.path.basename(file_path)
                    message = f"""🔍 检测到单天数据重复

📁 文件: {filename}
📅 目标日期: {target_date}
📊 数据库现有: {existing_count}条记录

请选择处理方式：
• 覆盖：删除该日期的所有数据，导入新数据
• 追加：保留现有数据，追加新数据
• 取消：停止导入操作"""

                    choice = messagebox.askyesnocancel(
                        "智能日期处理 - 单天数据",
                        f"{message}\n\n点击'是'覆盖，'否'追加，'取消'停止",
                        icon='warning'
                    )

                    if choice is True:
                        result_queue.put('overwrite')
                    elif choice is False:
                        result_queue.put('append')
                    else:
                        result_queue.put('cancel')

                except Exception as e:
                    self.logger.error(f"GUI对话框错误: {e}")
                    result_queue.put('cancel')

            if threading.current_thread() is threading.main_thread():
                show_dialog_in_main_thread()
            else:
                self.logger.warning("在工作线程中，使用控制台交互")
                return self._console_universal_single_day_choice(target_date, existing_count, file_path)

            # 🔧 修复：提示框不需要超时，可以无限停留
            try:
                return result_queue.get()  # 移除超时限制
            except Exception as e:
                self.logger.warning(f"获取用户选择失败: {e}")
                return 'cancel'

        except Exception as e:
            self.logger.error(f"显示单天对话框失败: {e}")
            return 'cancel'

    def _show_universal_monthly_dialog(self, target_year: str, target_month: str, existing_count: int, total_days: int, file_path: str) -> str:
        """显示通用的月度数据处理对话框"""
        try:
            import queue
            import threading

            result_queue = queue.Queue()

            def show_dialog_in_main_thread():
                try:
                    # 🔧 Bug修复：tkinter已在文件顶部导入，无需重复导入
                    if not TKINTER_AVAILABLE:
                        result_queue.put('append')
                        return

                    filename = os.path.basename(file_path)
                    message = f"""🔍 检测到月度数据重复

📁 文件: {filename}
📅 目标月份: {target_year}年{target_month}月
📊 数据库现有: {existing_count}条记录
📅 文件包含: {total_days}天的数据

请选择处理方式：
• 覆盖：删除该月份的所有数据，导入新数据
• 追加：保留现有数据，追加新数据
• 取消：停止导入操作"""

                    choice = messagebox.askyesnocancel(
                        "智能日期处理 - 月度数据",
                        f"{message}\n\n点击'是'覆盖整月，'否'追加，'取消'停止",
                        icon='warning'
                    )

                    if choice is True:
                        result_queue.put('overwrite')
                    elif choice is False:
                        result_queue.put('append')
                    else:
                        result_queue.put('cancel')

                except Exception as e:
                    self.logger.error(f"GUI对话框错误: {e}")
                    result_queue.put('cancel')

            if threading.current_thread() is threading.main_thread():
                show_dialog_in_main_thread()
            else:
                self.logger.warning("在工作线程中，使用控制台交互")
                return self._console_universal_monthly_choice(target_year, target_month, existing_count, total_days, file_path)

            # 🔧 修复：提示框不需要超时，可以无限停留
            try:
                return result_queue.get()  # 移除timeout参数，允许无限等待
            except Exception as e:
                self.logger.warning(f"获取用户选择失败: {e}")
                return 'cancel'

        except Exception as e:
            self.logger.error(f"显示月度对话框失败: {e}")
            return 'cancel'

    def _console_universal_single_day_choice(self, target_date: str, existing_count: int, file_path: str) -> str:
        """控制台版本的通用单天选择"""
        filename = os.path.basename(file_path)
        print(f"\n🔍 检测到单天数据重复")
        print(f"📁 文件: {filename}")
        print(f"📅 目标日期: {target_date}")
        print(f"📊 数据库现有: {existing_count}条记录")
        print("\n请选择处理方式:")
        print("1. 覆盖 - 删除该日期的所有数据，导入新数据")
        print("2. 追加 - 保留现有数据，追加新数据")
        print("3. 取消 - 停止导入操作")

        while True:
            choice = input("请输入选择 (1/2/3): ").strip()
            if choice == '1':
                return 'overwrite'
            elif choice == '2':
                return 'append'
            elif choice == '3':
                return 'cancel'
            else:
                print("无效选择，请输入 1、2 或 3")

    def _console_universal_monthly_choice(self, target_year: str, target_month: str, existing_count: int, total_days: int, file_path: str) -> str:
        """控制台版本的通用月度选择"""
        filename = os.path.basename(file_path)
        print(f"\n🔍 检测到月度数据重复")
        print(f"📁 文件: {filename}")
        print(f"📅 目标月份: {target_year}年{target_month}月")
        print(f"📊 数据库现有: {existing_count}条记录")
        print(f"📅 文件包含: {total_days}天的数据")
        print("\n请选择处理方式:")
        print("1. 覆盖 - 删除该月份的所有数据，导入新数据")
        print("2. 追加 - 保留现有数据，追加新数据")
        print("3. 取消 - 停止导入操作")

        while True:
            choice = input("请输入选择 (1/2/3): ").strip()
            if choice == '1':
                return 'overwrite'
            elif choice == '2':
                return 'append'
            elif choice == '3':
                return 'cancel'
            else:
                print("无效选择，请输入 1、2 或 3")

    def _delete_universal_single_day_data(self, target_date: str, platform: str, tables_to_clear: list) -> Dict[str, int]:
        """删除通用的单天数据"""
        delete_results = {}

        # 🔧 Bug修复：使用platform参数记录日志
        platform_info = f" (平台: {platform})" if platform else ""
        self.logger.info(f"🗑️ 开始删除通用单天数据{platform_info}: {target_date}")

        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                for table_name in tables_to_clear:
                    # 🔧 安全修复：检查表是否存在，使用参数化查询
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                    if not cursor.fetchone():
                        delete_results[table_name] = 0
                        continue

                    # 统计要删除的记录数
                    count_query = f"SELECT COUNT(*) FROM {table_name} WHERE Order_time >= ? AND Order_time < date(?, '+1 day')"
                    cursor.execute(count_query, (target_date, target_date))
                    count_to_delete = cursor.fetchone()[0]

                    if count_to_delete > 0:
                        # 执行删除
                        delete_query = f"DELETE FROM {table_name} WHERE Order_time >= ? AND Order_time < date(?, '+1 day')"
                        cursor.execute(delete_query, (target_date, target_date))
                        delete_results[table_name] = count_to_delete
                        self.logger.info(f"从 {table_name} 删除了 {count_to_delete} 条 {target_date} 的数据")
                    else:
                        delete_results[table_name] = 0

                # 🔧 修复：使用连接池的提交方法
                conn.commit()

        except Exception as e:
            self.logger.error(f"删除单天数据失败: {e}")
            raise

        return delete_results

    def _delete_universal_monthly_data(self, target_year: str, target_month: str, platform: str, tables_to_clear: list) -> Dict[str, int]:
        """删除通用的月度数据"""
        delete_results = {}

        # 🔧 Bug修复：使用platform参数记录日志
        platform_info = f" (平台: {platform})" if platform else ""
        self.logger.info(f"🗑️ 开始删除通用月度数据{platform_info}: {target_year}-{target_month}")

        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                for table_name in tables_to_clear:
                    # 🔧 安全修复：检查表是否存在，使用参数化查询
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                    if not cursor.fetchone():
                        delete_results[table_name] = 0
                        continue

                    # 🔧 修复：使用LIKE模式匹配，更可靠
                    like_pattern = f"{target_year}-{target_month}%"
                    print(f"🔧 [DEBUG] 月度删除模式: {like_pattern} 在表 {table_name}")
                    sys.stdout.flush()

                    # 统计要删除的记录数
                    count_query = f"""
                        SELECT COUNT(*) FROM {table_name}
                        WHERE Order_time LIKE ?
                    """
                    cursor.execute(count_query, (like_pattern,))
                    count_to_delete = cursor.fetchone()[0]
                    print(f"🔧 [DEBUG] 表 {table_name} 中找到 {count_to_delete} 条要删除的记录")
                    sys.stdout.flush()

                    if count_to_delete > 0:
                        # 执行删除
                        delete_query = f"""
                            DELETE FROM {table_name}
                            WHERE Order_time LIKE ?
                        """
                        cursor.execute(delete_query, (like_pattern,))
                        delete_results[table_name] = count_to_delete
                        self.logger.info(f"从 {table_name} 删除了 {count_to_delete} 条 {target_year}-{target_month} 的数据")
                        print(f"🔧 [DEBUG] 成功删除 {count_to_delete} 条记录")
                        sys.stdout.flush()
                    else:
                        delete_results[table_name] = 0
                        print(f"🔧 [DEBUG] 表 {table_name} 中没有要删除的数据")
                        sys.stdout.flush()

                # 🔧 修复：使用连接池的提交方法
                conn.commit()

        except Exception as e:
            self.logger.error(f"删除月度数据失败: {e}")
            raise

        return delete_results

    def _delete_data_by_date(self, target_date: str, platform: str) -> Dict[str, int]:
        """
        🆕 根据日期删除数据库中的数据

        Args:
            target_date: 目标日期 (YYYY-MM-DD)
            platform: 平台类型

        Returns:
            删除结果统计 {表名: 删除数量}
        """
        # 🔧 安全修复：验证日期格式
        if not target_date or not isinstance(target_date, str):
            raise ValueError("目标日期必须是非空字符串")

        # 验证日期格式 (YYYY-MM-DD)
        import re
        if not re.match(r'^\d{4}-\d{2}-\d{2}$', target_date):
            raise ValueError(f"日期格式无效: {target_date}，期望格式: YYYY-MM-DD")

        # 验证日期有效性
        try:
            datetime.strptime(target_date, '%Y-%m-%d')
        except ValueError as e:
            raise ValueError(f"无效的日期: {target_date}, 错误: {e}")

        delete_results = {}
        tables_to_clear = self._get_tables_to_clear(platform)

        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                for table_name in tables_to_clear:
                    try:
                        # 🔧 安全修复：检查表是否存在，使用参数化查询
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                        if not cursor.fetchone():
                            self.logger.debug(f"表 {table_name} 不存在，跳过")
                            continue

                        # 查看实际的Order_time格式
                        sample_query = f"SELECT Order_time FROM {table_name} LIMIT 3"
                        cursor.execute(sample_query)
                        samples = cursor.fetchall()
                        self.logger.info(f"表 {table_name} 的Order_time样本: {samples}")

                        # 🔧 修复：使用LIKE模式匹配，更可靠
                        count_query = f"""
                        SELECT COUNT(*) FROM {table_name}
                        WHERE Order_time LIKE ?
                        """
                        like_pattern = f"{target_date}%"
                        self.logger.info(f"查询模式: {like_pattern}")
                        cursor.execute(count_query, (like_pattern,))
                        count_to_delete = cursor.fetchone()[0]

                        if count_to_delete > 0:
                            # 执行删除
                            delete_query = f"""
                            DELETE FROM {table_name}
                            WHERE Order_time LIKE ?
                            """
                            cursor.execute(delete_query, (like_pattern,))

                            delete_results[table_name] = count_to_delete
                            self.logger.info(f"从 {table_name} 删除了 {count_to_delete} 条 {target_date} 的数据")
                        else:
                            self.logger.info(f"表 {table_name} 中没有 {target_date} 的数据")
                            delete_results[table_name] = 0

                    except Exception as e:
                        self.logger.error(f"删除表 {table_name} 的数据失败: {e}")
                        delete_results[table_name] = -1  # 标记为失败

                # 🔧 修复：使用连接池的提交方法
                conn.commit()

                total_deleted = sum(count for count in delete_results.values() if count > 0)
                self.logger.info(f"总计删除了 {total_deleted} 条 {target_date} 的数据")

        except Exception as e:
            # 🔧 增强异常处理：回滚事务并提供详细错误信息
            try:
                if 'conn' in locals():
                    conn.rollback()
                    self.logger.warning("数据库事务已回滚")
            except Exception as rollback_error:
                self.logger.error(f"回滚事务失败: {rollback_error}")

            self.logger.error(f"删除日期数据失败: {e}")
            self.logger.error(f"目标日期: {target_date}, 平台: {platform}")
            raise DatabaseError(f"Failed to delete data by date: {e}", operation="delete_by_date")

        return delete_results

    def _determine_target_table(self, platform: str, order_status: str, order_types: str = None, order_type_mode: str = None) -> str:
        """
        智能确定目标表（支持模糊匹配和多语言）

        Args:
            platform: 平台类型 (IOT/ZERO/APP)
            order_status: 订单状态
            order_types: 订单类型（可选，用于API订单检测）
            order_type_mode: 订单类型模式（如'Refunding+Close'）

        Returns:
            目标表名
        """
        # 🔧 用户要求：Refunding+Close模式的表路由限制
        if order_type_mode == 'Refunding+Close':
            # 在Refunding+Close模式下，只能路由到Refunding和Close表
            status = str(order_status).strip().lower() if order_status else ""

            # 检查是否为Refunding状态
            if any(keyword in status for keyword in ['refund', '退款', 'refunding']):
                return f"{platform}_Sales_Refunding"

            # 检查是否为Close状态
            if any(keyword in status for keyword in ['close', 'closed', '关闭', '结束']):
                return f"{platform}_Sales_Close"

            # 如果都不匹配，默认为Refunding表
            self.logger.warning(f"Refunding+Close模式下无法识别状态'{order_status}'，默认路由到Refunding表")
            return f"{platform}_Sales_Refunding"

        # 🔧 关键修复：优先检查API订单（仅在非Refunding+Close模式下）
        if order_types:
            order_types_str = str(order_types).strip().lower()
            if 'api' in order_types_str:
                self.logger.debug(f"API order detected (Order_types: '{order_types}'), routing to APP_Sales")
                return "APP_Sales"

        # 清理和标准化状态
        status = str(order_status).strip().lower() if order_status else ""

        if not status:
            default_table = f"{platform}_Sales"
            self.logger.debug(f"Order status is empty, using default table: {default_table}")
            return default_table

        # 1. First try exact matching (backward compatibility)
        status_mapping = STATUS_TABLE_MAPPING.get(platform, {})
        for status_key, table_name in status_mapping.items():
            if status.lower() == status_key.lower():
                self.logger.debug(f"Exact match status '{status}' -> {table_name}")
                return table_name

        # 2. Smart fuzzy matching
        matched_category = self._smart_status_detection(status)
        if matched_category:
            table_name = f"{platform}_Sales{SMART_STATUS_PATTERNS[matched_category]['table_suffix']}"
            self.logger.info(f"Smart match status '{order_status}' -> {table_name} (category: {matched_category})")
            return table_name

        # 3. Default to main table
        default_table = f"{platform}_Sales"
        self.logger.warning(f"Unrecognized order status '{order_status}', using default table: {default_table}")
        return default_table

    def _filter_by_order_type(self, df: pd.DataFrame, order_type: str, platform: str) -> pd.DataFrame:
        """
        根据订单类型过滤数据

        Args:
            df: 原始数据框
            order_type: 订单类型 ('智能识别导入', '仅Refunding', '仅Close', 'Refunding+Close')
            platform: 平台类型

        Returns:
            过滤后的数据框
        """
        if order_type == "智能识别导入":
            # 🔧 修复：使用platform参数显示正确的表名
            platform_upper = platform.upper() if platform else "UNKNOWN"
            self.logger.info(f"智能识别导入模式（ALL）：{platform_upper}平台所有订单状态将根据智能规则路由到对应数据库表")
            self.logger.info(f"- Finished/Complete状态 → 主表 ({platform_upper}_Sales)")
            self.logger.info(f"- Refunding/Refunded状态 → 退款表 ({platform_upper}_Sales_Refunding)")
            self.logger.info(f"- Close/Closed状态 → 关闭表 ({platform_upper}_Sales_Close)")
            return df

        if 'Order_status' not in df.columns:
            self.logger.warning("数据中没有Order_status列，无法按订单类型过滤，返回所有数据")
            return df

        original_count = len(df)

        if order_type == "仅Refunding":
            # 只保留Refunding相关状态
            refunding_keywords = SMART_STATUS_PATTERNS['REFUNDING']['keywords']
            mask = df['Order_status'].astype(str).str.lower().str.strip().apply(
                lambda x: any(keyword in x for keyword in refunding_keywords)
            )
            filtered_df = df[mask].copy()
            self.logger.info(f"仅Refunding过滤：从{original_count}条记录中筛选出{len(filtered_df)}条Refunding状态记录")

        elif order_type == "仅Close":
            # 只保留Close相关状态
            close_keywords = SMART_STATUS_PATTERNS['CLOSED']['keywords']
            mask = df['Order_status'].astype(str).str.lower().str.strip().apply(
                lambda x: any(keyword in x for keyword in close_keywords)
            )
            filtered_df = df[mask].copy()
            self.logger.info(f"仅Close过滤：从{original_count}条记录中筛选出{len(filtered_df)}条Close状态记录")

        elif order_type == "Refunding+Close":
            # 保留Refunding和Close相关状态
            refunding_keywords = SMART_STATUS_PATTERNS['REFUNDING']['keywords']
            close_keywords = SMART_STATUS_PATTERNS['CLOSED']['keywords']
            all_keywords = refunding_keywords + close_keywords

            mask = df['Order_status'].astype(str).str.lower().str.strip().apply(
                lambda x: any(keyword in x for keyword in all_keywords)
            )
            filtered_df = df[mask].copy()
            self.logger.info(f"Refunding+Close过滤：从{original_count}条记录中筛选出{len(filtered_df)}条Refunding/Close状态记录")

        else:
            # 未知类型，返回所有数据
            self.logger.warning(f"未知的订单类型过滤选项：{order_type}，返回所有数据")
            filtered_df = df

        return filtered_df

    def _smart_status_detection(self, status: str) -> str:
        """
        智能状态检测（支持模糊匹配和多语言）

        Args:
            status: 订单状态（已转为小写）

        Returns:
            匹配的状态类别，如果没有匹配则返回None
        """
        # 遍历所有状态模式
        for category, pattern_info in SMART_STATUS_PATTERNS.items():
            keywords = pattern_info['keywords']

            # 检查是否包含任何关键词
            for keyword in keywords:
                if keyword.lower() in status:
                    self.logger.debug(f"Status '{status}' matches keyword '{keyword}' -> category: {category}")
                    return category

        return None

    def _analyze_data_distribution(self, df: pd.DataFrame, platform: str) -> Dict[str, int]:
        """
        分析数据分布情况

        Args:
            df: 数据DataFrame
            platform: 平台类型

        Returns:
            各表的数据分布统计
        """
        distribution = {}

        if 'Order_status' in df.columns:
            for _, row in df.iterrows():
                order_types = row.get('Order_types', '') if 'Order_types' in df.columns else None
                target_table = self._determine_target_table(platform, row.get('Order_status', ''), order_types, getattr(self, 'current_order_type', None))
                distribution[target_table] = distribution.get(target_table, 0) + 1
        else:
            # 如果没有Order_status列，全部导入到默认表
            default_table = f"{platform}_Sales"
            distribution[default_table] = len(df)

        return distribution

    def _get_data_for_table(self, df: pd.DataFrame, table_name: str, platform: str) -> pd.DataFrame:
        """
        🆕 根据表名从DataFrame中提取对应的数据

        Args:
            df: 原始数据DataFrame
            table_name: 目标表名
            platform: 平台类型

        Returns:
            属于该表的数据DataFrame
        """
        if df.empty:
            return pd.DataFrame()

        # 如果没有Order_status列，返回所有数据
        if 'Order_status' not in df.columns:
            return df.copy()

        # 🔧 关键修复：使用布尔索引而不是iterrows避免索引冲突
        if 'Order_status' not in df.columns:
            return df.copy()

        # 创建布尔掩码
        mask = df.apply(lambda row: self._determine_target_table(platform, row.get('Order_status', ''), row.get('Order_types', '') if 'Order_types' in df.columns else None, getattr(self, 'current_order_type', None)) == table_name, axis=1)

        # 使用布尔索引筛选数据并重置索引
        result_df = df[mask].copy().reset_index(drop=True)

        self.logger.debug(f"为表 {table_name} 提取了 {len(result_df)} 条记录")
        return result_df

    def smart_insert_data(self, df: pd.DataFrame, platform: str) -> Dict[str, int]:
        """
        智能插入数据到对应的表

        Args:
            df: 要插入的DataFrame
            platform: 平台类型

        Returns:
            各表的插入记录数统计
        """
        if df.empty:
            return {}

        # 首先分离API订单
        regular_df, api_df = self._separate_api_orders(df)

        # 处理API订单
        api_results = {}
        if not api_df.empty:
            self.logger.info(f"Found {len(api_df)} API orders, routing to APP_Sales table")
            api_results = self._process_api_orders(api_df)

        # 分析常规数据分布
        distribution = self._analyze_data_distribution(regular_df, platform)
        self.logger.info(f"Regular data distribution analysis: {distribution}")

        # 🔧 性能优化：高效按表分组常规数据，避免逐行处理
        table_data = {}
        if 'Order_status' in regular_df.columns:
            print(f"🔧 [SCRIPT] 开始按状态分组数据，总记录数: {len(regular_df)}", flush=True)

            # 🔧 优化：使用向量化操作替代逐行处理
            # 预先计算每个状态对应的目标表
            status_to_table = {}
            for status in regular_df['Order_status'].dropna().unique():
                table = self._determine_target_table(platform, status, None, getattr(self, 'current_order_type', None))
                status_to_table[status] = table

            print(f"🔧 [SCRIPT] 状态映射表: {status_to_table}", flush=True)

            # 🔧 优化：使用向量化映射替代lambda函数
            regular_df['target_table'] = regular_df['Order_status'].map(status_to_table)

            # 按目标表分组
            for target_table in status_to_table.values():
                if target_table:
                    table_df = regular_df[regular_df['target_table'] == target_table].copy()
                    table_df.drop('target_table', axis=1, inplace=True)  # 移除临时列
                    if not table_df.empty:
                        table_data[target_table] = table_df.to_dict('records')
                        print(f"🔧 [SCRIPT] 表 {target_table}: {len(table_df)} 条记录", flush=True)
        else:
            # 如果没有Order_status列，全部导入到默认表
            default_table = f"{platform}_Sales"
            table_data[default_table] = regular_df.to_dict('records')

        # 分表插入常规数据
        insert_results = {}
        total_inserted = 0

        for table_name, rows in table_data.items():
            if not rows:
                continue

            try:
                # 🔧 关键修复：转换为DataFrame并重置索引避免重复索引错误
                table_df = pd.DataFrame(rows).reset_index(drop=True)

                # 🔧 修复：确保每个表的数据都按Order_time排序，但保持日期字符串格式
                if 'Order_time' in table_df.columns and not table_df.empty:
                    try:
                        # 先转换为datetime进行排序，然后转回日期字符串格式
                        table_df['Order_time_temp'] = pd.to_datetime(table_df['Order_time'], errors='coerce')
                        table_df = table_df.sort_values('Order_time_temp', ascending=True)
                        # 🔧 关键修复：确保Order_time保持日期字符串格式 (YYYY-MM-DD)
                        table_df['Order_time'] = table_df['Order_time_temp'].dt.strftime('%Y-%m-%d')
                        table_df.drop('Order_time_temp', axis=1, inplace=True)
                        self.logger.debug(f"表 {table_name} 的数据已按Order_time排序并保持日期格式")
                    except Exception as e:
                        self.logger.warning(f"表 {table_name} Order_time排序失败: {e}")

                # 插入到对应表
                inserted_count = self._insert_to_table(table_df, table_name)
                insert_results[table_name] = inserted_count
                total_inserted += inserted_count

                self.logger.info(f"Table {table_name}: inserted {inserted_count} records")

            except Exception as e:
                self.logger.error(f"Table {table_name} insert failed: {e}")
                raise DatabaseError(f"Failed to insert into table {table_name}: {e}", operation="smart_insert", table=table_name)

        # 合并API订单结果
        insert_results.update(api_results)
        total_inserted += sum(api_results.values())

        self.logger.info(f"Smart insert completed, total inserted: {total_inserted} records (including {sum(api_results.values())} API orders)")
        return insert_results

    def _separate_api_orders(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        分离API订单数据

        Args:
            df: 原始DataFrame

        Returns:
            (常规订单DataFrame, API订单DataFrame)
        """
        if 'Order_types' not in df.columns:
            return df, pd.DataFrame()

        # 检测API订单（支持多种格式）
        api_mask = df['Order_types'].astype(str).str.strip().str.lower().str.contains('api', na=False)

        # 🔧 关键修复：分离数据并重置索引避免reindex错误
        api_df = df[api_mask].copy().reset_index(drop=True)
        regular_df = df[~api_mask].copy().reset_index(drop=True)

        if not api_df.empty:
            self.logger.info(f"Separated {len(api_df)} API orders from {len(df)} total records")

        return regular_df, api_df

    def _process_api_orders(self, api_df: pd.DataFrame) -> Dict[str, int]:
        """
        🔧 修复：处理API订单数据 - 支持智能路由到不同APP表

        Args:
            api_df: API订单DataFrame

        Returns:
            插入结果统计
        """
        if api_df.empty:
            return {}

        try:
            # 🔧 关键修复：API订单也需要根据状态智能路由
            self.logger.info(f"开始处理 {len(api_df)} 条API订单，将根据状态智能路由到APP表")

            # 分析API订单的状态分布
            api_distribution = self._analyze_data_distribution(api_df, 'APP')
            self.logger.info(f"API订单分布: {api_distribution}")

            # 智能插入API订单到对应的APP表
            insert_results = {}
            total_inserted = 0

            for table_name, count in api_distribution.items():
                if count > 0:
                    # 获取要插入到该表的数据
                    table_data = self._get_data_for_table(api_df, table_name, 'APP')

                    if not table_data.empty:
                        inserted_count = self._insert_to_table(table_data, table_name)
                        insert_results[table_name] = inserted_count
                        total_inserted += inserted_count
                        self.logger.info(f"{table_name}: 插入 {inserted_count} 条API订单记录")

            self.logger.info(f"API订单处理完成，总计插入 {total_inserted} 条记录")
            return insert_results

        except Exception as e:
            self.logger.error(f"Failed to process API orders: {e}")
            raise DatabaseError(f"Failed to process API orders: {e}", operation="api_insert", table="APP_Sales")

    def _insert_to_table(self, df: pd.DataFrame, table_name: str) -> int:
        """
        插入数据到指定表

        Args:
            df: 要插入的DataFrame
            table_name: 目标表名

        Returns:
            实际插入的记录数
        """
        if df.empty:
            return 0

        try:
            print(f"🔧 [SCRIPT] 开始插入到表 {table_name}，记录数: {len(df)}", flush=True)
            print(f"🔧 [SCRIPT] 尝试获取数据库连接...", flush=True)

            with get_connection() as conn:
                print(f"🔧 [SCRIPT] 数据库连接获取成功", flush=True)

                # 🔧 关键修复：确保DataFrame只包含数据库表中实际存在的列
                print(f"🔧 [SCRIPT] 开始过滤列...", flush=True)
                df_filtered = self._filter_columns_for_table(df, table_name)
                print(f"🔧 [SCRIPT] 列过滤完成，剩余记录数: {len(df_filtered)}", flush=True)

                # 🔧 修复：在插入前验证和修复Transaction_Num
                if 'Transaction_Num' in df_filtered.columns:
                    print(f"🔧 [SCRIPT] 验证Transaction_Num字段...", flush=True)
                    original_count = len(df_filtered)

                    # 统计修复前的状态
                    empty_count = (df_filtered['Transaction_Num'] == '').sum()
                    nan_count = df_filtered['Transaction_Num'].isna().sum()

                    # 应用安全转换
                    df_filtered['Transaction_Num'] = df_filtered['Transaction_Num'].apply(self._safe_convert_transaction_num)

                    # 统计修复后的状态
                    empty_after = (df_filtered['Transaction_Num'] == '').sum()
                    valid_count = original_count - empty_after

                    print(f"🔧 [SCRIPT] Transaction_Num验证完成: 原始空值={empty_count}, NaN={nan_count}, 修复后有效={valid_count}", flush=True)

                if df_filtered.empty:
                    self.logger.warning(f"过滤后的数据为空，跳过插入到 {table_name}")
                    return 0

                # 🔧 修复：按Order_time排序，但保持日期字符串格式
                if 'Order_time' in df_filtered.columns:
                    try:
                        # 🔧 关键修复：使用临时列进行排序，保持原格式
                        df_filtered['Order_time_temp'] = pd.to_datetime(df_filtered['Order_time'], errors='coerce')
                        # 按时间升序排序，最新的数据在最后
                        df_filtered = df_filtered.sort_values('Order_time_temp', ascending=True)
                        # 🔧 关键修复：删除临时列，保持Order_time为日期字符串格式
                        df_filtered.drop('Order_time_temp', axis=1, inplace=True)
                        self.logger.info(f"数据已按Order_time排序，保持日期字符串格式")
                    except Exception as e:
                        self.logger.warning(f"Order_time排序失败，使用原始顺序: {e}")
                else:
                    self.logger.warning(f"表 {table_name} 中没有Order_time列，无法排序")

                # 🔧 修复：使用参数化查询获取插入前的记录数
                cursor = conn.connection.cursor()
                cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                count_before = cursor.fetchone()[0]

                # 🔧 修复：使用安全的事务管理
                total_inserted = 0
                try:
                    # 🔧 修复：分批插入数据，每批独立事务
                    total_batches = (len(df_filtered) + self.batch_size - 1) // self.batch_size
                    print(f"🔧 [SCRIPT] 开始分批插入，总批次: {total_batches}", flush=True)

                    for i in range(0, len(df_filtered), self.batch_size):
                        batch_df = df_filtered.iloc[i:i+self.batch_size]
                        batch_num = i // self.batch_size + 1

                        try:
                            # 每批使用独立事务
                            batch_df.to_sql(table_name, conn.connection, if_exists='append', index=False)
                            total_inserted += len(batch_df)

                            # 🔧 修复：添加进度输出，确保进程有响应
                            print(f"🔧 [SCRIPT] 批次 {batch_num}/{total_batches} 完成，插入 {len(batch_df)} 条记录", flush=True)
                            self.logger.debug(f"Inserted batch data to {table_name}: {len(batch_df)} records")

                        except Exception as batch_error:
                            print(f"🔧 [SCRIPT] 批次 {batch_num} 插入失败: {batch_error}", flush=True)
                            self.logger.error(f"批次插入失败 (批次 {batch_num}): {batch_error}")
                            # 🔧 修复：单个批次失败不影响其他批次
                            continue

                    # 🔧 修复：使用连接池的提交方法
                    conn.commit()

                except Exception as e:
                    # 🔧 修复：改进错误处理
                    self.logger.error(f"插入操作失败: {e}")
                    raise DatabaseError(f"Insert operation failed: {e}", operation="insert", table=table_name)

                # 🔧 修复：使用安全的查询验证插入结果
                cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                count_after = cursor.fetchone()[0]
                actual_inserted = count_after - count_before

                return actual_inserted

        except Exception as e:
            if isinstance(e, DatabaseError):
                raise
            raise DatabaseError(f"Failed to insert data: {e}", operation="insert", table=table_name)

    def insert_data(self, df: pd.DataFrame, platform: str) -> int:
        """
        插入数据到数据库
        
        Args:
            df: 要插入的DataFrame
            platform: 平台类型
            
        Returns:
            实际插入的记录数
        """
        if df.empty:
            return 0
        
        try:
            with get_connection() as conn:
                table_name = f"{platform}_Sales"

                # 🔧 关键修复：确保DataFrame只包含数据库表中实际存在的列
                df_filtered = self._filter_columns_for_table(df, table_name)

                if df_filtered.empty:
                    self.logger.warning(f"过滤后的数据为空，跳过插入到 {table_name}")
                    return 0

                # 获取插入前的记录数
                cursor = conn.connection.cursor()
                # 🔒 安全修复：使用安全的表名验证和SQL构建
                safe_table = SQLSecurityValidator.sanitize_table_name(table_name)
                cursor.execute(f"SELECT COUNT(*) FROM {safe_table}")
                count_before = cursor.fetchone()[0]

                # 🔧 修复：使用连接池的事务管理，避免手动事务控制
                total_inserted = 0
                try:
                    # 使用连接池的自动事务管理
                    conn.begin_transaction()

                    # 分批插入数据
                    for i in range(0, len(df_filtered), self.batch_size):
                        batch_df = df_filtered.iloc[i:i+self.batch_size]
                        batch_df.to_sql(table_name, conn.connection, if_exists='append', index=False)
                        total_inserted += len(batch_df)
                        self.logger.debug(f"已插入批次数据: {len(batch_df)} 条")

                    # 所有批次成功，提交事务
                    conn.commit()

                except Exception as e:
                    # 任何批次失败，回滚所有操作
                    conn.rollback()
                    raise DatabaseError(f"批量插入数据失败: {e}", operation="插入", table=table_name)
                
                # 验证插入结果
                # 🔒 安全修复：使用安全的表名验证和SQL构建
                safe_table = SQLSecurityValidator.sanitize_table_name(table_name)
                cursor.execute(f"SELECT COUNT(*) FROM {safe_table}")
                count_after = cursor.fetchone()[0]
                actual_inserted = count_after - count_before

                self.logger.info(f"数据插入完成，实际插入: {actual_inserted} 条记录")
                return actual_inserted
                
        except Exception as e:
            if isinstance(e, DatabaseError):
                raise
            raise DatabaseError(f"插入数据失败: {e}", operation="插入", table=f"{platform}_Sales")
    
    def process_file(self, file_path: str, platform: str, order_type: str = '智能识别导入') -> Dict[str, Any]:
        """
        智能处理单个文件（带备份和错误恢复）

        Args:
            file_path: 文件路径
            platform: 平台类型
            order_type: 订单类型过滤

        Returns:
            处理结果字典
        """
        # 🔧 验证修改是否生效的明显标记
        print(f"🔧 [VERIFICATION] process_file方法开始执行 - 修改已生效！")
        print(f"🔧 [VERIFICATION] 文件: {file_path}")
        print(f"🔧 [VERIFICATION] 平台: {platform}")
        print(f"🔧 [VERIFICATION] 订单类型: {order_type}")
        sys.stdout.flush()

        # 🔧 设置当前订单类型，供表路由使用
        self.current_order_type = order_type

        result = {
            'success': False,
            'file_path': file_path,
            'platform': platform,
            'total_rows': 0,
            'new_rows': 0,
            'duplicate_rows': 0,
            'inserted_rows': 0,
            'table_distribution': {},
            'errors': []
        }

        # 🔧 修复：使用智能备份管理器的安全操作包装器，添加UI进度条支持
        def _safe_process():
            print(f"🔧 [DEBUG] _safe_process函数开始执行")
            sys.stdout.flush()

            # 🔧 新增：设置基于文件位置的日志保存
            print(f"🔧 [DEBUG] 准备设置文件日志")
            sys.stdout.flush()
            self.setup_file_based_logging(file_path)
            print(f"🔧 [DEBUG] 文件日志设置完成")
            sys.stdout.flush()

            # 🔧 新增：清理旧日志文件（30天前的）
            print(f"🔧 [DEBUG] 准备清理旧日志")
            sys.stdout.flush()
            self.cleanup_old_logs(30)
            print(f"🔧 [DEBUG] 旧日志清理完成")
            sys.stdout.flush()

            print(f"🔧 [DEBUG] 准备记录开始日志")
            sys.stdout.flush()
            self.logger.info(f"Starting smart file processing: {file_path}, platform: {platform}")
            print(f"🔧 [DEBUG] 开始日志记录完成")
            sys.stdout.flush()

            # 🔧 关键改进：在所有模式下都进行智能日期分析
            print(f"🔧 [DEBUG] 准备开始智能日期分析")
            sys.stdout.flush()
            self.logger.info("🔍 开始智能日期分析...")
            print(f"🔧 [DEBUG] 智能日期分析日志记录完成")
            sys.stdout.flush()

            try:
                print(f"🔧 [DEBUG] 开始调用_extract_month_from_file_content")
                sys.stdout.flush()
                extract_result = self._extract_month_from_file_content(file_path)
                print(f"🔧 [DEBUG] _extract_month_from_file_content调用成功")
                print(f"🔧 [DEBUG] 返回结果: {extract_result}")
                print(f"🔧 [DEBUG] 返回结果类型: {type(extract_result)}")
                if extract_result:
                    print(f"🔧 [DEBUG] 返回结果长度: {len(extract_result) if hasattr(extract_result, '__len__') else 'N/A'}")
                sys.stdout.flush()
            except Exception as e:
                print(f"🔧 [DEBUG] _extract_month_from_file_content调用失败: {e}")
                sys.stdout.flush()
                extract_result = None

            print(f"🔧 [DEBUG] 检查条件: extract_result={bool(extract_result)}")
            if extract_result:
                print(f"🔧 [DEBUG] extract_result存在，检查长度")
                sys.stdout.flush()
            else:
                print(f"🔧 [DEBUG] extract_result为空，跳过智能处理")
                sys.stdout.flush()

            if extract_result and len(extract_result) >= 4:
                print(f"🔧 [DEBUG] 条件满足，开始智能处理")
                sys.stdout.flush()

                # 🔧 修复：将变量定义移到try-except外部
                target_year, target_month, confidence, record_count = extract_result[:4]
                extra_info = extract_result[4] if len(extract_result) > 4 else {}
                is_single_day = extra_info.get('is_single_day', False)
                target_date = extra_info.get('target_date')
                total_days = extra_info.get('total_days', 0)

                print(f"🔧 [DEBUG] 变量解析完成: {target_year}-{target_month}, 单天={is_single_day}, 总天数={total_days}")
                sys.stdout.flush()

                try:
                    print(f"🔧 [DEBUG] 开始记录日志信息")
                    sys.stdout.flush()

                    self.logger.info(f"✅ 日期分析结果: {target_year}年{target_month}月")
                    self.logger.info(f"📊 置信度: {confidence:.1%}, 记录数: {record_count}")

                    if is_single_day:
                        self.logger.info(f"📅 单天数据: {target_date}")
                    else:
                        self.logger.info(f"📅 多天数据: {total_days}天")

                    print(f"🔧 [DEBUG] 日志信息记录完成")
                    sys.stdout.flush()

                except Exception as e:
                    print(f"🔧 [DEBUG] 日志记录失败: {e}")
                    sys.stdout.flush()

                # 保存分析结果到result中
                result['date_analysis'] = {
                    'year': target_year,
                    'month': target_month,
                    'confidence': confidence,
                    'record_count': record_count,
                    'is_single_day': is_single_day,
                    'target_date': target_date,
                    'total_days': total_days
                }

                print(f"🔧 [DEBUG] 准备开始智能处理逻辑")
                sys.stdout.flush()

                # 🔧 智能处理：根据日期分析结果提供相应选项
                if is_single_day:
                    print(f"🔧 [DEBUG] 执行单天数据处理")
                    sys.stdout.flush()
                    # 单天数据处理
                    single_day_result = self._handle_single_day_data_universal(df if 'df' in locals() else None, platform, target_date, file_path)
                    if single_day_result == 'cancel':
                        result['success'] = False
                        result['cancelled_by_user'] = True
                        return result
                    elif single_day_result == 'skip_duplicate_check':
                        result['skip_duplicate_detection'] = True
                else:
                    print(f"🔧 [DEBUG] 执行多天数据处理")
                    sys.stdout.flush()
                    # 多天数据处理
                    monthly_result = self._handle_monthly_data_universal(df if 'df' in locals() else None, platform, target_year, target_month, total_days, file_path)
                    print(f"🔧 [DEBUG] 多天数据处理结果: {monthly_result}")
                    sys.stdout.flush()
                    if monthly_result == 'cancel':
                        result['success'] = False
                        result['cancelled_by_user'] = True
                        return result
                    elif monthly_result == 'skip_duplicate_check':
                        result['skip_duplicate_detection'] = True

                print(f"🔧 [DEBUG] 智能处理逻辑执行完成")
                sys.stdout.flush()
            else:
                self.logger.warning("⚠️ 日期分析失败，将使用标准流程")
                result['date_analysis'] = None

            # 步骤1: 验证文件
            self.validate_file(file_path)

            # 步骤2: 加载和验证数据
            df = self.load_and_validate_data(file_path, platform)

            # 🔧 关键修复：检查 df 是否为 None
            if df is None:
                raise DataProcessingError("数据加载失败：DataFrame 为 None", file_path=file_path, stage="data_loading")

            result['total_rows'] = len(df)

            # 🆕 步骤2.5: 根据订单类型过滤数据 (45%)

            df = self._filter_by_order_type(df, order_type, platform)

            # 🔧 关键修复：再次检查过滤后的 df
            if df is None:
                raise DataProcessingError("数据过滤失败：DataFrame 为 None", file_path=file_path, stage="data_filtering")

            result['filtered_rows'] = len(df)
            self.logger.info(f"After order type filtering ({order_type}): {len(df)} rows remaining")

            # 步骤3: 分析数据分布 (50%)

            distribution = self._analyze_data_distribution(df, platform)
            result['table_distribution'] = distribution
            self.logger.info(f"Data will be distributed to tables: {distribution}")

            # 🆕 步骤4: 智能重复数据检测 (60%)

            # 🔧 新增：根据订单类型选择检测策略
            if order_type == "增量导入" or "增量" in order_type:
                self.logger.info("使用智能增量重复检测策略")
                fully_duplicate, partial_different, new_data = self.smart_incremental_duplicate_check(df, platform)
            else:
                self.logger.info("使用标准重复检测策略")
                fully_duplicate, partial_different, new_data = self.check_duplicate_data(df, platform)

            # 🔧 添加调试信息
            print(f"🔧 [DEBUG] 重复检测结果:")
            print(f"🔧 [DEBUG] 完全重复: {len(fully_duplicate)} 条")
            print(f"🔧 [DEBUG] 部分重复: {len(partial_different)} 条")
            print(f"🔧 [DEBUG] 新数据: {len(new_data)} 条")

            result['duplicate_rows'] = len(fully_duplicate) + len(partial_different)
            result['new_rows'] = len(new_data)

            # 🆕 步骤4.5: 重复数据处理策略 (65%) - 优先处理

            # 🔧 检查是否跳过重复检测（用户已在单天/月度检测中处理）
            if result.get('skip_duplicate_detection', False):
                self.logger.info("⏭️ 跳过常规重复检测（已在单天/月度检测中处理）")
                duplicate_strategy = 'skip'
            else:
                # 🔧 检查是否为Refunding+Close模式，避免重复用户交互
                skip_refunding_close_analysis = (
                    hasattr(self, 'current_order_type') and
                    self.current_order_type == 'Refunding+Close'
                )

                if skip_refunding_close_analysis:
                    # 🔧 Refunding+Close模式：使用简化分析流程，避免重复提示框
                    self.logger.info("🔧 Refunding+Close模式：使用简化分析流程，避免重复用户交互")
                    print(f"🔧 [DEBUG] Refunding+Close模式：跳过重复分析对话框")
                    sys.stdout.flush()

                    # 根据重复数据情况选择合适的默认策略
                    if len(fully_duplicate) > 0 or len(partial_different) > 0:
                        duplicate_strategy = 'skip'  # 有重复数据时跳过重复数据，只导入新数据
                        self.logger.info(f"🔧 检测到重复数据，默认策略：跳过重复数据")
                    else:
                        duplicate_strategy = 'skip'  # 没有重复数据时正常导入
                        self.logger.info(f"🔧 没有重复数据，默认策略：正常导入")
                else:
                    # 🆕 原有流程：先进行完整分析，再让用户选择
                    analysis_result = self._analyze_file_and_database_comprehensive(df, platform, fully_duplicate, partial_different, new_data, file_path)
                    duplicate_strategy = self._handle_duplicate_with_analysis(analysis_result)

                if duplicate_strategy == 'cancel':
                    result['success'] = False
                    result['errors'].append("用户取消导入操作")
                    self.logger.info("Import cancelled by user due to duplicate data")
                    return result

            # 🆕 步骤4.6: 条件性缺失记录检测 (68%) - 只在增量更新时检查

            if duplicate_strategy == 'incremental':
                self.logger.info("增量更新模式：检测数据库中存在但文件中缺失的记录")
                missing_report = self._detect_missing_records(df, platform)
                result['missing_report'] = missing_report

                if missing_report['has_missing']:
                    # 处理缺失记录
                    missing_strategy = self._handle_missing_records_interaction(missing_report, platform)
                    if missing_strategy == 'cancel':
                        result['success'] = False
                        result['errors'].append("用户取消导入操作（由于缺失记录）")
                        self.logger.info("Import cancelled by user due to missing records")
                        return result

                    # 执行缺失记录处理
                    missing_result = self._process_missing_records(missing_report, missing_strategy, file_path)
                    result['missing_processing'] = missing_result
                else:
                    self.logger.info("增量更新模式：未发现缺失记录")
            else:
                self.logger.info(f"策略 {duplicate_strategy}：跳过缺失记录检测")

            # 🆕 步骤5: 应用重复数据处理策略 (70%)
            final_data, strategy_stats = self._apply_duplicate_handling_strategy(
                duplicate_strategy, fully_duplicate, partial_different, new_data, platform
            )
            result['strategy_applied'] = duplicate_strategy
            result['strategy_stats'] = strategy_stats

            # 🆕 步骤6: 处理特殊策略 (80%)
            print(f"🔧 [DEBUG] 检查特殊策略: {duplicate_strategy}")
            sys.stdout.flush()

            if duplicate_strategy == 'refresh_daily':
                print(f"🔧 [DEBUG] 进入refresh_daily策略处理")
                sys.stdout.flush()

                # 🔧 智能删除策略：根据日期分析结果决定删除范围
                if 'date_analysis' in result and result['date_analysis']:
                    date_analysis = result['date_analysis']
                    is_single_day = date_analysis.get('is_single_day', False)
                    target_year = date_analysis.get('year')
                    target_month = date_analysis.get('month')
                    total_days = date_analysis.get('total_days', 0)

                    print(f"🔧 [DEBUG] 使用智能日期分析结果")
                    print(f"🔧 [DEBUG] 单天数据: {is_single_day}, 年月: {target_year}-{target_month}, 总天数: {total_days}")
                    sys.stdout.flush()

                    if is_single_day:
                        # 单天数据：删除指定日期
                        target_date = date_analysis.get('target_date')
                        self.logger.info(f"🆕 执行重新更新策略：删除单天数据 {target_date}")
                        print(f"🔧 [DEBUG] 删除单天数据: {target_date}")
                        sys.stdout.flush()
                        delete_results = self._delete_data_by_date(target_date, platform)
                        total_deleted = sum(count for count in delete_results.values() if count > 0)
                        self.logger.info(f"重新更新策略：删除了 {total_deleted} 条 {target_date} 的数据")
                    else:
                        # 多天数据：检查订单类型，决定删除策略
                        current_order_type = getattr(self, 'current_order_type', '智能识别导入')
                        print(f"🔧 [DEBUG] 当前订单类型: {current_order_type}")
                        sys.stdout.flush()

                        if current_order_type == 'Refunding+Close':
                            # 🔧 只有Refunding+Close模式才能删除整个月
                            target_table = self._determine_target_table_from_data(df, platform)
                            self.logger.info(f"🆕 执行重新更新策略：删除目标表 {target_table} 的 {target_year}-{target_month} 数据")
                            print(f"🔧 [DEBUG] Refunding+Close模式，删除整月数据: {target_table} 的 {target_year}-{target_month}")
                            sys.stdout.flush()

                            # 只删除目标表的数据
                            delete_results = self._delete_universal_monthly_data(target_year, target_month, platform, [target_table])
                            total_deleted = sum(count for count in delete_results.values() if count > 0)
                            self.logger.info(f"重新更新策略：从 {target_table} 删除了 {total_deleted} 条 {target_year}-{target_month} 的数据")
                        else:
                            # 🔧 修复：对于多天数据，即使非Refunding+Close模式也应该删除整个月份
                            target_table = self._determine_target_table_from_data(df, platform)
                            self.logger.info(f"🆕 执行重新更新策略：删除目标表 {target_table} 的整月数据 {target_year}-{target_month}")
                            print(f"🔧 [DEBUG] 删除目标表 {target_table} 的整月数据: {target_year}-{target_month}")
                            sys.stdout.flush()

                            # 删除整个月份的数据
                            delete_results = self._delete_target_table_monthly_data(target_year, target_month, target_table)
                            total_deleted = sum(count for count in delete_results.values() if count > 0)
                            self.logger.info(f"重新更新策略：从 {target_table} 删除了 {total_deleted} 条 {target_year}-{target_month} 的数据")
                else:
                    # 备用方案：使用传统的单日期提取
                    print(f"🔧 [DEBUG] 未找到智能日期分析结果，使用传统方法")
                    sys.stdout.flush()
                    self.logger.info("🆕 执行重新更新策略：删除当天数据后重新导入")
                    target_date = self._extract_file_date(file_path, df)
                    print(f"🔧 [DEBUG] 提取的目标日期: {target_date}")
                    sys.stdout.flush()
                    delete_results = self._delete_data_by_date(target_date, platform)
                    total_deleted = sum(count for count in delete_results.values() if count > 0)
                    self.logger.info(f"重新更新策略：删除了 {total_deleted} 条 {target_date} 的数据")

                result['deleted_data'] = delete_results
                print(f"🔧 [DEBUG] 删除结果: {delete_results}")
                print(f"🔧 [DEBUG] 总计删除: {total_deleted} 条数据")
                sys.stdout.flush()

            elif duplicate_strategy != 'refresh_daily' and order_type == 'Refunding+Close':
                # 🆕 月度刷新策略：专门用于退款数据的月度刷新
                self.logger.info("🆕 执行月度刷新策略：删除指定月份的Refunding和Close数据后重新导入")

                # 🔧 关键改进：从文件内容提取年月（更准确）
                extract_result = self._extract_month_from_file_content(file_path)

                if extract_result and len(extract_result) >= 4:
                    target_year, target_month, confidence, record_count = extract_result[:4]
                    extra_info = extract_result[4] if len(extract_result) > 4 else {}

                    self.logger.info(f"✅ 识别的目标年月: {target_year}-{target_month}")
                    self.logger.info(f"📊 置信度: {confidence:.1%}, 该月记录数: {record_count}")

                    # 🔧 新增：单天/多天检测逻辑
                    is_single_day = extra_info.get('is_single_day', False)
                    target_date = extra_info.get('target_date')
                    total_days = extra_info.get('total_days', 0)

                    if is_single_day:
                        self.logger.info(f"📅 检测到单天数据: {target_date}")
                        # 🔧 实现单条记录重复检测
                        single_day_result = self._handle_single_day_data(df, platform, target_date)
                        if single_day_result == 'cancel':
                            result['refresh_type'] = 'single_day_cancelled'
                            result['success'] = False
                            return result
                        elif single_day_result == 'skip_duplicate_check':
                            # 用户选择了覆盖，跳过常规重复检测
                            result['skip_duplicate_detection'] = True
                    else:
                        self.logger.info(f"📅 检测到多天数据: {total_days}天")
                        # 🔧 实现月度重复检测
                        monthly_result = self._handle_monthly_data(df, platform, target_year, target_month, total_days)
                        if monthly_result == 'cancel':
                            result['refresh_type'] = 'monthly_cancelled'
                            result['success'] = False
                            return result
                        elif monthly_result == 'skip_duplicate_check':
                            # 用户选择了月度覆盖，跳过常规重复检测
                            result['skip_duplicate_detection'] = True

                    # 如果置信度较低，给出警告
                    if confidence < 0.8:
                        self.logger.warning(f"⚠️ 月份识别置信度较低 ({confidence:.1%})，请确认数据正确性")

                    # 只清理Refunding和Close相关的表
                    refunding_close_tables = [
                        f"{platform}_Sales_Refunding",
                        f"{platform}_Sales_Close"
                    ]

                    # 删除指定月份的数据
                    delete_results = self._delete_data_by_month(target_year, target_month, platform, refunding_close_tables)
                    result['deleted_data'] = delete_results
                    result['refresh_type'] = 'monthly'
                    result['target_year'] = target_year
                    result['target_month'] = target_month
                    result['confidence'] = confidence
                    result['target_records'] = record_count

                    total_deleted = sum(count for count in delete_results.values() if count > 0)
                    self.logger.info(f"🗑️ 月度刷新策略：删除了 {total_deleted} 条 {target_year}-{target_month} 的Refunding/Close数据")
                else:
                    self.logger.error("❌ 无法从文件内容识别年月信息，跳过月度数据删除")
                    result['refresh_type'] = 'monthly_failed'
                    result['error'] = 'month_extraction_failed'

            # 🆕 步骤6.5: 处理overwrite策略的重复记录删除 (82%)
            if 'duplicate_stats' in result and result['duplicate_stats'].get('need_delete_duplicates', False):
                duplicate_records = result['duplicate_stats'].get('duplicate_records')
                if duplicate_records is not None and not duplicate_records.empty:
                    print(f"🔧 [DEBUG] overwrite策略：删除 {len(duplicate_records)} 条重复记录")
                    sys.stdout.flush()

                    try:
                        deleted_count = self._delete_duplicate_records_for_overwrite(duplicate_records, platform)
                        result['deleted_duplicates'] = deleted_count
                        self.logger.info(f"overwrite策略：成功删除 {deleted_count} 条重复记录")
                    except Exception as e:
                        error_msg = f"删除重复记录失败: {str(e)}"
                        print(f"❌ {error_msg}")
                        sys.stdout.flush()
                        result['errors'].append(error_msg)

            # 🆕 步骤7: 智能插入数据到对应表 (85%)
            insert_results = {}
            if not final_data.empty:
                print(f"🔧 [DEBUG] 开始插入数据，共 {len(final_data)} 条记录")
                sys.stdout.flush()

                try:
                    insert_results = self.smart_insert_data(final_data, platform)
                    result['inserted_rows'] = sum(insert_results.values())
                    result['table_insert_details'] = insert_results

                    print(f"🔧 [DEBUG] 数据插入完成，结果: {insert_results}")
                    sys.stdout.flush()

                    # Detailed logging
                    for table_name, count in insert_results.items():
                        self.logger.info(f"{table_name}: inserted {count} records")
                        print(f"✅ {table_name}: 成功插入 {count} 条记录")

                except Exception as e:
                    error_msg = f"数据插入失败: {str(e)}"
                    print(f"❌ {error_msg}")
                    sys.stdout.flush()
                    result['errors'].append(error_msg)
                    result['success'] = False
                    self.logger.error(error_msg)
                    return result
            else:
                print(f"🔧 [DEBUG] 处理后没有数据需要插入")
                sys.stdout.flush()
                self.logger.info("No data to insert after duplicate handling")

            # 🆕 步骤8: 数据完整性验证 (95%)

            integrity_report = self.validate_data_integrity(df, platform, insert_results)
            result['integrity_report'] = integrity_report

            # 记录完整性验证结果
            self.logger.info(f"数据完整性验证 - 质量评分: {integrity_report['data_quality_score']}/100")
            if integrity_report['errors']:
                for error in integrity_report['errors']:
                    self.logger.error(f"完整性验证错误: {error}")
            if integrity_report['warnings']:
                for warning in integrity_report['warnings']:
                    self.logger.warning(f"完整性验证警告: {warning}")


            result['success'] = True
            self.logger.info(f"🎉 增强智能文件处理完成: {file_path}")
            return result

        try:
            # 🔧 修复：直接调用_safe_process函数并获取返回值
            print(f"🔧 [DEBUG] 准备调用_safe_process函数")
            sys.stdout.flush()
            result = _safe_process()
            print(f"🔧 [DEBUG] _safe_process函数执行完成")
            sys.stdout.flush()

        except Exception as e:
            error_msg = str(e)
            result['errors'].append(error_msg)
            self.logger.error(f"Smart file processing failed: {file_path}, error: {error_msg}")
            print(f"🔧 [DEBUG] _safe_process函数执行失败: {error_msg}")
            sys.stdout.flush()

        return result

    def _determine_target_table_from_data(self, df: pd.DataFrame, platform: str) -> str:
        """
        🔧 根据数据内容确定目标表（用于重新更新功能）

        Args:
            df: 数据DataFrame
            platform: 平台类型

        Returns:
            目标表名
        """
        print(f"🔧 [DEBUG] 开始确定目标表，平台: {platform}")
        sys.stdout.flush()

        # 检查Order_status列，确定数据类型
        if 'Order_status' in df.columns:
            # 分析所有状态，找出主要的状态类型
            status_counts = df['Order_status'].value_counts()
            print(f"🔧 [DEBUG] 状态分布: {dict(status_counts)}")
            sys.stdout.flush()

            # 统计各表的预期记录数
            table_counts = {}
            for status, count in status_counts.items():
                target_table = self._determine_target_table(platform, status, None, getattr(self, 'current_order_type', None))
                if target_table not in table_counts:
                    table_counts[target_table] = 0
                table_counts[target_table] += count

            print(f"🔧 [DEBUG] 表分布预测: {table_counts}")
            sys.stdout.flush()

            # 返回记录数最多的表
            if table_counts:
                target_table = max(table_counts.items(), key=lambda x: x[1])[0]
                print(f"🔧 [DEBUG] 确定目标表: {target_table}")
                sys.stdout.flush()
                return target_table

        # 默认返回主表
        default_table = f"{platform}_Sales"
        print(f"🔧 [DEBUG] 使用默认表: {default_table}")
        sys.stdout.flush()
        return default_table

    def _determine_target_table_from_data(self, df: pd.DataFrame, platform: str) -> str:
        """
        🔧 根据数据内容确定主要导入的目标表

        Args:
            df: 数据DataFrame
            platform: 平台类型

        Returns:
            目标表名
        """
        print(f"🔧 [DEBUG] 开始确定目标表，平台: {platform}")
        sys.stdout.flush()

        # 检查Order_status列，确定数据类型
        if 'Order_status' in df.columns:
            # 分析所有状态，找出主要的状态类型
            status_counts = df['Order_status'].value_counts()
            print(f"🔧 [DEBUG] 状态分布: {dict(status_counts)}")
            sys.stdout.flush()

            # 统计各表的预期记录数
            table_counts = {}
            for status, count in status_counts.items():
                target_table = self._determine_target_table(platform, status, None, getattr(self, 'current_order_type', None))
                if target_table not in table_counts:
                    table_counts[target_table] = 0
                table_counts[target_table] += count

            print(f"🔧 [DEBUG] 表分布预测: {table_counts}")
            sys.stdout.flush()

            # 返回记录数最多的表
            if table_counts:
                target_table = max(table_counts.items(), key=lambda x: x[1])[0]
                print(f"🔧 [DEBUG] 确定目标表: {target_table}")
                sys.stdout.flush()
                return target_table

        # 默认返回主表
        default_table = f"{platform}_Sales"
        print(f"🔧 [DEBUG] 使用默认表: {default_table}")
        sys.stdout.flush()
        return default_table

    def _delete_target_table_data(self, target_date: str, target_table: str) -> Dict[str, int]:
        """
        🔧 只删除指定表的指定日期数据

        Args:
            target_date: 目标日期 (YYYY-MM-DD)
            target_table: 目标表名

        Returns:
            删除结果统计 {表名: 删除数量}
        """
        delete_results = {}

        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                # 🔧 安全修复：检查表是否存在，使用参数化查询
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (target_table,))
                if not cursor.fetchone():
                    print(f"🔧 [DEBUG] 表 {target_table} 不存在")
                    sys.stdout.flush()
                    delete_results[target_table] = 0
                    return delete_results

                # 查看实际的Order_time格式
                sample_query = f"SELECT Order_time FROM {target_table} LIMIT 3"
                cursor.execute(sample_query)
                samples = cursor.fetchall()
                print(f"🔧 [DEBUG] 表 {target_table} 的Order_time样本: {samples}")
                sys.stdout.flush()

                # 使用LIKE模式匹配
                like_pattern = f"{target_date}%"
                count_query = f"SELECT COUNT(*) FROM {target_table} WHERE Order_time LIKE ?"
                cursor.execute(count_query, (like_pattern,))
                count_to_delete = cursor.fetchone()[0]

                print(f"🔧 [DEBUG] 表 {target_table} 中找到 {count_to_delete} 条要删除的记录")
                sys.stdout.flush()

                if count_to_delete > 0:
                    # 执行删除
                    delete_query = f"DELETE FROM {target_table} WHERE Order_time LIKE ?"
                    cursor.execute(delete_query, (like_pattern,))
                    delete_results[target_table] = count_to_delete
                    print(f"🔧 [DEBUG] 成功从 {target_table} 删除了 {count_to_delete} 条记录")
                    sys.stdout.flush()
                else:
                    delete_results[target_table] = 0
                    print(f"🔧 [DEBUG] 表 {target_table} 中没有要删除的数据")
                    sys.stdout.flush()

                # 🔧 修复：使用连接池的提交方法
                conn.commit()

        except Exception as e:
            print(f"🔧 [DEBUG] 删除表 {target_table} 的数据失败: {e}")
            sys.stdout.flush()
            delete_results[target_table] = -1

        return delete_results

    def _delete_target_table_monthly_data(self, target_year: str, target_month: str, target_table: str) -> Dict[str, int]:
        """
        🔧 删除指定表的整个月份数据

        Args:
            target_year: 目标年份
            target_month: 目标月份
            target_table: 目标表名

        Returns:
            删除结果统计 {表名: 删除数量}
        """
        delete_results = {}

        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                # 🔧 安全修复：检查表是否存在，使用参数化查询
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (target_table,))
                if not cursor.fetchone():
                    print(f"🔧 [DEBUG] 表 {target_table} 不存在")
                    sys.stdout.flush()
                    delete_results[target_table] = 0
                    return delete_results

                # 查看实际的Order_time格式
                sample_query = f"SELECT Order_time FROM {target_table} LIMIT 3"
                cursor.execute(sample_query)
                samples = cursor.fetchall()
                print(f"🔧 [DEBUG] 表 {target_table} 的Order_time样本: {samples}")
                sys.stdout.flush()

                # 使用LIKE模式匹配整个月份
                like_pattern = f"{target_year}-{target_month}%"
                count_query = f"SELECT COUNT(*) FROM {target_table} WHERE Order_time LIKE ?"
                cursor.execute(count_query, (like_pattern,))
                count_to_delete = cursor.fetchone()[0]

                print(f"🔧 [DEBUG] 表 {target_table} 中找到 {count_to_delete} 条 {target_year}-{target_month} 的记录要删除")
                sys.stdout.flush()

                if count_to_delete > 0:
                    # 执行删除
                    delete_query = f"DELETE FROM {target_table} WHERE Order_time LIKE ?"
                    cursor.execute(delete_query, (like_pattern,))
                    delete_results[target_table] = count_to_delete
                    print(f"🔧 [DEBUG] 成功从 {target_table} 删除了 {count_to_delete} 条 {target_year}-{target_month} 的记录")
                    sys.stdout.flush()
                else:
                    delete_results[target_table] = 0
                    print(f"🔧 [DEBUG] 表 {target_table} 中没有 {target_year}-{target_month} 的数据")
                    sys.stdout.flush()

                # 🔧 修复：使用连接池的提交方法
                conn.commit()

        except Exception as e:
            print(f"🔧 [DEBUG] 删除表 {target_table} 的月度数据失败: {e}")
            sys.stdout.flush()
            delete_results[target_table] = -1

        return delete_results

    def _analyze_file_and_database_comprehensive(self, df: pd.DataFrame, platform: str,
                                                fully_duplicate: pd.DataFrame, partial_different: pd.DataFrame,
                                                new_data: pd.DataFrame, file_path: str) -> Dict[str, Any]:
        """
        🆕 完整分析文件内容和数据库状态

        Args:
            df: 文件数据
            platform: 平台类型
            fully_duplicate: 完全重复数据
            partial_different: 部分重复数据
            new_data: 新数据
            file_path: 文件路径

        Returns:
            完整的分析结果字典
        """
        print(f"🔧 [DEBUG] 开始完整分析文件和数据库状态")
        sys.stdout.flush()

        analysis = {
            'file_info': {},
            'database_info': {},
            'duplicate_info': {},
            'recommendations': [],
            'available_actions': []
        }

        # 1. 分析文件内容
        analysis['file_info'] = {
            'filename': os.path.basename(file_path),
            'total_records': len(df),
            'date_range': self._analyze_date_range(df),
            'order_types': self._analyze_order_types(df),
            'target_table': self._determine_target_table_from_data(df, platform),
            'platforms': [platform]
        }

        # 2. 分析数据库现状
        analysis['database_info'] = self._analyze_database_status(platform, analysis['file_info']['date_range'])

        # 3. 分析重复情况
        analysis['duplicate_info'] = {
            'fully_duplicate': len(fully_duplicate),
            'partial_different': len(partial_different),
            'new_data': len(new_data),
            'total_conflicts': len(fully_duplicate) + len(partial_different),
            'conflict_details': self._analyze_conflict_details(fully_duplicate, partial_different)
        }

        # 4. 生成建议和可用操作
        analysis['recommendations'] = self._generate_recommendations(analysis)
        analysis['available_actions'] = self._determine_available_actions(analysis)

        print(f"🔧 [DEBUG] 完整分析完成")
        sys.stdout.flush()

        return analysis

    def _analyze_date_range(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析文件中的日期范围"""
        date_info = {'dates': [], 'range': '', 'is_single_day': True, 'total_days': 0}

        if 'Order_time' in df.columns:
            try:
                unique_dates = set()
                for order_time in df['Order_time'].dropna():
                    if pd.notna(order_time):
                        try:
                            if isinstance(order_time, str):
                                parsed_date = pd.to_datetime(order_time)
                            else:
                                parsed_date = order_time
                            date_str = parsed_date.strftime('%Y-%m-%d')
                            unique_dates.add(date_str)
                        except:
                            continue

                date_info['dates'] = sorted(list(unique_dates))
                date_info['total_days'] = len(unique_dates)
                date_info['is_single_day'] = len(unique_dates) == 1

                if unique_dates:
                    if len(unique_dates) == 1:
                        date_info['range'] = list(unique_dates)[0]
                    else:
                        date_info['range'] = f"{min(unique_dates)} 至 {max(unique_dates)}"

            except Exception as e:
                print(f"🔧 [DEBUG] 日期分析失败: {e}")

        return date_info

    def _analyze_order_types(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析订单类型分布"""
        type_info = {'status_distribution': {}, 'order_types': {}, 'main_type': ''}

        if 'Order_status' in df.columns:
            type_info['status_distribution'] = df['Order_status'].value_counts().to_dict()
            # 确定主要类型
            if type_info['status_distribution']:
                main_status = max(type_info['status_distribution'].items(), key=lambda x: x[1])[0]
                type_info['main_type'] = main_status

        if 'Order_types' in df.columns:
            type_info['order_types'] = df['Order_types'].value_counts().to_dict()

        return type_info

    def _analyze_database_status(self, platform: str, date_range: Dict[str, Any]) -> Dict[str, Any]:
        """分析数据库中相关数据的状态"""
        db_info = {'tables': {}, 'total_records': 0, 'date_conflicts': {}}

        try:
            with get_connection() as conn:
                cursor = conn.connection.cursor()

                # 检查各个相关表的数据量
                tables_to_check = [f"{platform}_Sales", f"{platform}_Sales_Refunding", f"{platform}_Sales_Close"]

                for table_name in tables_to_check:
                    try:
                        # 🔧 安全修复：使用参数化查询防止SQL注入
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                        if cursor.fetchone():
                            # 总记录数
                            # 🔒 安全修复：使用安全的表名验证和SQL构建
                            safe_table = SQLSecurityValidator.sanitize_table_name(table_name)
                            cursor.execute(f"SELECT COUNT(*) FROM {safe_table}")
                            total_count = cursor.fetchone()[0]

                            # 相关日期的记录数
                            date_count = 0
                            if date_range['dates']:
                                for date in date_range['dates']:
                                    # 🔧 安全修复：验证表名并使用安全的SQL构建
                                    import re
                                    if not re.match(r'^[a-zA-Z0-9_]+$', table_name):
                                        continue  # 跳过不安全的表名
                                    cursor.execute(f'SELECT COUNT(*) FROM "{table_name}" WHERE Order_time LIKE ?', (f"{date}%",))
                                    date_count += cursor.fetchone()[0]

                            db_info['tables'][table_name] = {
                                'total_records': total_count,
                                'date_related_records': date_count
                            }
                            db_info['total_records'] += total_count

                            if date_count > 0:
                                db_info['date_conflicts'][table_name] = date_count

                    except Exception as e:
                        error_msg = str(e).lower()
                        if 'malformed' in error_msg or 'corrupt' in error_msg:
                            print(f"🚨 [CRITICAL] 数据库损坏检测: 表 {table_name} - {e}")
                            self.logger.error(f"数据库损坏: {table_name} - {e}")
                            # 标记数据库损坏，但继续处理其他表
                            db_info['database_corrupted'] = True
                            db_info['corrupted_tables'] = db_info.get('corrupted_tables', [])
                            db_info['corrupted_tables'].append(table_name)
                        else:
                            print(f"🔧 [DEBUG] 检查表 {table_name} 失败: {e}")

        except Exception as e:
            error_msg = str(e).lower()
            if 'malformed' in error_msg or 'corrupt' in error_msg:
                print(f"🚨 [CRITICAL] 数据库严重损坏: {e}")
                self.logger.critical(f"数据库严重损坏，无法继续分析: {e}")
                db_info['database_corrupted'] = True
                db_info['critical_error'] = str(e)
            else:
                print(f"🔧 [DEBUG] 数据库状态分析失败: {e}")

        return db_info

    def _analyze_conflict_details(self, fully_duplicate: pd.DataFrame, partial_different: pd.DataFrame) -> Dict[str, Any]:
        """分析冲突详情"""
        details = {'samples': [], 'affected_fields': []}

        # 提供重复数据样本
        if len(fully_duplicate) > 0:
            display_cols = ['Transaction_Num', 'Order_No', 'Order_time', 'Order_price']
            available_cols = [col for col in display_cols if col in fully_duplicate.columns]
            if available_cols:
                sample_data = fully_duplicate[available_cols].head(3)
                details['samples'] = sample_data.to_dict('records')

        # 🔧 Bug修复：分析部分重复数据的受影响字段
        if len(partial_different) > 0:
            # 分析哪些字段存在差异
            affected_fields = []
            for col in partial_different.columns:
                if col not in ['Transaction_Num', 'Order_No', 'Order_time']:  # 排除关键匹配字段
                    affected_fields.append(col)
            details['affected_fields'] = affected_fields[:5]  # 最多显示5个字段

        return details

    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """基于分析结果生成建议"""
        recommendations = []

        file_info = analysis['file_info']
        db_info = analysis['database_info']
        dup_info = analysis['duplicate_info']

        # 🚨 优先检查数据库损坏问题
        if db_info.get('database_corrupted', False):
            recommendations.append("🚨 严重警告：检测到数据库损坏！")
            if db_info.get('corrupted_tables'):
                corrupted_tables = ', '.join(db_info['corrupted_tables'])
                recommendations.append(f"🔧 损坏的表：{corrupted_tables}")
            recommendations.append("💡 建议：立即停止操作，使用备份恢复数据库")
            recommendations.append("🛠️ 或者运行数据库修复工具")
            return recommendations  # 数据库损坏时，只显示修复建议

        # 基于数据特征生成建议
        if file_info['date_range']['is_single_day']:
            recommendations.append("✅ 检测到单天数据，建议使用重新更新策略")
        else:
            recommendations.append(f"📅 检测到多天数据({file_info['date_range']['total_days']}天)，建议谨慎选择删除策略")

        if dup_info['total_conflicts'] > 0:
            if dup_info['fully_duplicate'] > dup_info['partial_different']:
                recommendations.append("🔄 大部分为完全重复，建议跳过重复数据")
            else:
                recommendations.append("📝 存在部分重复，建议使用增量更新")

        # 基于数据库状态生成建议
        total_conflicts = sum(db_info['date_conflicts'].values()) if db_info['date_conflicts'] else 0
        if total_conflicts > 1000:
            recommendations.append("⚠️ 数据库中存在大量相关数据，删除操作需谨慎")

        return recommendations

    def _determine_available_actions(self, analysis: Dict[str, Any]) -> List[Dict[str, str]]:
        """确定可用的操作选项"""
        actions = []

        db_info = analysis['database_info']
        dup_info = analysis['duplicate_info']

        # 🚨 数据库损坏时，只提供安全选项
        if db_info.get('database_corrupted', False):
            actions.append({
                'id': 'cancel',
                'name': '🚨 立即停止',
                'description': '数据库已损坏，立即停止操作以防止进一步损坏'
            })
            actions.append({
                'id': 'backup_restore',
                'name': '🔧 恢复备份',
                'description': '使用最近的备份恢复数据库（推荐）'
            })
            return actions

        # 正常情况下的操作选项
        if dup_info['new_data'] > 0:
            actions.append({
                'id': 'skip',
                'name': '跳过重复数据',
                'description': f'只导入 {dup_info["new_data"]} 条新数据，跳过重复数据'
            })

        if dup_info['total_conflicts'] > 0:
            actions.append({
                'id': 'overwrite',
                'name': '覆盖更新',
                'description': f'用新数据覆盖 {dup_info["total_conflicts"]} 条重复数据'
            })

            actions.append({
                'id': 'incremental',
                'name': '增量更新',
                'description': f'智能更新 {dup_info["partial_different"]} 条部分重复数据'
            })

        # 重新更新选项
        actions.append({
            'id': 'refresh_daily',
            'name': '重新更新',
            'description': '删除相关数据后重新导入（根据数据特征智能删除）'
        })

        # 取消选项
        actions.append({
            'id': 'cancel',
            'name': '取消导入',
            'description': '停止导入操作，不做任何更改'
        })

        return actions

    def _handle_duplicate_with_analysis(self, analysis: Dict[str, Any]) -> str:
        """基于完整分析结果处理重复数据"""
        try:
            # 🔧 Bug修复：tkinter已在文件顶部导入，无需重复导入
            if not TKINTER_AVAILABLE:
                return 'append'

            # 🔧 新增：智能识别导入模式的自动处理逻辑
            if hasattr(self, 'current_order_type') and self.current_order_type == '智能识别导入':
                dup_info = analysis['duplicate_info']

                print(f"🤖 智能识别导入模式：分析重复数据情况")
                print(f"   - 新数据: {dup_info['new_data']} 条")
                print(f"   - 完全重复: {dup_info['fully_duplicate']} 条")
                print(f"   - 部分重复: {dup_info['partial_different']} 条")
                print(f"   - 总冲突: {dup_info['total_conflicts']} 条")
                sys.stdout.flush()

                # 如果没有重复数据，只有新数据，自动选择跳过重复数据（实际导入新数据）
                if dup_info['total_conflicts'] == 0 and dup_info['new_data'] > 0:
                    self.logger.info(f"🤖 智能识别导入：检测到 {dup_info['new_data']} 条新数据，无重复数据，自动导入")
                    print(f"🤖 智能识别导入：检测到 {dup_info['new_data']} 条新数据，无重复数据，自动导入")
                    sys.stdout.flush()
                    return 'skip'  # 跳过重复数据，导入新数据

                # 如果只有少量重复数据，自动选择增量更新
                elif dup_info['total_conflicts'] > 0 and dup_info['total_conflicts'] <= 10:
                    self.logger.info(f"🤖 智能识别导入：检测到少量重复数据({dup_info['total_conflicts']}条)，自动选择增量更新")
                    print(f"🤖 智能识别导入：检测到少量重复数据({dup_info['total_conflicts']}条)，自动选择增量更新")
                    sys.stdout.flush()
                    return 'incremental'

                # 如果没有任何数据（既没有新数据也没有重复数据），直接跳过
                elif dup_info['new_data'] == 0 and dup_info['total_conflicts'] == 0:
                    self.logger.info(f"🤖 智能识别导入：没有检测到任何数据，跳过导入")
                    print(f"🤖 智能识别导入：没有检测到任何数据，跳过导入")
                    sys.stdout.flush()
                    return 'skip'

            # 🔧 新增：Refunding+Close模式的特殊处理逻辑
            elif hasattr(self, 'current_order_type') and self.current_order_type == 'Refunding+Close':
                dup_info = analysis['duplicate_info']

                print(f"🔄 Refunding+Close模式：分析重复数据情况")
                print(f"   - 新数据: {dup_info['new_data']} 条")
                print(f"   - 完全重复: {dup_info['fully_duplicate']} 条")
                print(f"   - 部分重复: {dup_info['partial_different']} 条")
                print(f"   - 总冲突: {dup_info['total_conflicts']} 条")
                sys.stdout.flush()

                # 如果没有重复数据，只有新数据，自动选择跳过重复数据（实际导入新数据）
                if dup_info['total_conflicts'] == 0 and dup_info['new_data'] > 0:
                    self.logger.info(f"🔄 Refunding+Close模式：检测到 {dup_info['new_data']} 条新数据，无重复数据，自动导入")
                    print(f"🔄 Refunding+Close模式：检测到 {dup_info['new_data']} 条新数据，无重复数据，自动导入")
                    sys.stdout.flush()
                    return 'skip'  # 跳过重复数据，导入新数据

                # 如果有重复数据，推荐使用重新更新策略（删除相关数据后重新导入）
                elif dup_info['total_conflicts'] > 0:
                    self.logger.info(f"🔄 Refunding+Close模式：检测到重复数据({dup_info['total_conflicts']}条)，推荐重新更新策略")
                    print(f"🔄 Refunding+Close模式：检测到重复数据({dup_info['total_conflicts']}条)，推荐重新更新策略")
                    sys.stdout.flush()
                    # 对于Refunding+Close模式，显示对话框让用户选择，但推荐重新更新
                    # 不自动选择，让用户确认操作

                # 如果没有任何数据，直接跳过
                elif dup_info['new_data'] == 0 and dup_info['total_conflicts'] == 0:
                    self.logger.info(f"🔄 Refunding+Close模式：没有检测到任何数据，跳过导入")
                    print(f"🔄 Refunding+Close模式：没有检测到任何数据，跳过导入")
                    sys.stdout.flush()
                    return 'skip'

            # 构建详细的分析报告
            report = self._build_analysis_report(analysis)

            # 显示分析结果对话框
            return self._show_analysis_dialog(analysis, report)

        except Exception as e:
            print(f"🔧 [DEBUG] 分析对话框失败: {e}")
            sys.stdout.flush()
            # 回退到原有逻辑
            return 'cancel'

    def _build_analysis_report(self, analysis: Dict[str, Any]) -> str:
        """构建详细的分析报告"""
        file_info = analysis['file_info']
        db_info = analysis['database_info']
        dup_info = analysis['duplicate_info']

        # 🚨 数据库损坏时的特殊报告
        if db_info.get('database_corrupted', False):
            report = f"""🚨 数据库损坏检测报告

📁 文件信息：
• 文件名：{file_info['filename']}
• 总记录数：{file_info['total_records']} 条
• 目标表：{file_info['target_table']}

💾 数据库状态：
🚨 严重警告：数据库已损坏！"""

            if db_info.get('corrupted_tables'):
                report += f"\n• 损坏的表：{', '.join(db_info['corrupted_tables'])}"

            if db_info.get('critical_error'):
                report += f"\n• 错误详情：{db_info['critical_error']}"

            report += f"""

💡 紧急建议："""
            for rec in analysis['recommendations']:
                report += f"\n• {rec}"

            return report

        # 正常情况的报告
        # 🔧 检查是否为Refunding+Close模式，添加特殊说明
        mode_info = ""
        if hasattr(self, 'current_order_type') and self.current_order_type == 'Refunding+Close':
            mode_info = f"""
🔄 导入模式：Refunding+Close（退款+关闭订单）
• 此模式专门处理退款和关闭状态的订单
• 系统已优化重复检测逻辑，提供精确的数据分类
• 推荐使用"重新更新"策略确保数据一致性"""

        report = f"""📊 数据导入分析报告{mode_info}

📁 文件信息：
• 文件名：{file_info['filename']}
• 总记录数：{file_info['total_records']} 条
• 日期范围：{file_info['date_range']['range']}
• 数据天数：{file_info['date_range']['total_days']} 天
• 目标表：{file_info['target_table']}

💾 数据库现状：
• 数据库总记录：{db_info['total_records']} 条"""

        if db_info['date_conflicts']:
            report += "\n• 相关日期冲突："
            for table, count in db_info['date_conflicts'].items():
                report += f"\n  - {table}: {count} 条"

        # 🔧 为Refunding+Close模式添加特殊的重复数据分析说明
        duplicate_analysis_title = "🔍 重复数据分析："
        if hasattr(self, 'current_order_type') and self.current_order_type == 'Refunding+Close':
            duplicate_analysis_title = "🔍 重复数据分析（Refunding+Close模式）："

        report += f"""

{duplicate_analysis_title}
• 完全重复：{dup_info['fully_duplicate']} 条（与数据库中现有记录完全相同）
• 部分重复：{dup_info['partial_different']} 条（部分字段不同，需要更新）
• 新数据：{dup_info['new_data']} 条（数据库中不存在的新记录）
• 总冲突：{dup_info['total_conflicts']} 条（需要处理的重复数据总数）"""

        # 🔧 为Refunding+Close模式添加特殊说明
        if hasattr(self, 'current_order_type') and self.current_order_type == 'Refunding+Close':
            if dup_info['total_conflicts'] > 0:
                report += f"""

🔄 Refunding+Close模式特别提示：
• 检测到 {dup_info['total_conflicts']} 条重复数据
• 建议使用"重新更新"策略，确保退款和关闭订单数据的完整性
• 系统将智能删除相关数据后重新导入，避免数据不一致"""
            else:
                report += f"""

🔄 Refunding+Close模式特别提示：
• 未检测到重复数据，可以安全导入 {dup_info['new_data']} 条新记录
• 系统已优化处理流程，确保退款和关闭订单数据的准确性"""

        report += f"""

💡 系统建议："""

        for rec in analysis['recommendations']:
            report += f"\n• {rec}"

        return report

    def _show_analysis_dialog(self, analysis: Dict[str, Any], report: str) -> str:
        """显示分析结果对话框"""
        import tkinter as tk
        from tkinter import ttk, messagebox

        result = {'choice': 'cancel'}

        def on_action_selected():
            selection = action_var.get()
            if selection:
                result['choice'] = selection
                dialog.destroy()
            else:
                messagebox.showwarning("提示", "请选择一个操作")

        def on_cancel():
            result['choice'] = 'cancel'
            dialog.destroy()

        # 创建对话框
        dialog = tk.Toplevel()

        # 🔧 为Refunding+Close模式优化对话框标题
        dialog_title = "数据导入分析 - 请确认操作"
        if hasattr(self, 'current_order_type') and self.current_order_type == 'Refunding+Close':
            dialog_title = "🔄 Refunding+Close模式 - 数据导入分析"

        dialog.title(dialog_title)
        dialog.geometry("600x500")
        dialog.resizable(True, True)
        dialog.transient()
        dialog.grab_set()

        # 分析报告区域
        report_frame = ttk.LabelFrame(dialog, text="📊 分析报告", padding=10)
        report_frame.pack(fill='both', expand=True, padx=10, pady=5)

        report_text = tk.Text(report_frame, wrap='word', height=15, font=('Microsoft YaHei', 9))
        scrollbar = ttk.Scrollbar(report_frame, orient='vertical', command=report_text.yview)
        report_text.configure(yscrollcommand=scrollbar.set)

        report_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        report_text.insert('1.0', report)
        report_text.config(state='disabled')

        # 操作选择区域
        action_frame = ttk.LabelFrame(dialog, text="🎯 请选择操作", padding=10)
        action_frame.pack(fill='x', padx=10, pady=5)

        action_var = tk.StringVar()

        for action in analysis['available_actions']:
            radio = ttk.Radiobutton(
                action_frame,
                text=f"{action['name']} - {action['description']}",
                variable=action_var,
                value=action['id']
            )
            radio.pack(anchor='w', pady=2)

        # 按钮区域
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill='x', padx=10, pady=10)

        ttk.Button(button_frame, text="确认执行", command=on_action_selected).pack(side='right', padx=5)
        ttk.Button(button_frame, text="取消", command=on_cancel).pack(side='right')

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        dialog.wait_window()
        return result['choice']


def main():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description='优化版数据导入脚本')

    # 🚨 关键修复：支持多种参数格式，兼容主应用程序调用
    parser.add_argument('file', nargs='?', help='要导入的文件路径（位置参数）')
    parser.add_argument('platform', nargs='?', choices=['IOT', 'ZERO', 'APP'], help='平台类型（位置参数）')

    # 支持主应用程序使用的参数格式
    parser.add_argument('--file', dest='file_main_app', help='要导入的文件路径（主应用程序格式）')
    parser.add_argument('--platform', dest='platform_main_app', choices=['IOT', 'ZERO', 'APP'], help='平台类型（主应用程序格式）')

    # 支持其他命名参数格式
    parser.add_argument('--file-path', dest='file_named', help='要导入的文件路径（命名参数）')
    parser.add_argument('--platform-type', dest='platform_named', choices=['IOT', 'ZERO', 'APP'], help='平台类型（命名参数）')

    parser.add_argument('--db_path', help='数据库路径')
    parser.add_argument('--order_type', default='智能识别导入',
                       choices=['智能识别导入', '仅Refunding', '仅Close', 'Refunding+Close'],
                       help='订单类型过滤')

    args = parser.parse_args()

    # 🚨 关键修复：按优先级确定实际使用的文件路径和平台
    # 🚨 Bug修复：更安全的参数解析逻辑
    file_path = None
    platform = None
    
    # 按优先级检查文件路径参数
    if args.file_main_app:  # 主应用程序格式优先
        file_path = args.file_main_app
    elif args.file:  # 位置参数
        file_path = args.file
    elif args.file_named:  # 命名参数
        file_path = args.file_named
    
    # 按优先级检查平台参数
    if args.platform_main_app:  # 主应用程序格式优先
        platform = args.platform_main_app
    elif args.platform:  # 位置参数
        platform = args.platform
    elif args.platform_named:  # 命名参数
        platform = args.platform_named

    # 验证必需参数
    if not file_path:
        print("❌ 错误：必须提供文件路径")
        print("用法1（位置参数）: python data_import_optimized.py <文件路径> <平台类型>")
        print("用法2（命名参数）: python data_import_optimized.py --file-path <文件路径> --platform-type <平台类型>")
        return 1

    if not platform:
        print("❌ 错误：必须提供平台类型 (IOT, ZERO, APP)")
        print("用法1（位置参数）: python data_import_optimized.py <文件路径> <平台类型>")
        print("用法2（命名参数）: python data_import_optimized.py --file-path <文件路径> --platform-type <平台类型>")
        return 1
    
    try:
        # 🔧 添加早期调试信息，强制刷新输出
        print(f"🔧 [DEBUG] 脚本开始执行")
        sys.stdout.flush()
        print(f"🔧 [DEBUG] 参数解析完成")
        sys.stdout.flush()
        print(f"🔧 [DEBUG] 文件路径: {file_path}")
        sys.stdout.flush()
        print(f"🔧 [DEBUG] 平台: {platform}")
        sys.stdout.flush()
        print(f"🔧 [DEBUG] 数据库路径: {args.db_path}")
        sys.stdout.flush()

        print(f"🔧 [DEBUG] 开始创建DataImportProcessor")
        sys.stdout.flush()

        # 创建处理器
        processor = DataImportProcessor(args.db_path)

        print(f"🔧 [DEBUG] DataImportProcessor创建成功")
        sys.stdout.flush()

        # 处理文件
        print(f"🔧 [DEBUG] 开始处理文件")
        sys.stdout.flush()

        result = processor.process_file(file_path, platform, args.order_type)

        print(f"🔧 [DEBUG] 文件处理完成")
        sys.stdout.flush()
        
        # 🔧 修复：输出中文日志，优化格式，包含新功能信息
        if result['success']:
            try:
                print("✅ 增强智能导入完成")
                print(f"📁 文件: {os.path.basename(file_path)}")
                print(f"🏷️ 平台: {platform}")
                print(f"📊 总行数: {result['total_rows']}")
                print(f"🆕 新数据: {result['new_rows']}")
                print(f"🔄 重复数据: {result['duplicate_rows']}")
                print(f"💾 实际插入: {result['inserted_rows']}")

                # 🆕 显示重复数据处理策略
                if 'strategy_applied' in result:
                    strategy_names = {
                        'skip': '跳过重复数据',
                        'overwrite': '覆盖更新',
                        'incremental': '增量更新',
                        'continue': '无重复数据'
                    }
                    strategy_name = strategy_names.get(result['strategy_applied'], result['strategy_applied'])
                    print(f"🔧 处理策略: {strategy_name}")

                # 🆕 显示数据完整性评分
                if 'integrity_report' in result:
                    score = result['integrity_report']['data_quality_score']
                    print(f"📈 数据质量评分: {score}/100")

                # 显示表分布信息
                if 'table_distribution' in result and result['table_distribution']:
                    print("\n📋 数据分布:")
                    for table_name, count in result['table_distribution'].items():
                        print(f"  - {table_name}: {count} 条记录")

                # 显示插入详情
                if 'table_insert_details' in result and result['table_insert_details']:
                    print("\n📥 插入详情:")
                    for table_name, count in result['table_insert_details'].items():
                        print(f"  - {table_name}: 插入 {count} 条记录")


            except UnicodeEncodeError:
                # 如果输出失败，只输出基本信息
                print("✅ 智能导入完成")
                print(f"📊 总行数: {result['total_rows']}")
                print(f"💾 实际插入: {result['inserted_rows']}")

            return 0
        else:
            try:
                print("❌ 智能导入失败")
                for error in result['errors']:
                    # 清理错误消息中的特殊字符
                    clean_error = str(error).encode('ascii', 'ignore').decode('ascii')
                    print(f"错误: {clean_error}")
            except UnicodeEncodeError:
                print("❌ 智能导入失败")
                print("由于编码问题无法显示错误详情")
            return 1

    except Exception as e:
        print(f"❌ 程序执行失败 - {e}")
        return 1


if __name__ == "__main__":
    # 🔧 添加最早期的调试输出
    print("🔧 [SCRIPT] 脚本main函数开始执行", flush=True)
    try:
        result = main()
        print(f"🔧 [SCRIPT] main函数执行完成，返回码: {result}", flush=True)
        sys.exit(result)
    except Exception as e:
        print(f"🔧 [SCRIPT] main函数执行失败: {e}", flush=True)
        import traceback
        traceback.print_exc()
        sys.exit(1)
