#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简测试脚本 - 直接测试导入脚本
"""

import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

print("🔧 最简测试：直接运行导入脚本")
print("=" * 40)

try:
    print("📋 测试1: 导入pandas...")
    import pandas as pd
    print("✅ pandas导入成功")
    
    print("📋 测试2: 导入基础模块...")
    import sqlite3
    from datetime import datetime
    print("✅ 基础模块导入成功")
    
    print("📋 测试3: 测试路径...")
    print(f"当前目录: {current_dir}")
    print(f"父目录: {parent_dir}")
    print(f"Python路径: {sys.path[:3]}")
    
    print("📋 测试4: 检查utils模块...")
    utils_path = os.path.join(parent_dir, 'utils')
    print(f"utils路径: {utils_path}")
    print(f"utils存在: {os.path.exists(utils_path)}")
    
    if os.path.exists(utils_path):
        utils_files = os.listdir(utils_path)
        print(f"utils文件: {utils_files}")
    
    print("📋 测试5: 尝试导入utils模块...")
    try:
        from utils.logger import get_logger
        print("✅ utils.logger导入成功")
    except Exception as e:
        print(f"❌ utils.logger导入失败: {e}")
    
    try:
        from utils.exceptions import *
        print("✅ utils.exceptions导入成功")
    except Exception as e:
        print(f"❌ utils.exceptions导入失败: {e}")
    
    print("🎉 基础测试完成！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n📋 现在尝试直接运行导入脚本...")
try:
    # 直接运行导入脚本，看看具体错误
    import subprocess
    result = subprocess.run([
        sys.executable, 
        "data_import_optimized.py", 
        "--help"
    ], capture_output=True, text=True, cwd=current_dir)
    
    print(f"返回码: {result.returncode}")
    print(f"输出: {result.stdout}")
    print(f"错误: {result.stderr}")
    
except Exception as e:
    print(f"❌ 运行导入脚本失败: {e}")

print("\n✅ 测试完成")
