2025-06-18 17:08:53 - smart_backup_manager - ERROR - ❌ 操作失败: 导入_IOT_online order Flow20250618 (1).xlsx, 错误: DataProcessingError: 加载数据失败: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-18 17:21:29 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-19 08:38:57 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-19 08:50:52 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DataProcessingError: Failed to load data: 'charmap' codec can't encode characters in position 9-14: character maps to <undefined>
2025-06-19 08:57:15 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DatabaseError: Failed to insert into table IOT_Sales: DatabaseError: Batch insert failed: table IOT_Sales has no column named Copartner name
2025-06-19 09:03:32 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DatabaseError: Failed to check duplicate data: You are trying to merge on int64 and object columns for key 'Equipment_ID'. If you wish to proceed you should use pd.concat
2025-06-19 09:16:59 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_online order Flow20250618 (1).xlsx, 错误: DatabaseError: Failed to insert into table IOT_Sales_Close: DatabaseError: Batch insert failed: table IOT_Sales_Close has no column named Transaction_ID
2025-06-19 17:16:45 - smart_backup_manager - ERROR - ❌ 操作失败: import_ZERO_130625 CHINA ZERO.xlsx, 错误: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:51 - smart_backup_manager - ERROR - ❌ 操作失败: import_ZERO_140625 CHINA ZERO.xlsx, 错误: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-19 17:16:57 - smart_backup_manager - ERROR - ❌ 操作失败: import_ZERO_150625 CHINA ZERO.xlsx, 错误: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-30 15:33:19 - smart_backup_manager - ERROR - ❌ 操作失败: import_ZERO_250625 CHINA ZERO.xlsx, 错误: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Serial number
2025-06-30 16:01:20 - smart_backup_manager - ERROR - ❌ 操作失败: import_ZERO_250625 CHINA ZERO.xlsx, 错误: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Transaction ID
2025-07-08 15:19:46 - smart_backup_manager - ERROR - ❌ 操作失败: import_ZERO_030725 CHINA ZERO.xlsx, 错误: DataProcessingError: Failed to load data: 'NoneType' object has no attribute 'columns'
2025-07-08 15:41:46 - smart_backup_manager - ERROR - ❌ 操作失败: import_ZERO_030725 CHINA ZERO.xlsx, 错误: DatabaseError: Failed to insert into table ZERO_Sales: DatabaseError: Batch insert failed: table ZERO_Sales has no column named Matched_Flag
2025-07-08 16:51:00 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_030725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to process API orders: 'DataImportProcessor' object has no attribute '_get_data_for_table'
2025-07-08 16:58:22 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_030725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to process API orders: 'DataImportProcessor' object has no attribute '_get_data_for_table'
2025-07-08 17:03:54 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_030725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to process API orders: 'DataImportProcessor' object has no attribute '_get_data_for_table'
2025-07-08 17:08:02 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_030725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to insert into table IOT_Sales_Close: DatabaseError: Batch insert failed: table IOT_Sales_Close has no column named source_table
2025-07-08 17:11:00 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_030725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to insert into table IOT_Sales_Close: DatabaseError: Batch insert failed: table IOT_Sales_Close has no column named source_table
2025-07-09 14:58:11 - smart_backup_manager - ERROR - ❌ 操作失败: import_ZERO_040725 CHINA ZERO.xlsx, 错误: 'AppLogger' object has no attribute 'addHandler'
2025-07-10 10:44:58 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 10:51:05 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 11:46:53 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 11:52:17 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 11:58:44 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 12:12:02 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 12:14:59 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to check duplicate data across tables: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-07-10 12:42:43 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:02:24 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:04:47 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:08:21 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:15:09 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to process API orders: Reindexing only valid with uniquely valued Index objects
2025-07-10 14:17:03 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_050725 CHINA IOT.xlsx, 错误: DatabaseError: Failed to insert into table IOT_Sales: Reindexing only valid with uniquely valued Index objects
2025-07-11 17:03:04 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_IOT 06 REF.xlsx, 错误: DataProcessingError: Failed to load data: 未找到包含 'IOT' 标识的工作表，无法开始导入。可用工作表: ['Sheet0']
2025-07-11 17:03:14 - smart_backup_manager - ERROR - 创建数据库备份失败: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\Users\\<USER>\\Desktop\\Day Report\\database\\backups\\backup_操作前_import_IOT_IOT_06xlsx_20250711_170313.db'
2025-07-11 17:03:14 - smart_backup_manager - ERROR - ❌ 操作失败: import_IOT_IOT 06.xlsx, 错误: DatabaseError: 创建数据库备份失败: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\Users\\<USER>\\Desktop\\Day Report\\database\\backups\\backup_操作前_import_IOT_IOT_06xlsx_20250711_170313.db'
