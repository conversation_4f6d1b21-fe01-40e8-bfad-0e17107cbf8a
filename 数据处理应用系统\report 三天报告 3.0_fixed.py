import pandas as pd
import numpy as np
import re
from datetime import datetime, timedelta
import os
import warnings
import argparse
import sys
import io
import codecs

# 定义排除API order类型的函数
def exclude_api_orders(df):
    """排除DataFrame中的API order类型记录
    
    Args:
        df (pandas.DataFrame): 包含订单数据的DataFrame
        
    Returns:
        pandas.DataFrame: 排除API order类型后的DataFrame
    """
    return df[~df["Order types"].str.strip().str.lower().str.contains("api", na=False)]

# 修复Windows命令行中文显示问题
if sys.platform == 'win32':
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 添加命令行参数处理
parser = argparse.ArgumentParser(description="处理SETTLEMENT和CHINA文件")
parser.add_argument("--file1", help="第一文件路径（SETTLEMENT文件）")
parser.add_argument("--file2", help="第二文件路径（CHINA文件）")
parser.add_argument("--sheet_name", help="第一文件的Sheet名称，默认为TRANSACTION_LIST")
args = parser.parse_args()

# 抑制 openpyxl 警告（可选）
warnings.simplefilter("ignore", category=UserWarning)

# -----------------------【配置区域】-----------------------
# 使用命令行参数或默认值
base_dir = r"C:/Users/<USER>/Desktop/Day report 3\IOT"

# 第一文件（文件名：SETTLEMENT_REPORT_03062025_IOT.xlsx，sheet名：TRANSACTION_LIST）
file1_name = "SETTLEMENT_REPORT_03062025_IOT.xlsx"
file1_path = args.file1 if args.file1 else os.path.join(base_dir, file1_name)
sheet_name = "TRANSACTION_LIST"  # 请确认

# 第二文件（文件名：030625 CHINA IOT.xlsx）
file2_name = "030625 CHINA IOT.xlsx"
file2_path = args.file2 if args.file2 else os.path.join(base_dir, file2_name)

# 输出文件配置
# 不再创建新文件，而是在原始第二文件中添加新的sheet
output_file_path = file2_path  # 直接使用第二文件路径
output_data_sheet = "DATA"     # 数据sheet名称
output_log_sheet = "LOG"      # 日志sheet名称
# ---------------------------------------------------------

# 打印使用的文件路径，便于调试
print(f"使用第一文件路径: {file1_path}")
print(f"使用第二文件路径: {file2_path}")

# 使用命令行参数指定的sheet名称或自动检测
try:
    xls = pd.ExcelFile(file1_path)
    available_sheets = xls.sheet_names
    
    # 如果命令行指定了sheet_name，则使用指定的
    if args.sheet_name:
        sheet_name = args.sheet_name
        if sheet_name not in available_sheets:
            print(f"警告: 指定的Sheet名称 '{sheet_name}' 在文件中不存在，可用的Sheet有: {available_sheets}")
            print(f"将尝试使用第一个可用的Sheet: {available_sheets[0]}")
            sheet_name = available_sheets[0]
    # 否则自动检测
    elif 'TRANSACTION_LIST' in available_sheets:
        sheet_name = 'TRANSACTION_LIST'
    else:
        sheet_name = available_sheets[0]  # 使用第一个sheet
    
    print(f"使用sheet: {sheet_name}")
except Exception as e:
    print(f"读取Excel文件出错: {str(e)}")
    sys.exit(1)

# -----------------------【第一文件数据处理】-----------------------
df1 = pd.read_excel(file1_path, sheet_name=sheet_name, header=0, engine="openpyxl")
print("第一文件读取的列名：", df1.columns.tolist())
print(f"第一文件列数：{df1.shape[1]}")

# 检查必要的列是否存在，而不是硬编码列数
required_columns = ["Date", "Time", "Transaction ID", "Order ID", "Bill Amt", "Status"]
missing_columns = [col for col in required_columns if col not in df1.columns]
if missing_columns:
    raise ValueError(f"第一文件缺少必要的列: {missing_columns}，请检查文件内容。")

# 如果列数不是预期的27列，给出警告但继续处理
if df1.shape[1] != 27:
    print(f"警告：第一文件列数为 {df1.shape[1]}，预期为27列。将继续处理，但请检查文件格式是否正确。")

# 检查Transaction ID列是否存在
if "Transaction ID" not in df1.columns:
    print("警告：第一文件中未找到'Transaction ID'列，将无法使用Transaction ID进行匹配")
    df1["Transaction ID"] = ""
else:
    print("找到'Transaction ID'列，将用于匹配")
    # 标准化Transaction ID（去除空格）
    df1["Transaction ID"] = df1["Transaction ID"].astype(str).apply(lambda x: x.strip())

# -----------------------【日期一致性检查】-----------------------
# 从文件名中提取日期
def extract_date_from_filename(filename):
    # 尝试从文件名中提取6位数字作为日期 (DDMMYY格式)
    match = re.search(r'(\d{6})', os.path.basename(filename))
    if match:
        date_str = match.group(1)
        # 假设格式为DDMMYY
        try:
            day = int(date_str[:2])
            month = int(date_str[2:4])
            year = int(date_str[4:6]) + 2000  # 假设是21世纪
            return datetime(year, month, day).date()
        except ValueError:
            return None
    return None

# 从第二文件名提取日期
file2_date = extract_date_from_filename(file2_path)

# 智能处理Date和Time列
if "Time" in df1.columns:
    # 如果有独立的Time列，合并Date和Time
    print("检测到独立的Time列，合并Date和Time信息")
    # 确保Date列是日期格式
    if pd.api.types.is_numeric_dtype(df1["Date"]):
        df1["Date"] = pd.to_datetime(df1["Date"], unit='d', origin='1899-12-30', errors="coerce")
    else:
        df1["Date"] = pd.to_datetime(df1["Date"], errors="coerce")

    # 处理Time列
    df1["Time"] = pd.to_datetime(df1["Time"], errors="coerce").dt.strftime("%H:%M:%S")

    # 合并Date和Time创建完整的DateTime
    df1["DateTime_str"] = df1["Date"].dt.strftime("%m/%d/%Y") + " " + df1["Time"].astype(str)
    df1["DateTime"] = pd.to_datetime(df1["DateTime_str"], format="%m/%d/%Y %H:%M:%S", errors="coerce")

    # 检查转换结果
    if df1["DateTime"].isnull().any():
        failed = df1[df1["DateTime"].isnull()]
        print("警告：以下记录 DateTime 转换失败：")
        print(failed[["Date", "Time", "DateTime_str"]])
        # 对于转换失败的记录，尝试只使用Date
        df1.loc[df1["DateTime"].isnull(), "DateTime"] = df1.loc[df1["DateTime"].isnull(), "Date"]
        print(f"已使用Date列填补 {df1['DateTime'].isnull().sum()} 条转换失败的记录")
else:
    # 如果没有独立的Time列，假设Date列包含完整的日期时间信息
    print("未检测到独立的Time列，假设Date列包含完整的日期时间信息")
    df1["DateTime"] = pd.to_datetime(df1["Date"], errors="coerce")
    if df1["DateTime"].isnull().any():
        failed = df1[df1["DateTime"].isnull()]
        print("警告：以下记录 Date 转换失败，将被删除，请检查这些数据：")
        print(failed["Date"])
        df1 = df1[df1["DateTime"].notnull()].reset_index(drop=True)

# 生成24小时制时间字符串供后续匹配使用
df1["Time24"] = df1["DateTime"].dt.strftime("%H:%M:%S")

# 提取第一文件中的日期进行比较
if not df1.empty and pd.notnull(df1["Date"].iloc[0]):
    file1_dates = df1["DateTime"].dt.date.unique()
    
    # 检查第二文件名中的日期是否在第一文件的日期范围内
    if file2_date is not None and len(file1_dates) > 0:
        if file2_date not in file1_dates:
            print(f"错误: 文件日期不匹配!")
            print(f"第一文件日期: {[d.strftime('%Y-%m-%d') for d in file1_dates]}")
            print(f"第二文件日期: {file2_date.strftime('%Y-%m-%d')}")
            print("处理已停止，请确保两个文件的日期一致。")
            sys.exit(1)
        else:
            print(f"日期一致性检查通过: {file2_date.strftime('%Y-%m-%d')}")
    else:
        print("警告: 无法从文件名中提取日期进行一致性检查，将继续处理。")

# -----------------------【筛选和统计】-----------------------
df1["Status"] = df1["Status"].astype(str)
df1_filtered = df1[df1["Status"].str.strip().str.lower().str.contains("settled")].copy()
if df1_filtered.empty:
    print("警告：第一文件筛选结果为空，请检查 Status 列数据！")
df1_filtered["Bill Amt"] = pd.to_numeric(df1_filtered["Bill Amt"], errors="coerce")
total_bill_amt = df1_filtered["Bill Amt"].sum()
freq_bill_amt = df1_filtered["Bill Amt"].round(2).value_counts().to_dict()

# 标准化 Order ID（去除空格）并判断类型
df1_filtered["Order ID"] = df1_filtered["Order ID"].astype(str).apply(lambda x: x.replace(" ", ""))


def check_order_id(oid):
    oid_str = str(oid).replace(" ", "")
    if re.search(r"[A-Za-z]", oid_str):
        return "anomaly"
    digits = re.sub(r"\D", "", oid_str)
    if len(digits) == 9:
        return "9_digit"
    elif len(digits) > 9:
        return "over_9"
    else:
        return "other"


df1_filtered["OrderID_Type"] = df1_filtered["Order ID"].apply(check_order_id)
anomaly_records = df1_filtered[df1_filtered["OrderID_Type"] == "anomaly"]

# 统计9位ID的数量
nine_digit_count = df1_filtered[df1_filtered["OrderID_Type"] == "9_digit"].shape[0]
print(f"第一文件9位ID数量：{nine_digit_count}")

# 统计第一文件中每个9位ID出现的次数
nine_digit_ids_count = {}
for _, row in df1_filtered[df1_filtered["OrderID_Type"] == "9_digit"].iterrows():
    oid = row["Order ID"]
    nine_digit_ids_count[oid] = nine_digit_ids_count.get(oid, 0) + 1

# -----------------------【第二文件数据处理】-----------------------
df2 = pd.read_excel(file2_path, engine="openpyxl")
df2.columns = [col.strip() for col in df2.columns]
mapping = {}
for col in df2.columns:
    cl = col.strip().lower()
    if cl in ["order price", "orderprice"]:
        mapping[col] = "Order price"
    elif cl in ["order status", "orderstatus"]:
        mapping[col] = "Order status"
    elif cl in ["order time", "ordertime"]:
        mapping[col] = "Order time"
    elif cl in ["equipment id", "equipmentid"]:
        mapping[col] = "Equipment ID"
    elif cl in ["order no.", "orderno"]:
        mapping[col] = "Order No."
df2.rename(columns=mapping, inplace=True)
print("第二文件标准化后列名：", df2.columns.tolist())
required = ["Order price", "Order status", "Order time"]
for r in required:
    if r not in df2.columns:
        raise KeyError(f"第二文件缺少关键列 {r}，请检查文件！")
df2["Order price"] = pd.to_numeric(df2["Order price"], errors="coerce")
df2["Payment"] = pd.to_numeric(df2["Payment"], errors="coerce")
df2["Order time"] = pd.to_datetime(df2["Order time"], errors="coerce")
df2["Time"] = df2["Order time"].dt.strftime("%H:%M:%S")
df2["Order status"] = df2["Order status"].astype(str)
# 添加API order类型排除条件
df2_finish = df2[(df2["Order status"].str.strip().str.lower() == "finish") & 
               (~df2["Order types"].str.strip().str.lower().str.contains("api", na=False))].copy()
freq_order_price = df2_finish["Order price"].round(2).value_counts().to_dict()
for col in ["Equipment ID", "Order No."]:
    if col in df2.columns:
        df2[col] = df2[col].astype(str).apply(lambda x: x.replace(" ", ""))
for col in ["Equipment name", "Branch name"]:
    if col not in df2.columns:
        df2[col] = ""

# 添加Order types字段
if "Order types" not in df2.columns:
    df2["Order types"] = ""

# 保存原始数据用于日志对比
df2_original = df2.copy()
# 添加API order类型排除条件
df2_original_finish = df2_original[(df2_original["Order status"].str.strip().str.lower() == "finish") & 
                                 (~df2_original["Order types"].str.strip().str.lower().str.contains("api", na=False))]
original_total = df2_original_finish["Order price"].sum()
original_freq = df2_original_finish["Order price"].round(2).value_counts().to_dict()

# 统计第二文件中每个9位ID出现的次数
second_nine_digit_ids_count = {}
for _, row in df2[df2["Equipment ID"].apply(lambda x: len(re.sub(r"\D", "", str(x))) == 9 and not re.search(r"[A-Za-z]", str(x)))].iterrows():
    # 添加API order类型排除条件
    if row["Order status"].strip().lower() == "finish" and not (str(row["Order types"]).strip().lower().find("api") >= 0):
        oid = row["Equipment ID"]
        second_nine_digit_ids_count[oid] = second_nine_digit_ids_count.get(oid, 0) + 1

# -----------------------【备份第二文件】-----------------------
df2_backup = df2.copy()

# -----------------------【新增内存标记变量】-----------------------
matched_indices_second = set()
processed_9digit_ids = {}

# 统计9位ID出现次数
for _, row in df1_filtered[df1_filtered["OrderID_Type"] == "9_digit"].iterrows():
    oid = row["Order ID"]
    processed_9digit_ids[oid] = processed_9digit_ids.get(oid, 0) + 1

# 添加标记列，用于标记成功匹配的数据
if "Matched_Flag" not in df2.columns:
    df2["Matched_Flag"] = False

# -----------------------【冲突检测函数】-----------------------
def check_conflict(oid, phase):
    if phase == "over_9":
        matches = df2[df2["Order No."] == oid]
        if any(i in matched_indices_second for i in matches.index):
            add_log((0, oid, f"Conflict detected! Order No. {oid} was matched by 9-digit ID"))
            return True
    return False

# -----------------------【匹配更新规则】-----------------------
if "Matched Order ID" not in df2.columns:
    df2["Matched Order ID"] = ""

# 添加Transaction ID列，用于存储匹配的Transaction ID
if "Transaction ID" not in df2.columns:
    df2["Transaction ID"] = ""

# 初始化调试日志列表，日志存储为元组：(金额, Order ID, 信息)
note_logs = []


def add_log(log_tuple):
    if log_tuple not in note_logs:
        note_logs.append(log_tuple)


df2["OrderTime_dt"] = pd.to_datetime(df2["Order time"], errors="coerce")

# 添加对超过9位ID的计数器，类似于9位ID的处理方式
processed_over9_ids = {}

# 统计超过9位ID出现次数
for _, row in df1_filtered[df1_filtered["OrderID_Type"] == "over_9"].iterrows():
    oid = row["Order ID"]
    processed_over9_ids[oid] = processed_over9_ids.get(oid, 0) + 1

# 分阶段处理：先处理9位ID，再处理超长ID，最后处理异常值
for phase in ["9_digit", "over_9", "anomaly"]:
    for idx, row in df1_filtered[df1_filtered["OrderID_Type"] == phase].iterrows():
        try:
            oid = row["Order ID"]
            amt = row["Bill Amt"]
            dt1 = row["DateTime"]
            t_val = row["Time24"]
            trans_id = row["Transaction ID"]  # 获取Transaction ID
            
            # 根据ID类型设置Order types
            if phase == "9_digit":
                search_field = "Equipment ID"
                default_eq = oid
                order_type = "Offline order"  # 9位ID对应Offline order
            elif phase == "over_9":
                search_field = "Order No."
                default_eq = ""
                order_type = "Normal order"   # 超过9位ID对应Normal order
            else:
                order_type = "Anomaly order"  # 异常ID使用特殊标记
            
            # 跳过已处理的9位ID
            if phase == "9_digit" and processed_9digit_ids.get(oid, 0) <= 0:
                continue
                
            # 跳过已处理的超过9位ID
            if phase == "over_9" and processed_over9_ids.get(oid, 0) <= 0:
                continue
                
            if phase == "anomaly":
                add_log((amt, oid, f"{row['DateTime']} {oid} ANOMALY inserted RM{amt:.2f}"))
                new_row = {
                    "Equipment ID": oid,
                    "Order price": amt,
                    "Order status": "Finish",
                    "Order time": dt1,
                    "Time": t_val,
                    "Matched Order ID": "",
                    "Transaction ID": trans_id,  # 添加Transaction ID
                    "Matched_Flag": True,  # 添加标记
                    "Order types": order_type  # 设置Order types
                }
                df2 = pd.concat([df2, pd.DataFrame([new_row])], ignore_index=True)
                continue
                
            if check_conflict(oid, phase):  # 新增冲突检测
                continue
                
            updated_flag = False
            
            # 首先尝试通过Transaction ID匹配（如果有效）
            if trans_id and trans_id.strip() and trans_id.strip() != "nan":
                # 检查是否已经有匹配的Transaction ID
                trans_matches = df2[df2["Transaction ID"] == trans_id]
                trans_matches = trans_matches[trans_matches.index.map(lambda x: x not in matched_indices_second)]
                
                if not trans_matches.empty:
                    # 找到Transaction ID匹配的记录
                    for m_idx, m_row in trans_matches.iterrows():
                        # 更新记录
                        if pd.notnull(m_row["Order price"]) and abs(m_row["Order price"] - amt) > 1e-2:
                            old_price = m_row["Order price"]
                            df2.at[m_idx, "Order price"] = amt
                            df2.at[m_idx, "Order status"] = "Finish"
                            df2.at[m_idx, "Order types"] = order_type
                            
                            if phase == "9_digit":
                                df2.at[m_idx, "Equipment ID"] = oid
                                df2.at[m_idx, "Time"] = t_val
                                df2.at[m_idx, "Order time"] = dt1
                            else:
                                if str(m_row["Equipment ID"]).strip() == "":
                                    df2.at[m_idx, "Matched Order ID"] = oid
                                    
                            matched_indices_second.add(m_idx)
                            df2.at[m_idx, "Matched_Flag"] = True
                            add_log((amt, oid, f"{row['DateTime']} {oid} (Transaction ID: {trans_id}) updated price from RM{old_price:.2f} to RM{amt:.2f}"))
                            
                            if phase == "9_digit":
                                processed_9digit_ids[oid] -= 1
                            elif phase == "over_9":
                                processed_over9_ids[oid] -= 1
                            updated_flag = True
                            break
                        elif pd.notnull(m_row["Order price"]) and abs(m_row["Order price"] - amt) <= 1e-2:
                            if m_row["Order status"].strip().lower() != "finish":
                                old_status = m_row["Order status"]
                                df2.at[m_idx, "Order status"] = "Finish"
                                df2.at[m_idx, "Order types"] = order_type
                                
                                if phase == "9_digit":
                                    df2.at[m_idx, "Equipment ID"] = oid
                                    df2.at[m_idx, "Time"] = t_val
                                    df2.at[m_idx, "Order time"] = dt1
                                else:
                                    if str(m_row["Equipment ID"]).strip() == "":
                                        df2.at[m_idx, "Matched Order ID"] = oid
                                        
                                matched_indices_second.add(m_idx)
                                df2.at[m_idx, "Matched_Flag"] = True
                                add_log((amt, oid, f"{row['DateTime']} {oid} (Transaction ID: {trans_id}) updated status from {old_status} to Finish RM{amt:.2f}"))
                                
                                if phase == "9_digit":
                                    processed_9digit_ids[oid] -= 1
                                elif phase == "over_9":
                                    processed_over9_ids[oid] -= 1
                                updated_flag = True
                                break
                            else:
                                # 状态已经是Finish，只需标记为匹配
                                matched_indices_second.add(m_idx)
                                df2.at[m_idx, "Matched_Flag"] = True
                                df2.at[m_idx, "Order types"] = order_type
                                
                                if phase == "9_digit":
                                    processed_9digit_ids[oid] -= 1
                                elif phase == "over_9":
                                    processed_over9_ids[oid] -= 1
                                updated_flag = True
                                add_log((amt, oid, f"{row['DateTime']} {oid} (Transaction ID: {trans_id}) matched by Transaction ID RM{amt:.2f}"))
                                break
            
            # 如果Transaction ID匹配失败，继续使用原有的匹配逻辑
            if not updated_flag:
                thresholds = [10, 30, 180, 300, 600, 1800, 3600, 10800]  # 递进式时间阈值
                
                for threshold in thresholds:
                    matches = df2[df2[search_field] == oid]
                    matches = matches[matches.index.map(lambda x: x not in matched_indices_second)]
                    matches = matches[matches["OrderTime_dt"].apply(
                        lambda x: dt1 and x and abs((x - dt1).total_seconds()) <= threshold)]
                        
                    if not matches.empty:
                        existing = matches[
                            (matches["Order price"].round(2) == round(amt, 2)) &
                            (matches["Order status"].str.strip().str.lower() == "finish")
                        ]
                        
                        if not existing.empty:
                            for m_idx in existing.index:
                                if threshold == 10800:
                                    df2.at[m_idx, "Order time"] = dt1
                                    df2.at[m_idx, "Time"] = t_val
                                matched_indices_second.add(m_idx)
                                df2.at[m_idx, "Matched_Flag"] = True  # 添加标记
                                df2.at[m_idx, "Order types"] = order_type  # 设置Order types
                                df2.at[m_idx, "Transaction ID"] = trans_id  # 添加Transaction ID
                                if phase == "9_digit":
                                    processed_9digit_ids[oid] -= 1
                                elif phase == "over_9":
                                    processed_over9_ids[oid] -= 1
                            updated_flag = True
                            break
                            
                        else:
                            for m_idx, m_row in matches.iterrows():
                                if pd.notnull(m_row["Order price"]) and abs(m_row["Order price"] - amt) > 1e-2:
                                    old_price = m_row["Order price"]
                                    df2.at[m_idx, "Order price"] = amt
                                    df2.at[m_idx, "Order status"] = "Finish"
                                    df2.at[m_idx, "Order types"] = order_type  # 设置Order types
                                    df2.at[m_idx, "Transaction ID"] = trans_id  # 添加Transaction ID
                                    
                                    if phase == "9_digit":
                                        df2.at[m_idx, "Equipment ID"] = oid
                                        df2.at[m_idx, "Time"] = t_val
                                        df2.at[m_idx, "Order time"] = dt1
                                    else:
                                        if str(m_row["Equipment ID"]).strip() == "":
                                            df2.at[m_idx, "Matched Order ID"] = oid
                                            
                                    matched_indices_second.add(m_idx)
                                    df2.at[m_idx, "Matched_Flag"] = True  # 添加标记
                                    add_log((amt, oid, f"{row['DateTime']} {oid} updated price from RM{old_price:.2f} to RM{amt:.2f}"))
                                    
                                    if phase == "9_digit":
                                        processed_9digit_ids[oid] -= 1
                                    elif phase == "over_9":
                                        processed_over9_ids[oid] -= 1
                                    updated_flag = True
                                    break
                                    
                                elif pd.notnull(m_row["Order price"]) and abs(m_row["Order price"] - amt) <= 1e-2:
                                    if m_row["Order status"].strip().lower() != "finish":
                                        old_status = m_row["Order status"]
                                        df2.at[m_idx, "Order status"] = "Finish"
                                        df2.at[m_idx, "Order types"] = order_type  # 设置Order types
                                        df2.at[m_idx, "Transaction ID"] = trans_id  # 添加Transaction ID
                                        
                                        if phase == "9_digit":
                                            df2.at[m_idx, "Equipment ID"] = oid
                                            df2.at[m_idx, "Time"] = t_val
                                            df2.at[m_idx, "Order time"] = dt1
                                        else:
                                            if str(m_row["Equipment ID"]).strip() == "":
                                                df2.at[m_idx, "Matched Order ID"] = oid
                                            
                                    matched_indices_second.add(m_idx)
                                    df2.at[m_idx, "Matched_Flag"] = True  # 添加标记
                                    add_log((amt, oid, f"{row['DateTime']} {oid} updated status from {old_status} to Finish RM{amt:.2f}"))
                                    
                                    if phase == "9_digit":
                                        processed_9digit_ids[oid] -= 1
                                    elif phase == "over_9":
                                        processed_over9_ids[oid] -= 1
                                    updated_flag = True
                                    break
                        if updated_flag:
                            break
                            
            if not updated_flag:
                new_row = {
                    search_field: oid,
                    "Order price": amt,
                    "Order status": "Finish",
                    "Order time": dt1,
                    "Time": t_val,
                    "Equipment ID": default_eq,
                    "Matched Order ID": oid if phase == "over_9" else "",
                    "Transaction ID": trans_id,  # 添加Transaction ID
                    "Matched_Flag": True,
                    "Order types": order_type
                }
                df2 = pd.concat([df2, pd.DataFrame([new_row])], ignore_index=True)
                add_log((amt, oid, f"{row['DateTime']} {oid} CHINA NO RECORD inserted RM{amt:.2f}"))
                
                # 减少计数器
                if phase == "9_digit":
                    processed_9digit_ids[oid] -= 1
                elif phase == "over_9":
                    processed_over9_ids[oid] -= 1
                
        except Exception as e:
            add_log((0, oid, f"Error processing {oid}: {str(e)}"))
            continue
# ... existing code ...
# -----------------------【删除未匹配的数据】-----------------------
# 删除未匹配的数据并记录到最终日志中
df2_before_delete = df2.copy()
# 使用排除API order类型的函数
df2_unmatched = df2[(df2["Order status"].str.strip().str.lower() == "finish") & 
                   (~df2["Matched_Flag"])]
# 只保留非API order类型的记录
df2_unmatched = exclude_api_orders(df2_unmatched)

if not df2_unmatched.empty:
    unmatched_total = df2_unmatched["Order price"].sum()
    unmatched_count = len(df2_unmatched)
    
    # 记录被删除的数据详情到日志中
    note_logs.append((0, "", f"删除未匹配数据: {unmatched_count}条，总金额: RM{unmatched_total:.2f}"))
    for _, row in df2_unmatched.iterrows():
        # 添加订单时间信息
        order_time_str = row["Order time"].strftime("%Y-%m-%d %H:%M:%S") if pd.notnull(row["Order time"]) else "无时间信息"
        note_logs.append((row["Order price"], str(row["Equipment ID"]), f"删除未匹配记录: RM{row['Order price']:.2f}, 时间: {order_time_str}"))
    
    # 删除未匹配的数据，使用排除API order类型的函数
    df2_to_exclude = df2[(df2["Order status"].str.strip().str.lower() == "finish") & 
                       (~df2["Matched_Flag"])]
    # 只保留非API order类型的记录
    df2_to_exclude = exclude_api_orders(df2_to_exclude)
    df2 = df2.drop(df2_to_exclude.index)

# -----------------------【频率修正】-----------------------
# 计算修正后的频率和总金额，使用排除API order类型的函数
df2_after = df2[(df2["Order status"].str.strip().str.lower() == "finish")].copy()
df2_after = exclude_api_orders(df2_after)
after_total = df2_after["Order price"].sum()
after_freq = df2_after["Order price"].round(2).value_counts().to_dict()

# 记录初始处理后的数据状态，用于后续自动修正
df2_after_initial = df2_after.copy()
after_total_initial = after_total
after_freq_initial = after_freq.copy()

# -----------------------【自动修正函数】-----------------------
def auto_correct_discrepancies():
    """
    自动检测并修正第一文件和第二文件之间的金额差异。
    该函数在数据处理完成后执行，确保两个文件的总金额完全一致。
    
    处理逻辑：
    1. 比较第一文件和第二文件的总金额
    2. 如果存在差异，查找导致差异的订单
    3. 自动修正金额差异，确保两个文件的总金额完全一致
    4. 记录所有修正操作到日志中，包括详细的订单信息
    5. 提供修正统计信息（添加订单数量、删除订单数量、总修正订单数量）
    
    返回：
        bool: 是否进行了修正操作
    """
    global df1_filtered, df2, df2_after, after_total, total_bill_amt, note_logs, df2_after_initial, after_total_initial, after_freq_initial
    
    # 使用初始处理后的数据状态进行比较，避免重复修正
    # 检查是否存在金额差异
    if abs(total_bill_amt - after_total_initial) < 0.01:
        return False  # 无需修正
        
    # 初始化当前差异变量
    current_diff = total_bill_amt - after_total_initial
    
    correction_logs = []
    correction_logs.append((0, "", f"开始自动修正金额差异: 第一文件 RM{total_bill_amt:.2f} vs 第二文件 RM{after_total_initial:.2f}, 差异: RM{abs(total_bill_amt - after_total_initial):.2f}"))
    
    # 创建第一文件和第二文件的订单金额映射
    # 使用排除API order类型的函数
    df1_orders = {}
    processed_9digit_ids_time = {}
    processed_over9_ids_time = {}
    
    # 使用初始处理后的数据进行比较和修正
    df2_for_correction = df2_after_initial.copy()
    original_freq = after_freq_initial.copy()
    
    # 初始化时间记录字典
    for _, row in df1_filtered.iterrows():
        oid = row["Order ID"]
        id_type = row["OrderID_Type"]
        dt = row["DateTime"]
        
        if id_type == "9_digit":
            if oid not in processed_9digit_ids_time:
                processed_9digit_ids_time[oid] = []
            processed_9digit_ids_time[oid].append(dt)
        elif id_type == "over_9":
            if oid not in processed_over9_ids_time:
                processed_over9_ids_time[oid] = []
            processed_over9_ids_time[oid].append(dt)
    
    for _, row in df1_filtered.iterrows():
        oid = row["Order ID"]
        amt = row["Bill Amt"]
        dt = row["DateTime"]
        id_type = row["OrderID_Type"]
        trans_id = row["Transaction ID"] if "Transaction ID" in row and pd.notnull(row["Transaction ID"]) else ""
        
        # 对于重复ID，创建包含时间信息的唯一键
        # 检查是否是重复ID
        is_duplicate = False
        time_key = ""
        
        if id_type == "9_digit" and oid in processed_9digit_ids and processed_9digit_ids[oid] > 1:
            is_duplicate = True
            # 查找最接近的时间记录
            closest_time = None
            min_diff = float('inf')
            for recorded_time in processed_9digit_ids_time[oid]:
                diff = abs((recorded_time - dt).total_seconds())
                if diff < min_diff:
                    min_diff = diff
                    closest_time = recorded_time
            
            # 如果时间差超过1秒，添加时间戳到键中
            if min_diff > 1:
                time_key = dt.strftime("%H%M%S")
                print(f"自动修正: 重复9位ID {oid} 时间差较大({min_diff}秒)，添加时间戳到键中: {time_key}")
        
        elif id_type == "over_9" and oid in processed_over9_ids and processed_over9_ids[oid] > 1:
            is_duplicate = True
            # 查找最接近的时间记录
            closest_time = None
            min_diff = float('inf')
            for recorded_time in processed_over9_ids_time[oid]:
                diff = abs((recorded_time - dt).total_seconds())
                if diff < min_diff:
                    min_diff = diff
                    closest_time = recorded_time
            
            # 如果时间差超过1秒，添加时间戳到键中
            if min_diff > 1:
                time_key = dt.strftime("%H%M%S")
                print(f"自动修正: 重复超过9位ID {oid} 时间差较大({min_diff}秒)，添加时间戳到键中: {time_key}")
        
        # 创建唯一键：订单ID + 时间戳(如果是重复ID) + 金额（四舍五入到2位小数）+ Transaction ID
        if is_duplicate and time_key:
            key = (oid, time_key, round(amt, 2), trans_id)
        else:
            key = (oid, round(amt, 2), trans_id)
            
        df1_orders[key] = {
            "datetime": dt,
            "id_type": id_type,
            "processed": False,
            "transaction_id": trans_id
        }
    
    # 首先统计df2_after中每个ID出现的次数和时间信息
    df2_id_counts = {}
    df2_id_times = {}
    
    for _, row in df2_after.iterrows():
        # 根据ID类型确定使用哪个字段作为订单ID
        if row["Order types"] == "Offline order":  # 9位ID
            oid = row["Equipment ID"]
            id_type = "9_digit"
        elif row["Order types"] == "Normal order":  # 超过9位ID
            oid = row["Order No."]
            id_type = "over_9"
        else:  # 其他情况，尝试两个字段
            oid = row["Equipment ID"] if str(row["Equipment ID"]).strip() else row["Order No."]
            id_type = "other"
        
        dt = row["Order time"]
        
        # 统计ID出现次数和时间
        if oid not in df2_id_counts:
            df2_id_counts[oid] = 0
            df2_id_times[oid] = []
        df2_id_counts[oid] += 1
        if isinstance(dt, pd.Timestamp):
            df2_id_times[oid].append(dt)
    
    # 创建df2_orders，使用与df1_orders一致的键创建逻辑
    df2_orders = {}
    df2_orders_with_time = {}
    df2_row_info = {}  # 存储行信息以便后续详细日志
    
    for _, row in df2_after.iterrows():
        # 根据ID类型确定使用哪个字段作为订单ID
        if row["Order types"] == "Offline order":  # 9位ID
            oid = row["Equipment ID"]
            id_type = "9_digit"
        elif row["Order types"] == "Normal order":  # 超过9位ID
            oid = row["Order No."]
            id_type = "over_9"
        else:  # 其他情况，尝试两个字段
            oid = row["Equipment ID"] if str(row["Equipment ID"]).strip() else row["Order No."]
            id_type = "other"
        
        amt = row["Order price"]
        dt = row["Order time"]
        trans_id = row["Transaction ID"] if "Transaction ID" in row and pd.notnull(row["Transaction ID"]) else ""
        
        # 检查是否是重复ID并需要添加时间戳
        is_duplicate = df2_id_counts.get(oid, 1) > 1
        time_key = ""
        
        if is_duplicate and isinstance(dt, pd.Timestamp):
            # 计算与其他相同ID记录的时间差
            min_diff = float('inf')
            for other_time in df2_id_times[oid]:
                if other_time != dt:
                    diff = abs((other_time - dt).total_seconds())
                    if diff < min_diff:
                        min_diff = diff
            
            # 如果时间差超过1秒，添加时间戳到键中
            if min_diff > 1:
                time_key = dt.strftime("%H%M%S")
                correction_logs.append((0, "", f"自动修正: 重复ID {oid} 在df2中时间差较大({min_diff:.1f}秒)，添加时间戳: {time_key}"))
        
        # 创建唯一键：订单ID + 时间戳(如果是重复ID) + 金额（四舍五入到2位小数）+ Transaction ID
        if is_duplicate and time_key:
            key = (oid, time_key, round(amt, 2), trans_id)
            df2_orders_with_time[key] = {
                "processed": False,
                "row_idx": row.name,
                "transaction_id": trans_id
            }
        else:
            key = (oid, round(amt, 2), trans_id)
            df2_orders[key] = {
                "processed": False,
                "row_idx": row.name,
                "transaction_id": trans_id
            }
        
        # 存储行信息用于详细日志
        row_info = {
            "index": row.name,
            "equipment_id": str(row.get("Equipment ID", "")),
            "order_no": str(row.get("Order No.", "")),
            "order_price": amt,
            "order_time": dt,
            "payment_date": row.get("Payment date", ""),
            "equipment_name": str(row.get("Equipment name", "")),
            "branch_name": str(row.get("Branch name", "")),
            "order_types": str(row.get("Order types", "")),
            "transaction_id": trans_id
        }
        
        if is_duplicate and time_key:
            df2_row_info[key] = row_info
        else:
            df2_row_info[key] = row_info
    
    # 查找第一文件中存在但第二文件中不存在的订单
    missing_orders = []
    for key, info in df1_orders.items():
        # 检查是否是带时间的键
        if len(key) == 4:  # 带时间的键 (oid, time_key, amt, trans_id)
            if key not in df2_orders_with_time:
                # 先尝试通过Transaction ID匹配
                trans_id = key[3]
                found_by_trans_id = False
                
                if trans_id and str(trans_id).strip() and str(trans_id).lower() != "nan":
                    # 检查是否有相同Transaction ID的记录
                    for df2_key in list(df2_orders.keys()) + list(df2_orders_with_time.keys()):
                        df2_trans_id = df2_key[2] if len(df2_key) == 3 else df2_key[3]
                        if df2_trans_id == trans_id:
                            # 找到了相同Transaction ID的记录，不添加到missing_orders
                            found_by_trans_id = True
                            break
                
                if not found_by_trans_id:
                    missing_orders.append({
                        "oid": key[0],
                        "amt": key[2],  # 注意索引变化
                        "datetime": info["datetime"],
                        "id_type": info["id_type"],
                        "time_key": key[1],  # 保存时间键
                        "transaction_id": trans_id,  # 保存Transaction ID
                        "store_name": df1_filtered[df1_filtered["Order ID"] == key[0]]["Store Name"].iloc[0] if not df1_filtered[df1_filtered["Order ID"] == key[0]]["Store Name"].empty else ""
                    })
        else:  # 基本键 (oid, amt, trans_id)
            if key not in df2_orders:
                # 先尝试通过Transaction ID匹配
                trans_id = key[2]
                found_by_trans_id = False
                
                if trans_id and str(trans_id).strip() and str(trans_id).lower() != "nan":
                    # 检查是否有相同Transaction ID的记录
                    for df2_key in list(df2_orders.keys()) + list(df2_orders_with_time.keys()):
                        df2_trans_id = df2_key[2] if len(df2_key) == 3 else df2_key[3]
                        if df2_trans_id == trans_id:
                            # 找到了相同Transaction ID的记录，不添加到missing_orders
                            found_by_trans_id = True
                            break
                
                if not found_by_trans_id:
                    missing_orders.append({
                        "oid": key[0],
                        "amt": key[1],
                        "datetime": info["datetime"],
                        "id_type": info["id_type"],
                        "transaction_id": trans_id,  # 保存Transaction ID
                        "store_name": df1_filtered[df1_filtered["Order ID"] == key[0]]["Store Name"].iloc[0] if not df1_filtered[df1_filtered["Order ID"] == key[0]]["Store Name"].empty else ""
                    })
    
    # 查找第二文件中存在但第一文件中不存在的订单
    extra_orders = []
    
    # 创建Transaction ID到df1订单的映射
    df1_trans_id_map = {}
    for df1_key in list(df1_orders.keys()):
        if len(df1_key) == 3:  # 基本键 (oid, amt, trans_id)
            trans_id = df1_key[2]
        else:  # 带时间的键 (oid, time_key, amt, trans_id)
            trans_id = df1_key[3]
        
        if trans_id and str(trans_id).strip() and str(trans_id).lower() != "nan":
            df1_trans_id_map[str(trans_id)] = df1_key
    
    # 检查基本键
    for key in df2_orders.keys():
        oid, amt, trans_id = key
        # 先通过Transaction ID检查是否在第一文件中存在
        found_by_trans_id = False
        
        if trans_id and str(trans_id).strip() and str(trans_id).lower() != "nan":
            if str(trans_id) in df1_trans_id_map:
                # 通过Transaction ID找到匹配，标记为已处理
                found_by_trans_id = True
                # 在df2_orders中标记为已处理，避免被自动修正删除
                df2_orders[key]["processed"] = True
                continue
        
        # 如果没有通过Transaction ID找到匹配，再检查订单ID和金额
        if not found_by_trans_id and key not in df1_orders:
            # 查找对应的行
            if df2_after["Equipment ID"].eq(oid).any():
                row = df2_after[df2_after["Equipment ID"] == oid]
            elif df2_after["Order No."].eq(oid).any():
                row = df2_after[df2_after["Order No."] == oid]
            else:
                continue
            
            # 如果有多行，尝试匹配金额
            if len(row) > 1:
                row = row[row["Order price"].round(2) == amt]
                # 如果有Transaction ID，进一步匹配
                if trans_id and "Transaction ID" in row.columns:
                    row_with_trans_id = row[row["Transaction ID"] == trans_id]
                    if not row_with_trans_id.empty:
                        row = row_with_trans_id
            
            if not row.empty:
                dt = row["Order time"].iloc[0]
                id_type = "9_digit" if len(str(oid)) == 9 else "over_9"
                row_index = row.index[0]
                # 在df2_orders中标记为已处理，避免被自动修正删除
                df2_orders[key]["processed"] = True
                # 保存更多行信息用于详细日志
                extra_orders.append({
                    "oid": oid,
                    "amt": amt,
                    "datetime": dt,
                    "id_type": id_type,
                    "transaction_id": trans_id,
                    "index": row_index,
                    "row": row.iloc[0],  # 保存整行数据以便获取更多信息
                    "processed": True  # 标记为已处理
                })
    
    # 查找第二文件中存在但第一文件中不存在的带时间戳的订单
    for key in df2_orders_with_time.keys():
        # 先通过Transaction ID检查是否在第一文件中存在
        trans_id = key[3] if len(key) > 3 else ""
        found_by_trans_id = False
        
        if trans_id and str(trans_id).strip() and str(trans_id).lower() != "nan":
            if str(trans_id) in df1_trans_id_map:
                # 通过Transaction ID找到匹配，标记为已处理
                found_by_trans_id = True
                # 在df2_orders_with_time中标记为已处理，避免被自动修正删除
                df2_orders_with_time[key]["processed"] = True
                continue
        
        # 如果没有通过Transaction ID找到匹配，再检查订单ID、时间和金额
        if not found_by_trans_id and key not in df1_orders:
            # 查找对应的行以获取更多信息
            for _, row in df2_after.iterrows():
                if ((row["Equipment ID"] == key[0] or row["Order No."] == key[0]) and 
                    abs(row["Order price"] - key[2]) < 0.01):  # 注意索引变化
                    # 如果有Transaction ID，进一步匹配
                    if trans_id and "Transaction ID" in row and row["Transaction ID"] != trans_id:
                        continue  # 如果Transaction ID不匹配，跳过此行
                    
                    # 在df2_orders_with_time中标记为已处理，避免被自动修正删除
                    df2_orders_with_time[key]["processed"] = True
                    # 保存更多行信息用于详细日志
                    df2_row_info = {
                        "oid": key[0],
                        "amt": key[2],  # 注意索引变化
                        "index": row.name,
                        "key_type": "time_based",
                        "time_key": key[1],
                        "transaction_id": trans_id,
                        "datetime": row["Order time"] if "Order time" in row else None,
                        "id_type": "9_digit" if len(str(key[0])) == 9 else 
                                  ("over_9" if len(str(key[0])) > 9 else "other"),
                        "row": row,  # 保存整行数据以便获取更多信息
                        "processed": True  # 标记为已处理
                    }
                    extra_orders.append(df2_row_info)
                    break
    
   
    
    # 计算每个金额类别的差异 - 使用初始处理后的频率数据
    amount_diff = {}
    for amt in set(list(freq_bill_amt.keys()) + list(original_freq.keys())):
        first_count = freq_bill_amt.get(amt, 0)
        second_count = original_freq.get(amt, 0)
        diff = second_count - first_count
        if diff != 0:
            amount_diff[amt] = diff
            
    # 记录使用的是初始处理后的数据进行比较
    correction_logs.append((0, "", f"使用初始处理后的数据进行比较: 第一文件 RM{total_bill_amt:.2f} vs 第二文件 RM{after_total_initial:.2f}"))
    
    # 记录修正前的金额类别差异
    correction_logs.append((0, "", f"修正前金额类别差异情况:"))
    for amt, diff in sorted(amount_diff.items(), key=lambda x: abs(x[1]), reverse=True):
        correction_logs.append((amt, "", f"金额 RM{amt:.2f}: 差异 {diff}个订单 (第一文件 {freq_bill_amt.get(amt, 0)}个 vs 第二文件 {original_freq.get(amt, 0)}个)"))
    
    # 处理缺失的订单（第一文件有但第二文件没有）- 优先处理差异较大的金额类别
    orders_to_add = []
    # 按差异绝对值从大到小排序金额类别
    sorted_amounts = sorted([(amt, diff) for amt, diff in amount_diff.items() if diff < 0], 
                           key=lambda x: abs(x[1]), reverse=True)
    
    for amt, diff in sorted_amounts:
        # 获取第一文件中此金额类别的订单数量
        first_file_count = freq_bill_amt.get(amt, 0)
        # 获取第二文件中此金额类别的当前订单数量
        second_file_count = original_freq.get(amt, 0)
        
        # 确保添加后的数量不会超过第一文件中的数量
        max_to_add = max(0, first_file_count - second_file_count)
        
        # 记录此金额类别的处理计划 - 简化版本
        correction_logs.append((0, "", f"计划添加 {max_to_add} 个订单"))
        
        # 找出此金额的缺失订单
        missing_of_amount = [order for order in missing_orders if order["amt"] == amt]
        
        # 尝试添加缺失订单，但不超过计算出的最大可添加数量
        added_from_missing = 0
        for i in range(min(max_to_add, len(missing_of_amount))):
            order = missing_of_amount[i]
            orders_to_add.append(order)
            amount_diff[amt] += 1
            current_diff -= amt
            added_from_missing += 1
            
            # 如果此金额类别的差异已解决，从差异字典中移除
            if amount_diff[amt] == 0:
                del amount_diff[amt]
        
        # 如果仍然需要添加更多订单但没有足够的missing_orders，尝试从第一文件复制订单
        remaining_to_add = max_to_add - added_from_missing
        if remaining_to_add > 0:
            correction_logs.append((0, "", f"尝试从第一文件复制 {remaining_to_add} 个订单"))
            
            # 从第一文件中找到此金额的订单
            df1_orders_for_amount = df1[(df1["Bill Amt"].round(2) == amt) & 
                                      (df1["Status"].str.strip().str.lower() == "settled")]
            # 检查df1是否有'Order types'列，如果有才进行API订单过滤
            if "Order types" in df1.columns:
                df1_orders_for_amount = exclude_api_orders(df1_orders_for_amount)
            
            if not df1_orders_for_amount.empty:
                # 按时间排序，优先使用较早的订单
                if "Order time" in df1_orders_for_amount.columns:
                    df1_orders_for_amount = df1_orders_for_amount.sort_values(by="Order time")
                
                # 最多复制remaining_to_add个订单
                for i in range(min(remaining_to_add, len(df1_orders_for_amount))):
                    row = df1_orders_for_amount.iloc[i]
                    
                    # 获取订单ID和时间
                    # 在df1中，使用Order ID作为主要标识符
                    oid = row["Order ID"] if "Order ID" in row else ""
                    dt = row["DateTime"] if "DateTime" in row else pd.Timestamp.now()
                    
                    # 确定ID类型
                    id_type = "9_digit" if len(str(oid)) == 9 else ("over_9" if len(str(oid)) > 9 else "anomaly")
                    
                    # 获取Transaction ID
                    trans_id = str(row.get("Transaction ID", "")).strip() if "Transaction ID" in row else ""
                    
                    # 创建新的订单信息
                    new_order = {
                        "oid": oid,
                        "amt": amt,
                        "datetime": dt,
                        "id_type": id_type,
                        "transaction_id": trans_id,
                        "copied_from_df1": True  # 标记为从df1复制
                    }
                    
                    orders_to_add.append(new_order)
                    amount_diff[amt] += 1
                    current_diff -= amt
                    
                    # 如果此金额类别的差异已解决，从差异字典中移除
                    if amount_diff[amt] == 0:
                        del amount_diff[amt]
                    
                    correction_logs.append((0, "", f"计划复制订单: {oid}"))
            else:
                correction_logs.append((0, "", f"警告: 无法找到匹配订单进行复制"))
    
    # 处理多余的订单（第二文件有但第一文件没有）- 优先处理差异较大的金额类别
    indices_to_remove = []
    # 按差异绝对值从大到小排序金额类别
    sorted_amounts = sorted([(amt, diff) for amt, diff in amount_diff.items() if diff > 0], 
                           key=lambda x: abs(x[1]), reverse=True)
    
    if extra_orders:
        for amt, diff in sorted_amounts:
            # 获取第一文件中此金额类别的订单数量
            first_file_count = freq_bill_amt.get(amt, 0)
            # 获取第二文件中此金额类别的当前订单数量
            second_file_count = original_freq.get(amt, 0)
            
            # 确保删除后的数量不会低于第一文件中的数量
            max_to_remove = max(0, second_file_count - first_file_count)
            
            # 找出此金额的多余订单
            extra_of_amount = [order for order in extra_orders 
                              if order["amt"] == amt and not order.get("processed", False)]
            
            # 尝试移除多余订单，但不超过计算出的最大可删除数量
            for i in range(min(max_to_remove, len(extra_of_amount))):
                order = extra_of_amount[i]
                indices_to_remove.append(order["index"])
                amount_diff[amt] -= 1
                current_diff += amt
                
                # 如果此金额类别的差异已解决，从差异字典中移除
                if amount_diff[amt] == 0:
                    del amount_diff[amt]
    
    # 执行修正操作
    modified = False
    added_orders = 0
    removed_orders = 0
    
    # 添加缺失的订单
    for order in orders_to_add:
        oid = order["oid"]
        amt = order["amt"]
        dt = order["datetime"]
        id_type = order["id_type"]
        t_val = dt.strftime("%H:%M:%S")
        
        # 根据ID类型设置Order types和搜索字段
        if id_type == "9_digit":
            search_field = "Equipment ID"
            default_eq = oid
            order_type = "Offline order"  # 9位ID对应Offline order
        elif id_type == "over_9":
            search_field = "Order No."
            default_eq = ""
            order_type = "Normal order"   # 超过9位ID对应Normal order
        else:  # anomaly
            search_field = "Equipment ID"
            default_eq = oid
            order_type = "Anomaly order"  # 异常ID使用特殊标记
        
        # 创建新行
        new_row = {
            search_field: oid,
            "Order price": amt,
            "Order status": "Finish",
            "Order time": dt,
            "Time": t_val,
            "Equipment ID": default_eq if search_field != "Equipment ID" else oid,
            "Matched Order ID": oid if search_field != "Equipment ID" else "",
            "Matched_Flag": True,
            "Order types": order_type,
            "Transaction ID": order.get("transaction_id", "")
        }
        
        # 获取详细信息
        eq_id = default_eq if search_field != "Equipment ID" else oid
        order_no = oid if search_field == "Order No." else ""
        
        # 创建简化的日志记录，包含ID、金额、时间和Transaction ID
        trans_id = order.get("transaction_id", "")
        trans_id_info = f", Transaction ID: {trans_id}" if trans_id else ""
        detail_info = f"自动修正: 添加缺失订单 {oid}, 金额 RM{amt:.2f}, 时间 {dt}{trans_id_info}"
        
        # 添加新行
        df2 = pd.concat([df2, pd.DataFrame([new_row])], ignore_index=True)
        correction_logs.append((amt, oid, detail_info))
        modified = True
        added_orders += 1
    
    # 删除多余的订单
    if indices_to_remove:
        for idx in indices_to_remove:
            row = df2.loc[idx]
            oid = row["Equipment ID"] if str(row["Equipment ID"]).strip() else row["Order No."]
            amt = row["Order price"]
            # 获取更多详细信息
            eq_id = str(row["Equipment ID"]).strip()
            order_no = str(row["Order No."]).strip()
            order_time = str(row["Order time"]).strip() if "Order time" in row else ""
            payment_date = str(row["Payment date"]).strip() if "Payment date" in row else ""
            equipment_name = str(row["Equipment name"]).strip() if "Equipment name" in row else ""
            branch_name = str(row["Branch name"]).strip() if "Branch name" in row else ""
            order_type = str(row["Order types"]).strip() if "Order types" in row else ""
            
            # 查找对应的extra_order信息
            extra_info = ""
            id_type_info = ""
            for order in extra_orders:
                if order["index"] == idx:
                    if order.get("key_type") == "time_based":
                        extra_info = f", 时间戳: {order['time_key']}"
                    if order.get("id_type"):
                        id_type_info = f", ID类型: {order['id_type']}"
                    break
            
            # 创建简化的日志记录，包含ID、金额、时间和Transaction ID
            trans_id = row.get("Transaction ID", "") if "Transaction ID" in row else ""
            trans_id_info = f", Transaction ID: {trans_id}" if trans_id else ""
            detail_info = f"自动修正: 删除多余订单 {oid}, 金额 RM{amt:.2f}, 时间 {order_time if order_time else '无时间信息'}{trans_id_info}"
            
            correction_logs.append((amt, oid, detail_info))
            removed_orders += 1
        
        # 删除指定索引的行
        df2 = df2.drop(indices_to_remove).reset_index(drop=True)
        modified = True
    
    # 如果进行了修改，重新计算修正后的总金额
    if modified:
        # 重新计算修正后的频率和总金额
        # 使用排除API order类型的函数
        df2_after = df2[(df2["Order status"].str.strip().str.lower() == "finish")].copy()
        df2_after = exclude_api_orders(df2_after)
        
        # 更新全局变量
        globals()["df2_after"] = df2_after
        globals()["after_total"] = df2_after["Order price"].sum()
        after_total = globals()["after_total"]
        
        # 计算修正后的每个金额类别的数量
        corrected_freq = df2_after["Order price"].round(2).value_counts().to_dict()
        
        # 记录修正是基于初始处理后的数据进行的，避免重复修正
        correction_logs.append((0, "", f"修正完成后: 第一文件 RM{total_bill_amt:.2f} vs 修正后第二文件 RM{after_total:.2f}"))
        correction_logs.append((0, "", f"修正是基于初始处理后的数据进行的，避免重复修正"))
        
        # 检查是否有金额类别超出第一文件数量的情况
        excess_categories = []
        for amt in corrected_freq.keys():
            first_count = freq_bill_amt.get(amt, 0)
            second_count = corrected_freq.get(amt, 0)
            if second_count > first_count:
                excess_categories.append((amt, first_count, second_count))
        
        # 如果有超出情况，进行额外的修正
        additional_removed = 0
        if excess_categories:
            correction_logs.append((0, "", f"检测到修正后仍有金额类别超出第一文件数量，进行额外修正:"))
            
            for amt, first_count, second_count in excess_categories:
                excess_count = second_count - first_count
                correction_logs.append((amt, "", f"金额 RM{amt:.2f}: 需要额外删除 {excess_count}个订单"))
                
                # 查找此金额的订单
                excess_rows = df2_after[df2_after["Order price"].round(2) == amt]
                
                if not excess_rows.empty:
                    # 按时间排序，优先删除最新的订单
                    if "Order time" in excess_rows.columns:
                        excess_rows = excess_rows.sort_values(by="Order time", ascending=False)
                    
                    # 获取要删除的行索引
                    indices_to_remove = excess_rows.index[:excess_count].tolist()
                    
                    # 记录删除的订单
                    for idx in indices_to_remove:
                        row = df2.loc[idx]
                        oid = row["Equipment ID"] if str(row["Equipment ID"]).strip() else row["Order No."]
                        order_time = str(row["Order time"]).strip() if "Order time" in row else ""
                        trans_id = str(row.get("Transaction ID", "")).strip() if "Transaction ID" in row else ""
                        trans_id_info = f", Transaction ID: {trans_id}" if trans_id and trans_id.lower() != "nan" else ""
                        
                        detail_info = f"额外修正: 删除超出订单 {oid}, 金额 RM{amt:.2f}, 时间 {order_time if order_time else '无时间信息'}{trans_id_info}"
                        correction_logs.append((amt, oid, detail_info))
                        additional_removed += 1
                    
                    # 删除指定索引的行
                    df2 = df2.drop(indices_to_remove).reset_index(drop=True)
                    
                    # 重新计算修正后的频率和总金额
                    df2_after = df2[(df2["Order status"].str.strip().str.lower() == "finish")].copy()
                    df2_after = exclude_api_orders(df2_after)
                    
                    # 更新全局变量
                    globals()["df2_after"] = df2_after
                    globals()["after_total"] = df2_after["Order price"].sum()
                    after_total = globals()["after_total"]
                    
                    # 更新修正后的频率
                    corrected_freq = df2_after["Order price"].round(2).value_counts().to_dict()
        
        # 添加修正结果到日志
        summary_info = f"自动修正完成: 第一文件 RM{total_bill_amt:.2f} vs 修正后第二文件 RM{after_total:.2f}, 剩余差异: RM{abs(total_bill_amt - after_total):.2f}\n"
        summary_info += f"  - 添加订单数量: {added_orders}\n"
        summary_info += f"  - 删除订单数量: {removed_orders + additional_removed}\n"
        summary_info += f"  - 总修正订单数量: {added_orders + removed_orders + additional_removed}"
        
        correction_logs.append((0, "", summary_info))
        
        # 添加每个金额类别修正后的详细信息
        correction_logs.append((0, "", f"修正后金额类别情况:"))
        for amt in sorted(set(list(freq_bill_amt.keys()) + list(corrected_freq.keys()))):
            first_count = freq_bill_amt.get(amt, 0)
            second_count = corrected_freq.get(amt, 0)
            diff = second_count - first_count
            if diff == 0:
                status = "匹配"
            elif diff > 0:
                status = f"超出 {diff}个"
            else:  # diff < 0
                status = f"缺少 {abs(diff)}个"
            correction_logs.append((amt, "", f"金额 RM{amt:.2f}: 第一文件 {first_count}个 vs 第二文件 {second_count}个 - {status}"))
        
        # 将修正日志添加到主日志
        note_logs.extend(correction_logs)
    
    return modified

# 在验证总金额后调用自动修正函数
# 在验证总金额后调用自动修正函数之前添加freq_compare_logs变量的定义
# 初始化验证日志列表
freq_compare_logs = []

# 添加总金额验证信息
if abs(total_bill_amt - after_total) < 0.01:
    freq_compare_logs.append(("", f"Verification passed: Final total matches first file total RM{total_bill_amt:.2f}"))
else:
    freq_compare_logs.append(("", f"WARNING: Final total RM{after_total:.2f} does not match first file total RM{total_bill_amt:.2f}, difference: RM{abs(after_total - total_bill_amt):.2f}"))

# 在验证总金额后调用自动修正函数
# 检查是否存在金额类别不匹配的情况
amount_category_mismatch = False
amount_category_details = []
for amt in set(list(freq_bill_amt.keys()) + list(original_freq.keys())):
    first_count = freq_bill_amt.get(amt, 0)
    second_count = original_freq.get(amt, 0)
    if first_count != second_count:
        amount_category_mismatch = True
        amount_category_details.append((amt, first_count, second_count))

# 记录金额类别不匹配的简化信息
if amount_category_mismatch:
    note_logs.append((0, "", f"检测到金额类别不匹配情况"))
    # 只记录总体差异信息，不记录每个金额类别的详细差异
    total_diff_count = sum(abs(first_count - second_count) for amt, first_count, second_count in amount_category_details)
    note_logs.append((0, "", f"总计 {total_diff_count} 个订单金额类别不匹配"))

# 如果总金额差异或金额类别不匹配，启动自动修正
if abs(total_bill_amt - after_total) >= 0.01 or amount_category_mismatch:
    print("检测到金额差异或金额类别不匹配，启动自动修正...")
    # 尝试多次修正，最多尝试3次
    max_attempts = 3
    attempt = 0
    success = False
    
    while attempt < max_attempts and not success:
        attempt += 1
        print(f"修正尝试 {attempt}/{max_attempts}...")
        
        if auto_correct_discrepancies():
            # 重新检查金额类别是否匹配
            amount_category_match = True
            # 重新计算修正后的频率
            df2_after_correction = df2[(df2["Order status"].str.strip().str.lower() == "finish")].copy()
            df2_after_correction = exclude_api_orders(df2_after_correction)
            corrected_freq = df2_after_correction["Order price"].round(2).value_counts().to_dict()
            
            # 检查每个金额类别是否匹配
            remaining_mismatches = []
            for amt in set(list(freq_bill_amt.keys()) + list(corrected_freq.keys())):
                first_count = freq_bill_amt.get(amt, 0)
                second_count = corrected_freq.get(amt, 0)
                if first_count != second_count:
                    amount_category_match = False
                    remaining_mismatches.append((amt, first_count, second_count))
            
            # 如果所有金额类别都匹配，或者已经尝试了最大次数，则退出循环
            if amount_category_match or attempt >= max_attempts:
                success = True
                break
        else:
            # 如果修正失败，退出循环
            break
    
    if success:
        print(f"自动修正完成，修正后总金额: RM{after_total:.2f}")
        # 在日志中添加修正记录
        note_logs.append((0, "", f"自动修正后总金额从 RM{total_bill_amt:.2f} 变更为 RM{after_total:.2f}"))
        
        # 记录剩余的金额类别不匹配情况 - 简化版本
        if not amount_category_match:
            total_remaining_diff = sum(abs(first_count - second_count) for amt, first_count, second_count in remaining_mismatches)
            note_logs.append((0, "", f"警告: 修正后仍有 {total_remaining_diff} 个订单金额类别不匹配"))
        
        # 更新验证通过信息
        for i, (id_val, log_val) in enumerate(freq_compare_logs):
            if log_val.startswith("WARNING: Final total"):
                if abs(total_bill_amt - after_total) < 0.01 and amount_category_match:
                    freq_compare_logs[i] = ("", f"Verification passed: Final total and all amount categories match first file (after auto-correction)")
                elif abs(total_bill_amt - after_total) < 0.01:
                    freq_compare_logs[i] = ("", f"Partial verification: Final total matches RM{total_bill_amt:.2f} but some amount categories still mismatch (after auto-correction)")
                else:
                    freq_compare_logs[i] = ("", f"WARNING: Final total RM{after_total:.2f} does not match first file total RM{total_bill_amt:.2f}, difference: RM{abs(after_total - total_bill_amt):.2f} (after auto-correction)")
                break
    else:
        print("无法自动修正金额差异，请手动检查数据")
        note_logs.append((0, "", f"警告: 无法自动修正所有金额类别不匹配，请手动检查数据"))


# -----------------------【字段补全策略】-----------------------
# 对于新插入的记录，补全其他字段
for idx, row in df2.iterrows():
    if row["Matched_Flag"] and (pd.isnull(row["Equipment name"]) or row["Equipment name"] == ""):
        # 策略1：从同一设备ID的其他记录中获取
        if "Equipment ID" in df2.columns and str(row["Equipment ID"]).strip() != "":
            same_eq = df2_backup[df2_backup["Equipment ID"] == row["Equipment ID"]]
            if not same_eq.empty:
                for col in ["Equipment name", "Branch name"]:
                    if col in same_eq.columns and col in df2.columns:
                        valid_values = same_eq[col].dropna().unique()
                        if len(valid_values) > 0 and str(valid_values[0]).strip() != "":
                            df2.at[idx, col] = valid_values[0]
        
        # 策略2：从同一订单号的其他记录中获取
        if "Order No." in df2.columns and str(row["Order No."]).strip() != "":
            same_order = df2_backup[df2_backup["Order No."] == row["Order No."]]
            if not same_order.empty:
                for col in ["Equipment name", "Branch name", "Equipment ID"]:
                    if col in same_order.columns and col in df2.columns:
                        if pd.isnull(row[col]) or str(row[col]).strip() == "":
                            valid_values = same_order[col].dropna().unique()
                            if len(valid_values) > 0 and str(valid_values[0]).strip() != "":
                                df2.at[idx, col] = valid_values[0]
        
        

# 方法2：利用映射字典（以 Equipment ID 为键）
df2_method2 = df2.copy()
df2_backup["Equipment ID"] = df2_backup["Equipment ID"].fillna("").astype(str).str.strip()
mapping_ename = df2_backup.drop_duplicates("Equipment ID").set_index("Equipment ID")["Equipment name"]
mapping_bname = df2_backup.drop_duplicates("Equipment ID").set_index("Equipment ID")["Branch name"]
df2_method2["Equipment name"] = df2_method2["Equipment ID"].fillna("").astype(str).str.strip().map(
    mapping_ename).fillna(df2_method2["Equipment name"])
df2_method2["Branch name"] = df2_method2["Equipment ID"].fillna("").astype(str).str.strip().map(
    mapping_bname).fillna(df2_method2["Branch name"])

# -----------------------【初始化频率比较日志】-----------------------
# 使用原始第二文件数据进行对比，排除API order类型
df2_original_finish = df2_original[df2_original["Order status"].str.strip().str.lower() == "finish"]
df2_original_finish = exclude_api_orders(df2_original_finish)
original_total = df2_original_finish["Order price"].sum()
original_freq = df2_original_finish["Order price"].round(2).value_counts().to_dict() 

# 生成频率比较日志 
freq_compare_logs = [] 
freq_compare_logs.append(("", f"{df1_filtered['DateTime'].min().strftime('%Y-%m-%d %H:%M:%S')}")) 
freq_compare_logs.append(("", f"RAZER : RM{total_bill_amt:.2f}")) 
freq_compare_logs.append(("", f"CHINA : RM{original_total:.2f}")) 

# 添加空行 
freq_compare_logs.append(("", "")) 

# 添加验证信息 
freq_compare_logs.append(("", f"First file total (settled): RM{total_bill_amt:.2f}")) 

# 添加验证通过信息 
if abs(total_bill_amt - after_total) < 0.01:
    freq_compare_logs.append(("", f"Verification passed: Final total matches first file total RM{total_bill_amt:.2f}"))
else:
    freq_compare_logs.append(("", f"WARNING: Final total RM{after_total:.2f} does not match first file total RM{total_bill_amt:.2f}, difference: RM{abs(after_total - total_bill_amt):.2f}"))

# 添加空行 
freq_compare_logs.append(("", "")) 

# 合并所有金额（注意：original_freq已排除API order类型）
all_amounts = sorted(set(list(freq_bill_amt.keys()) + list(original_freq.keys()))) 

# 先生成所有金额的汇总信息
for amt in all_amounts: 
    first_count = freq_bill_amt.get(amt, 0) 
    second_count = original_freq.get(amt, 0) 
    diff = second_count - first_count 
    
    # 移除过滤条件，显示所有金额的汇总信息
    if diff == 0: 
        msg = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count}" 
    elif diff > 0: 
        msg = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (MORE: {diff})" 
    else:  # diff < 0 
        msg = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (LESS: {abs(diff)})" 
    
    freq_compare_logs.append(("", msg))

# 添加空行
freq_compare_logs.append(("", ""))

# 然后再显示每个金额下的详细修改日志，并在每个金额前添加标题
for amt in all_amounts: 
    first_count = freq_bill_amt.get(amt, 0) 
    second_count = original_freq.get(amt, 0) 
    diff = second_count - first_count 
    
    # 移除条件判断，显示所有金额的详细日志
    if True:  # 显示所有金额
        # 查找与该金额相关的日志
        related_logs = [log for log in note_logs if abs(log[0] - amt) < 0.01]
        
        # 只有当有相关日志时才添加该金额的标题和日志
        if related_logs:
            # 添加金额标题
            if diff == 0: 
                title = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count}" 
            elif diff > 0: 
                title = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (MORE: {diff})" 
            else:  # diff < 0 
                title = f"RM{amt:.2f} x {first_count} (First file) | Second file: RM{amt:.2f} x {second_count} (LESS: {abs(diff)})" 
            
            freq_compare_logs.append(("", title))
            
            # 添加该金额下的所有相关日志
            for log in related_logs:
                freq_compare_logs.append((log[1], log[2]))
            
            # 在每个金额的日志组后添加空行，除非是最后一个金额
            if amt != all_amounts[-1]:
                freq_compare_logs.append(("", ""))

# 创建日志DataFrame，ID列在左边
log_df = pd.DataFrame(freq_compare_logs, columns=["ID", "Log"])

# -----------------------【输出结果】-----------------------
# 使用ExcelWriter写入多个sheet
with pd.ExcelWriter(output_file_path, engine="openpyxl", mode="a", if_sheet_exists="replace") as writer:
    # 写入数据sheet
    df2.to_excel(writer, sheet_name=output_data_sheet, index=False)
    # 写入日志sheet
    log_df.to_excel(writer, sheet_name=output_log_sheet, index=False)

print(f"处理完成！结果已写入 {output_file_path}")
print(f"- 数据已写入 {output_data_sheet} sheet")
print(f"- 日志已写入 {output_log_sheet} sheet")
print(f"第二文件最终总金额: RM{after_total:.2f}")