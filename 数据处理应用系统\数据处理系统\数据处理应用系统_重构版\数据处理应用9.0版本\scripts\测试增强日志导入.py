#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强日志导入 - 使用增强的错误日志测试导入过程
"""

import os
import sys
import subprocess
from pathlib import Path

def test_import_with_enhanced_logging():
    """使用增强的错误日志测试导入过程"""
    print("🔧 测试增强日志导入")
    print("=" * 80)
    
    # 设置非交互模式
    os.environ['NON_INTERACTIVE'] = '1'
    os.environ['AUTO_DUPLICATE_HANDLING'] = 'overwrite'
    os.environ['AUTO_MISSING_HANDLING'] = 'ignore'
    
    # 测试文件路径
    test_file = "050725 CHINA IOT.xlsx"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        print("💡 请将文件放在scripts目录下")
        return False
    
    print(f"📁 测试文件: {test_file}")
    
    # 构建导入命令
    script_path = "data_import_optimized.py"
    db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
    
    cmd = [
        sys.executable,
        script_path,
        test_file,
        "IOT",
        db_path
    ]
    
    print(f"🔄 执行命令: {' '.join(cmd)}")
    print("-" * 80)
    
    try:
        # 执行导入命令并捕获输出
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        print("📋 导入输出:")
        print(result.stdout)
        
        if result.stderr:
            print("\n❌ 错误输出:")
            print(result.stderr)
        
        print(f"\n📊 返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ 导入成功")
            return True
        else:
            print("❌ 导入失败")
            
            # 分析错误输出
            if "Series is ambiguous" in result.stderr:
                print("🔍 发现Series错误，分析错误位置...")
                analyze_series_error(result.stderr)
            
            return False
    
    except subprocess.TimeoutExpired:
        print("❌ 导入超时（5分钟）")
        return False
    except Exception as e:
        print(f"❌ 执行导入命令失败: {e}")
        return False

def analyze_series_error(error_output):
    """分析Series错误输出"""
    print("\n🔍 Series错误分析:")
    print("-" * 40)
    
    lines = error_output.split('\n')
    
    # 查找错误相关的行
    error_context = []
    found_error = False
    
    for i, line in enumerate(lines):
        if "Series is ambiguous" in line:
            found_error = True
            # 获取错误前后的上下文
            start = max(0, i - 5)
            end = min(len(lines), i + 5)
            error_context = lines[start:end]
            break
    
    if found_error:
        print("📍 错误上下文:")
        for line in error_context:
            print(f"  {line}")
    
    # 查找可能的错误位置
    potential_locations = [
        "smart_incremental_duplicate_check",
        "_enhanced_duplicate_detection",
        "_check_duplicates_by_table",
        "apply",
        "isna().all()",
        "notna().any()"
    ]
    
    print("\n🎯 可能的错误位置:")
    for location in potential_locations:
        if location in error_output:
            print(f"  ✓ 发现: {location}")

def main():
    """主函数"""
    print("🔧 增强日志导入测试")
    print("=" * 80)
    
    success = test_import_with_enhanced_logging()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果")
    print("=" * 80)
    
    if success:
        print("🎉 导入测试成功")
        print("✅ 增强的错误日志工作正常")
        print("✅ Series错误已修复")
    else:
        print("❌ 导入测试失败")
        print("🔧 需要进一步分析错误日志")
        print("💡 检查上述错误分析，定位具体问题")
    
    return success

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 测试{'成功' if success else '失败'}")
    input("按回车键退出...")
