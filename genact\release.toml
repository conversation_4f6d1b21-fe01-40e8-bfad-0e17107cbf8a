sign-commit = true
sign-tag = true
pre-release-replacements = [
  {file="index.html", search="genact [0-9][a-z0-9\\.-]+", replace="genact {{version}}"},
  {file="CHANGELOG.md", search="Unreleased", replace="{{version}}"},
  {file="CHANGELOG.md", search="\\.\\.\\.HEAD", replace="...{{tag_name}}", exactly=1},
  {file="CHANGELOG.md", search="ReleaseDate", replace="{{date}}"},
  {file="CHANGELOG.md", search="<!-- next-header -->", replace="<!-- next-header -->\n\n## [Unreleased] - ReleaseDate"},
  {file="CHANGELOG.md", search="<!-- next-url -->", replace="<!-- next-url -->\n[Unreleased]: https://github.com/svenstaro/genact/compare/{{tag_name}}...HEAD", exactly=1},
]
