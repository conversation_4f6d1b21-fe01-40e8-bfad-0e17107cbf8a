#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单数据库连接测试脚本
避免导入GUI模块，专注测试数据库连接
"""

import os
import sqlite3
import configparser
from datetime import datetime

def test_config_files():
    """测试配置文件中的数据库路径"""
    print("🔍 测试配置文件数据库路径...")
    
    config_files = [
        "config.ini",
        "数据处理应用系统/config.ini"
    ]
    
    results = {}
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                config = configparser.ConfigParser()
                config.read(config_file, encoding='utf-8')
                
                if config.has_section('Database'):
                    db_path = config.get('Database', 'db_path', fallback='')
                    if db_path:
                        print(f"  ✅ {config_file}: {db_path}")
                        results[config_file] = db_path
                    else:
                        print(f"  ❌ {config_file}: 数据库路径为空")
                        results[config_file] = None
                else:
                    print(f"  ❌ {config_file}: 缺少Database配置段")
                    results[config_file] = None
                    
            except Exception as e:
                print(f"  ❌ {config_file}: 读取失败 - {e}")
                results[config_file] = None
        else:
            print(f"  ⚠️ {config_file}: 文件不存在")
            results[config_file] = None
    
    return results

def test_database_connection(db_path):
    """测试数据库连接"""
    print(f"\n🔗 测试数据库连接: {db_path}")
    
    if not db_path:
        print("  ❌ 数据库路径为空")
        return False
    
    if not os.path.exists(db_path):
        print(f"  ❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        with sqlite3.connect(db_path, timeout=10) as conn:
            cursor = conn.cursor()
            
            # 测试基本查询
            cursor.execute("SELECT sqlite_version()")
            version = cursor.fetchone()[0]
            print(f"  ✅ SQLite版本: {version}")
            
            # 获取表列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"  ✅ 数据库表数量: {len(tables)}")
            
            if tables:
                print("  📊 主要数据库表:")
                for table in tables[:5]:  # 显示前5个表
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                        count = cursor.fetchone()[0]
                        print(f"    - {table[0]}: {count} 条记录")
                    except Exception as e:
                        print(f"    - {table[0]}: 查询失败 ({e})")
                
                if len(tables) > 5:
                    print(f"    ... 还有 {len(tables) - 5} 个表")
            
            # 测试写入权限
            try:
                cursor.execute("CREATE TEMP TABLE test_write (id INTEGER)")
                cursor.execute("INSERT INTO test_write (id) VALUES (1)")
                cursor.execute("SELECT COUNT(*) FROM test_write")
                count = cursor.fetchone()[0]
                if count == 1:
                    print(f"  ✅ 数据库写入权限正常")
                else:
                    print(f"  ⚠️ 数据库写入测试异常")
            except Exception as e:
                print(f"  ⚠️ 数据库写入权限测试失败: {e}")
            
            return True
            
    except Exception as e:
        print(f"  ❌ 连接失败: {e}")
        return False

def test_path_consistency():
    """测试路径一致性"""
    print(f"\n🔄 测试路径一致性...")
    
    config_results = test_config_files()
    
    # 获取所有非空的数据库路径
    db_paths = [path for path in config_results.values() if path]
    
    if not db_paths:
        print("  ❌ 没有找到任何数据库路径")
        return False
    
    # 检查路径是否一致
    unique_paths = set(db_paths)
    
    if len(unique_paths) == 1:
        print(f"  ✅ 所有配置文件使用相同的数据库路径")
        return list(unique_paths)[0]
    else:
        print(f"  ⚠️ 发现不一致的数据库路径:")
        for path in unique_paths:
            print(f"    - {path}")
        return list(unique_paths)[0]  # 返回第一个路径进行测试

def test_script_db_path_functions():
    """测试脚本中的数据库路径获取函数"""
    print(f"\n📜 测试脚本数据库路径函数...")
    
    # 测试数据导入脚本的DEFAULT_DB_PATH
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        default_db_path = os.path.join(script_dir, "database", "sales_reports.db")
        print(f"  📍 数据导入脚本默认路径: {default_db_path}")
        
        if os.path.exists(default_db_path):
            print(f"    ✅ 默认路径文件存在")
        else:
            print(f"    ⚠️ 默认路径文件不存在")
    except Exception as e:
        print(f"    ❌ 获取默认路径失败: {e}")
    
    # 测试退款脚本的路径获取函数
    try:
        # 模拟退款脚本的get_db_path函数
        config_file = "config.ini"
        if os.path.exists(config_file):
            config = configparser.ConfigParser()
            config.read(config_file, encoding='utf-8')
            if config.has_section('Database'):
                refund_db_path = config.get('Database', 'db_path', fallback=None)
                if refund_db_path:
                    print(f"  📍 退款脚本配置路径: {refund_db_path}")
                    if os.path.exists(refund_db_path):
                        print(f"    ✅ 退款脚本路径文件存在")
                    else:
                        print(f"    ⚠️ 退款脚本路径文件不存在")
                else:
                    print(f"    ❌ 退款脚本无法从配置获取路径")
            else:
                print(f"    ❌ 退款脚本配置文件缺少Database段")
        else:
            print(f"    ❌ 退款脚本找不到配置文件")
    except Exception as e:
        print(f"    ❌ 退款脚本路径测试失败: {e}")

def main():
    """主函数"""
    print("🚀 简单数据库连接测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # 1. 测试路径一致性
    main_db_path = test_path_consistency()
    
    # 2. 测试数据库连接
    if main_db_path:
        db_connection_ok = test_database_connection(main_db_path)
    else:
        print("❌ 无法获取数据库路径，跳过连接测试")
        db_connection_ok = False
    
    # 3. 测试脚本路径函数
    test_script_db_path_functions()
    
    # 4. 生成简单报告
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    if main_db_path:
        print(f"✅ 数据库路径配置: {main_db_path}")
    else:
        print(f"❌ 数据库路径配置: 未找到有效路径")
    
    if db_connection_ok:
        print(f"✅ 数据库连接: 正常")
    else:
        print(f"❌ 数据库连接: 失败")
    
    # 检查数据库文件大小
    if main_db_path and os.path.exists(main_db_path):
        file_size = os.path.getsize(main_db_path)
        file_size_mb = file_size / (1024 * 1024)
        print(f"📊 数据库文件大小: {file_size_mb:.2f} MB")
        
        # 检查数据库目录
        db_dir = os.path.dirname(main_db_path)
        if os.path.exists(db_dir):
            print(f"✅ 数据库目录: {db_dir}")
            
            # 检查备份目录
            backup_dir = os.path.join(db_dir, "backups")
            if os.path.exists(backup_dir):
                backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.db')]
                print(f"📦 备份文件数量: {len(backup_files)}")
            else:
                print(f"⚠️ 备份目录不存在: {backup_dir}")
        else:
            print(f"❌ 数据库目录不存在: {db_dir}")
    
    # 总体评估
    if main_db_path and db_connection_ok:
        print(f"\n🎉 总体评估: 数据库连接配置正确！")
        print(f"✅ 所有应用和脚本应该能够正常连接数据库")
        return True
    else:
        print(f"\n⚠️ 总体评估: 数据库连接存在问题")
        print(f"🔧 需要检查配置文件和数据库文件")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
