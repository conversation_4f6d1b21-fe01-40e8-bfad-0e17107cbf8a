#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全版数据导入脚本
修复所有可能导致数据库损坏的问题
"""

import sys
import os
import sqlite3
import threading
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import pandas as pd

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.connection_pool import get_connection
from database.database_decorators import safe_db_operation, transaction_wrapper

class SafeDataImportProcessor:
    """安全的数据导入处理器"""
    
    def __init__(self, db_path: Optional[str] = None):
        self.db_path = db_path
        self.batch_size = 1000
        self.logger = self._setup_logger()
        
        # 🔧 修复：DDL操作锁，防止并发ALTER TABLE
        self._ddl_lock = threading.Lock()
        
        # 🔧 修复：删除操作协调器，防止冲突
        self._deletion_coordinator = DeletionCoordinator()
    
    def _setup_logger(self):
        """设置日志记录器"""
        # 🔧 Bug修复：logging已在文件顶部导入，无需重复导入
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @safe_db_operation(timeout=60.0, retry_count=3)
    def safe_mark_records_as_deleted(self, missing_df: pd.DataFrame) -> int:
        """🔧 安全标记记录为已删除状态"""
        deleted_count = 0
        
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                
                # 按表分组处理
                for table_name in missing_df['source_table'].unique():
                    # 🔧 修复：安全添加删除标记字段
                    self._safe_add_deleted_column(cursor, table_name)
                    
                    table_records = missing_df[missing_df['source_table'] == table_name]
                    
                    # 🔧 修复：批量处理，避免长事务
                    for batch_start in range(0, len(table_records), self.batch_size):
                        batch_end = min(batch_start + self.batch_size, len(table_records))
                        batch_records = table_records.iloc[batch_start:batch_end]
                        
                        batch_deleted = self._mark_batch_as_deleted(cursor, table_name, batch_records)
                        deleted_count += batch_deleted
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"标记删除记录失败: {e}")
            raise
        
        return deleted_count
    
    def _safe_add_deleted_column(self, cursor, table_name: str):
        """🔧 安全添加删除标记字段"""
        with self._ddl_lock:  # 防止并发DDL操作
            try:
                # 🔧 修复：使用参数化查询检查字段
                cursor.execute("""
                    SELECT name FROM pragma_table_info(?) 
                    WHERE name='is_deleted'
                """, (table_name,))
                
                if not cursor.fetchone():
                    # 🔧 Bug修复：使用更安全的表名处理，避免SQL注入风险
                    # 验证表名是否安全（只包含字母、数字、下划线）
                    import re
                    if not re.match(r'^[a-zA-Z0-9_]+$', table_name):
                        raise ValueError(f"不安全的表名: {table_name}")

                    # 使用参数化查询更安全
                    safe_table_name = f'"{table_name}"'

                    cursor.execute(f"""
                        ALTER TABLE {safe_table_name}
                        ADD COLUMN is_deleted INTEGER DEFAULT 0
                    """)

                    # 添加deleted_at字段
                    cursor.execute(f"""
                        ALTER TABLE {safe_table_name}
                        ADD COLUMN deleted_at TEXT
                    """)

                    # 添加delete_reason字段
                    cursor.execute(f"""
                        ALTER TABLE {safe_table_name}
                        ADD COLUMN delete_reason TEXT
                    """)
                    
                    self.logger.info(f"为表 {table_name} 添加了删除标记字段")
                    
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    # 字段已存在，忽略错误
                    self.logger.debug(f"表 {table_name} 的删除字段已存在")
                else:
                    raise
    
    def _mark_batch_as_deleted(self, cursor, table_name: str, batch_records: pd.DataFrame) -> int:
        """批量标记记录为已删除"""
        deleted_count = 0
        
        for _, record in batch_records.iterrows():
            try:
                # 🔧 修复：使用参数化查询，防止SQL注入
                cursor.execute(f"""
                    UPDATE "{table_name}"
                    SET is_deleted = 1,
                        deleted_at = datetime('now'),
                        delete_reason = ?
                    WHERE Transaction_Num = ? AND Order_time = ?
                """, ('文件中缺失', record['Transaction_Num'], record['Order_time']))
                
                if cursor.rowcount > 0:
                    deleted_count += 1
                    
            except Exception as e:
                self.logger.warning(f"标记记录删除失败: {record['Transaction_Num']}, 错误: {e}")
                continue
        
        return deleted_count
    
    @safe_db_operation(timeout=120.0, retry_count=2)
    def safe_delete_data_by_month(self, target_year: str, target_month: str, 
                                 platform: str, tables_to_clear: list = None) -> Dict[str, int]:
        """🔧 安全的月度数据删除"""
        
        # 🔧 修复：请求删除操作许可
        operation_id = self._deletion_coordinator.request_deletion(
            f"{platform}_monthly", f"{target_year}-{target_month}"
        )
        
        try:
            if not target_year or not target_month:
                raise ValueError("年份和月份不能为空")
            
            # 验证输入
            year_int = int(target_year)
            month_int = int(target_month)
            if not (2020 <= year_int <= 2030) or not (1 <= month_int <= 12):
                raise ValueError(f"无效的日期: {target_year}-{target_month}")
            
            formatted_year = f"{year_int:04d}"
            formatted_month = f"{month_int:02d}"
            
            delete_results = {}
            if tables_to_clear is None:
                tables_to_clear = self._get_tables_to_clear(platform)
            
            with get_connection() as conn:
                cursor = conn.cursor()
                
                for table_name in tables_to_clear:
                    # 🔧 修复：安全检查表存在性
                    if not self._table_exists(cursor, table_name):
                        delete_results[table_name] = 0
                        continue
                    
                    # 🔧 修复：批量删除，避免长事务
                    deleted_count = self._batch_delete_monthly_data(
                        cursor, table_name, formatted_year, formatted_month
                    )
                    delete_results[table_name] = deleted_count
                
                conn.commit()
                
                total_deleted = sum(delete_results.values())
                self.logger.info(f"总计删除了 {total_deleted} 条 {formatted_year}-{formatted_month} 的数据")
            
            return delete_results
            
        finally:
            # 🔧 修复：释放删除操作许可
            self._deletion_coordinator.release_deletion(operation_id)
    
    def _table_exists(self, cursor, table_name: str) -> bool:
        """安全检查表是否存在"""
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name=?
        """, (table_name,))
        return cursor.fetchone() is not None
    
    def _batch_delete_monthly_data(self, cursor, table_name: str, 
                                  year: str, month: str) -> int:
        """批量删除月度数据"""
        # 🔧 修复：使用索引友好的范围查询
        start_date = f"{year}-{month}-01"
        if month == "12":
            end_date = f"{int(year)+1}-01-01"
        else:
            end_date = f"{year}-{int(month)+1:02d}-01"
        
        # 先统计要删除的记录数
        cursor.execute(f"""
            SELECT COUNT(*) FROM "{table_name}"
            WHERE Order_time >= ? AND Order_time < ?
        """, (start_date, end_date))
        
        total_count = cursor.fetchone()[0]
        
        if total_count == 0:
            return 0
        
        # 🔧 修复：分批删除，避免长时间锁定
        deleted_count = 0
        batch_size = 1000
        
        while True:
            cursor.execute(f"""
                DELETE FROM "{table_name}"
                WHERE rowid IN (
                    SELECT rowid FROM "{table_name}"
                    WHERE Order_time >= ? AND Order_time < ?
                    LIMIT ?
                )
            """, (start_date, end_date, batch_size))
            
            batch_deleted = cursor.rowcount
            deleted_count += batch_deleted
            
            if batch_deleted < batch_size:
                break  # 没有更多记录需要删除
            
            # 短暂休息，释放锁
            import time
            time.sleep(0.01)
        
        self.logger.info(f"从 {table_name} 删除了 {deleted_count} 条 {year}-{month} 的数据")
        return deleted_count
    
    def _get_tables_to_clear(self, platform: str) -> List[str]:
        """获取要清理的表列表"""
        if platform == 'IOT':
            return ['IOT_Sales', 'IOT_Sales_Close', 'IOT_Sales_Refunding']
        elif platform == 'ZERO':
            return ['ZERO_Sales', 'ZERO_Sales_Close', 'ZERO_Sales_Refunding']
        elif platform == 'APP':
            return ['APP_Sales']
        else:
            raise ValueError(f"不支持的平台: {platform}")

class DeletionCoordinator:
    """删除操作协调器，防止冲突操作"""
    
    def __init__(self):
        self.active_operations = {}
        self.lock = threading.Lock()
        self.operation_counter = 0
    
    def request_deletion(self, operation_type: str, target: str) -> str:
        """请求删除操作许可"""
        with self.lock:
            # 检查是否有冲突操作
            for op_id, op_info in self.active_operations.items():
                if self._operations_conflict(operation_type, target, op_info):
                    raise RuntimeError(f"删除操作冲突: {operation_type} vs {op_info['type']}")
            
            # 注册新操作
            self.operation_counter += 1
            operation_id = f"del_{self.operation_counter}_{int(datetime.now().timestamp())}"
            
            self.active_operations[operation_id] = {
                'type': operation_type,
                'target': target,
                'start_time': datetime.now()
            }
            
            return operation_id
    
    def release_deletion(self, operation_id: str):
        """释放删除操作许可"""
        with self.lock:
            self.active_operations.pop(operation_id, None)
    
    def _operations_conflict(self, new_type: str, new_target: str, existing_op: Dict) -> bool:
        """检查操作是否冲突"""
        # 同一目标的操作冲突
        if new_target == existing_op['target']:
            return True
        
        # 同一平台的不同类型操作可能冲突
        new_platform = new_type.split('_')[0] if '_' in new_type else new_type
        existing_platform = existing_op['type'].split('_')[0] if '_' in existing_op['type'] else existing_op['type']
        
        if new_platform == existing_platform:
            return True
        
        return False

# 使用示例
if __name__ == "__main__":
    processor = SafeDataImportProcessor()
    print("安全数据导入处理器已初始化")
