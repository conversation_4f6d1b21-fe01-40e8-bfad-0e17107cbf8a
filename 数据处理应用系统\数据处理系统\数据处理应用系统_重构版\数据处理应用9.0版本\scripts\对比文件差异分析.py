#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比文件差异分析 - 分析为什么两个IOT文件会有不同的导入结果
"""

import pandas as pd
import os
from pathlib import Path

def analyze_file_differences():
    """分析两个文件的差异"""
    print("🔍 分析文件差异：为什么050725和060725文件结果不同")
    print("=" * 80)
    
    # 文件路径（需要用户提供实际路径）
    file1_path = "050725 CHINA IOT.xlsx"  # 失败的文件
    file2_path = "060725 CHINA IOT.xlsx"  # 成功的文件
    
    try:
        # 检查文件是否存在
        if not os.path.exists(file1_path):
            print(f"❌ 文件不存在: {file1_path}")
            print("💡 请将文件放在脚本同目录下，或提供正确路径")
            return False
        
        if not os.path.exists(file2_path):
            print(f"❌ 文件不存在: {file2_path}")
            print("💡 请将文件放在脚本同目录下，或提供正确路径")
            return False
        
        print(f"📁 分析文件1: {file1_path}")
        print(f"📁 分析文件2: {file2_path}")
        
        # 读取两个文件
        print("\n🔍 读取文件基本信息...")
        
        # 获取文件大小
        size1 = os.path.getsize(file1_path)
        size2 = os.path.getsize(file2_path)
        print(f"📊 文件大小对比:")
        print(f"  {file1_path}: {size1:,} 字节")
        print(f"  {file2_path}: {size2:,} 字节")
        print(f"  差异: {abs(size1-size2):,} 字节")
        
        # 读取Excel文件
        try:
            # 检查工作表
            xl1 = pd.ExcelFile(file1_path)
            xl2 = pd.ExcelFile(file2_path)
            
            print(f"\n📋 工作表对比:")
            print(f"  {file1_path}: {xl1.sheet_names}")
            print(f"  {file2_path}: {xl2.sheet_names}")
            
            # 找到IOT工作表
            iot_sheet1 = None
            iot_sheet2 = None
            
            for sheet in xl1.sheet_names:
                if 'IOT' in sheet.upper():
                    iot_sheet1 = sheet
                    break
            
            for sheet in xl2.sheet_names:
                if 'IOT' in sheet.upper():
                    iot_sheet2 = sheet
                    break
            
            if not iot_sheet1:
                print(f"❌ {file1_path} 中没有找到IOT工作表")
                return False
            
            if not iot_sheet2:
                print(f"❌ {file2_path} 中没有找到IOT工作表")
                return False
            
            print(f"\n📊 使用工作表:")
            print(f"  {file1_path}: '{iot_sheet1}'")
            print(f"  {file2_path}: '{iot_sheet2}'")
            
            # 读取数据
            df1 = pd.read_excel(file1_path, sheet_name=iot_sheet1)
            df2 = pd.read_excel(file2_path, sheet_name=iot_sheet2)
            
            print(f"\n📊 数据量对比:")
            print(f"  {file1_path}: {len(df1):,} 行 x {len(df1.columns)} 列")
            print(f"  {file2_path}: {len(df2):,} 行 x {len(df2.columns)} 列")
            
            # 对比列名
            print(f"\n📋 列名对比:")
            cols1 = set(df1.columns)
            cols2 = set(df2.columns)
            
            common_cols = cols1 & cols2
            only_in_1 = cols1 - cols2
            only_in_2 = cols2 - cols1
            
            print(f"  共同列: {len(common_cols)} 个")
            print(f"  仅在{file1_path}: {len(only_in_1)} 个")
            print(f"  仅在{file2_path}: {len(only_in_2)} 个")
            
            if only_in_1:
                print(f"  仅在{file1_path}的列: {list(only_in_1)}")
            if only_in_2:
                print(f"  仅在{file2_path}的列: {list(only_in_2)}")
            
            # 检查关键列的数据类型和内容
            key_columns = ['Order time', 'Transaction Num', 'Order types', 'Order_status']
            
            print(f"\n🔍 关键列分析:")
            for col in key_columns:
                if col in df1.columns and col in df2.columns:
                    print(f"\n  📊 列: {col}")
                    
                    # 数据类型
                    print(f"    数据类型: {df1[col].dtype} vs {df2[col].dtype}")
                    
                    # 空值统计
                    null1 = df1[col].isnull().sum()
                    null2 = df2[col].isnull().sum()
                    print(f"    空值数量: {null1} vs {null2}")
                    
                    # 唯一值数量
                    unique1 = df1[col].nunique()
                    unique2 = df2[col].nunique()
                    print(f"    唯一值数量: {unique1} vs {unique2}")
                    
                    # 样本值
                    sample1 = df1[col].dropna().head(3).tolist()
                    sample2 = df2[col].dropna().head(3).tolist()
                    print(f"    样本值1: {sample1}")
                    print(f"    样本值2: {sample2}")
                    
                elif col in df1.columns:
                    print(f"  ⚠️ 列 '{col}' 仅存在于 {file1_path}")
                elif col in df2.columns:
                    print(f"  ⚠️ 列 '{col}' 仅存在于 {file2_path}")
                else:
                    print(f"  ❌ 列 '{col}' 在两个文件中都不存在")
            
            # 检查Order time格式差异
            if 'Order time' in df1.columns and 'Order time' in df2.columns:
                print(f"\n🕒 Order time格式详细分析:")
                
                # 检查时间格式模式
                time_samples1 = df1['Order time'].dropna().astype(str).head(10).tolist()
                time_samples2 = df2['Order time'].dropna().astype(str).head(10).tolist()
                
                print(f"  {file1_path} 时间样本:")
                for i, time_val in enumerate(time_samples1, 1):
                    print(f"    {i:2d}. '{time_val}' (长度: {len(time_val)})")
                
                print(f"  {file2_path} 时间样本:")
                for i, time_val in enumerate(time_samples2, 1):
                    print(f"    {i:2d}. '{time_val}' (长度: {len(time_val)})")
                
                # 检查时间格式模式
                def analyze_time_patterns(series, filename):
                    patterns = {}
                    for time_val in series.dropna().astype(str).head(100):
                        if len(time_val) == 10 and time_val.count('-') == 2:
                            patterns['date_only'] = patterns.get('date_only', 0) + 1
                        elif len(time_val) > 10 and ':' in time_val:
                            patterns['datetime'] = patterns.get('datetime', 0) + 1
                        else:
                            patterns['other'] = patterns.get('other', 0) + 1
                    return patterns
                
                patterns1 = analyze_time_patterns(df1['Order time'], file1_path)
                patterns2 = analyze_time_patterns(df2['Order time'], file2_path)
                
                print(f"  时间格式模式分析:")
                print(f"    {file1_path}: {patterns1}")
                print(f"    {file2_path}: {patterns2}")
            
            # 检查数据完整性
            print(f"\n🔍 数据完整性分析:")
            
            # 检查完全空行
            empty_rows1 = df1.isnull().all(axis=1).sum()
            empty_rows2 = df2.isnull().all(axis=1).sum()
            print(f"  完全空行: {empty_rows1} vs {empty_rows2}")
            
            # 检查关键字段的空值率
            key_fields = ['Order time', 'Transaction Num', 'Equipment ID']
            for field in key_fields:
                if field in df1.columns and field in df2.columns:
                    null_rate1 = df1[field].isnull().sum() / len(df1) * 100
                    null_rate2 = df2[field].isnull().sum() / len(df2) * 100
                    print(f"  {field} 空值率: {null_rate1:.1f}% vs {null_rate2:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 读取Excel文件失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 文件分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_code_logic_differences():
    """分析代码逻辑中可能导致不同结果的地方"""
    print(f"\n🔍 分析代码逻辑：可能导致不同结果的原因")
    print("-" * 80)
    
    potential_issues = [
        {
            "问题": "数据量触发不同代码路径",
            "描述": "大文件和小文件可能触发不同的处理逻辑",
            "代码位置": "smart_incremental_duplicate_check方法",
            "可能原因": "数据量大时内存优化逻辑可能有bug"
        },
        {
            "问题": "时间格式解析差异",
            "描述": "不同的时间格式可能导致解析失败",
            "代码位置": "standardize_date方法",
            "可能原因": "正则表达式匹配失败或dateutil解析异常"
        },
        {
            "问题": "空值处理差异",
            "描述": "不同的空值分布可能触发Series错误",
            "代码位置": "重复检测方法",
            "可能原因": "空值过多导致Series布尔值判断错误"
        },
        {
            "问题": "列名差异",
            "描述": "列名不同可能导致字段映射失败",
            "代码位置": "_standardize_column_names方法",
            "可能原因": "列名标准化逻辑有遗漏"
        },
        {
            "问题": "数据类型差异",
            "描述": "不同的数据类型可能导致处理逻辑分歧",
            "代码位置": "_standardize_data_types方法",
            "可能原因": "数据类型转换失败"
        }
    ]
    
    for i, issue in enumerate(potential_issues, 1):
        print(f"\n{i}. {issue['问题']}")
        print(f"   描述: {issue['描述']}")
        print(f"   代码位置: {issue['代码位置']}")
        print(f"   可能原因: {issue['可能原因']}")
    
    print(f"\n💡 建议检查顺序:")
    print("1. 首先检查文件基本信息差异（数据量、列名、数据类型）")
    print("2. 重点关注Order time字段的格式差异")
    print("3. 检查Transaction Num字段的完整性")
    print("4. 分析空值分布是否触发Series错误")
    print("5. 确认数据量是否触发不同的处理路径")

def main():
    """主函数"""
    print("🔍 文件差异分析：找出导致不同结果的原因")
    print("=" * 80)
    
    # 分析文件差异
    file_analysis_success = analyze_file_differences()
    
    # 分析代码逻辑
    analyze_code_logic_differences()
    
    print(f"\n🎯 分析总结")
    print("=" * 80)
    
    if file_analysis_success:
        print("✅ 文件差异分析完成")
        print("💡 请重点关注上述发现的差异，特别是:")
        print("  - Order time字段的格式差异")
        print("  - 数据量差异")
        print("  - 空值分布差异")
        print("  - 列名或数据类型差异")
    else:
        print("❌ 文件差异分析失败")
        print("💡 请确保文件路径正确，并重新运行分析")
    
    print(f"\n🔧 下一步建议:")
    print("1. 根据发现的差异，在代码中添加针对性的错误处理")
    print("2. 在关键位置添加详细日志，记录处理过程")
    print("3. 对发现的差异进行专门的兼容性处理")

if __name__ == "__main__":
    main()
    input("按回车键退出...")
