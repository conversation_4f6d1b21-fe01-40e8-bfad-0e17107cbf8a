#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AppLogger修复 - 验证日志系统错误修复
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_applogger_import():
    """测试AppLogger导入"""
    print("🔧 测试AppLogger导入")
    print("=" * 60)
    
    try:
        from utils.logger import get_logger, AppLogger
        print("✅ AppLogger导入成功")
        
        # 测试get_logger函数
        logger = get_logger('test_logger')
        print(f"✅ get_logger返回类型: {type(logger)}")
        
        # 检查logger属性
        if hasattr(logger, 'logger'):
            print(f"✅ logger.logger类型: {type(logger.logger)}")
            print(f"✅ logger.logger有addHandler方法: {hasattr(logger.logger, 'addHandler')}")
        else:
            print(f"✅ logger直接有addHandler方法: {hasattr(logger, 'addHandler')}")
        
        return True
        
    except Exception as e:
        print(f"❌ AppLogger导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_import_processor():
    """测试DataImportProcessor"""
    print(f"\n🔧 测试DataImportProcessor")
    print("=" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        print("✅ DataImportProcessor导入成功")
        
        # 尝试创建实例
        processor = DataImportProcessor()
        print("✅ DataImportProcessor实例创建成功")
        
        # 检查logger属性
        print(f"✅ processor.logger类型: {type(processor.logger)}")
        
        if hasattr(processor.logger, 'logger'):
            print(f"✅ processor.logger.logger类型: {type(processor.logger.logger)}")
            print(f"✅ processor.logger.logger有addHandler: {hasattr(processor.logger.logger, 'addHandler')}")
        else:
            print(f"✅ processor.logger有addHandler: {hasattr(processor.logger, 'addHandler')}")
        
        return True
        
    except Exception as e:
        print(f"❌ DataImportProcessor测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_file_setup():
    """测试日志文件设置"""
    print(f"\n🔧 测试日志文件设置")
    print("=" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        # 创建处理器实例
        processor = DataImportProcessor()
        
        # 模拟设置日志文件
        test_file_path = "C:/Users/<USER>/Desktop/test_file.xlsx"
        processor.current_file_path = test_file_path
        
        # 尝试设置日志文件（这会触发addHandler调用）
        try:
            processor._setup_file_logging()
            print("✅ 日志文件设置成功")
            
            # 检查日志文件是否创建
            if processor.log_file_path and os.path.exists(processor.log_file_path):
                print(f"✅ 日志文件已创建: {processor.log_file_path}")
            else:
                print(f"⚠️ 日志文件路径: {processor.log_file_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 日志文件设置失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 日志文件设置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_addhandler_fix():
    """测试addHandler修复"""
    print(f"\n🔧 测试addHandler修复")
    print("=" * 60)
    
    try:
        import logging
        from utils.logger import get_logger
        
        # 获取logger
        logger = get_logger('test_addhandler')
        
        # 创建一个测试handler
        test_handler = logging.StreamHandler()
        test_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        test_handler.setFormatter(test_formatter)
        
        # 测试修复后的addHandler逻辑
        print("🔧 测试addHandler修复逻辑...")
        
        if hasattr(logger, 'logger'):
            print("   使用logger.logger.addHandler")
            logger.logger.addHandler(test_handler)
            print("✅ logger.logger.addHandler成功")
        else:
            print("   使用logger.addHandler")
            logger.addHandler(test_handler)
            print("✅ logger.addHandler成功")
        
        # 测试日志记录
        logger.info("测试日志消息")
        print("✅ 日志记录成功")
        
        return True
        
    except Exception as e:
        print(f"❌ addHandler修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_simulation():
    """模拟导入过程测试"""
    print(f"\n🔧 模拟导入过程测试")
    print("=" * 60)
    
    try:
        from data_import_optimized import DataImportProcessor
        
        # 创建处理器
        processor = DataImportProcessor()
        
        # 模拟文件路径
        test_file = "C:/Users/<USER>/Desktop/test_import.xlsx"
        
        # 测试基本方法调用
        print("🔧 测试基本方法调用...")
        
        # 测试日志记录
        processor.logger.info("测试导入开始")
        print("✅ 日志记录正常")
        
        # 测试平台识别（不需要实际文件）
        platform = processor._detect_platform_from_filename("040725 CHINA ZERO.xlsx")
        print(f"✅ 平台识别: {platform}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 AppLogger修复验证工具")
    print("=" * 80)
    
    try:
        # 测试1：AppLogger导入
        test1_result = test_applogger_import()
        
        # 测试2：DataImportProcessor
        test2_result = test_data_import_processor()
        
        # 测试3：日志文件设置
        test3_result = test_log_file_setup()
        
        # 测试4：addHandler修复
        test4_result = test_addhandler_fix()
        
        # 测试5：导入模拟
        test5_result = test_import_simulation()
        
        print("\n" + "=" * 80)
        print("🎯 测试结果总结")
        print("=" * 80)
        
        tests = [
            ("AppLogger导入", test1_result),
            ("DataImportProcessor", test2_result),
            ("日志文件设置", test3_result),
            ("addHandler修复", test4_result),
            ("导入模拟", test5_result)
        ]
        
        passed = 0
        for test_name, result in tests:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{len(tests)} 通过")
        
        if passed == len(tests):
            print("🎉 所有测试通过")
            print("✅ AppLogger错误已修复")
            print("✅ 数据导入应该能正常工作")
        elif passed >= len(tests) * 0.8:
            print("✅ 大部分测试通过")
            print("⚠️ 还有少量问题需要解决")
        else:
            print("❌ 多个测试失败")
            print("🔧 需要进一步修复")
        
        return passed >= len(tests) * 0.8
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 修复验证{'成功' if success else '失败'}")
    input("按回车键退出...")
