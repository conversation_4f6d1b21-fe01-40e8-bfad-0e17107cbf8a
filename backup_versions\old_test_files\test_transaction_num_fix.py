# -*- coding: utf-8 -*-
"""
测试Transaction Num列修复
验证Transaction Num列是否能正确保留和导入
"""

import pandas as pd
from datetime import datetime
from intelligent_column_mapper import IntelligentColumnMapper

def test_transaction_num_preservation():
    """测试Transaction Num列保留功能"""
    print("🔍 Transaction Num列保留测试")
    print("=" * 80)
    
    # 用户更新后的16个列（包含Transaction Num）
    user_required_columns = [
        'Copartner name', 'Order No.', 'Transaction Num', 'Order types', 'Order status', 
        'Order price', 'Payment', 'Order time', 'Equipment ID', 
        'Equipment name', 'Branch name', 'Payment date', 'User name', 
        'Time', 'Matched Order ID', 'OrderTime_dt'
    ]
    
    print(f"📋 用户指定的16个列（包含Transaction Num）:")
    for i, col in enumerate(user_required_columns, 1):
        highlight = " ← 关键列！" if col == 'Transaction Num' else ""
        print(f"  {i:2d}. {col}{highlight}")
    
    # 创建包含Transaction Num数据的测试数据
    test_data = {
        # 用户需要的列（包含Transaction Num）
        'Copartner name': ['商户A', '商户B', '商户C'],
        'Order No.': ['ORD001', 'ORD002', 'ORD003'],
        'Transaction Num': ['TXN001', 'TXN002', 'TXN003'],  # 🔧 关键测试数据
        'Order types': ['Normal', 'Api order', 'Normal'],
        'Order status': ['Finished', 'Finished', 'Pending'],
        'Order price': [10.50, 25.00, 15.75],
        'Payment': [10.50, 25.00, 15.75],
        'Order time': ['2025-06-30 10:00:00', '2025-06-30 11:00:00', '2025-06-30 12:00:00'],
        'Equipment ID': ['EQ001', 'EQ002', 'EQ003'],
        'Equipment name': ['设备A', '设备B', '设备C'],
        'Branch name': ['分店A', '分店B', '分店C'],
        'Payment date': ['2025-06-30', '2025-06-30', '2025-06-30'],
        'User name': ['用户A', '用户B', '用户C'],
        'Time': ['10:00', '11:00', '12:00'],
        'Matched Order ID': ['MATCH001', 'MATCH002', None],
        'OrderTime_dt': ['2025-06-30 10:00:00', '2025-06-30 11:00:00', '2025-06-30 12:00:00'],
        
        # 应该被忽略的额外列
        'Serial number': ['001', '002', '003'],
        'Merchant name': ['Merchant A', 'Merchant B', 'Merchant C'],
        'Ticke code': ['TIC001', 'TIC002', 'TIC003'],
        'Payment type': ['Card', 'Cash', 'Card'],
        'Monetary unit': ['RM', 'RM', 'RM'],
        'Equipment type': ['Type A', 'Type B', 'Type A'],
        'Equipment Model': ['Model X', 'Model Y', 'Model X'],
        'Area type': ['Area 1', 'Area 2', 'Area 1'],
        'Matched_Flag': [True, True, False],
        'Transaction ID': ['2951553687', '2951553688', '2951553689'],
    }
    
    df = pd.DataFrame(test_data)
    
    print(f"\n📊 测试数据统计:")
    print(f"  总列数: {len(df.columns)}")
    print(f"  用户需要的列: {len(user_required_columns)}")
    print(f"  额外列: {len(df.columns) - len(user_required_columns)}")
    
    print(f"\n📋 Transaction Num原始数据:")
    print(f"  {list(df['Transaction Num'])}")
    
    # 应用智能列名映射
    mapper = IntelligentColumnMapper()
    df_mapped, analysis = mapper.apply_intelligent_mapping(df)
    
    print(f"\n📊 映射结果:")
    print(f"  原始列数: {analysis['total_columns']}")
    print(f"  保留列数: {len(df_mapped.columns)}")
    print(f"  忽略列数: {len(analysis['ignored_columns'])}")
    print(f"  匹配率: {analysis['mapping_confidence']:.1%}")
    
    print(f"\n✅ 保留的列 ({len(df_mapped.columns)} 列):")
    for i, col in enumerate(df_mapped.columns, 1):
        highlight = " ← Transaction Num映射结果！" if col == 'Transaction_Num' else ""
        print(f"  {i:2d}. {col}{highlight}")
    
    # 🔧 关键验证：Transaction Num数据是否保留
    print(f"\n🔍 Transaction Num数据验证:")
    
    if 'Transaction_Num' in df_mapped.columns:
        print(f"  ✅ Transaction_Num列存在于映射结果中")
        
        # 检查数据内容
        transaction_num_data = df_mapped['Transaction_Num'].tolist()
        print(f"  📋 Transaction_Num映射后数据: {transaction_num_data}")
        
        # 验证数据完整性
        original_data = ['TXN001', 'TXN002', 'TXN003']
        if transaction_num_data == original_data:
            print(f"  ✅ Transaction Num数据完整保留")
        else:
            print(f"  ❌ Transaction Num数据不一致")
            print(f"      原始: {original_data}")
            print(f"      映射后: {transaction_num_data}")
            return False
        
        # 检查是否有空值
        null_count = df_mapped['Transaction_Num'].isnull().sum()
        if null_count == 0:
            print(f"  ✅ Transaction Num无空值")
        else:
            print(f"  ⚠️ Transaction Num有 {null_count} 个空值")
        
    else:
        print(f"  ❌ Transaction_Num列不存在于映射结果中")
        print(f"  📋 可用列: {list(df_mapped.columns)}")
        return False
    
    # 验证其他关键列
    print(f"\n🔍 其他关键列验证:")
    key_columns = ['Order_No', 'Order_price', 'Equipment_ID']
    for col in key_columns:
        if col in df_mapped.columns:
            print(f"  ✅ {col}: 存在")
        else:
            print(f"  ❌ {col}: 缺失")
    
    # 检查忽略的列
    print(f"\n🚫 忽略的额外列:")
    for col in analysis['ignored_columns']:
        print(f"  - {col}")
    
    # 模拟数据库插入测试
    print(f"\n🔧 模拟数据库插入测试:")
    
    # 检查列名是否符合数据库要求
    db_compatible_columns = []
    problematic_columns = []
    
    for col in df_mapped.columns:
        if ' ' in col or any(char in col for char in ['(', ')', '.', '-']):
            problematic_columns.append(col)
        else:
            db_compatible_columns.append(col)
    
    print(f"  数据库兼容列: {len(db_compatible_columns)}")
    print(f"  问题列: {len(problematic_columns)}")
    
    if not problematic_columns:
        print(f"  ✅ 所有列名都符合数据库要求")
    else:
        print(f"  ⚠️ 发现问题列名: {problematic_columns}")
    
    # 检查Transaction_Num是否在数据库兼容列中
    if 'Transaction_Num' in db_compatible_columns:
        print(f"  ✅ Transaction_Num符合数据库要求")
    else:
        print(f"  ❌ Transaction_Num不符合数据库要求")
        return False
    
    return True

def test_missing_transaction_num_scenario():
    """测试缺失Transaction Num的场景"""
    print(f"\n🔧 缺失Transaction Num场景测试:")
    print("=" * 50)
    
    # 创建不包含Transaction Num的数据
    partial_data = {
        'Copartner name': ['商户A', '商户B'],
        'Order No.': ['ORD001', 'ORD002'],
        'Order price': [10.50, 25.00],
        'Equipment ID': ['EQ001', 'EQ002'],
        # 故意不包含Transaction Num
        'Serial number': ['001', '002'],  # 额外列，应该被忽略
    }
    
    df_partial = pd.DataFrame(partial_data)
    
    print(f"📋 部分数据列名: {list(df_partial.columns)}")
    print(f"⚠️ 注意: 不包含Transaction Num列")
    
    mapper = IntelligentColumnMapper()
    df_mapped, analysis = mapper.apply_intelligent_mapping(df_partial)
    
    print(f"📋 处理后列名: {list(df_mapped.columns)}")
    
    # 检查是否为缺失的Transaction Num创建了空列
    if 'Transaction_Num' in df_mapped.columns:
        print(f"✅ 为缺失的Transaction Num创建了空列")
        
        # 检查空列的内容
        transaction_num_values = df_mapped['Transaction_Num'].tolist()
        print(f"📋 Transaction_Num空列内容: {transaction_num_values}")
        
        # 验证是否都是None或空值
        all_null = all(pd.isnull(val) or val == '' or val is None for val in transaction_num_values)
        if all_null:
            print(f"✅ Transaction_Num空列正确创建（全部为空值）")
        else:
            print(f"⚠️ Transaction_Num空列包含非空值: {transaction_num_values}")
    else:
        print(f"❌ 未为缺失的Transaction Num创建空列")
        return False
    
    return True

def main():
    """主函数"""
    success1 = test_transaction_num_preservation()
    success2 = test_missing_transaction_num_scenario()
    
    print(f"\n📄 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 测试目标: 验证Transaction Num列是否能正确保留和导入")
    
    overall_success = success1 and success2
    print(f"✅ 测试结果: {'成功' if overall_success else '失败'}")
    
    if overall_success:
        print(f"\n🎉 Transaction Num列修复验证成功！")
        print(f"  📋 功能: Transaction Num列正确保留在16个指定列中")
        print(f"  🔧 映射: Transaction Num → Transaction_Num")
        print(f"  💾 数据: Transaction Num数据完整保留，无丢失")
        print(f"  🛡️ 容错: 缺失Transaction Num时自动创建空列")
        print(f"  ✅ 数据库兼容: 列名符合数据库要求")
    else:
        print(f"\n⚠️ Transaction Num列修复验证失败，需要进一步调试")

if __name__ == "__main__":
    main()
