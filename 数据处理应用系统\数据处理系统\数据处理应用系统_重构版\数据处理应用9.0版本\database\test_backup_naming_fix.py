#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 备份命名修复验证测试

验证备份命名优化修复：
1. 新创建的备份使用人性化命名 ✅
2. 备份列表显示人性化信息 ✅
3. 时间戳显示正确 ✅
4. 文件名不包含技术细节 ✅

作者: Claude 4.0 sonnet
创建时间: 2025-01-22
"""

import os
import sys
import sqlite3
import tempfile
import time
from datetime import datetime
from pathlib import Path

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟依赖
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")
    def critical(self, msg): print(f"CRITICAL: {msg}")
    def debug(self, msg): print(f"DEBUG: {msg}")

class DatabaseError(Exception): pass
class BackupError(Exception): pass

def get_logger(name): return MockLogger()

# 模拟导入
sys.modules['utils.exceptions'] = type(sys)('utils.exceptions')
sys.modules['utils.exceptions'].DatabaseError = DatabaseError
sys.modules['utils.exceptions'].BackupError = BackupError
sys.modules['utils.logger'] = type(sys)('utils.logger')
sys.modules['utils.logger'].get_logger = get_logger

try:
    from backup_coordinator import BackupCoordinator
    from backup_manager import DatabaseBackupManager
    print("✅ 成功导入修复后的备份组件")
except ImportError as e:
    print(f"❌ 无法导入备份组件: {e}")
    sys.exit(1)


class BackupNamingFixTest:
    """备份命名修复验证测试"""
    
    def __init__(self):
        self.test_results = []
        self.temp_dir = None
        self.test_db_path = None
        self.backup_coordinator = None
        self.backup_manager = None
    
    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置备份命名修复测试环境...")
        
        self.temp_dir = Path(tempfile.mkdtemp(prefix="backup_naming_test_"))
        self.test_db_path = self.temp_dir / "test_database.db"
        
        # 创建测试数据库
        self._create_test_database()
        
        # 初始化备份组件
        self.backup_coordinator = BackupCoordinator(str(self.test_db_path))
        self.backup_manager = DatabaseBackupManager(str(self.test_db_path))
        
        print(f"✅ 备份命名修复测试环境已设置: {self.temp_dir}")
    
    def _create_test_database(self):
        """创建测试数据库"""
        with sqlite3.connect(self.test_db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE test_data (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    operation_type TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            test_data = [
                ("命名测试数据1", "数据导入"),
                ("命名测试数据2", "退款处理"),
                ("命名测试数据3", "手动备份")
            ]
            cursor.executemany("INSERT INTO test_data (name, operation_type) VALUES (?, ?)", test_data)
            conn.commit()
    
    def test_coordinator_naming(self):
        """🔧 测试1：备份协调器命名优化"""
        print("\n📝 测试1：备份协调器命名优化")
        
        test_operations = [
            "数据导入",
            "退款处理", 
            "手动备份",
            "Excel文件导入前",
            "CSV数据导入前",
            "恢复前备份",
            "数据修复"
        ]
        
        try:
            for operation in test_operations:
                filename = self.backup_coordinator.generate_safe_backup_filename(operation, "app")
                print(f"  ✅ {operation} -> {filename}")
                
                # 检查文件名是否人性化
                is_human_readable = self._check_filename_human_readable(filename, operation)
                self.test_results.append((f"协调器命名-{operation}", is_human_readable, filename))
                
                if not is_human_readable:
                    print(f"    ⚠️ 文件名需要进一步优化")
                
                time.sleep(0.1)  # 确保时间戳不同
                
        except Exception as e:
            print(f"❌ 协调器命名测试失败: {e}")
            self.test_results.append(("协调器命名", False, str(e)))
    
    def test_manager_naming(self):
        """🔧 测试2：备份管理器命名优化"""
        print("\n📝 测试2：备份管理器命名优化")
        
        test_operations = [
            "数据导入前备份",
            "退款处理前备份", 
            "手动创建备份",
            "恢复前安全备份",
            "数据修复备份"
        ]
        
        try:
            for operation in test_operations:
                backup_file = self.backup_manager.backup_database(operation)
                
                if backup_file:
                    filename = os.path.basename(backup_file)
                    print(f"  ✅ {operation} -> {filename}")
                    
                    # 检查文件名是否人性化
                    is_human_readable = self._check_filename_human_readable(filename, operation)
                    self.test_results.append((f"管理器命名-{operation}", is_human_readable, filename))
                    
                    if not is_human_readable:
                        print(f"    ⚠️ 文件名需要进一步优化")
                else:
                    print(f"  ❌ {operation} -> 备份失败")
                    self.test_results.append((f"管理器命名-{operation}", False, "备份失败"))
                
                time.sleep(0.1)
                
        except Exception as e:
            print(f"❌ 管理器命名测试失败: {e}")
            self.test_results.append(("管理器命名", False, str(e)))
    
    def test_backup_list_display(self):
        """🔧 测试3：备份列表显示优化"""
        print("\n📋 测试3：备份列表显示优化")
        
        try:
            # 获取备份列表
            backup_list = self.backup_manager.get_backup_list()
            
            if backup_list:
                print(f"  ✅ 找到 {len(backup_list)} 个备份文件:")
                
                for i, backup_info in enumerate(backup_list[:5]):  # 只显示前5个
                    filename = backup_info.get('filename', '未知')
                    human_name = backup_info.get('human_readable_name', '未知')
                    created = backup_info.get('created', '未知时间')
                    size_mb = backup_info.get('size', 0) / (1024 * 1024)
                    operation_type = backup_info.get('operation_type', '未知操作')
                    
                    print(f"    {i+1}. {human_name}")
                    print(f"       文件名: {filename}")
                    print(f"       操作类型: {operation_type}")
                    print(f"       创建时间: {created}")
                    print(f"       大小: {size_mb:.1f} MB")
                    print()
                    
                    # 检查是否有人性化信息
                    has_human_info = (
                        human_name != '未知' and 
                        operation_type != '未知操作' and
                        created != '未知时间'
                    )
                    
                    self.test_results.append((f"列表显示-{i+1}", has_human_info, f"{human_name}"))
                
                print("  ✅ 备份列表显示测试完成")
            else:
                print("  ❌ 没有找到备份文件")
                self.test_results.append(("列表显示", False, "没有备份文件"))
                
        except Exception as e:
            print(f"❌ 备份列表显示测试失败: {e}")
            self.test_results.append(("列表显示", False, str(e)))
    
    def test_time_accuracy(self):
        """🔧 测试4：时间戳准确性"""
        print("\n⏰ 测试4：时间戳准确性")
        
        try:
            # 记录当前时间
            start_time = datetime.now()
            
            # 创建备份
            backup_file = self.backup_manager.backup_database("时间戳测试")
            
            # 记录结束时间
            end_time = datetime.now()
            
            if backup_file:
                # 获取文件的创建时间
                file_stat = os.stat(backup_file)
                file_time = datetime.fromtimestamp(file_stat.st_mtime)
                
                # 检查时间是否在合理范围内
                time_diff = abs((file_time - start_time).total_seconds())
                
                if time_diff <= 60:  # 1分钟内
                    print(f"  ✅ 时间戳准确: 文件时间 {file_time}, 创建时间范围 {start_time} - {end_time}")
                    self.test_results.append(("时间戳准确性", True, f"差异{time_diff:.1f}秒"))
                else:
                    print(f"  ❌ 时间戳不准确: 文件时间 {file_time}, 创建时间范围 {start_time} - {end_time}")
                    self.test_results.append(("时间戳准确性", False, f"差异{time_diff:.1f}秒"))
            else:
                print(f"  ❌ 备份创建失败")
                self.test_results.append(("时间戳准确性", False, "备份创建失败"))
                
        except Exception as e:
            print(f"❌ 时间戳准确性测试失败: {e}")
            self.test_results.append(("时间戳准确性", False, str(e)))
    
    def _check_filename_human_readable(self, filename: str, operation: str) -> bool:
        """检查文件名是否人性化"""
        # 检查基本格式
        if not filename.startswith('backup_') or not filename.endswith('.db'):
            return False
        
        # 检查是否包含技术细节
        technical_details = ['pid', '_r', 'random', 'process', 'thread', '_f']
        has_technical = any(detail in filename.lower() for detail in technical_details)
        
        if has_technical:
            return False
        
        # 检查是否保留中文字符（如果原操作名包含中文）
        if any('\u4e00' <= char <= '\u9fff' for char in operation):
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in filename)
            if not has_chinese:
                return False
        
        # 检查时间戳格式（应该是YYYYMMDD_HHMMSS，不包含微秒）
        parts = filename.replace('.db', '').split('_')
        if len(parts) >= 3:
            try:
                # 最后两部分应该是日期和时间
                date_part = parts[-2]
                time_part = parts[-1]
                datetime.strptime(f"{date_part}_{time_part}", "%Y%m%d_%H%M%S")
                
                # 检查是否包含微秒（长度超过6位）
                if len(time_part) > 6:
                    return False
                    
            except ValueError:
                return False
        
        return True
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        try:
            if self.temp_dir and self.temp_dir.exists():
                import shutil
                shutil.rmtree(self.temp_dir)
                print(f"✅ 已清理测试目录: {self.temp_dir}")
        except Exception as e:
            print(f"⚠️ 清理测试环境失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始备份命名修复验证测试")
        print("=" * 70)
        
        try:
            self.setup_test_environment()
            
            # 运行各项测试
            self.test_coordinator_naming()
            self.test_manager_naming()
            self.test_backup_list_display()
            self.test_time_accuracy()
            
            # 显示测试结果
            self.show_test_results()
            
        finally:
            self.cleanup_test_environment()
    
    def show_test_results(self):
        """显示测试结果"""
        print("\n" + "=" * 70)
        print("📊 备份命名修复验证结果")
        print("=" * 70)
        
        passed = 0
        failed = 0
        
        for test_name, success, details in self.test_results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{status} {test_name}: {details}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print("=" * 70)
        print(f"总计: {passed + failed} 项测试")
        print(f"✅ 通过: {passed} 项")
        print(f"❌ 失败: {failed} 项")
        
        if failed == 0:
            print("\n🎉 所有测试通过！备份命名修复完全成功！")
            print("\n🔧 修复成果：")
            print("   ✅ 新创建的备份使用人性化命名")
            print("   ✅ 备份列表显示人性化信息")
            print("   ✅ 时间戳显示正确")
            print("   ✅ 文件名不包含技术细节")
            print("   ✅ 保留中文字符支持")
            print("   ✅ 智能冲突解决机制")
        else:
            print(f"\n⚠️ 有 {failed} 项测试失败，需要进一步检查")


def main():
    """主函数"""
    test_suite = BackupNamingFixTest()
    test_suite.run_all_tests()


if __name__ == "__main__":
    main()
