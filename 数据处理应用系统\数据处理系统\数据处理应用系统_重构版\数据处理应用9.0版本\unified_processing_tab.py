# ==================== 原始数据处理选项卡 ====================
# 恢复到代码优化之前的原始设计

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from ui_components import ModernUIColors, ModernUIFonts, ModernUIMetrics, ModernButton, ModernProgressBar, ModernStatusMessage
import os
import threading

class UnifiedProcessingTab:
    """现代化数据处理选项卡 - 原始设计"""

    def __init__(self, parent, gui_updater, config, file_manager, process_runner, backup_manager=None):
        self.parent = parent
        self.gui_updater = gui_updater
        self.config = config
        self.file_manager = file_manager
        self.process_runner = process_runner
        self.backup_manager = backup_manager  # 添加备份管理器
        self.tab_type = "processing"

        # 文件变量
        self.file1_var = tk.StringVar()
        self.file2_var = tk.StringVar()
        self.sheet_name_var = tk.StringVar(value="TRANSACTION_LIST")
        # 使用9.0版本作为默认选择
        default_script = "report 模块化设计 9.0.py"

        self.script_var = tk.StringVar(value=default_script)

        # 创建UI
        self.create_modern_ui()

        # 注册组件
        self._register_components()

        # 显示初始消息
        self.log_message("请使用浏览按钮选择文件")

    def log_message(self, message: str):
        """记录日志消息 - 使用智能过滤机制"""
        self.gui_updater.safe_log(message, self.tab_type)
    def create_modern_ui(self):
        """创建现代化UI"""
        # 主容器 - 使用grid布局确保更好的控制
        main_container = tk.Frame(self.parent, bg=ModernUIColors.PRIMARY_BG)
        main_container.pack(fill=tk.BOTH, expand=True, padx=ModernUIMetrics.SPACING_LG,
                           pady=ModernUIMetrics.SPACING_LG)

        # 配置grid权重
        main_container.grid_rowconfigure(1, weight=1)  # 让下半部分可扩展
        main_container.grid_columnconfigure(0, weight=1)

        # 上半部分：文件选择和操作区域（固定高度）
        top_frame = tk.Frame(main_container, bg=ModernUIColors.PRIMARY_BG)
        top_frame.grid(row=0, column=0, sticky="ew", pady=(0, ModernUIMetrics.SPACING_MD))

        # 创建文件选择区域
        self.create_file_selection_area(top_frame)

        # 创建操作区域
        self.create_operation_area(top_frame)

        # 下半部分：状态和日志区域（可扩展，但确保最小高度）
        bottom_frame = tk.Frame(main_container, bg=ModernUIColors.PRIMARY_BG)
        bottom_frame.grid(row=1, column=0, sticky="nsew")

        # 配置下半部分的grid权重
        bottom_frame.grid_rowconfigure(0, weight=1)  # 状态日志区域可扩展（第0行）
        bottom_frame.grid_columnconfigure(0, weight=1)

        # 创建状态和日志区域
        self.create_status_log_area(bottom_frame)
    
    def create_file_selection_area(self, parent):
        """创建文件选择区域"""
        # 文件选择卡片
        file_card = self.create_card(parent, "📁 文件选择")

        # 第一文件选择
        self.create_file_input(
            file_card,
            "第一文件 (SETTLEMENT):",
            self.file1_var,
            self.browse_file1,
            "选择SETTLEMENT报表文件"
        )

        # Sheet名称输入
        sheet_frame = tk.Frame(file_card, bg="#f3f7eb")
        sheet_frame.pack(fill=tk.X, pady=(ModernUIMetrics.SPACING_SM, 0))

        tk.Label(
            sheet_frame,
            text="Sheet名称:",
            font=(ModernUIFonts.FONT_FAMILY, ModernUIFonts.BODY_SIZE),
            bg="#f3f7eb",
            fg=ModernUIColors.TEXT_PRIMARY
        ).pack(anchor=tk.W)

        sheet_entry = tk.Entry(
            sheet_frame,
            textvariable=self.sheet_name_var,
            font=(ModernUIFonts.FONT_FAMILY, ModernUIFonts.BODY_SIZE),
            relief="flat",
            bd=1,
            highlightthickness=1,
            highlightcolor=ModernUIColors.PRIMARY_BUTTON
        )
        sheet_entry.pack(fill=tk.X, pady=(ModernUIMetrics.SPACING_XS, 0))

        # 第二文件选择
        self.create_file_input(
            file_card,
            "第二文件 (CHINA):",
            self.file2_var,
            self.browse_file2,
            "选择CHINA报表文件"
        )

        # 脚本选择
        self.create_script_selection(file_card)

    def create_file_input(self, parent, label_text, var, browse_command, placeholder):
        """创建文件输入组件"""
        input_frame = tk.Frame(parent, bg="#f3f7eb")
        input_frame.pack(fill=tk.X, pady=(ModernUIMetrics.SPACING_MD, 0))

        # 标签
        tk.Label(
            input_frame,
            text=label_text,
            font=(ModernUIFonts.FONT_FAMILY, ModernUIFonts.BODY_SIZE),
            bg="#f3f7eb",
            fg=ModernUIColors.TEXT_PRIMARY
        ).pack(anchor=tk.W)

        # 输入区域
        entry_frame = tk.Frame(input_frame, bg="#f3f7eb")
        entry_frame.pack(fill=tk.X, pady=(ModernUIMetrics.SPACING_XS, 0))

        # 文件路径输入框
        entry = tk.Entry(
            entry_frame,
            textvariable=var,
            font=(ModernUIFonts.FONT_FAMILY, ModernUIFonts.BODY_SIZE),
            relief="flat",
            bd=1,
            highlightthickness=1,
            highlightcolor=ModernUIColors.PRIMARY_BUTTON
        )
        entry.pack(side=tk.LEFT, fill=tk.X, expand=True,
                   padx=(0, ModernUIMetrics.SPACING_SM))

        # 浏览按钮
        browse_btn = ModernButton(
            entry_frame,
            text="📂 浏览",
            command=browse_command,
            style="secondary",
            width=80,
            height=30
        )
        browse_btn.pack(side=tk.RIGHT)

    def create_script_selection(self, parent):
        """创建脚本选择区域"""
        script_frame = tk.Frame(parent, bg="#f3f7eb")
        script_frame.pack(fill=tk.X, pady=(ModernUIMetrics.SPACING_MD, 0))

        tk.Label(
            script_frame,
            text="处理脚本:",
            font=(ModernUIFonts.FONT_FAMILY, ModernUIFonts.BODY_SIZE),
            bg="#f3f7eb",
            fg=ModernUIColors.TEXT_PRIMARY
        ).pack(anchor=tk.W)

        # 脚本选择下拉框
        script_combo = ttk.Combobox(
            script_frame,
            textvariable=self.script_var,
            values=[
                "report 模块化设计 9.0.py (架构优化版 - 推荐)",
                "report 模块化设计 8.0.py",
                "report 模块化设计 7.0.py",
                "report 脚本 3.0.py"
            ],
            state="readonly",
            font=(ModernUIFonts.FONT_FAMILY, ModernUIFonts.BODY_SIZE)
        )
        script_combo.pack(fill=tk.X, pady=(ModernUIMetrics.SPACING_XS, 0))

        # 🔧 修复：阻止下拉框响应鼠标滚轮事件
        def block_mousewheel_unified(event):
            """阻止鼠标滚轮事件传播到下拉框"""
            return "break"

        script_combo.bind('<MouseWheel>', block_mousewheel_unified)
        script_combo.bind('<Button-4>', block_mousewheel_unified)  # Linux
        script_combo.bind('<Button-5>', block_mousewheel_unified)  # Linux

        # 脚本描述
        self.script_desc_label = tk.Label(
            script_frame,
            text="🚀 架构优化版 + 性能提升50% + Bug修复 + 内存优化",
            font=(ModernUIFonts.FONT_FAMILY, ModernUIFonts.SMALL_SIZE),
            bg="#f3f7eb",
            fg=ModernUIColors.SUCCESS
        )
        self.script_desc_label.pack(anchor=tk.W, pady=(ModernUIMetrics.SPACING_XS, 0))

        # 绑定选择事件
        script_combo.bind('<<ComboboxSelected>>', self._on_script_changed)

    def create_operation_area(self, parent):
        """创建操作区域"""
        # 操作区域 - 使用无框架设计
        operation_area = self.create_operation_area_frameless(parent, "🚀 操作控制")

        # 按钮容器
        button_container = tk.Frame(operation_area, bg=ModernUIColors.PRIMARY_BG)
        button_container.pack(fill=tk.X, pady=(6, 0))

        # 主要操作按钮 - 使用自适应尺寸
        self.process_btn = ModernButton(
            button_container,
            text="开始处理",  # 移除emoji，使用自适应尺寸
            command=self.process_files,
            style="primary"
            # 不指定width和height，让按钮自适应
        )
        self.process_btn.pack(side=tk.LEFT, padx=(0, 8))

        # 次要操作按钮 - 使用自适应尺寸
        self.clear_btn = ModernButton(
            button_container,
            text="清空选择",  # 移除emoji，使用自适应尺寸
            command=self.clear_selection,
            style="secondary"
            # 不指定width和height，让按钮自适应
        )
        self.clear_btn.pack(side=tk.LEFT)

        # 进度条 - 初始隐藏
        self.progress_bar = ModernProgressBar(operation_area, width=400, height=8)
        # 不在初始化时显示进度条

    def create_operation_area_frameless(self, parent, title):
        """创建无框架操作区域"""
        # 操作区域容器 - 无白色背景
        operation_container = tk.Frame(parent, bg=ModernUIColors.PRIMARY_BG)
        operation_container.pack(fill=tk.X, pady=(0, 8))  # 减小间距

        # 操作标题
        title_frame = tk.Frame(operation_container, bg=ModernUIColors.PRIMARY_BG)
        title_frame.pack(fill=tk.X, pady=(0, 6))  # 减小间距

        tk.Label(
            title_frame,
            text=title,
            font=(ModernUIFonts.FONT_FAMILY, ModernUIFonts.SUBTITLE_SIZE, "bold"),
            bg=ModernUIColors.PRIMARY_BG,
            fg=ModernUIColors.TEXT_PRIMARY
        ).pack(anchor=tk.W)

        # 操作内容区域 - 直接使用背景色，无白色框
        content_area = tk.Frame(operation_container, bg=ModernUIColors.PRIMARY_BG)
        content_area.pack(fill=tk.BOTH, expand=True, padx=8, pady=4)  # 减小边距

        return content_area

    def create_card(self, parent, title):
        """创建标准卡片容器"""
        # 卡片容器
        card_container = tk.Frame(parent, bg=ModernUIColors.PRIMARY_BG)
        card_container.pack(fill=tk.X, pady=(0, ModernUIMetrics.SPACING_LG))

        # 卡片标题
        title_frame = tk.Frame(card_container, bg=ModernUIColors.PRIMARY_BG)
        title_frame.pack(fill=tk.X, pady=(0, ModernUIMetrics.SPACING_SM))

        tk.Label(
            title_frame,
            text=title,
            font=(ModernUIFonts.FONT_FAMILY, ModernUIFonts.SUBTITLE_SIZE, "bold"),
            bg=ModernUIColors.PRIMARY_BG,
            fg=ModernUIColors.TEXT_PRIMARY
        ).pack(anchor=tk.W)

        # 卡片内容
        card_content = tk.Frame(
            card_container,
            bg="#f3f7eb",
            relief="flat",
            bd=1,
            highlightbackground=ModernUIColors.BORDER,
            highlightthickness=1
        )
        card_content.pack(fill=tk.BOTH, expand=True)

        # 内容区域
        content_area = tk.Frame(card_content, bg="#f3f7eb")
        content_area.pack(fill=tk.BOTH, expand=True,
                         padx=ModernUIMetrics.SPACING_LG,
                         pady=ModernUIMetrics.SPACING_LG)

        return content_area



    def browse_file1(self):
        """浏览第一文件"""
        filename = filedialog.askopenfilename(
            title="选择第一文件 (SETTLEMENT)",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.file1_var.set(filename)
    
    def browse_file2(self):
        """浏览第二文件"""
        filename = filedialog.askopenfilename(
            title="选择第二文件 (CHINA)",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.file2_var.set(filename)
    
    def on_script_change(self):
        """脚本选择变化时更新描述 - 传统版功能"""
        selected_script = self.script_var.get()
        if "模块化设计 9.0" in selected_script:
            self.script_desc_label.config(text="🚀 架构优化版 + 性能提升50% + Bug修复 + 内存优化",
                                        fg=ModernUIColors.SUCCESS)
        elif "模块化设计 8.0" in selected_script:
            self.script_desc_label.config(text="最新版本 + Bug修复 + 性能优化 + 模块化设计",
                                        fg=ModernUIColors.SUCCESS)
        elif "模块化设计 7.0" in selected_script:
            self.script_desc_label.config(text="模块化设计 + 智能检测 + 日期分组",
                                        fg=ModernUIColors.SUCCESS)
        elif "脚本 3.0" in selected_script:
            self.script_desc_label.config(text="原始版本 + 智能检测",
                                        fg=ModernUIColors.INFO)
    
    def clear_files(self):
        """清空文件选择"""
        self.file1_var.set("")
        self.file2_var.set("")
        self.sheet_name_var.set("TRANSACTION_LIST")

    def process_files(self):
        """处理文件 - 统一的处理逻辑"""
        print("🔧 调试：UnifiedProcessingTab.process_files 被调用")
        file1_path = self.file1_var.get().strip()
        file2_path = self.file2_var.get().strip()
        sheet_name = self.sheet_name_var.get().strip()
        print(f"🔧 调试：文件路径 - file1: {file1_path}, file2: {file2_path}, sheet: {sheet_name}")

        # 输入验证
        if not file1_path:
            if hasattr(self.gui_updater, 'safe_modern_dialog'):
                self.gui_updater.safe_modern_dialog(
                    title="输入错误",
                    message="请选择第一文件 (SETTLEMENT)",
                    dialog_type="warning"
                )
            else:
                messagebox.showwarning("输入错误", "请选择第一文件 (SETTLEMENT)")
            return

        if not file2_path:
            if hasattr(self.gui_updater, 'safe_modern_dialog'):
                self.gui_updater.safe_modern_dialog(
                    title="输入错误",
                    message="请选择第二文件 (CHINA)",
                    dialog_type="warning"
                )
            else:
                messagebox.showwarning("输入错误", "请选择第二文件 (CHINA)")
            return

        # 设置处理状态
        self.set_processing_state(True)

        # 在新线程中执行处理
        def process_thread():
            try:
                print("🔧 调试：process_thread 开始执行")
                # 使用原始的处理逻辑
                script_name = self.script_var.get()
                print(f"🔧 调试：使用脚本: {script_name}")
                # 🔧 修复：使用现有的同步方法执行脚本
                success = self._run_processing_script(
                    file1_path, file2_path, script_name, sheet_name
                )

                # 更新UI状态
                if hasattr(self.gui_updater, 'root'):
                    self.gui_updater.root.after(0, lambda: self.set_processing_state(False))
                else:
                    self.parent.after(0, lambda: self.set_processing_state(False))

                if success:
                    success_msg = "文件处理成功完成！"
                    if hasattr(self.gui_updater, 'safe_modern_dialog'):
                        if hasattr(self.gui_updater, 'root'):
                            self.gui_updater.root.after(0, lambda: self.gui_updater.safe_modern_dialog(
                                title="处理完成",
                                message=success_msg,
                                dialog_type="success"
                            ))
                        else:
                            self.parent.after(0, lambda: messagebox.showinfo("处理完成", success_msg))
                    else:
                        self.parent.after(0, lambda: messagebox.showinfo("处理完成", success_msg))

            except Exception as e:
                error_msg = f"文件处理失败：{str(e)}"
                # 更新UI状态
                if hasattr(self.gui_updater, 'root'):
                    self.gui_updater.root.after(0, lambda: self.set_processing_state(False))
                else:
                    self.parent.after(0, lambda: self.set_processing_state(False))

                if hasattr(self.gui_updater, 'safe_modern_dialog'):
                    if hasattr(self.gui_updater, 'root'):
                        self.gui_updater.root.after(0, lambda: self.gui_updater.safe_modern_dialog(
                            title="处理失败",
                            message=error_msg,
                            dialog_type="error"
                        ))
                    else:
                        self.parent.after(0, lambda: messagebox.showerror("处理失败", error_msg))
                else:
                    self.parent.after(0, lambda: messagebox.showerror("处理失败", error_msg))

        print("🔧 调试：启动处理线程")
        thread = threading.Thread(target=process_thread, daemon=True)
        thread.start()
        print("🔧 调试：处理线程已启动")

    def create_status_log_area(self, parent):
        """创建状态区域 - 优化版，移除冗余的日志区域"""
        # 处理状态区域 - 扩展高度显示更多信息
        status_card = self.create_expandable_card_grid(parent, "📊 处理状态与结果", row=0)

        # 状态消息组件
        self.status_message = ModernStatusMessage(status_card)

        # 详细状态文本框 - 用于显示处理结果和统计信息
        status_detail_frame = tk.Frame(status_card, bg="#f3f7eb")
        status_detail_frame.pack(fill=tk.BOTH, expand=True, pady=(ModernUIMetrics.SPACING_MD, 0))

        # 🔧 新增：创建滚动框架
        scroll_frame = tk.Frame(status_detail_frame, bg="#fafafa")
        scroll_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(
            scroll_frame,
            wrap=tk.WORD,
            font=("STKaiti", 13, "bold"),  # 🔧 13号粗体楷体
            bg="#fafafa",
            fg=ModernUIColors.TEXT_PRIMARY,
            relief="flat",
            bd=0,
            padx=ModernUIMetrics.SPACING_SM,
            pady=ModernUIMetrics.SPACING_SM,
            height=12  # 🔧 设置固定高度
        )
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        scrollbar1 = tk.Scrollbar(scroll_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.config(yscrollcommand=scrollbar1.set)
        scrollbar1.pack(side=tk.RIGHT, fill=tk.Y)

        # 设置只读
        self.log_text.config(state=tk.DISABLED)

    def create_expandable_card_grid(self, parent, title, row):
        """创建可扩展的卡片（使用grid布局）"""
        # 卡片容器
        card_container = tk.Frame(parent, bg=ModernUIColors.PRIMARY_BG)
        card_container.grid(row=row, column=0, sticky="nsew", pady=(0, ModernUIMetrics.SPACING_LG))

        # 卡片标题
        title_frame = tk.Frame(card_container, bg=ModernUIColors.PRIMARY_BG)
        title_frame.pack(fill=tk.X, pady=(0, ModernUIMetrics.SPACING_SM))

        tk.Label(
            title_frame,
            text=title,
            font=(ModernUIFonts.FONT_FAMILY, ModernUIFonts.SUBTITLE_SIZE, "bold"),
            bg=ModernUIColors.PRIMARY_BG,
            fg=ModernUIColors.TEXT_PRIMARY
        ).pack(anchor=tk.W)

        # 卡片内容
        card_content = tk.Frame(
            card_container,
            bg="#f3f7eb",
            relief="flat",
            bd=1,
            highlightbackground=ModernUIColors.BORDER,
            highlightthickness=1
        )
        card_content.pack(fill=tk.BOTH, expand=True)

        # 内容区域
        content_area = tk.Frame(card_content, bg="#f3f7eb")
        content_area.pack(fill=tk.BOTH, expand=True,
                         padx=ModernUIMetrics.SPACING_LG,
                         pady=ModernUIMetrics.SPACING_LG)

        return content_area

    def _on_script_changed(self, event=None):
        """脚本选择变化时更新描述"""
        selected_script = self.script_var.get()
        if "模块化设计 9.0" in selected_script:
            self.script_desc_label.config(text="🚀 架构优化版 + 性能提升50% + Bug修复 + 内存优化",
                                        fg=ModernUIColors.SUCCESS)
        elif "模块化设计 8.0" in selected_script:
            self.script_desc_label.config(text="最新版本 + Bug修复 + 性能优化 + 模块化设计",
                                        fg=ModernUIColors.SUCCESS)
        elif "模块化设计 7.0" in selected_script:
            self.script_desc_label.config(text="智能数据处理 + 模块化设计 + 日期分组",
                                        fg=ModernUIColors.SUCCESS)
        elif "脚本 3.0" in selected_script:
            self.script_desc_label.config(text="原始版本 + 智能检测",
                                        fg=ModernUIColors.INFO)

    def clear_selection(self):
        """清空选择"""
        self.file1_var.set("")
        self.file2_var.set("")
        self.sheet_name_var.set("TRANSACTION_LIST")

    def set_processing_state(self, is_processing):
        """设置处理状态"""
        if hasattr(self, 'process_btn'):
            self.process_btn.config(state=tk.DISABLED if is_processing else tk.NORMAL)
        if hasattr(self, 'clear_btn'):
            self.clear_btn.config(state=tk.DISABLED if is_processing else tk.NORMAL)

        # 控制进度条显示
        if hasattr(self, 'progress_bar'):
            if is_processing:
                self.progress_bar.pack(pady=(6, 0))
                self.progress_bar.start()
            else:
                self.progress_bar.stop()
                self.progress_bar.hide()

    def _register_components(self):
        """注册组件"""
        # 注册日志组件
        if hasattr(self, 'log_text'):
            self.gui_updater.register_log_widget(self.tab_type, self.log_text)

        # 注册进度条组件
        if hasattr(self, 'progress_bar'):
            self.gui_updater.register_progress_bar(self.tab_type, self.progress_bar)

        # 注册状态消息组件
        if hasattr(self, 'status_message'):
            self.gui_updater.register_status_message(self.tab_type, self.status_message)

    def _run_processing_script(self, file1_path, file2_path, script_name, sheet_name):
        """运行处理脚本"""
        try:
            # 构建脚本路径
            script_path = os.path.join(os.path.dirname(__file__), script_name)

            # 准备参数 - 修复为命名参数格式
            args = ["--file1", file1_path, "--file2", file2_path]
            if sheet_name:
                args.extend(["--sheet_name", sheet_name])

            # 运行脚本 - 添加立即错误检查
            print(f"🔧 [ERROR_CHECK] 准备运行脚本 - 路径: {script_path}")
            print(f"🔧 [ERROR_CHECK] 脚本参数: {args}")

            # 立即检查脚本路径
            if not script_path:
                error_msg = "❌ [IMMEDIATE_ERROR] 脚本路径为空！"
                print(error_msg)
                self.gui_updater.safe_log(error_msg, self.tab_type)
                return

            if not os.path.exists(script_path):
                error_msg = f"❌ [IMMEDIATE_ERROR] 脚本文件不存在: {script_path}"
                print(error_msg)
                self.gui_updater.safe_log(error_msg, self.tab_type)
                return

            # 立即检查process_runner
            if not self.process_runner:
                error_msg = "❌ [IMMEDIATE_ERROR] ProcessRunner未初始化！"
                print(error_msg)
                self.gui_updater.safe_log(error_msg, self.tab_type)
                return

            print(f"🔧 [ERROR_CHECK] 开始执行 process_runner.run_script...")
            try:
                result = self.process_runner.run_script(script_path, args)
                print(f"✅ [ERROR_CHECK] 脚本执行完成，结果类型: {type(result)}")
            except Exception as e:
                error_msg = f"❌ [IMMEDIATE_ERROR] 脚本执行异常: {e}"
                print(error_msg)
                self.gui_updater.safe_log(error_msg, self.tab_type)
                import traceback
                traceback.print_exc()
                return

            # 显示脚本输出到日志 (优化进度条显示)
            if hasattr(result, 'stdout') and result.stdout:
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        # 检查是否为进度条，如果是则转换为简洁格式
                        if self._is_progress_bar_line(line.strip()):
                            progress_msg = self._extract_progress_info(line.strip())
                            if progress_msg:  # 只在关键节点显示
                                self.gui_updater.safe_log(progress_msg, self.tab_type)
                        elif self._should_filter_line(line.strip()):
                            # 过滤掉冗余信息
                            pass
                        else:
                            # 正常日志直接显示
                            self.gui_updater.safe_log(line.strip(), self.tab_type)

            # 显示错误输出（过滤进度条）
            if hasattr(result, 'stderr') and result.stderr:
                for line in result.stderr.strip().split('\n'):
                    if line.strip() and not self._is_progress_bar_line(line.strip()):
                        # 检查是否为处理总结信息
                        if self._is_summary_info(line.strip()):
                            self.gui_updater.safe_log(line.strip(), self.tab_type)
                        else:
                            self.gui_updater.safe_log(f"错误: {line.strip()}", self.tab_type)

            # 更新完成状态
            if hasattr(self, 'log_text'):
                if result.returncode == 0:
                    self.gui_updater.safe_log(f"✅ 处理完成：{script_name}", self.tab_type)
                else:
                    self.gui_updater.safe_log(f"❌ 处理失败：{script_name} (返回码: {result.returncode})", self.tab_type)

            return result.returncode == 0

        except Exception as e:
            if hasattr(self, 'log_text'):
                self.gui_updater.safe_log(f"❌ 处理异常：{str(e)}", self.tab_type)
            return False

    def _is_progress_bar_line(self, line):
        """检测是否为进度条输出行"""
        # 检测tqdm进度条特征
        progress_indicators = [
            '%|',  # 百分比和进度条
            '条/s',  # 速度指示器
            '[32m',  # ANSI颜色代码
            '[0m',   # ANSI重置代码
            '处理数据记录:',
            '处理 9_digit 阶段:',
            '处理 over_9 阶段:',
            '处理 anomaly 阶段:'
        ]

        return any(indicator in line for indicator in progress_indicators)

    def _extract_progress_info(self, line):
        """从进度条中提取关键信息并格式化为简洁消息"""
        import re

        # 清理ANSI代码
        clean_line = re.sub(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])', '', line)

        # 提取百分比和阶段信息
        stage_match = re.search(r'处理\s+(\w+)\s+阶段:\s*(\d+)%', clean_line)
        if stage_match:
            stage_name = stage_match.group(1)
            percentage = int(stage_match.group(2))

            # 只在关键节点显示进度
            if percentage in [0, 25, 50, 75, 100]:
                stage_display = {
                    '9_digit': '9位ID处理',
                    'over_9': '超9位ID处理',
                    'anomaly': '异常数据处理',
                    '数据记录': '数据记录处理'
                }.get(stage_name, f'{stage_name}处理')

                if percentage == 0:
                    return f"🚀 开始{stage_display}..."
                elif percentage == 100:
                    return f"✅ {stage_display}完成"
                else:
                    return f"⏳ {stage_display}进度: {percentage}%"

        # 处理通用进度条格式 (如: 处理数据记录: 100%|██████████| 100/100)
        general_progress = re.search(r'(\w+(?:\w+)*?):\s*(\d+)%\|[█▉▊▋▌▍▎▏\s]*\|\s*(\d+)/(\d+)', clean_line)
        if general_progress:
            task_name = general_progress.group(1)
            percentage = int(general_progress.group(2))
            current = int(general_progress.group(3))
            total = int(general_progress.group(4))

            # 只在关键节点显示进度
            if percentage in [0, 25, 50, 75, 100]:
                if percentage == 0:
                    return f"🚀 开始{task_name}..."
                elif percentage == 100:
                    return f"✅ {task_name}完成 ({current}/{total})"
                else:
                    return f"⏳ {task_name}进度: {percentage}% ({current}/{total})"

        return None

    def _should_filter_line(self, line):
        """检测是否应该过滤掉的冗余日志行 - 强化版过滤器"""

        # 如果是空行，直接过滤
        if not line.strip():
            return True

        # 🔧 修复：优先检查重要信息，绝不过滤
        important_patterns = [
            "处理完成！结果已写入",
            "数据已写入",
            "日志已写入",
            "第一文件总金额:",
            "第二文件最终金额:",
            "金额差异:",
            "金额匹配成功",
            "脚本执行完成",
            "✅ 脚本执行完成",
            "模块化数据处理完成",
            "✅ 模块化数据处理完成",
            "处理完成，耗时:",
            "✅ 处理完成，耗时:",
            "📊 transaction_id 模式统计:",
            "处理: ",
            "匹配: ",
            "插入: ",
            " 条",
            "============================================================",
            "📊 数据处理完成总结",
            "数据处理完成总结"
        ]

        # 如果包含重要信息，绝不过滤
        for pattern in important_patterns:
            if pattern in line:
                return False

        filter_patterns = [
            # === 核心冗余信息过滤 ===
            # 重复的Transaction ID统计信息
            'Transaction ID统计:',
            'Transaction ID统计: 成功',
            'Transaction ID统计:',

            # 修复过程的详细信息
            '🔍 修复前匹配数:',
            '✅ 数据类型修复生效！',
            '数据类型修复生效',
            '修复前匹配数',
            '修复后匹配数',

            # 索引相关信息
            '- 索引',
            '索引31:',
            '索引30:',
            '索引29:',
            '索引28:',
            '索引27:',
            '索引26:',
            '索引25:',
            '索引24:',
            '索引23:',
            '索引22:',
            '索引21:',
            '索引20:',
            '索引19:',
            '索引18:',
            '索引17:',
            '索引16:',
            '索引15:',
            '索引14:',
            '索引13:',
            '索引12:',
            '索引11:',
            '索引10:',
            '索引9:',
            '索引8:',
            '索引7:',
            '索引6:',
            '索引5:',
            '索引4:',
            '索引3:',
            '索引2:',
            '索引1:',
            '索引0:',
            '价格=',
            '状态=Finish',

            # === 技术细节过滤 ===
            # 文件结构信息
            '第一文件读取的列名：',
            '第一文件列数：',
            '第二文件列数：',
            '⏰ 检测到独立的Time列',
            '警告：第一文件列数为',
            '⏰ 处理日期时间数据...',
            '📅 日期一致性检查通过:',
            '第一文件9位ID数量：',
            '检查Transaction ID一致性...',
            '✅ 所有Transaction ID都是唯一的',

            # Transaction Num检测过程
            '🔍 开始智能检测Transaction Num匹配能力...',
            '✅ 检测到 Transaction Num 列',
            '📊 第二文件总记录数:',
            '📊 第二文件Transaction Num非空记录数:',
            '📊 第二文件Transaction Num唯一值数量:',
            '📊 第二文件Transaction Num填充率:',
            '🔍 检查Transaction Num与Transaction ID的匹配能力...',
            '📊 第一文件有效Transaction ID数量:',
            '📊 第二文件有效Transaction Num数量:',
            '📊 第一文件Transaction ID样本:',
            '📊 第二文件Transaction Num样本:',
            '📊 可匹配的Transaction ID/Num数量:',
            '📊 匹配的Transaction ID样本:',
            '📊 匹配率:',
            '✅ Transaction Num具备匹配能力',
            '🔄 将使用Transaction ID匹配方式',
            '🎯 匹配模式: Transaction ID匹配',
            '📝 将使用Transaction ID进行数据匹配和同步',
            '开始数据处理...',
            '🎯 匹配模式设置为: transaction_id',
            '📊 发现 1 个日期分组',
            '📅 处理日期:',

            # === 状态统计信息过滤 ===
            'True:',
            'False:',
            'NaN:',
            'Matched_Flag',
            'Finish          ',
            'Close           ',
            'Refunding       ',

            # === 恢复过程信息过滤 ===
            '✅ Transaction Num修复:',
            '✅ Equipment信息恢复:',
            '✅ Order No.信息恢复:',
            'Transaction ID插入:',
            'Transaction ID匹配模式：数据已通过Transaction ID同步，无需自动修正',
            '🔄 开始执行数据恢复...',

            # === 额外的冗余信息过滤 ===
            '总记录数:',
            '已标记记录数:',
            '未标记记录数:',
            '第一文件没有Order types列，无法检查API订单',

            # === 通用模式过滤 ===
            # 过滤包含特定模式的行
        ]

        # 检查是否匹配任何过滤模式
        for pattern in filter_patterns:
            if pattern in line:
                return True

        # 额外的模式匹配过滤
        # 过滤纯数字统计行（如 "32", "0" 等单独的数字）
        if line.strip().isdigit() and len(line.strip()) <= 3:
            return True

        # 过滤只包含状态名称的行
        status_only_patterns = ['Finish', 'Close', 'Refunding', 'True', 'False', 'NaN']
        if line.strip() in status_only_patterns:
            return True

        return False

    def _is_summary_info(self, line):
        """检测是否为重要的处理总结信息 - 强化版识别器"""
        summary_patterns = [
            # === 核心处理结果 ===
            '第一文件总金额:',
            '第二文件最终金额:',
            '第一文件总金额：',
            '第二文件最终金额：',
            '金额差异:',
            '金额匹配成功！',
            '金额匹配成功',
            '详细日志已保存至:',
            '脚本执行完成',
            '处理完成：',
            '处理完成:',

            # === 处理结果信息 ===
            '处理完成！结果已写入:',
            '- 数据已写入',
            '- 日志已写入',
            'Transaction ID恢复:',
            '使用指定的文件路径:',
            '第一文件:',
            '第二文件:',
            'Sheet名称:',
            '🎯 最终使用sheet:',
            '最终使用sheet:',

            # === 统计信息 ===
            '📊 transaction_id 模式统计:',
            'transaction_id 模式统计:',
            '处理: ',
            '匹配: ',
            '插入: ',
            '处理:',
            '匹配:',
            '插入:',
            '条',

            # === 时间和性能信息 ===
            '✅ 处理完成，耗时:',
            '处理完成，耗时:',
            '耗时:',
            '秒',

            # === 文件路径信息 ===
            '使用第一文件路径:',
            '使用第二文件路径:',
            '第一文件路径:',
            '第二文件路径:',

            # === 分隔符和标记 ===
            '============================================================',
            '详细日志已保存',
            '📊 开始数据处理...',
            '开始数据处理...',
            '🚀 启动模块化数据处理器...'
        ]

        return any(pattern in line for pattern in summary_patterns)
